---
name: "Example Template"
description: "A simple example template for demonstration"
variables:
  project_name: "Name of the project"
  author: "Author name"
  license: "Project license"
tags: ["example", "demo"]
version: "1.0.0"
requires_confirmation: false
---

# {{ project_name }}

This project was created by **{{ author }}** and is licensed under {{ license }}.

## Getting Started

Welcome to {{ project_name }}! This is an example template that demonstrates:

- Variable substitution
- Markdown content
- YAML frontmatter
- Template metadata

Generated on {{ date }} at {{ time }}.

## System Information

- User: {{ username }}
- OS: {{ os }}
- Architecture: {{ arch }}
- Working Directory: {{ cwd }}

## Random Data

- UUID: {{ uuid }}
- Random Number: {{ random_number }}

---

Template processed successfully!