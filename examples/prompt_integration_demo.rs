//! Example demonstrating the prompt design architecture integration with AutoRun

use autorun::prompts::{
    <PERSON><PERSON><PERSON><PERSON><PERSON>, Prompt, PromptContext, PromptIntegration, PromptMessage, PromptMetadata,
    PromptRole, PromptType,
};
use std::collections::HashMap;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize tracing for debugging
    tracing_subscriber::fmt::init();

    println!("🚀 AutoRun Prompt Design Architecture Demo");
    println!("==========================================\n");

    // 1. Initialize the prompt integration system
    println!("📚 Initializing Prompt System...");
    let mut prompt_integration = PromptIntegration::new()?;

    // Check if system is properly initialized
    if prompt_integration.is_initialized() {
        println!("✅ Prompt system initialized successfully");

        // Show statistics
        let stats = prompt_integration.get_stats()?;
        println!(
            "   📊 Loaded {} prompts ({} system, {} user)",
            stats.total_prompts, stats.system_prompts, stats.user_prompts
        );
        println!(
            "   🎯 Cache hit ratio: {:.1}%",
            stats.cache_hit_ratio * 100.0
        );
    } else {
        println!("⚠️  No prompts loaded - creating examples...");
        create_example_prompts(&mut prompt_integration)?;
    }

    println!();

    // 2. Demonstrate system prompt rendering
    println!("🤖 Rendering System Prompt...");
    match prompt_integration.render_system_prompt() {
        Ok(messages) => {
            for (i, message) in messages.iter().enumerate() {
                println!(
                    "   Message {}: [{}] {} chars",
                    i + 1,
                    message.role,
                    message.content.len()
                );
                if i == 0 {
                    // Show first 200 chars of system prompt
                    let preview = if message.content.len() > 200 {
                        format!("{}...", &message.content[..200])
                    } else {
                        message.content.clone()
                    };
                    println!("   Preview: {}", preview);
                }
            }
        }
        Err(e) => println!("   ❌ Failed to render system prompt: {}", e),
    }

    println!();

    // 3. Demonstrate context building
    println!("🔧 Building Comprehensive Context...");
    let context = prompt_integration.build_context()?;

    println!(
        "   🌍 Environment: {} on {}",
        context.environment.os, context.environment.platform
    );
    println!(
        "   📁 Working dir: {}",
        context.environment.working_directory
    );
    println!("   🔧 Git repo: {}", context.environment.is_git_repo);
    if let Some(ref branch) = context.environment.git_branch {
        println!("   🌿 Git branch: {}", branch);
    }
    println!("   📊 Variables: {}", context.variables.len());
    println!("   💬 History: {} messages", context.history.len());

    println!();

    // 4. Demonstrate code analysis prompt
    println!("🔍 Code Analysis Prompt Demo...");
    if let Ok(_) = prompt_integration
        .prompt_manager
        .get_prompt("code_analysis")
    {
        let mut analysis_context = context.clone();
        analysis_context = analysis_context
            .add_variable(
                "code_snippet",
                r#"
fn unsafe_add(a: i32, b: i32) -> i32 {
    let result = a + b;
    if result < a || result < b {
        panic!("Integer overflow detected!");
    }
    result
}
"#,
            )
            .add_variable("language", "rust")
            .add_variable("analysis_type", "code_quality");

        match prompt_integration
            .prompt_manager
            .render_prompt("code_analysis", &analysis_context)
        {
            Ok(messages) => {
                println!(
                    "   ✅ Generated code analysis prompt with {} messages",
                    messages.len()
                );
                if let Some(last_msg) = messages.last() {
                    let preview = if last_msg.content.len() > 300 {
                        format!("{}...", &last_msg.content[..300])
                    } else {
                        last_msg.content.clone()
                    };
                    println!("   📋 Prompt preview: {}", preview);
                }
            }
            Err(e) => println!("   ❌ Failed to render analysis prompt: {}", e),
        }
    } else {
        println!("   ⚠️  Code analysis prompt not found");
    }

    println!();

    // 5. Demonstrate custom prompt creation
    println!("🎨 Creating Custom Prompt...");
    let custom_prompt = create_custom_debugging_prompt()?;
    prompt_integration
        .prompt_manager
        .register_prompt(custom_prompt)?;

    let mut debug_context = context.clone();
    debug_context = debug_context
        .add_variable(
            "error_message",
            "thread 'main' panicked at 'index out of bounds'",
        )
        .add_variable("code_location", "src/main.rs:42")
        .add_variable("backtrace", "Available");

    match prompt_integration
        .prompt_manager
        .render_prompt("debug_assistant", &debug_context)
    {
        Ok(messages) => {
            println!("   ✅ Generated custom debugging prompt");
            println!("   📝 Messages: {}", messages.len());
        }
        Err(e) => println!("   ❌ Failed to render debug prompt: {}", e),
    }

    println!();

    // 6. Demonstrate conversation context
    println!("💬 Conversation Context Demo...");
    let mut conversation_context = ContextBuilder::new()
        .user_message("I need help optimizing this Rust function for performance")
        .assistant_message("I'd be happy to help! Please share the function you'd like to optimize.")
        .user_message("Here's the function: fn slow_search(data: &[i32], target: i32) -> Option<usize> { ... }")
        .variable("optimization_focus", "performance")
        .variable("language", "rust")
        .build();

    // Merge with system context
    conversation_context = prompt_integration
        .build_context()?
        .merge(conversation_context);

    println!(
        "   💭 Built conversation with {} messages",
        conversation_context.history.len()
    );
    println!("   🎯 Variables: {}", conversation_context.variables.len());

    println!();

    // 7. Show available prompts
    println!("📋 Available Prompts:");
    let all_prompts = prompt_integration.prompt_manager.list_prompts()?;
    for prompt_meta in &all_prompts {
        println!(
            "   • {} (v{}) - {} [{}]",
            prompt_meta.name, prompt_meta.version, prompt_meta.description, prompt_meta.prompt_type
        );
    }

    println!();

    // 8. Performance and caching demo
    println!("⚡ Performance Demo...");
    let start = std::time::Instant::now();

    for i in 0..10 {
        let test_context = context.clone().add_variable("iteration", i);
        let _ = prompt_integration.prompt_manager.render_string(
            "Iteration {{ iteration }}: Current time is {{ env.current_date }}",
            &test_context,
        )?;
    }

    let elapsed = start.elapsed();
    println!("   🏃 Rendered 10 prompts in {:?}", elapsed);

    let final_stats = prompt_integration.get_stats()?;
    println!(
        "   📈 Final cache hit ratio: {:.1}%",
        final_stats.cache_hit_ratio * 100.0
    );

    println!();
    println!("🎉 Demo completed successfully!");
    println!("   The prompt design architecture is fully integrated with AutoRun");
    println!("   and ready for use in your agentic coding assistant!");

    Ok(())
}

/// Create example prompts if none are found
fn create_example_prompts(
    integration: &mut PromptIntegration,
) -> Result<(), Box<dyn std::error::Error>> {
    // Create a simple system prompt
    let system_prompt = Prompt::new(
        "simple_system",
        "1.0.0",
        "Simple system prompt for demo",
        PromptType::SystemBase,
        "You are AutoRun, a helpful coding assistant.",
    );

    integration.prompt_manager.register_prompt(system_prompt)?;

    println!("   ✅ Created example prompts");
    Ok(())
}

/// Create a custom debugging assistant prompt
fn create_custom_debugging_prompt() -> Result<Prompt, Box<dyn std::error::Error>> {
    let mut metadata = PromptMetadata::new(
        "debug_assistant",
        "1.0.0",
        "Debugging assistant for runtime errors",
        PromptType::ErrorHandling,
    );

    metadata = metadata
        .with_tag("debugging")
        .with_tag("error_handling")
        .with_required_variable("error_message")
        .with_required_variable("code_location")
        .with_optional_variable("backtrace", "Not available");

    let system_template = r#"
You are an expert debugging assistant specializing in {{ language | default(value="Rust") }}.
Your role is to help developers understand and fix runtime errors efficiently.

Focus on:
- Root cause analysis
- Clear explanations of what went wrong
- Specific fix recommendations
- Prevention strategies for similar issues
"#
    .trim();

    let main_template = r#"
I encountered a runtime error and need help debugging it:

**Error Message:** {{ error_message }}
**Location:** {{ code_location }}
**Backtrace:** {{ backtrace }}

Please help me:
1. Understand what caused this error
2. Identify the root cause
3. Provide specific steps to fix it
4. Suggest how to prevent similar errors in the future

{% if additional_context %}
**Additional Context:**
{{ additional_context }}
{% endif %}
"#
    .trim();

    let prompt = Prompt::new(
        metadata.name.clone(),
        metadata.version.clone(),
        metadata.description.clone(),
        metadata.prompt_type.clone(),
        main_template.to_string(),
    )
    .with_system_template(system_template.to_string());

    Ok(prompt)
}
