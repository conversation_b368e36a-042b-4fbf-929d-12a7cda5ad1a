//! Example demonstrating TiktokenProvider usage with OpenAI models

use autorun::llm::Message;
use autorun::prompts::tokenization::{
    <PERSON><PERSON><PERSON><PERSON>Provider, TokenizerConfig, TokenizerFactory, TokenizerProvider,
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== TiktokenProvider Demo ===\n");

    // Test different OpenAI models
    let models = vec!["gpt-4o", "gpt-4", "gpt-3.5-turbo", "text-davinci-003"];

    for model in models {
        println!("Testing model: {}", model);

        // Create provider using factory
        match TokenizerFactory::create_provider(model) {
            Ok(provider) => {
                let config = TokenizerConfig {
                    model: model.to_string(),
                    ..Default::default()
                };

                // Test simple text tokenization
                let text = "Hello, this is a test of the tiktoken provider for OpenAI models!";
                let token_count = provider.count_tokens(text, &config).await?;
                println!("  Text: \"{}\"", text);
                println!("  Token count: {}", token_count);

                // Test encoding and decoding
                let tokens = provider.encode(text, &config).await?;
                let decoded = provider.decode(&tokens, &config).await?;
                println!("  Encode/decode successful: {}", text == decoded);

                // Get model info
                let info = provider.get_model_info(model).await?;
                println!("  Max context tokens: {}", info.max_context_tokens);
                println!("  Max output tokens: {}", info.max_output_tokens);

                if let (Some(input_cost), Some(output_cost)) =
                    (info.input_token_cost, info.output_token_cost)
                {
                    println!(
                        "  Cost: ${:.4}/1K input, ${:.4}/1K output",
                        input_cost, output_cost
                    );
                }

                // Calculate usage
                let usage = provider.calculate_usage(text, &config).await?;
                println!("  Usage: {:.1}% of context window", usage.usage_percentage);
                println!("  Remaining tokens: {}", usage.remaining_tokens);
            }
            Err(e) => {
                println!("  Failed to create provider: {}", e);
            }
        }
        println!();
    }

    // Test chat message tokenization
    println!("Testing chat message tokenization:");
    let provider = TiktokenProvider::new("gpt-4").unwrap();

    let messages = vec![
        Message::system("You are a helpful coding assistant."),
        Message::user("Write a hello world program in Rust."),
        Message::assistant("Here's a simple Hello World program in Rust:\n\n```rust\nfn main() {\n    println!(\"Hello, world!\");\n}\n```"),
    ];

    let chat_tokens = provider.count_chat_tokens(&messages)?;
    println!("  Total chat tokens: {}", chat_tokens);

    // Test truncation
    println!("\nTesting text truncation:");
    let long_text = "This is a very long text that will be truncated. ".repeat(100);
    let config = TokenizerConfig::default();
    let provider = TokenizerFactory::create_provider("gpt-4")?;

    match provider.truncate_to_limit(&long_text, 50, &config).await {
        Ok(truncated) => {
            let truncated_tokens = provider.count_tokens(&truncated, &config).await?;
            println!("  Original length: {} chars", long_text.len());
            println!("  Truncated to {} tokens", truncated_tokens);
            println!(
                "  Truncated text: {}...",
                &truncated[..50.min(truncated.len())]
            );
        }
        Err(e) => {
            println!("  Truncation failed: {}", e);
        }
    }

    Ok(())
}
