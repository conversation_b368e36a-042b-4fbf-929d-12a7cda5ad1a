//! Demonstrates the tokenization module functionality
//!
//! Run with: cargo run --example tokenization_demo

use autorun::prompts::tokenization::{
    PaddingStrategy, TokenizationError, TokenizerConfig, TokenizerFactory, TruncationStrategy,
};
use std::collections::HashMap;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== AutoRun Tokenization Demo ===\n");

    // Create a sample tokenizer configuration
    let mut config = TokenizerConfig {
        model: "claude-3-sonnet".to_string(),
        max_tokens: 4096,
        include_special_tokens: true,
        custom_limits: HashMap::new(),
        padding_strategy: PaddingStrategy::None,
        truncation_strategy: TruncationStrategy::End,
    };

    // Add custom limits for specific use cases
    config
        .custom_limits
        .insert("system_prompt".to_string(), 1000);
    config
        .custom_limits
        .insert("user_message".to_string(), 2000);

    println!("Configuration:");
    println!("  Model: {}", config.model);
    println!("  Max Tokens: {}", config.max_tokens);
    println!(
        "  Include Special Tokens: {}",
        config.include_special_tokens
    );
    println!("  Padding Strategy: {:?}", config.padding_strategy);
    println!("  Truncation Strategy: {:?}", config.truncation_strategy);
    println!("  Custom Limits: {:?}", config.custom_limits);
    println!();

    // Demonstrate supported models
    println!("Supported Models:");
    for model in TokenizerFactory::supported_models() {
        println!("  - {}", model);
    }
    println!();

    // Try to create a tokenizer (will fail for now as implementations aren't added yet)
    match TokenizerFactory::create_provider(&config.model) {
        Ok(_provider) => {
            println!("✓ Successfully created tokenizer for {}", config.model);
            // In a real implementation, we would use the provider here
        }
        Err(TokenizationError::TokenizerNotAvailable { model }) => {
            println!("✗ Tokenizer not yet implemented for model: {}", model);
            println!("  (This is expected - implementations will be added in future tasks)");
        }
        Err(e) => {
            println!("✗ Unexpected error: {}", e);
        }
    }
    println!();

    // Demonstrate different configuration options
    println!("Example Configurations:");

    // Configuration for chat completion
    let chat_config = TokenizerConfig {
        model: "gpt-4".to_string(),
        max_tokens: 8192,
        include_special_tokens: true,
        custom_limits: HashMap::from([
            ("system".to_string(), 1500),
            ("user".to_string(), 3000),
            ("assistant".to_string(), 3500),
        ]),
        padding_strategy: PaddingStrategy::None,
        truncation_strategy: TruncationStrategy::End,
    };
    println!("  Chat Completion Config: {:?}", chat_config);

    // Configuration for embeddings
    let embedding_config = TokenizerConfig {
        model: "text-embedding-ada-002".to_string(),
        max_tokens: 8191,
        include_special_tokens: false,
        custom_limits: HashMap::new(),
        padding_strategy: PaddingStrategy::MaxLength,
        truncation_strategy: TruncationStrategy::End,
    };
    println!("  Embedding Config: {:?}", embedding_config);

    // Configuration for code generation
    let code_config = TokenizerConfig {
        model: "claude-3-opus".to_string(),
        max_tokens: 16384,
        include_special_tokens: true,
        custom_limits: HashMap::from([
            ("context".to_string(), 12000),
            ("generation".to_string(), 4000),
        ]),
        padding_strategy: PaddingStrategy::None,
        truncation_strategy: TruncationStrategy::Both,
    };
    println!("  Code Generation Config: {:?}", code_config);

    println!("\n✓ Tokenization module is ready for implementation!");

    Ok(())
}
