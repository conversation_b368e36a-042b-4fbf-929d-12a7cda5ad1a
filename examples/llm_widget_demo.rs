// Example demonstrating LLM widget generation integration

use autorun::agent::core::{Agent<PERSON>ore, WidgetGenerationRequest};
use autorun::config::Config;
use autorun::llm::client::AnthropicClient;
use autorun::tools::executor::ToolExecutor;
use autorun::tools::ExecutionContext;
use autorun::ui::llm_integration::LLMWidgetIntegration;
use autorun::ui::widgets::{WidgetFactory, WidgetRegistry};
use serde_json::json;
use std::path::PathBuf;
use std::sync::Arc;
use tokio::sync::RwLock;

#[tokio::main]
async fn main() -> autorun::errors::Result<()> {
    // Initialize tracing
    tracing_subscriber::fmt::init();

    println!("🚀 LLM Widget Generation Demo");
    println!("==============================");

    // Create mock configuration
    let config = Config::default();
    let execution_context = ExecutionContext::with_config(
        PathBuf::from("/tmp"),
        config.clone(),
        "demo-session".to_string(),
    );

    // Create tool executor
    let tool_executor = Arc::new(ToolExecutor::new());

    // Create LLM provider (mock for demo)
    let llm_provider = Arc::new(MockLLMProvider::new());

    // Create widget factory and registry
    let widget_factory = Arc::new(WidgetFactory::new());
    let widget_registry = Arc::new(WidgetRegistry::new(widget_factory.clone()));

    // Create LLM widget integration
    let widget_integration = Arc::new(RwLock::new(LLMWidgetIntegration::new(
        llm_provider.clone(),
        widget_factory.clone(),
        widget_registry.clone(),
    )));

    // Create agent core
    let mut agent_core = AgentCore::new(llm_provider, tool_executor, execution_context);
    agent_core.set_widget_integration(widget_integration.clone());

    // Demo widget generation requests
    let demo_requests = vec![
        WidgetGenerationRequest {
            widget_type: "checkbox".to_string(),
            context: "User wants to select multiple features to enable".to_string(),
            config: json!({
                "features": ["Feature A", "Feature B", "Feature C"],
                "default_selected": ["Feature A"]
            }),
        },
        WidgetGenerationRequest {
            widget_type: "progress".to_string(),
            context: "Show progress of file download operation".to_string(),
            config: json!({
                "total": 100,
                "current": 45,
                "label": "Downloading files..."
            }),
        },
        WidgetGenerationRequest {
            widget_type: "list".to_string(),
            context: "Display a list of available commands".to_string(),
            config: json!({
                "items": ["help", "list", "run", "quit"],
                "selectable": true
            }),
        },
    ];

    // Test widget generation
    for (i, request) in demo_requests.iter().enumerate() {
        println!(
            "\n📝 Demo {}: Generating {} widget",
            i + 1,
            request.widget_type
        );
        println!("Context: {}", request.context);

        match agent_core.handle_widget_generation(request.clone()).await {
            Ok(widget_id) => {
                println!("✅ Widget generated successfully!");
                println!("   Widget ID: {}", widget_id);
                println!("   Type: {}", request.widget_type);

                // Test widget update
                println!("🔄 Testing widget update...");
                let update_event = autorun::agent::core::WidgetUpdateEvent {
                    widget_id: widget_id.clone(),
                    event_type: "refresh".to_string(),
                    data: json!({"timestamp": chrono::Utc::now()}),
                };

                match agent_core.handle_widget_update(update_event).await {
                    Ok(_) => println!("✅ Widget update successful!"),
                    Err(e) => println!("❌ Widget update failed: {}", e),
                }
            }
            Err(e) => {
                println!("❌ Widget generation failed: {}", e);
            }
        }
    }

    // Test contextual widget generation
    println!("\n🧠 Testing contextual widget generation...");
    agent_core
        .add_user_message("I need to track the progress of multiple tasks".to_string())
        .await?;

    match agent_core.generate_contextual_widget("progress").await {
        Ok(widget_id) => {
            println!("✅ Contextual widget generated!");
            println!("   Widget ID: {}", widget_id);
        }
        Err(e) => {
            println!("❌ Contextual widget generation failed: {}", e);
        }
    }

    // Display debug information
    println!("\n🔍 Debug Information:");
    let debug_info = {
        let integration_guard = widget_integration.read().await;
        integration_guard.get_debug_info()
    };

    println!("   Cache size: {}", debug_info.cache_size);
    println!("   Cache TTL: {} seconds", debug_info.cache_ttl_seconds);
    println!("   Registered widgets: {:?}", debug_info.registered_widgets);
    println!(
        "   Available widget types: {:?}",
        debug_info.available_widget_types
    );

    println!("\n🎉 Demo completed successfully!");
    Ok(())
}

// Mock LLM provider for demo purposes
struct MockLLMProvider;

impl MockLLMProvider {
    fn new() -> Self {
        Self
    }
}

#[async_trait::async_trait]
impl autorun::llm::LLMProvider for MockLLMProvider {
    async fn complete(
        &self,
        messages: Vec<autorun::llm::Message>,
    ) -> autorun::errors::Result<String> {
        // Mock response for widget configuration
        let last_message = messages.last().map(|m| &m.content).unwrap_or("");

        if last_message.contains("checkbox") {
            Ok(r#"{
                "widget_type": "checkbox",
                "id": "checkbox-demo-123",
                "title": "Feature Selection",
                "properties": {
                    "items": [
                        {"label": "Feature A", "checked": true},
                        {"label": "Feature B", "checked": false},
                        {"label": "Feature C", "checked": false}
                    ],
                    "multi_select": true
                },
                "layout": {
                    "constraints": [{"Min": 5}],
                    "direction": "Vertical"
                }
            }"#
            .to_string())
        } else if last_message.contains("progress") {
            Ok(r#"{
                "widget_type": "progress",
                "id": "progress-demo-456",
                "title": "Download Progress",
                "properties": {
                    "value": 45,
                    "max": 100,
                    "label": "Downloading files...",
                    "show_percentage": true
                },
                "layout": {
                    "constraints": [{"Length": 3}],
                    "direction": "Horizontal"
                }
            }"#
            .to_string())
        } else if last_message.contains("list") {
            Ok(r#"{
                "widget_type": "list",
                "id": "list-demo-789",
                "title": "Available Commands",
                "properties": {
                    "items": ["help", "list", "run", "quit"],
                    "selected_index": 0,
                    "highlight_style": "bold"
                },
                "layout": {
                    "constraints": [{"Percentage": 50}],
                    "direction": "Vertical"
                }
            }"#
            .to_string())
        } else {
            Ok(r#"{
                "widget_type": "input",
                "id": "input-fallback",
                "title": "User Input",
                "properties": {
                    "placeholder": "Enter text...",
                    "value": "",
                    "max_length": 100
                },
                "layout": {
                    "constraints": [{"Length": 3}],
                    "direction": "Horizontal"
                }
            }"#
            .to_string())
        }
    }

    fn as_any(&self) -> Option<&dyn std::any::Any> {
        Some(self)
    }
}
