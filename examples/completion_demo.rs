use autorun::{
    commands::CommandRegistry,
    ui::completion::{CompletionEngine, CompletionPopup},
};
use std::sync::Arc;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("Testing AutoRun Completion System");

    // Create a command registry
    let registry = Arc::new(CommandRegistry::new());

    // Create the completion engine
    let engine = CompletionEngine::new(registry);

    // Test command completion
    println!("\n1. Testing command completion for '/h':");
    let suggestions = engine.get_suggestions("/h", 2, None).await;
    for suggestion in &suggestions {
        println!(
            "  - {} ({}): {}",
            suggestion.icon, suggestion.display, suggestion.description
        );
    }

    // Test parameter completion
    println!("\n2. Testing parameter completion for '/help ':");
    let suggestions = engine.get_suggestions("/help ", 6, None).await;
    for suggestion in &suggestions {
        println!(
            "  - {} ({}): {}",
            suggestion.icon, suggestion.display, suggestion.description
        );
    }

    // Test completion popup
    println!("\n3. Testing completion popup:");
    let mut popup = CompletionPopup::new();
    println!("  Initial visibility: {}", popup.is_visible());

    if !suggestions.is_empty() {
        popup.update_suggestions(suggestions);
        println!("  After adding suggestions: {}", popup.is_visible());

        if let Some(selected) = popup.get_selected() {
            println!("  Selected suggestion: {}", selected.display);
        }
    }

    // Test usage recording
    println!("\n4. Testing usage recording:");
    engine.record_usage("help", vec!["clear".to_string()]).await;
    engine
        .record_usage("save", vec!["conversation.txt".to_string()])
        .await;

    println!("  Usage recorded successfully!");

    // Test suggestions again to see if usage affects scoring
    println!("\n5. Testing suggestions after usage recording:");
    let suggestions = engine.get_suggestions("/", 1, None).await;
    for suggestion in &suggestions {
        println!(
            "  - {} (score: {:.1}): {}",
            suggestion.display, suggestion.score, suggestion.description
        );
    }

    println!("\nCompletion system test completed successfully! 🎉");
    Ok(())
}
