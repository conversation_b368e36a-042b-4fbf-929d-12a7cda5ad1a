// Enhanced List Widget Demo
// Demonstrates AI-powered list features, filtering, sorting, and checkbox integration

use autorun::agent::core::{WidgetGenerationRequest, WidgetUpdateEvent};
use autorun::errors::Result;
use autorun::ui::widgets::enhanced_list::{
    EnhancedListConfig, EnhancedListItem, EnhancedListWidget, EnhancedListWidgetBuilder,
    ListItemStyle, PaginationConfig, SortConfig,
};
use autorun::ui::widgets::{
    AiWidget, LayoutConstraint, LayoutDirection, WidgetBuilder, WidgetConfig, WidgetLayout,
};
use crossterm::event::{KeyCode, KeyEvent, KeyModifiers};
use serde_json::{json, Value};
use std::collections::HashMap;

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize tracing
    tracing_subscriber::fmt::init();

    println!("🚀 Enhanced List Widget Demo");
    println!("=============================");

    // Demo 1: Basic Enhanced List
    demo_basic_list().await?;

    // Demo 2: Multi-select with Checkboxes
    demo_checkbox_integration().await?;

    // Demo 3: Pagination and Sorting
    demo_pagination_and_sorting().await?;

    // Demo 4: AI Widget Generation
    demo_ai_generation().await?;

    // Demo 5: Dynamic Content Updates
    demo_dynamic_updates().await?;

    println!("\n✅ All Enhanced List Widget demos completed successfully!");
    Ok(())
}

async fn demo_basic_list() -> Result<()> {
    println!("\n📋 Demo 1: Basic Enhanced List");
    println!("──────────────────────────────");

    let config = EnhancedListConfig {
        id: "demo-basic-list".to_string(),
        title: Some("Programming Languages".to_string()),
        items: vec![
            EnhancedListItem {
                id: "rust".to_string(),
                content: "Rust".to_string(),
                description: Some("Systems programming language".to_string()),
                metadata: HashMap::from([
                    ("year".to_string(), json!(2010)),
                    ("paradigm".to_string(), json!("systems")),
                ]),
                selectable: true,
                checkable: false,
                checked: false,
                style: Some(ListItemStyle {
                    fg_color: Some("red".to_string()),
                    bg_color: None,
                    modifiers: vec!["bold".to_string()],
                }),
                children: None,
            },
            EnhancedListItem {
                id: "python".to_string(),
                content: "Python".to_string(),
                description: Some("High-level programming language".to_string()),
                metadata: HashMap::from([
                    ("year".to_string(), json!(1991)),
                    ("paradigm".to_string(), json!("general-purpose")),
                ]),
                selectable: true,
                checkable: false,
                checked: false,
                style: Some(ListItemStyle {
                    fg_color: Some("blue".to_string()),
                    bg_color: None,
                    modifiers: vec!["italic".to_string()],
                }),
                children: None,
            },
            EnhancedListItem {
                id: "javascript".to_string(),
                content: "JavaScript".to_string(),
                description: Some("Dynamic web programming language".to_string()),
                metadata: HashMap::from([
                    ("year".to_string(), json!(1995)),
                    ("paradigm".to_string(), json!("web")),
                ]),
                selectable: true,
                checkable: false,
                checked: false,
                style: Some(ListItemStyle {
                    fg_color: Some("yellow".to_string()),
                    bg_color: None,
                    modifiers: vec!["underlined".to_string()],
                }),
                children: None,
            },
        ],
        show_descriptions: true,
        filterable: true,
        searchable: true,
        ..Default::default()
    };

    let widget = EnhancedListWidget::new(config);

    println!(
        "✅ Basic list created with {} items",
        widget.get_state()["total_item_count"]
    );
    println!("   Widget ID: {}", widget.id());
    println!("   Widget Type: {}", widget.widget_type());
    println!("   Description: {}", widget.description());

    // Simulate some navigation
    println!("🎮 Simulating navigation...");
    let state = widget.get_state();
    println!(
        "   Current state: {}",
        serde_json::to_string_pretty(&state)?
    );

    Ok(())
}

async fn demo_checkbox_integration() -> Result<()> {
    println!("\n☑️  Demo 2: Multi-select with Checkboxes");
    println!("─────────────────────────────────────────");

    let config = EnhancedListConfig {
        id: "demo-checkbox-list".to_string(),
        title: Some("Select Features to Install".to_string()),
        items: vec![
            EnhancedListItem {
                id: "feature-a".to_string(),
                content: "Feature A - Core Functionality".to_string(),
                description: Some("Essential core features (required)".to_string()),
                metadata: HashMap::from([
                    ("required".to_string(), json!(true)),
                    ("size_mb".to_string(), json!(15)),
                ]),
                selectable: true,
                checkable: true,
                checked: true, // Pre-selected required feature
                style: Some(ListItemStyle {
                    fg_color: Some("green".to_string()),
                    bg_color: None,
                    modifiers: vec!["bold".to_string()],
                }),
                children: None,
            },
            EnhancedListItem {
                id: "feature-b".to_string(),
                content: "Feature B - Advanced Tools".to_string(),
                description: Some("Advanced development tools".to_string()),
                metadata: HashMap::from([
                    ("required".to_string(), json!(false)),
                    ("size_mb".to_string(), json!(25)),
                ]),
                selectable: true,
                checkable: true,
                checked: false,
                style: None,
                children: None,
            },
            EnhancedListItem {
                id: "feature-c".to_string(),
                content: "Feature C - Documentation".to_string(),
                description: Some("Offline documentation and examples".to_string()),
                metadata: HashMap::from([
                    ("required".to_string(), json!(false)),
                    ("size_mb".to_string(), json!(8)),
                ]),
                selectable: true,
                checkable: true,
                checked: false,
                style: None,
                children: None,
            },
        ],
        multi_select: true,
        show_checkboxes: true,
        show_descriptions: true,
        ..Default::default()
    };

    let mut widget = EnhancedListWidget::new(config);

    println!("✅ Checkbox list created");
    println!(
        "   Multi-select enabled: {}",
        widget.get_state()["multi_select"]
    );

    // Simulate checkbox interactions
    println!("🎮 Simulating checkbox interactions...");

    // Toggle feature B
    let toggle_event = WidgetUpdateEvent {
        widget_id: "demo-checkbox-list".to_string(),
        event_type: "key_event".to_string(),
        data: json!({"key": "space"}),
    };

    widget.handle_event(&toggle_event).await?;
    println!("   Feature B toggled");

    // Select all
    let select_all_event = WidgetUpdateEvent {
        widget_id: "demo-checkbox-list".to_string(),
        event_type: "key_event".to_string(),
        data: json!({"key": "a"}),
    };

    widget.handle_event(&select_all_event).await?;
    println!("   All features selected");

    let final_state = widget.get_state();
    println!("   Final checked items: {}", final_state["checked_items"]);

    Ok(())
}

async fn demo_pagination_and_sorting() -> Result<()> {
    println!("\n📄 Demo 3: Pagination and Sorting");
    println!("──────────────────────────────────");

    // Create a list with many items to demonstrate pagination
    let mut items = Vec::new();
    for i in 1..=25 {
        items.push(EnhancedListItem {
            id: format!("item-{}", i),
            content: format!("Item {}", i),
            description: Some(format!("This is item number {}", i)),
            metadata: HashMap::from([
                ("index".to_string(), json!(i)),
                ("priority".to_string(), json!(i % 3)), // 0, 1, 2
            ]),
            selectable: true,
            checkable: true,
            checked: i % 5 == 0, // Every 5th item pre-checked
            style: if i % 2 == 0 {
                Some(ListItemStyle {
                    fg_color: Some("cyan".to_string()),
                    bg_color: None,
                    modifiers: vec![],
                })
            } else {
                None
            },
            children: None,
        });
    }

    let config = EnhancedListConfig {
        id: "demo-paginated-list".to_string(),
        title: Some("Large Dataset with Pagination".to_string()),
        items,
        multi_select: true,
        show_checkboxes: true,
        show_descriptions: false, // Hide descriptions for cleaner view
        pagination: PaginationConfig {
            enabled: true,
            page_size: 5,
            show_page_info: true,
        },
        sort_options: SortConfig {
            enabled: true,
            sort_by: "content".to_string(),
            ascending: true,
        },
        ..Default::default()
    };

    let mut widget = EnhancedListWidget::new(config);

    println!("✅ Paginated list created with 25 items");
    println!("   Page size: 5 items per page");

    let initial_state = widget.get_state();
    println!("   Current page: {}", initial_state["current_page"]);
    println!("   Visible items: {}", initial_state["visible_item_count"]);

    // Test sorting
    println!("🔄 Testing sort functionality...");
    let sort_event = WidgetUpdateEvent {
        widget_id: "demo-paginated-list".to_string(),
        event_type: "sort_update".to_string(),
        data: json!({
            "sort_config": {
                "enabled": true,
                "sort_by": "metadata.priority",
                "ascending": false
            }
        }),
    };

    widget.handle_event(&sort_event).await?;
    println!("   Sorted by priority (descending)");

    // Test filtering
    println!("🔍 Testing filter functionality...");
    let filter_event = WidgetUpdateEvent {
        widget_id: "demo-paginated-list".to_string(),
        event_type: "filter_update".to_string(),
        data: json!({"filter": "1"}), // Show only items containing "1"
    };

    widget.handle_event(&filter_event).await?;
    let filtered_state = widget.get_state();
    println!("   Filtered for items containing '1'");
    println!(
        "   Visible items after filter: {}",
        filtered_state["visible_item_count"]
    );

    Ok(())
}

async fn demo_ai_generation() -> Result<()> {
    println!("\n🤖 Demo 4: AI Widget Generation");
    println!("─────────────────────────────────");

    let builder = EnhancedListWidgetBuilder;

    // Simulate AI generation request
    let ai_request = WidgetGenerationRequest {
        widget_type: "enhanced_list".to_string(),
        context: "Create a to-do list with tasks for a software project".to_string(),
        config: json!({
            "title": "Project Tasks",
            "categories": ["Development", "Testing", "Documentation"],
            "priorities": ["High", "Medium", "Low"]
        }),
    };

    println!("🔧 Building widget from AI request...");
    println!("   Context: {}", ai_request.context);

    // Get schema for validation
    let schema = builder.get_schema();
    println!("✅ Widget schema available");
    println!("   Schema type: {}", schema["type"]);

    // Validate a sample config
    let sample_config = WidgetConfig {
        widget_type: "enhanced_list".to_string(),
        id: "ai-generated-list".to_string(),
        title: Some("AI Generated List".to_string()),
        properties: HashMap::from([(
            "enhanced_list_config".to_string(),
            json!({
                "id": "ai-generated-list",
                "title": "AI Generated List",
                "items": [
                    {
                        "id": "task1",
                        "content": "Implement user authentication",
                        "description": "Add login and registration functionality",
                        "metadata": {"priority": "high", "category": "development"},
                        "selectable": true,
                        "checkable": true,
                        "checked": false
                    }
                ],
                "multi_select": true,
                "show_checkboxes": true,
                "show_descriptions": true
            }),
        )]),
        layout: WidgetLayout {
            constraints: vec![LayoutConstraint::Percentage(80)],
            direction: LayoutDirection::Vertical,
        },
    };

    // Validate config
    match builder.validate_config(&sample_config) {
        Ok(()) => println!("✅ Sample config validation passed"),
        Err(e) => println!("❌ Sample config validation failed: {}", e),
    }

    // Build widget from AI request
    match builder.build(&ai_request).await {
        Ok(ai_widget) => {
            println!("✅ AI widget generation successful");
            println!("   Generated widget ID: {}", ai_widget.id());
            println!("   Widget type: {}", ai_widget.widget_type());
        }
        Err(e) => println!("❌ AI widget generation failed: {}", e),
    }

    Ok(())
}

async fn demo_dynamic_updates() -> Result<()> {
    println!("\n🔄 Demo 5: Dynamic Content Updates");
    println!("───────────────────────────────────");

    let config = EnhancedListConfig {
        id: "demo-dynamic-list".to_string(),
        title: Some("Dynamic Content Demo".to_string()),
        items: vec![EnhancedListItem {
            id: "initial".to_string(),
            content: "Initial item".to_string(),
            description: Some("This item was here from the start".to_string()),
            metadata: HashMap::new(),
            selectable: true,
            checkable: true,
            checked: false,
            style: None,
            children: None,
        }],
        auto_update: true,
        update_interval_ms: Some(2000),
        ..Default::default()
    };

    let mut widget = EnhancedListWidget::new(config);

    println!("✅ Dynamic list created");
    println!(
        "   Auto-update enabled: {}",
        widget.get_state()["auto_update"] != Value::Null
    );

    // Simulate content update from LLM
    println!("📡 Simulating content update from LLM...");

    let new_items = vec![
        json!({
            "id": "updated-1",
            "content": "Updated item 1",
            "description": "This item was added dynamically",
            "metadata": {"source": "llm", "timestamp": "2024-01-01T12:00:00Z"},
            "selectable": true,
            "checkable": true,
            "checked": false
        }),
        json!({
            "id": "updated-2",
            "content": "Updated item 2",
            "description": "Another dynamically added item",
            "metadata": {"source": "llm", "timestamp": "2024-01-01T12:01:00Z"},
            "selectable": true,
            "checkable": true,
            "checked": true
        }),
    ];

    let content_update = WidgetUpdateEvent {
        widget_id: "demo-dynamic-list".to_string(),
        event_type: "content_update".to_string(),
        data: json!({"items": new_items}),
    };

    widget.handle_event(&content_update).await?;

    let updated_state = widget.get_state();
    println!("✅ Content updated successfully");
    println!("   New item count: {}", updated_state["total_item_count"]);
    println!("   Checked items: {}", updated_state["checked_items"]);

    // Demonstrate search functionality
    println!("🔍 Testing search functionality...");
    let search_event = WidgetUpdateEvent {
        widget_id: "demo-dynamic-list".to_string(),
        event_type: "search_update".to_string(),
        data: json!({"search": "item 1"}),
    };

    widget.handle_event(&search_event).await?;
    let search_state = widget.get_state();
    println!("   Search for 'item 1' completed");
    println!(
        "   Visible items after search: {}",
        search_state["visible_item_count"]
    );

    Ok(())
}
