use autorun::llm::{
    LLMProviderWithStreaming, Message, OpenAICompatibleClient, OpenAICompatibleConfig,
};
use std::time::Duration;
use tokio;
use tracing::info;
use tracing_subscriber;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::INFO)
        .init();

    // Create OpenRouter client
    let openrouter_config = OpenAICompatibleConfig {
        api_key: std::env::var("OPENROUTER_API_KEY")
            .unwrap_or_else(|_| panic!("OPENROUTER_API_KEY environment variable is required")),
        base_url: "https://openrouter.ai/api/v1".to_string(),
        model: std::env::var("OPENROUTER_MODEL")
            .unwrap_or_else(|_| "anthropic/claude-3-sonnet-20240229".to_string()),
        max_tokens: 1024,
        temperature: 0.7,
        timeout: Duration::from_secs(120),
    };

    let client = OpenAICompatibleClient::new(openrouter_config)?;

    // Create a test message
    let messages = vec![Message {
        role: "user".to_string(),
        content:
            "Write a short story about a robot learning to paint. Make it at least 3 paragraphs."
                .to_string(),
        tool_call_id: None,
        tool_calls: None,
    }];

    info!("Starting streaming request to OpenRouter...");

    // Make streaming request
    let mut stream = client.complete_streaming(messages).await?;

    info!("Receiving streaming response...");

    // Process the stream
    let mut full_content = String::new();
    let mut chunk_count = 0;

    while let Some(chunk) = stream.next_chunk().await? {
        chunk_count += 1;

        if let Some(content) = &chunk.content {
            print!("{}", content);
            full_content.push_str(content);
            // Flush stdout to show content immediately
            use std::io::{self, Write};
            io::stdout().flush()?;
        }

        // Handle tool calls if any
        if !chunk.tool_calls.is_empty() {
            info!("Received tool calls: {:?}", chunk.tool_calls);
        }

        // Check if finished
        if let Some(finish_reason) = &chunk.finish_reason {
            info!("\nStream finished with reason: {}", finish_reason);
            break;
        }
    }

    println!("\n");
    info!("Streaming complete. Received {} chunks", chunk_count);
    info!("Total content length: {} characters", full_content.len());

    Ok(())
}
