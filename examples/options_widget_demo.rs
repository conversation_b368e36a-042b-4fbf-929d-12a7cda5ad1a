// Example demonstrating the Options Widget System
// This shows how to create and use radio buttons, dropdowns, and button groups

use autorun::ui::widgets::{
    ButtonGroupLayout, OptionItem, OptionsConfig, OptionsDisplayMode, OptionsWidget,
    OptionsWidgetBuilder, WidgetGenerationRequest,
};
use ratatui::prelude::*;
use serde_json::json;
use std::io::stdout;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("Options Widget System Demo");
    println!("==========================");

    // Create the widget builder
    let builder = OptionsWidgetBuilder;

    // Demo 1: Radio Button Widget
    println!("\n1. Radio Button Widget:");
    let radio_request = WidgetGenerationRequest {
        widget_type: "options".to_string(),
        context: "User preference selection".to_string(),
        config: json!({
            "id": "theme-selector",
            "title": "Select Theme",
            "display_mode": "radio",
            "config": {
                "options": [
                    {
                        "label": "Dark Theme",
                        "value": "dark",
                        "description": "Easier on the eyes in low light"
                    },
                    {
                        "label": "Light Theme",
                        "value": "light",
                        "description": "Better for daytime use"
                    },
                    {
                        "label": "Auto Theme",
                        "value": "auto",
                        "description": "Follows system settings"
                    }
                ],
                "multi_select": false,
                "required": true,
                "show_descriptions": true
            }
        }),
    };

    match builder.build(&radio_request).await {
        Ok(widget) => {
            println!("✓ Radio button widget created successfully");
            println!("  - ID: {}", widget.id());
            println!("  - Type: {}", widget.widget_type());
            println!("  - Description: {}", widget.description());
        }
        Err(e) => println!("✗ Failed to create radio widget: {}", e),
    }

    // Demo 2: Dropdown Widget
    println!("\n2. Dropdown Widget:");
    let dropdown_request = WidgetGenerationRequest {
        widget_type: "options".to_string(),
        context: "Language selection".to_string(),
        config: json!({
            "id": "language-selector",
            "title": "Programming Language",
            "display_mode": "dropdown",
            "config": {
                "options": [
                    {
                        "label": "Rust",
                        "value": "rust",
                        "icon": "🦀"
                    },
                    {
                        "label": "Python",
                        "value": "python",
                        "icon": "🐍"
                    },
                    {
                        "label": "JavaScript",
                        "value": "javascript",
                        "icon": "⚡"
                    },
                    {
                        "label": "Go",
                        "value": "go",
                        "icon": "🐹"
                    }
                ],
                "max_visible_items": 3
            }
        }),
    };

    match builder.build(&dropdown_request).await {
        Ok(widget) => {
            println!("✓ Dropdown widget created successfully");
            println!("  - ID: {}", widget.id());
            println!("  - Type: {}", widget.widget_type());
        }
        Err(e) => println!("✗ Failed to create dropdown widget: {}", e),
    }

    // Demo 3: Button Group Widget
    println!("\n3. Button Group Widget:");
    let button_request = WidgetGenerationRequest {
        widget_type: "options".to_string(),
        context: "Action selection".to_string(),
        config: json!({
            "id": "action-buttons",
            "title": "Quick Actions",
            "display_mode": "button_group",
            "config": {
                "options": [
                    {
                        "label": "Save",
                        "value": "save",
                        "icon": "💾"
                    },
                    {
                        "label": "Load",
                        "value": "load",
                        "icon": "📁"
                    },
                    {
                        "label": "Delete",
                        "value": "delete",
                        "icon": "🗑️"
                    },
                    {
                        "label": "Cancel",
                        "value": "cancel",
                        "icon": "❌"
                    }
                ],
                "button_layout": {
                    "type": "horizontal"
                }
            }
        }),
    };

    match builder.build(&button_request).await {
        Ok(widget) => {
            println!("✓ Button group widget created successfully");
            println!("  - ID: {}", widget.id());
            println!("  - Type: {}", widget.widget_type());
        }
        Err(e) => println!("✗ Failed to create button group widget: {}", e),
    }

    // Demo 4: Multi-select Options
    println!("\n4. Multi-select Options:");
    let multi_request = WidgetGenerationRequest {
        widget_type: "options".to_string(),
        context: "Feature selection".to_string(),
        config: json!({
            "id": "features-selector",
            "title": "Enable Features",
            "display_mode": "radio",
            "config": {
                "options": [
                    {
                        "label": "Auto-save",
                        "value": "autosave"
                    },
                    {
                        "label": "Syntax highlighting",
                        "value": "syntax"
                    },
                    {
                        "label": "Line numbers",
                        "value": "linenumbers"
                    },
                    {
                        "label": "Word wrap",
                        "value": "wordwrap"
                    }
                ],
                "multi_select": true,
                "required": false
            }
        }),
    };

    match builder.build(&multi_request).await {
        Ok(widget) => {
            println!("✓ Multi-select widget created successfully");
            println!("  - ID: {}", widget.id());
            println!("  - Multi-select enabled");
        }
        Err(e) => println!("✗ Failed to create multi-select widget: {}", e),
    }

    // Demo 5: Show widget schema
    println!("\n5. Widget Schema:");
    let schema = builder.get_schema();
    println!("✓ Schema available for AI integration");
    println!(
        "  - Properties: {}",
        schema["properties"].as_object().unwrap().len()
    );

    println!("\n✅ Options Widget System implemented successfully!");
    println!("   - Radio buttons with Unicode symbols ○/●");
    println!("   - Dropdown menus with expand/collapse");
    println!("   - Button groups (horizontal/vertical/grid)");
    println!("   - Multi-select support");
    println!("   - Keyboard navigation (↑/↓/Enter/Space)");
    println!("   - Dynamic option updates from AI");
    println!("   - JSON schema for configuration");

    Ok(())
}
