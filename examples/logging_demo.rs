use std::io;
use tracing::{debug, error, info};
use tracing_subscriber::{fmt, layer::SubscriberExt, util::SubscriberInitExt, EnvFilter, Layer};

fn main() {
    // Simulate the fixed logging behavior
    let is_tui_mode = std::env::args().nth(1) != Some("--non-tui".to_string());
    let force_console = std::env::var("AUTORUN_FORCE_CONSOLE_LOG").is_ok();

    // Setup file logging (always enabled)
    let file_appender = tracing_subscriber::fmt::layer()
        .with_writer(io::stderr) // In real app, this would be a file
        .with_ansi(false)
        .json();

    // Setup console logging (conditionally enabled)
    let enable_console = !is_tui_mode || force_console;

    if enable_console {
        // Both console and file logging
        let console_layer = fmt::layer().with_writer(io::stdout).pretty();

        tracing_subscriber::registry()
            .with(console_layer)
            .with(file_appender)
            .init();
    } else {
        // File logging only (TUI mode)
        tracing_subscriber::registry().with(file_appender).init();
    }

    // Demo logs
    info!("Starting application");
    info!(
        "Console logging: {}",
        if enable_console {
            "enabled"
        } else {
            "disabled (TUI safe mode)"
        }
    );

    if is_tui_mode && !force_console {
        println!("\n=== TUI Mode (Console Logging Disabled) ===");
        println!("The TUI would render here without log interference.");
        println!("Logs are being written to file/stderr in JSON format.");
        println!("\nSending a message...");
        info!("User sent a message");
        debug!("Processing message");
        println!("TUI remains stable! ✓");
    } else {
        println!("\n=== Non-TUI Mode (Console Logging Enabled) ===");
        info!("This log appears on console");
        error!("Error logs also visible");
        debug!("Debug information shown");
    }

    println!("\nTo test different modes:");
    println!("- TUI mode (default): cargo run --example logging_demo");
    println!("- Non-TUI mode: cargo run --example logging_demo -- --non-tui");
    println!(
        "- Force console in TUI: AUTORUN_FORCE_CONSOLE_LOG=1 cargo run --example logging_demo"
    );
}
