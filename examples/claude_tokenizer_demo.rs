//! Example demonstrating Claude tokenizer usage

use autorun::prompts::tokenization::{
    PaddingStrategy, TokenizerConfig, TokenizerFactory, TruncationStrategy,
};
use std::collections::HashMap;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("Claude Tokenizer Demo");
    println!("====================\n");

    // Create a tokenizer for Claude 3 Sonnet
    let provider = TokenizerFactory::create_provider("claude-3-sonnet")?;

    // Basic configuration
    let mut config = TokenizerConfig {
        model: "claude-3-sonnet".to_string(),
        max_tokens: 200_000,
        include_special_tokens: true,
        custom_limits: HashMap::new(),
        padding_strategy: PaddingStrategy::None,
        truncation_strategy: TruncationStrategy::End,
    };

    // Test text
    let test_text = "Hello! I'm testing the <PERSON> tokenizer. This is a sample text to demonstrate token counting, encoding, and decoding capabilities.";

    println!("Test text: {}", test_text);
    println!("Text length: {} characters\n", test_text.len());

    // Count tokens
    let token_count = provider.count_tokens(test_text, &config).await?;
    println!("Token count (with special tokens): {}", token_count);

    // Count without special tokens
    config.include_special_tokens = false;
    let token_count_no_special = provider.count_tokens(test_text, &config).await?;
    println!(
        "Token count (without special tokens): {}",
        token_count_no_special
    );

    // Get model information
    let model_info = provider.get_model_info(&config.model).await?;
    println!("\nModel Information:");
    println!("  Model: {}", model_info.model);
    println!("  Max context tokens: {}", model_info.max_context_tokens);
    println!("  Max output tokens: {}", model_info.max_output_tokens);
    if let Some(input_cost) = model_info.input_token_cost {
        println!("  Input cost per 1K tokens: ${:.3}", input_cost);
    }
    if let Some(output_cost) = model_info.output_token_cost {
        println!("  Output cost per 1K tokens: ${:.3}", output_cost);
    }

    // Calculate usage
    let usage = provider.calculate_usage(test_text, &config).await?;
    println!("\nToken Usage:");
    println!("  Total tokens: {}", usage.total_tokens);
    println!("  Prompt tokens: {}", usage.prompt_tokens);
    println!("  Usage percentage: {:.2}%", usage.usage_percentage);
    println!("  Remaining tokens: {}", usage.remaining_tokens);

    // Test encoding and decoding
    println!("\nEncoding and Decoding Test:");
    let tokens = provider.encode(test_text, &config).await?;
    println!("  Encoded to {} token IDs", tokens.len());
    println!("  First 10 tokens: {:?}", &tokens[..tokens.len().min(10)]);

    let decoded = provider.decode(&tokens, &config).await?;
    println!("  Decoded text matches: {}", decoded == test_text);

    // Test truncation
    println!("\nTruncation Test:");
    let long_text = test_text.repeat(10);
    println!("  Long text length: {} characters", long_text.len());

    let truncated = provider.truncate_to_limit(&long_text, 50, &config).await?;
    let truncated_tokens = provider.count_tokens(&truncated, &config).await?;
    println!("  Truncated to {} tokens", truncated_tokens);
    println!(
        "  Truncated text: {}...",
        &truncated[..50.min(truncated.len())]
    );

    // Test splitting
    println!("\nSplitting Test:");
    let chunks = provider.split_by_tokens(&long_text, 30, &config).await?;
    println!("  Split into {} chunks", chunks.len());
    for (i, chunk) in chunks.iter().enumerate().take(3) {
        let chunk_tokens = provider.count_tokens(chunk, &config).await?;
        println!(
            "  Chunk {}: {} tokens, {} chars",
            i + 1,
            chunk_tokens,
            chunk.len()
        );
    }

    // Test different Claude models
    println!("\nTesting Different Claude Models:");
    let models = vec![
        "claude-3-opus",
        "claude-3-haiku",
        "claude-2",
        "claude-instant",
    ];

    for model in models {
        let info = provider.get_model_info(model).await?;
        println!(
            "\n  {}: {} context tokens, {} output tokens",
            model, info.max_context_tokens, info.max_output_tokens
        );

        if let (Some(input), Some(output)) = (info.input_token_cost, info.output_token_cost) {
            println!(
                "    Pricing: ${:.4}/1K input, ${:.4}/1K output",
                input, output
            );
        }
    }

    // Demonstrate token validation
    println!("\nToken Limit Validation:");
    config.model = "claude-3-sonnet".to_string();

    match provider.validate_token_limit(test_text, &config).await {
        Ok(true) => println!("  ✓ Text is within token limits"),
        Err(e) => println!("  ✗ Text exceeds limits: {}", e),
        _ => {}
    }

    // Test with a very large text (simulated)
    let large_text = "x".repeat(1_000_000); // 1 million characters
    match provider.validate_token_limit(&large_text, &config).await {
        Ok(true) => println!("  ✓ Large text is within token limits"),
        Err(e) => println!("  ✗ Large text exceeds limits: {}", e),
        _ => {}
    }

    println!("\nDemo completed successfully!");
    Ok(())
}
