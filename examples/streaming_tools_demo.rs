use autorun::llm::{
    LLMProviderWithStreaming, Message, OpenAICompatibleClient, OpenAICompatibleConfig, ToolChoice,
};
use serde_json::json;
use std::time::Duration;
use tokio;
use tracing::info;
use tracing_subscriber;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::INFO)
        .init();

    // Create OpenRouter client
    let openrouter_config = OpenAICompatibleConfig {
        api_key: std::env::var("OPENROUTER_API_KEY")
            .unwrap_or_else(|_| panic!("OPENROUTER_API_KEY environment variable is required")),
        base_url: "https://openrouter.ai/api/v1".to_string(),
        model: std::env::var("OPENROUTER_MODEL")
            .unwrap_or_else(|_| "anthropic/claude-3-sonnet-20240229".to_string()),
        max_tokens: 1024,
        temperature: 0.7,
        timeout: Duration::from_secs(120),
    };

    let client = OpenAICompatibleClient::new(openrouter_config)?;

    // Define a simple weather tool
    let tools = vec![json!({
        "type": "function",
        "function": {
            "name": "get_weather",
            "description": "Get the current weather in a given location",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "The city and state, e.g. San Francisco, CA"
                    },
                    "unit": {
                        "type": "string",
                        "enum": ["celsius", "fahrenheit"],
                        "description": "The unit for temperature"
                    }
                },
                "required": ["location"]
            }
        }
    })];

    // Create a test message
    let messages = vec![Message {
        role: "user".to_string(),
        content: "What's the weather like in Tokyo and New York? Please check both cities."
            .to_string(),
        tool_call_id: None,
        tool_calls: None,
    }];

    info!("Starting streaming request with tools to OpenRouter...");

    // Make streaming request with tools
    let mut stream = client
        .complete_with_tools_streaming(messages, tools, Some(ToolChoice::Auto))
        .await?;

    info!("Receiving streaming response with tool support...");

    // Process the stream
    let mut full_content = String::new();
    let mut all_tool_calls = Vec::new();
    let mut chunk_count = 0;

    while let Some(chunk) = stream.next_chunk().await? {
        chunk_count += 1;

        // Handle text content
        if let Some(content) = &chunk.content {
            print!("{}", content);
            full_content.push_str(content);
            // Flush stdout to show content immediately
            use std::io::{self, Write};
            io::stdout().flush()?;
        }

        // Handle tool calls
        for tool_call in &chunk.tool_calls {
            info!(
                "\nTool call received: {} ({})",
                tool_call.name, tool_call.id
            );
            info!("Arguments: {}", tool_call.arguments);
            all_tool_calls.push(tool_call.clone());
        }

        // Check if finished
        if let Some(finish_reason) = &chunk.finish_reason {
            info!("\nStream finished with reason: {}", finish_reason);
            break;
        }
    }

    println!("\n");
    info!("Streaming complete. Received {} chunks", chunk_count);
    info!("Total content length: {} characters", full_content.len());
    info!("Total tool calls: {}", all_tool_calls.len());

    // Display all tool calls
    if !all_tool_calls.is_empty() {
        info!("\nAll tool calls made:");
        for (i, tool_call) in all_tool_calls.iter().enumerate() {
            info!("  {}. {} - {}", i + 1, tool_call.name, tool_call.arguments);
        }
    }

    Ok(())
}
