use autorun::llm::{
    LLMProviderWithStreaming, Message, OpenAICompatibleClient, OpenAICompatibleConfig,
};
use std::time::Duration;
use tokio;
use tokio::sync::oneshot;
use tokio::time::sleep;
use tracing::info;
use tracing_subscriber;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::INFO)
        .init();

    // Create OpenRouter client
    let openrouter_config = OpenAICompatibleConfig {
        api_key: std::env::var("OPENROUTER_API_KEY")
            .unwrap_or_else(|_| panic!("OPENROUTER_API_KEY environment variable is required")),
        base_url: "https://openrouter.ai/api/v1".to_string(),
        model: std::env::var("OPENROUTER_MODEL")
            .unwrap_or_else(|_| "anthropic/claude-3-sonnet-20240229".to_string()),
        max_tokens: 2048,
        temperature: 0.7,
        timeout: Duration::from_secs(120),
    };

    let client = OpenAICompatibleClient::new(openrouter_config)?;

    // Create a test message that will generate a long response
    let messages = vec![
        Message {
            role: "user".to_string(),
            content: "Write a detailed essay about the history of artificial intelligence, covering at least 10 major milestones. Make it very comprehensive.".to_string(),
            tool_call_id: None,
            tool_calls: None,
        }
    ];

    info!("Starting streaming request to OpenRouter with cancellation...");

    // Create abort handle
    let (abort_tx, abort_rx) = oneshot::channel::<()>();

    // Make streaming request
    let mut stream = client
        .complete_streaming(messages)
        .await?
        .with_abort_handle(abort_tx);

    info!("Receiving streaming response (will cancel after 2 seconds)...");

    // Spawn a task to cancel the stream after 2 seconds
    let cancel_handle = tokio::spawn(async move {
        sleep(Duration::from_secs(2)).await;
        info!("Triggering stream cancellation...");
        // The abort signal would be sent through the channel
    });

    // Process the stream
    let mut full_content = String::new();
    let mut chunk_count = 0;
    let start_time = std::time::Instant::now();

    // Create a select! to handle both stream reading and cancellation
    tokio::select! {
        _ = async {
            while let Some(chunk) = stream.next_chunk().await? {
                chunk_count += 1;

                if let Some(content) = &chunk.content {
                    print!("{}", content);
                    full_content.push_str(content);
                    // Flush stdout to show content immediately
                    use std::io::{self, Write};
                    io::stdout().flush()?;
                }

                // Simulate cancelling after receiving some content
                if start_time.elapsed() > Duration::from_secs(2) {
                    info!("\nCancelling stream after {} seconds...", start_time.elapsed().as_secs());
                    stream.abort();
                    break;
                }

                // Check if finished naturally
                if let Some(finish_reason) = &chunk.finish_reason {
                    info!("\nStream finished naturally with reason: {}", finish_reason);
                    break;
                }
            }
            Ok::<(), Box<dyn std::error::Error>>(())
        } => {
            info!("\nStream processing completed");
        }
        _ = abort_rx => {
            info!("\nReceived abort signal");
            stream.abort();
        }
    }

    println!("\n");
    info!(
        "Streaming stopped after {} seconds",
        start_time.elapsed().as_secs_f32()
    );
    info!("Received {} chunks", chunk_count);
    info!("Total content length: {} characters", full_content.len());

    // Clean up
    cancel_handle.abort();

    Ok(())
}
