// Integration tests for the Phase 1 Core Command Infrastructure
// Tests the complete command system integration with the enhanced TUI

use autorun_rs::commands::*;
use autorun_rs::tools::PermissionLevel;
use autorun_rs::ui::enhanced::input::*;
use std::sync::Arc;
use std::time::Duration;
use tokio::time::timeout;

/// Integration test configuration
struct TestConfig {
    pub registry: Arc<CommandRegistry>,
    pub completion_provider: Arc<UnifiedCompletionProvider>,
    pub executor: Arc<CommandExecutor>,
    pub enhanced_handler: EnhancedInputHandler,
}

impl TestConfig {
    async fn new() -> Self {
        // Initialize command system
        let command_config = CommandConfig::default();
        let registry = Arc::new(initialize_command_system(command_config).await.unwrap());

        // Create completion provider
        let completion_config = CompletionConfig::default();
        let completion_provider = Arc::new(UnifiedCompletionProvider::new(
            registry.clone(),
            completion_config,
        ));

        // Create command executor
        let executor_config = ExecutorConfig::default();
        let executor = Arc::new(CommandExecutor::new(registry.clone(), executor_config));

        // Create enhanced input handler
        let handler_config = HandlerConfig::default();
        let enhanced_handler = EnhancedInputHandler::new(
            registry.clone(),
            CompletionConfig::default(),
            handler_config,
        );

        Self {
            registry,
            completion_provider,
            executor,
            enhanced_handler,
        }
    }

    fn create_execution_context(&self) -> ExecutionContext {
        ExecutionContext {
            working_directory: std::env::current_dir().unwrap_or_default(),
            permissions: PermissionLevel::Full,
            session_data: std::collections::HashMap::new(),
            environment: std::env::vars().collect(),
            tool_registry: None,
        }
    }
}

#[tokio::test]
async fn test_complete_command_workflow() {
    let config = TestConfig::new().await;

    // Test 1: Context command workflow (@file)
    test_context_command_workflow(&config).await;

    // Test 2: Action command workflow (/edit)
    test_action_command_workflow(&config).await;

    // Test 3: Config command workflow (:set)
    test_config_command_workflow(&config).await;

    // Test 4: Completion integration
    test_completion_integration(&config).await;

    // Test 5: Input handler integration
    test_input_handler_integration(&config).await;

    // Test 6: Error handling integration
    test_error_handling_integration(&config).await;
}

async fn test_context_command_workflow(config: &TestConfig) {
    println!("Testing context command workflow...");

    // Parse context command
    let input = "@file src/main.rs";
    let parse_result = config
        .registry
        .parse_command(input, input.len())
        .await
        .unwrap();

    match parse_result {
        ParseResult::Success(command) => {
            assert_eq!(command.command_type, CommandType::Context);
            assert_eq!(command.name, "file");
            assert_eq!(command.args.len(), 1);
            assert_eq!(command.args[0], "src/main.rs");

            // Validate command
            let errors = config.registry.validate_command(&command).await.unwrap();
            assert!(errors.is_empty(), "Context command should be valid");

            // Execute command
            let context = config.create_execution_context();
            let result = config.executor.execute(command, context).await.unwrap();
            assert!(result.success, "Context command execution should succeed");
        }
        _ => panic!("Context command should parse successfully"),
    }
}

async fn test_action_command_workflow(config: &TestConfig) {
    println!("Testing action command workflow...");

    // Parse action command
    let input = "/edit src/main.rs --create";
    let parse_result = config
        .registry
        .parse_command(input, input.len())
        .await
        .unwrap();

    match parse_result {
        ParseResult::Success(command) => {
            assert_eq!(command.command_type, CommandType::Action);
            assert_eq!(command.name, "edit");
            assert_eq!(command.args.len(), 1);
            assert_eq!(command.args[0], "src/main.rs");
            assert!(command.has_option("create"));

            // Validate command
            let errors = config.registry.validate_command(&command).await.unwrap();
            assert!(errors.is_empty(), "Action command should be valid");

            // Execute command
            let context = config.create_execution_context();
            let result = config.executor.execute(command, context).await.unwrap();
            assert!(result.success, "Action command execution should succeed");
        }
        _ => panic!("Action command should parse successfully"),
    }
}

async fn test_config_command_workflow(config: &TestConfig) {
    println!("Testing config command workflow...");

    // Parse config command
    let input = ":set theme dark";
    let parse_result = config
        .registry
        .parse_command(input, input.len())
        .await
        .unwrap();

    match parse_result {
        ParseResult::Success(command) => {
            assert_eq!(command.command_type, CommandType::Config);
            assert_eq!(command.name, "set");
            assert_eq!(command.args.len(), 2);
            assert_eq!(command.args[0], "theme");
            assert_eq!(command.args[1], "dark");

            // Validate command
            let errors = config.registry.validate_command(&command).await.unwrap();
            assert!(errors.is_empty(), "Config command should be valid");

            // Execute command
            let context = config.create_execution_context();
            let result = config.executor.execute(command, context).await.unwrap();
            assert!(result.success, "Config command execution should succeed");
        }
        _ => panic!("Config command should parse successfully"),
    }
}

async fn test_completion_integration(config: &TestConfig) {
    println!("Testing completion integration...");

    let context = config.create_execution_context();

    // Test context completions
    let completions = config
        .completion_provider
        .get_completions("@f", 2, Some(&context))
        .await
        .unwrap();

    assert!(!completions.is_empty(), "Should have context completions");
    assert!(
        completions.iter().any(|c| c.text.contains("file")),
        "Should include 'file' completion"
    );

    // Test action completions
    let completions = config
        .completion_provider
        .get_completions("/e", 2, Some(&context))
        .await
        .unwrap();

    assert!(!completions.is_empty(), "Should have action completions");
    assert!(
        completions.iter().any(|c| c.text.contains("edit")),
        "Should include 'edit' completion"
    );

    // Test config completions
    let completions = config
        .completion_provider
        .get_completions(":s", 2, Some(&context))
        .await
        .unwrap();

    assert!(!completions.is_empty(), "Should have config completions");
    assert!(
        completions.iter().any(|c| c.text.contains("set")),
        "Should include 'set' completion"
    );

    // Test general completions (no specific command)
    let completions = config
        .completion_provider
        .get_completions("", 0, Some(&context))
        .await
        .unwrap();

    assert!(!completions.is_empty(), "Should have general completions");
    assert!(
        completions.iter().any(|c| c.text == "@"),
        "Should include '@' prefix"
    );
    assert!(
        completions.iter().any(|c| c.text == "/"),
        "Should include '/' prefix"
    );
    assert!(
        completions.iter().any(|c| c.text == ":"),
        "Should include ':' prefix"
    );
}

async fn test_input_handler_integration(config: &TestConfig) {
    println!("Testing input handler integration...");

    // Create input context
    let mut input_context = InputContext {
        text: String::new(),
        cursor_position: 0,
        mode: autorun_rs::ui::enhanced::Mode::Insert,
        completion_active: false,
        vim_state: None,
    };

    // Test that handler can handle insert mode events
    let key_event = crossterm::event::Event::Key(crossterm::event::KeyEvent::new(
        crossterm::event::KeyCode::Char('@'),
        crossterm::event::KeyModifiers::NONE,
    ));

    let can_handle = config
        .enhanced_handler
        .can_handle(&key_event, &input_context);
    assert!(
        can_handle,
        "Enhanced handler should handle insert mode key events"
    );

    // Test character insertion and completion triggering
    let mut handler = config.enhanced_handler;
    let result = handler.handle(&key_event, &mut input_context).unwrap();

    match result {
        InputResult::Handled => {
            assert_eq!(input_context.text, "@");
            assert_eq!(input_context.cursor_position, 1);
        }
        _ => panic!("Character insertion should be handled"),
    }
}

async fn test_error_handling_integration(config: &TestConfig) {
    println!("Testing error handling integration...");

    // Test parsing invalid command
    let parse_result = config
        .registry
        .parse_command("invalid input", 13)
        .await
        .unwrap();

    match parse_result {
        ParseResult::None => {
            // Expected for non-command input
        }
        _ => {
            // Also acceptable - depends on implementation
        }
    }

    // Test validation error
    let mut invalid_command = ParsedCommand::new(CommandType::Context, "@file".to_string(), 5);
    invalid_command.set_name("file".to_string());
    // Missing required argument

    let errors = config
        .registry
        .validate_command(&invalid_command)
        .await
        .unwrap();
    assert!(!errors.is_empty(), "Should have validation errors");

    // Test execution error
    let context = config.create_execution_context();
    let result = config
        .executor
        .execute(invalid_command, context)
        .await
        .unwrap();
    assert!(!result.success, "Invalid command execution should fail");
    assert!(result.error.is_some(), "Should have error message");
}

#[tokio::test]
async fn test_concurrent_operations() {
    println!("Testing concurrent operations...");

    let config = TestConfig::new().await;
    let context = config.create_execution_context();

    // Test concurrent parsing
    let parse_handles: Vec<_> = (0..10)
        .map(|i| {
            let registry = config.registry.clone();
            tokio::spawn(async move {
                let input = format!("@file test{}.rs", i);
                registry.parse_command(&input, input.len()).await
            })
        })
        .collect();

    let parse_results = futures::future::join_all(parse_handles).await;
    for result in parse_results {
        assert!(result.unwrap().is_ok());
    }

    // Test concurrent completions
    let completion_handles: Vec<_> = (0..10)
        .map(|i| {
            let provider = config.completion_provider.clone();
            let context = context.clone();
            tokio::spawn(async move {
                let input = format!("@f{}", i % 3);
                provider
                    .get_completions(&input, input.len(), Some(&context))
                    .await
            })
        })
        .collect();

    let completion_results = futures::future::join_all(completion_handles).await;
    for result in completion_results {
        assert!(result.unwrap().is_ok());
    }

    // Test concurrent executions
    let execution_handles: Vec<_> = (0..5)
        .map(|i| {
            let executor = config.executor.clone();
            let context = context.clone();
            tokio::spawn(async move {
                let mut command = ParsedCommand::new(
                    CommandType::Context,
                    format!("@file test{}.rs", i),
                    12 + i.to_string().len(),
                );
                command.set_name("file".to_string());
                command.add_arg(format!("test{}.rs", i));
                command.mark_complete();

                executor.execute(command, context).await
            })
        })
        .collect();

    let execution_results = futures::future::join_all(execution_handles).await;
    for result in execution_results {
        let exec_result = result.unwrap().unwrap();
        assert!(exec_result.success);
    }
}

#[tokio::test]
async fn test_performance_requirements() {
    println!("Testing performance requirements...");

    let config = TestConfig::new().await;
    let context = config.create_execution_context();

    // Test parsing performance
    let start = std::time::Instant::now();
    for i in 0..1000 {
        let input = format!("@file test{}.rs", i);
        let _ = config
            .registry
            .parse_command(&input, input.len())
            .await
            .unwrap();
    }
    let parse_duration = start.elapsed();
    assert!(
        parse_duration < Duration::from_secs(1),
        "1000 parses should complete in under 1 second, took {:?}",
        parse_duration
    );

    // Test completion performance
    let start = std::time::Instant::now();
    for i in 0..100 {
        let input = format!("@f{}", i % 10);
        let _ = config
            .completion_provider
            .get_completions(&input, input.len(), Some(&context))
            .await
            .unwrap();
    }
    let completion_duration = start.elapsed();
    assert!(
        completion_duration < Duration::from_secs(1),
        "100 completions should complete in under 1 second, took {:?}",
        completion_duration
    );

    // Test execution performance
    let start = std::time::Instant::now();
    for i in 0..50 {
        let mut command = ParsedCommand::new(
            CommandType::Context,
            format!("@file test{}.rs", i),
            12 + i.to_string().len(),
        );
        command.set_name("file".to_string());
        command.add_arg(format!("test{}.rs", i));
        command.mark_complete();

        let _ = config
            .executor
            .execute(command, context.clone())
            .await
            .unwrap();
    }
    let execution_duration = start.elapsed();
    assert!(
        execution_duration < Duration::from_secs(5),
        "50 executions should complete in under 5 seconds, took {:?}",
        execution_duration
    );
}

#[tokio::test]
async fn test_memory_usage() {
    println!("Testing memory usage...");

    let config = TestConfig::new().await;

    // Check initial stats
    let initial_registry_stats = config.registry.get_stats().await;
    let initial_completion_stats = config.completion_provider.get_stats().await;
    let initial_executor_stats = config.executor.get_stats().await;

    println!("Initial stats:");
    println!("  Registry: {:?}", initial_registry_stats);
    println!("  Completion: {:?}", initial_completion_stats);
    println!("  Executor: {:?}", initial_executor_stats);

    // Perform many operations to test memory growth
    let context = config.create_execution_context();

    for i in 0..100 {
        // Parse commands
        let input = format!("@file test{}.rs", i);
        let _ = config
            .registry
            .parse_command(&input, input.len())
            .await
            .unwrap();

        // Get completions
        let _ = config
            .completion_provider
            .get_completions(&input, input.len(), Some(&context))
            .await
            .unwrap();

        // Execute commands
        let mut command = ParsedCommand::new(CommandType::Context, input.clone(), input.len());
        command.set_name("file".to_string());
        command.add_arg(format!("test{}.rs", i));
        command.mark_complete();

        let _ = config
            .executor
            .execute(command, context.clone())
            .await
            .unwrap();
    }

    // Check final stats
    let final_registry_stats = config.registry.get_stats().await;
    let final_completion_stats = config.completion_provider.get_stats().await;
    let final_executor_stats = config.executor.get_stats().await;

    println!("Final stats:");
    println!("  Registry: {:?}", final_registry_stats);
    println!("  Completion: {:?}", final_completion_stats);
    println!("  Executor: {:?}", final_executor_stats);

    // Verify reasonable memory usage
    assert!(
        final_completion_stats.cache_entries < 1000,
        "Completion cache should not grow unbounded"
    );
    assert!(
        final_executor_stats.total_executions == 100,
        "Should have tracked all executions"
    );
}

#[tokio::test]
async fn test_system_integration_scenarios() {
    println!("Testing system integration scenarios...");

    let config = TestConfig::new().await;

    // Scenario 1: User types gradually and gets completions
    test_gradual_typing_scenario(&config).await;

    // Scenario 2: User executes multiple related commands
    test_command_sequence_scenario(&config).await;

    // Scenario 3: User makes errors and recovers
    test_error_recovery_scenario(&config).await;
}

async fn test_gradual_typing_scenario(config: &TestConfig) {
    let context = config.create_execution_context();

    // User types "@"
    let completions = config
        .completion_provider
        .get_completions("@", 1, Some(&context))
        .await
        .unwrap();
    assert!(!completions.is_empty());

    // User types "@f"
    let completions = config
        .completion_provider
        .get_completions("@f", 2, Some(&context))
        .await
        .unwrap();
    assert!(completions.iter().any(|c| c.text.contains("file")));

    // User types "@file"
    let completions = config
        .completion_provider
        .get_completions("@file", 5, Some(&context))
        .await
        .unwrap();
    assert!(!completions.is_empty());

    // User completes to "@file src/main.rs"
    let parse_result = config
        .registry
        .parse_command("@file src/main.rs", 17)
        .await
        .unwrap();

    match parse_result {
        ParseResult::Success(command) => {
            let result = config.executor.execute(command, context).await.unwrap();
            assert!(result.success);
        }
        _ => panic!("Final command should parse successfully"),
    }
}

async fn test_command_sequence_scenario(config: &TestConfig) {
    let context = config.create_execution_context();

    // Sequence: Reference file, edit it, save config
    let commands = vec![
        "@file src/main.rs",
        "/edit src/main.rs",
        ":set auto_save true",
    ];

    for cmd_str in commands {
        let parse_result = config
            .registry
            .parse_command(cmd_str, cmd_str.len())
            .await
            .unwrap();

        match parse_result {
            ParseResult::Success(command) => {
                let result = config
                    .executor
                    .execute(command, context.clone())
                    .await
                    .unwrap();
                assert!(result.success, "Command '{}' should succeed", cmd_str);
            }
            _ => panic!("Command '{}' should parse successfully", cmd_str),
        }
    }
}

async fn test_error_recovery_scenario(config: &TestConfig) {
    let context = config.create_execution_context();

    // User makes an error
    let invalid_result = config
        .registry
        .parse_command("@invalid_command", 16)
        .await
        .unwrap();

    // System should handle gracefully
    match invalid_result {
        ParseResult::Invalid(_) | ParseResult::None => {
            // Expected behavior
        }
        ParseResult::Success(command) | ParseResult::Partial(command) => {
            // If parsed, execution should fail gracefully
            let result = config
                .executor
                .execute(command, context.clone())
                .await
                .unwrap();
            assert!(!result.success);
            assert!(result.error.is_some());
        }
    }

    // User corrects and tries valid command
    let valid_result = config
        .registry
        .parse_command("@file src/main.rs", 17)
        .await
        .unwrap();

    match valid_result {
        ParseResult::Success(command) => {
            let result = config.executor.execute(command, context).await.unwrap();
            assert!(result.success);
        }
        _ => panic!("Valid command should parse successfully"),
    }
}
