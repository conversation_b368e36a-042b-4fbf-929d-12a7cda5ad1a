// Integration tests for Enhanced TUI
// Tests the complete enhanced TUI system including all components and their interactions

use autorun::ui::enhanced::{
    Action, ContentBlock, ContentBlockType, EnhancedTuiInterface, InputMode, Mode,
    NavigationCommand, TuiConfig,
};
use crossterm::event::{Event, KeyCode, KeyEvent, KeyModifiers};
use ratatui::backend::TestBackend;
use ratatui::Terminal;
use std::time::Duration;
use tokio::time::timeout;

/// Test the complete enhanced TUI system
#[tokio::test]
async fn test_enhanced_tui_complete_workflow() {
    let config = TuiConfig::default();
    let mut tui = EnhancedTuiInterface::new(config, false, false, false)
        .await
        .unwrap();

    // Test initial state
    assert_eq!(tui.get_state().mode, Mode::Normal);
    assert!(tui.is_ready());

    // Test mode switching
    let insert_event = Event::Key(KeyEvent::new(KeyCode::Char('i'), KeyModifiers::NONE));
    let result = tui.handle_event(insert_event).await.unwrap();
    assert_eq!(tui.get_state().mode, Mode::Insert);

    // Test input handling
    let char_event = Event::Key(KeyEvent::new(KeyCode::Char('h'), KeyModifiers::NONE));
    tui.handle_event(char_event).await.unwrap();

    let char_event = Event::Key(KeyEvent::new(KeyCode::Char('i'), KeyModifiers::NONE));
    tui.handle_event(char_event).await.unwrap();

    // Test submission
    let enter_event = Event::Key(KeyEvent::new(KeyCode::Enter, KeyModifiers::NONE));
    tui.handle_event(enter_event).await.unwrap();

    // Verify content was added
    let visible_blocks = tui.content_navigator.get_visible_blocks();
    assert!(!visible_blocks.is_empty());

    // Test navigation
    let escape_event = Event::Key(KeyEvent::new(KeyCode::Esc, KeyModifiers::NONE));
    tui.handle_event(escape_event).await.unwrap();
    assert_eq!(tui.get_state().mode, Mode::Normal);

    // Test content navigation
    let down_event = Event::Key(KeyEvent::new(KeyCode::Char('j'), KeyModifiers::NONE));
    tui.handle_event(down_event).await.unwrap();
}

/// Test content block navigation system
#[tokio::test]
async fn test_content_block_navigation() {
    let config = TuiConfig::default();
    let mut tui = EnhancedTuiInterface::new(config, false, false, false)
        .await
        .unwrap();

    // Add multiple content blocks
    let user_block = ContentBlock::user_message("Hello".to_string());
    let assistant_block = ContentBlock::assistant_response("Hi there!".to_string());
    let code_block =
        ContentBlock::code_block("println!(\"Hello\");".to_string(), Some("rust".to_string()));

    tui.content_navigator.add_block(user_block);
    tui.content_navigator.add_block(assistant_block);
    tui.content_navigator.add_block(code_block);

    // Test navigation commands
    let result = tui
        .content_navigator
        .execute_command(NavigationCommand::NextBlock);
    assert!(matches!(
        result,
        autorun::ui::enhanced::content_blocks::NavigationResult::Success
    ));

    let result = tui
        .content_navigator
        .execute_command(NavigationCommand::PreviousBlock);
    assert!(matches!(
        result,
        autorun::ui::enhanced::content_blocks::NavigationResult::Success
    ));

    let result = tui
        .content_navigator
        .execute_command(NavigationCommand::FirstBlock);
    assert!(matches!(
        result,
        autorun::ui::enhanced::content_blocks::NavigationResult::Success
    ));

    let result = tui
        .content_navigator
        .execute_command(NavigationCommand::LastBlock);
    assert!(matches!(
        result,
        autorun::ui::enhanced::content_blocks::NavigationResult::Success
    ));

    // Test selection
    let result = tui
        .content_navigator
        .execute_command(NavigationCommand::SelectCurrent);
    assert!(matches!(
        result,
        autorun::ui::enhanced::content_blocks::NavigationResult::Success
    ));

    let result = tui
        .content_navigator
        .execute_command(NavigationCommand::SelectAll);
    assert!(matches!(
        result,
        autorun::ui::enhanced::content_blocks::NavigationResult::Success
    ));

    let result = tui
        .content_navigator
        .execute_command(NavigationCommand::ClearSelection);
    assert!(matches!(
        result,
        autorun::ui::enhanced::content_blocks::NavigationResult::Success
    ));
}

/// Test REPL interface functionality
#[tokio::test]
async fn test_repl_interface() {
    use autorun::ui::enhanced::input::repl::{ReplConfig, ReplInterface};

    let config = ReplConfig::default();
    let mut repl = ReplInterface::new(config);

    // Test initial state
    assert!(repl.is_ready());
    assert_eq!(repl.get_mode(), &InputMode::Insert);
    assert_eq!(repl.get_input(), "");

    // Test input handling
    let char_event = Event::Key(KeyEvent::new(KeyCode::Char('h'), KeyModifiers::NONE));
    let result = repl.handle_event(&char_event).unwrap();
    // Note: This would require proper input handler implementation

    // Test submission
    let input = "test input".to_string();
    // Simulate setting input
    // let result = repl.submit().unwrap();
    // assert_eq!(result, input);

    // Test validation
    assert!(repl.validate_input());
}

/// Test component system
#[tokio::test]
async fn test_component_system() {
    use autorun::ui::enhanced::components::{
        ComponentConfig, ComponentFactory, ComponentRegistry, ComponentType,
    };
    use std::sync::Arc;

    let factory = Arc::new(ComponentFactory::new());
    let mut registry = ComponentRegistry::new(factory);

    // Test component creation
    let config = ComponentConfig {
        id: "test_combobox".to_string(),
        component_type: ComponentType::Combobox,
        properties: std::collections::HashMap::new(),
        style: autorun::ui::enhanced::components::ComponentStyle::default(),
        accessibility: autorun::ui::enhanced::components::AccessibilityConfig::default(),
    };

    // Note: This would require proper component builder registration
    // let component_id = registry.create_component(config).unwrap();
    // assert_eq!(component_id, "test_combobox");

    // Test focus management
    let next_focusable = registry.get_next_focusable(None);
    let prev_focusable = registry.get_previous_focusable(None);

    // Test component listing
    let components = registry.list_components();
    assert!(components.is_empty()); // No components registered yet
}

/// Test configuration system
#[tokio::test]
async fn test_configuration_system() {
    use autorun::ui::enhanced::config::{ConfigManager, TuiConfig};
    use tempfile::TempDir;

    let temp_dir = TempDir::new().unwrap();
    let config_dir = temp_dir.path().to_path_buf();

    let manager = ConfigManager::new(config_dir).await.unwrap();

    // Test configuration loading
    let config = manager.get_config().await;
    assert_eq!(config.ui.vim_mode, false);
    assert_eq!(config.ui.auto_completion, true);

    // Test configuration updating
    manager
        .update_config(|config| {
            config.ui.vim_mode = true;
            config.ui.font_size = 16;
        })
        .await
        .unwrap();

    let updated_config = manager.get_config().await;
    assert_eq!(updated_config.ui.vim_mode, true);
    assert_eq!(updated_config.ui.font_size, 16);

    // Test configuration validation
    let errors = manager.validate_config().await.unwrap();
    assert!(errors.is_empty());

    // Test configuration reset
    manager.reset_to_defaults().await.unwrap();
    let reset_config = manager.get_config().await;
    assert_eq!(reset_config.ui.vim_mode, false);
}

/// Test theme system
#[tokio::test]
async fn test_theme_system() {
    use autorun::ui::enhanced::config::theme::{Theme, ThemeManager};
    use tempfile::TempDir;

    let temp_dir = TempDir::new().unwrap();
    let themes_dir = temp_dir.path().to_path_buf();

    let mut theme_manager = ThemeManager::new(themes_dir).await.unwrap();

    // Test theme listing
    let themes = theme_manager.list_themes().await.unwrap();
    assert!(themes.contains(&"default".to_string()));
    assert!(themes.contains(&"dark".to_string()));
    assert!(themes.contains(&"light".to_string()));

    // Test theme loading
    let default_theme = theme_manager.load_theme("default").await.unwrap();
    assert_eq!(default_theme.metadata.name, "default");

    let dark_theme = theme_manager.load_theme("dark").await.unwrap();
    assert_eq!(dark_theme.metadata.name, "dark");

    // Test theme validation
    theme_manager.validate_theme(&default_theme).await.unwrap();

    // Test custom theme creation
    let mut custom_theme = Theme::default();
    custom_theme.metadata.name = "custom".to_string();
    custom_theme.metadata.description = "Custom test theme".to_string();

    theme_manager.save_theme(&custom_theme).await.unwrap();

    let loaded_custom = theme_manager.load_theme("custom").await.unwrap();
    assert_eq!(loaded_custom.metadata.name, "custom");
}

/// Test performance under load
#[tokio::test]
async fn test_performance_under_load() {
    let config = TuiConfig::default();
    let mut tui = EnhancedTuiInterface::new(config, false, false, false)
        .await
        .unwrap();

    // Add many content blocks
    for i in 0..1000 {
        let block = ContentBlock::user_message(format!("Message {}", i));
        tui.content_navigator.add_block(block);
    }

    // Test navigation performance
    let start = std::time::Instant::now();
    for _ in 0..100 {
        let result = tui
            .content_navigator
            .execute_command(NavigationCommand::NextBlock);
        assert!(matches!(
            result,
            autorun::ui::enhanced::content_blocks::NavigationResult::Success
        ));
    }
    let duration = start.elapsed();

    // Should complete navigation in reasonable time
    assert!(duration < Duration::from_millis(100));

    // Test rendering performance
    let mut terminal = Terminal::new(TestBackend::new(120, 40)).unwrap();

    let start = std::time::Instant::now();
    terminal
        .draw(|frame| {
            // This would require the render method to be synchronous or we'd need to block
            // For now, just test that we can create the terminal
        })
        .unwrap();
    let render_duration = start.elapsed();

    // Rendering should be fast
    assert!(render_duration < Duration::from_millis(50));
}

/// Test accessibility features
#[tokio::test]
async fn test_accessibility_features() {
    let config = TuiConfig::default();
    let mut tui = EnhancedTuiInterface::new(config, false, true, false)
        .await
        .unwrap();

    // Test screen reader mode
    assert_eq!(tui.get_state().config.preferences.screen_reader, true);

    // Test high contrast theme
    let mut config_manager = autorun::ui::enhanced::config::ConfigManager::new(
        std::path::PathBuf::from("/tmp/test_config"),
    )
    .await
    .unwrap();

    config_manager
        .update_config(|config| {
            config.accessibility.high_contrast = true;
            config.accessibility.large_text = true;
            config.accessibility.reduce_motion = true;
        })
        .await
        .unwrap();

    let config = config_manager.get_config().await;
    assert_eq!(config.accessibility.high_contrast, true);
    assert_eq!(config.accessibility.large_text, true);
    assert_eq!(config.accessibility.reduce_motion, true);
}

/// Test error handling and recovery
#[tokio::test]
async fn test_error_handling() {
    let config = TuiConfig::default();
    let mut tui = EnhancedTuiInterface::new(config, false, false, false)
        .await
        .unwrap();

    // Test invalid navigation
    let result = tui
        .content_navigator
        .execute_command(NavigationCommand::NextBlock);
    // Should handle gracefully when no blocks exist
    assert!(matches!(
        result,
        autorun::ui::enhanced::content_blocks::NavigationResult::NoChange
    ));

    // Test invalid configuration
    let temp_dir = tempfile::TempDir::new().unwrap();
    let config_dir = temp_dir.path().to_path_buf();

    // Create invalid config file
    let config_path = config_dir.join("config.toml");
    std::fs::create_dir_all(&config_dir).unwrap();
    std::fs::write(&config_path, "invalid toml content [[[").unwrap();

    // Should handle invalid config gracefully
    let result = autorun::ui::enhanced::config::ConfigManager::new(config_dir).await;
    assert!(result.is_err());
}

/// Test memory usage and cleanup
#[tokio::test]
async fn test_memory_management() {
    let config = TuiConfig {
        performance: autorun::ui::enhanced::config::PerformanceConfig {
            max_content_blocks: 100,
            max_history_entries: 50,
            ..Default::default()
        },
        ..Default::default()
    };

    let mut tui = EnhancedTuiInterface::new(config, false, false, false)
        .await
        .unwrap();

    // Add more blocks than the limit
    for i in 0..150 {
        let block = ContentBlock::user_message(format!("Message {}", i));
        tui.content_navigator.add_block(block);
    }

    // Should respect memory limits
    let visible_blocks = tui.content_navigator.get_visible_blocks();
    // Note: This would require implementing actual memory management
    // For now, just verify we can add blocks
    assert!(!visible_blocks.is_empty());
}

/// Test concurrent operations
#[tokio::test]
async fn test_concurrent_operations() {
    let config = TuiConfig::default();
    let mut tui = EnhancedTuiInterface::new(config, false, false, false)
        .await
        .unwrap();

    // Test concurrent event handling
    let events = vec![
        Event::Key(KeyEvent::new(KeyCode::Char('i'), KeyModifiers::NONE)),
        Event::Key(KeyEvent::new(KeyCode::Char('h'), KeyModifiers::NONE)),
        Event::Key(KeyEvent::new(KeyCode::Char('i'), KeyModifiers::NONE)),
        Event::Key(KeyEvent::new(KeyCode::Enter, KeyModifiers::NONE)),
        Event::Key(KeyEvent::new(KeyCode::Esc, KeyModifiers::NONE)),
    ];

    // Process events sequentially (simulating rapid input)
    for event in events {
        let result = timeout(Duration::from_millis(100), tui.handle_event(event)).await;
        assert!(result.is_ok());
    }

    // Verify final state
    assert_eq!(tui.get_state().mode, Mode::Normal);
}

/// Benchmark test for performance regression detection
#[tokio::test]
async fn benchmark_basic_operations() {
    let config = TuiConfig::default();
    let mut tui = EnhancedTuiInterface::new(config, false, false, false)
        .await
        .unwrap();

    // Benchmark content block creation
    let start = std::time::Instant::now();
    for i in 0..1000 {
        let block = ContentBlock::user_message(format!("Benchmark message {}", i));
        tui.content_navigator.add_block(block);
    }
    let creation_time = start.elapsed();

    // Should create blocks quickly
    assert!(creation_time < Duration::from_millis(100));

    // Benchmark navigation
    let start = std::time::Instant::now();
    for _ in 0..1000 {
        tui.content_navigator
            .execute_command(NavigationCommand::NextBlock);
    }
    let navigation_time = start.elapsed();

    // Should navigate quickly
    assert!(navigation_time < Duration::from_millis(50));

    println!("Performance benchmark:");
    println!("  Content creation: {:?}", creation_time);
    println!("  Navigation: {:?}", navigation_time);
}
