use autorun::{
    commands::CommandRegistry,
    ui::completion::{CompletionEngine, CompletionPopup},
};
use std::sync::Arc;

#[tokio::test]
async fn test_basic_command_completion() {
    let registry = Arc::new(CommandRegistry::new());
    let engine = CompletionEngine::new(registry);

    // Test command completion for "/he" should suggest "help"
    let suggestions = engine.get_suggestions("/he", 3, None).await;

    assert!(!suggestions.is_empty(), "Should have suggestions for '/he'");
    assert!(
        suggestions.iter().any(|s| s.text.contains("help")),
        "Should suggest 'help' command"
    );
}

#[tokio::test]
async fn test_parameter_completion() {
    let registry = Arc::new(CommandRegistry::new());
    let engine = CompletionEngine::new(registry);

    // Test parameter completion for "/help " should suggest command names
    let suggestions = engine.get_suggestions("/help ", 6, None).await;

    assert!(
        !suggestions.is_empty(),
        "Should have suggestions for '/help ' parameters"
    );
}

#[tokio::test]
async fn test_completion_popup() {
    let mut popup = CompletionPopup::new();

    assert!(!popup.is_visible(), "Popup should be hidden initially");

    // Test with empty suggestions
    popup.update_suggestions(vec![]);
    assert!(
        !popup.is_visible(),
        "Popup should remain hidden with empty suggestions"
    );

    // Test with actual suggestions
    use autorun::ui::completion::{CompletionSuggestion, SuggestionType};
    use std::collections::HashMap;

    let suggestions = vec![CompletionSuggestion {
        text: "help ".to_string(),
        display: "help".to_string(),
        description: "Show available commands".to_string(),
        suggestion_type: SuggestionType::Command,
        score: 100.0,
        icon: "📌".to_string(),
        metadata: HashMap::new(),
    }];

    popup.update_suggestions(suggestions);
    assert!(
        popup.is_visible(),
        "Popup should be visible with suggestions"
    );

    // Test navigation
    popup.move_selection_down(); // Should not crash on single item
    popup.move_selection_up(); // Should not crash

    let selected = popup.get_selected();
    assert!(selected.is_some(), "Should have a selected suggestion");
    assert_eq!(
        selected.unwrap().text,
        "help ",
        "Should select the help command"
    );
}

#[tokio::test]
async fn test_usage_recording() {
    let registry = Arc::new(CommandRegistry::new());
    let engine = CompletionEngine::new(registry);

    // Record some usage
    engine.record_usage("help", vec!["clear".to_string()]).await;
    engine.record_usage("help", vec!["save".to_string()]).await;

    // Get suggestions - help should be prioritized due to usage
    let suggestions = engine.get_suggestions("/h", 2, None).await;

    assert!(!suggestions.is_empty(), "Should have suggestions");
    // The help command should be among the top suggestions due to usage history
    let help_suggestion = suggestions.iter().find(|s| s.text.contains("help"));
    assert!(help_suggestion.is_some(), "Should suggest 'help' command");
}
