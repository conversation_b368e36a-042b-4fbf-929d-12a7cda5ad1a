//! Integration tests for <PERSON> tokenizer

use autorun::prompts::tokenization::{Token<PERSON><PERSON>rror, TokenizerConfig, TokenizerFactory};

#[tokio::test]
async fn test_claude_tokenizer_integration() {
    // Test that we can create a Claude tokenizer
    let provider = TokenizerFactory::create_provider("claude-3-sonnet")
        .expect("Should create Claude tokenizer");

    let config = TokenizerConfig {
        model: "claude-3-sonnet".to_string(),
        max_tokens: 200_000,
        include_special_tokens: false,
        ..Default::default()
    };

    // Test basic tokenization
    let text = "Hello, <PERSON>!";
    let token_count = provider
        .count_tokens(text, &config)
        .await
        .expect("Should count tokens");

    assert!(token_count > 0);
    assert!(token_count < 10); // Simple text should have few tokens
}

#[tokio::test]
async fn test_claude_model_variants() {
    let models = vec![
        "claude-3-opus-20240229",
        "claude-3-sonnet",
        "claude-3-haiku",
        "claude-2.1",
        "claude-instant-1.2",
    ];

    for model in models {
        let provider = TokenizerFactory::create_provider(model)
            .expect(&format!("Should create tokenizer for {}", model));

        let info = provider
            .get_model_info(model)
            .await
            .expect(&format!("Should get model info for {}", model));

        assert!(info.max_context_tokens > 0);
        assert!(info.max_output_tokens > 0);
    }
}

#[tokio::test]
async fn test_factory_availability() {
    // Claude models should be available
    assert!(TokenizerFactory::is_available("claude-3-sonnet"));
    assert!(TokenizerFactory::is_available("claude-3-opus"));
    assert!(TokenizerFactory::is_available("CLAUDE-3-HAIKU")); // Case insensitive

    // Non-Claude models should not be available
    assert!(!TokenizerFactory::is_available("gpt-4"));
    assert!(!TokenizerFactory::is_available("llama-2"));
}

#[tokio::test]
async fn test_token_limit_validation() {
    let provider = TokenizerFactory::create_provider("claude-3-sonnet")
        .expect("Should create Claude tokenizer");

    let config = TokenizerConfig {
        model: "claude-3-sonnet".to_string(),
        max_tokens: 200_000,
        include_special_tokens: false,
        ..Default::default()
    };

    // Small text should pass validation
    let small_text = "This is a small text.";
    assert!(provider
        .validate_token_limit(small_text, &config)
        .await
        .is_ok());

    // Extremely large text should fail validation
    let huge_text = "x".repeat(1_000_000); // 1 million characters
    match provider.validate_token_limit(&huge_text, &config).await {
        Err(TokenizationError::TokenLimitExceeded { .. }) => {
            // Expected error
        }
        _ => panic!("Expected TokenLimitExceeded error"),
    }
}

#[tokio::test]
async fn test_encode_decode_roundtrip() {
    let provider = TokenizerFactory::create_provider("claude-3-sonnet")
        .expect("Should create Claude tokenizer");

    let config = TokenizerConfig {
        model: "claude-3-sonnet".to_string(),
        max_tokens: 200_000,
        include_special_tokens: false,
        ..Default::default()
    };

    let original_text = "This is a test of encoding and decoding. It includes punctuation, numbers 123, and symbols @#$!";

    // Encode
    let tokens = provider
        .encode(original_text, &config)
        .await
        .expect("Should encode text");

    assert!(!tokens.is_empty());

    // Decode
    let decoded_text = provider
        .decode(&tokens, &config)
        .await
        .expect("Should decode tokens");

    // The decoded text should match the original
    assert_eq!(decoded_text, original_text);
}

#[tokio::test]
async fn test_text_splitting() {
    let provider = TokenizerFactory::create_provider("claude-3-sonnet")
        .expect("Should create Claude tokenizer");

    let config = TokenizerConfig {
        model: "claude-3-sonnet".to_string(),
        max_tokens: 200_000,
        include_special_tokens: false,
        ..Default::default()
    };

    let text = "The quick brown fox jumps over the lazy dog. ".repeat(20);
    let max_tokens_per_chunk = 20;

    let chunks = provider
        .split_by_tokens(&text, max_tokens_per_chunk, &config)
        .await
        .expect("Should split text");

    assert!(!chunks.is_empty());

    // Verify each chunk respects the token limit
    for chunk in &chunks {
        let chunk_tokens = provider
            .count_tokens(chunk, &config)
            .await
            .expect("Should count chunk tokens");
        assert!(chunk_tokens <= max_tokens_per_chunk);
    }
}
