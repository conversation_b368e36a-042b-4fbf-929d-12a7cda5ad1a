// Test our new configuration structures
use std::collections::HashMap;
use serde::{Deserialize, Serialize};

/// Provider-specific configuration settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ProviderSpecificConfig {
    /// Anthropic-specific configuration
    Anthropic(AnthropicProviderConfig),
    /// OpenRouter-specific configuration  
    OpenRouter(OpenRouterProviderConfig),
    /// OpenAI-specific configuration
    OpenAI(OpenAIProviderConfig),
    /// Custom provider configuration
    Custom(CustomProviderConfig),
}

/// Anthropic provider configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnthropicProviderConfig {
    #[serde(default)]
    pub custom_headers: Option<HashMap<String, String>>,
    #[serde(default)]
    pub timeout_seconds: Option<u64>,
    #[serde(default)]
    pub enable_streaming: Option<bool>,
}

/// OpenRouter provider configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct OpenRouterProviderConfig {
    #[serde(default)]
    pub site_url: Option<String>,
    #[serde(default)]
    pub app_name: Option<String>,
    #[serde(default)]
    pub custom_headers: Option<HashMap<String, String>>,
    #[serde(default)]
    pub timeout_seconds: Option<u64>,
    #[serde(default)]
    pub enable_streaming: Option<bool>,
}

/// OpenAI provider configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OpenAIProviderConfig {
    #[serde(default)]
    pub organization: Option<String>,
    #[serde(default)]
    pub custom_headers: Option<HashMap<String, String>>,
    #[serde(default)]
    pub timeout_seconds: Option<u64>,
    #[serde(default)]
    pub enable_streaming: Option<bool>,
}

/// Custom provider configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CustomProviderConfig {
    #[serde(default)]
    pub custom_headers: Option<HashMap<String, String>>,
    #[serde(default)]
    pub timeout_seconds: Option<u64>,
    #[serde(default)]
    pub enable_streaming: Option<bool>,
    #[serde(default)]
    pub auth_method: Option<String>,
    #[serde(default)]
    pub additional_settings: Option<HashMap<String, serde_json::Value>>,
}

/// Rate limiting configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitConfig {
    #[serde(default)]
    pub requests_per_minute: Option<u32>,
    #[serde(default)]
    pub requests_per_hour: Option<u32>,
    #[serde(default)]
    pub requests_per_day: Option<u32>,
    #[serde(default)]
    pub backoff_strategy: Option<BackoffStrategy>,
    #[serde(default = "default_true")]
    pub enabled: bool,
}

/// Backoff strategy for rate limiting
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BackoffStrategy {
    Linear { delay_seconds: u64 },
    Exponential { initial_delay_seconds: u64, max_delay_seconds: u64 },
    Fixed { delay_seconds: u64 },
}

/// Health check configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheckConfig {
    #[serde(default = "default_true")]
    pub enabled: bool,
    #[serde(default)]
    pub interval_seconds: Option<u64>,
    #[serde(default)]
    pub timeout_seconds: Option<u64>,
    #[serde(default)]
    pub endpoint: Option<String>,
    #[serde(default)]
    pub failure_threshold: Option<u32>,
}

/// API key source configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ApiKeySource {
    Environment { var_name: String },
    File { path: String },
    Config { key: String },
    KeychainService { service_name: String },
}

fn default_true() -> bool {
    true
}

/// Enhanced LLMConfig with new fields
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMConfig {
    pub provider: String,
    pub api_key: Option<String>,
    pub base_url: Option<String>,
    pub model: String,
    pub temperature: Option<f32>,
    pub max_tokens: Option<u32>,
    
    // Phase 1: Enhanced configuration fields (all optional for backward compatibility)
    #[serde(default)]
    pub provider_config: Option<ProviderSpecificConfig>,
    #[serde(default)]
    pub rate_limits: Option<RateLimitConfig>,
    #[serde(default)]
    pub health_check: Option<HealthCheckConfig>,
    #[serde(default)]
    pub fallback_providers: Option<Vec<String>>,
    #[serde(default)]
    pub api_key_sources: Option<Vec<ApiKeySource>>,
}

fn main() {
    // Test basic serialization and deserialization
    let config = LLMConfig {
        provider: "openrouter".to_string(),
        api_key: Some("test-key".to_string()),
        base_url: None,
        model: "anthropic/claude-3-sonnet".to_string(),
        temperature: Some(0.7),
        max_tokens: Some(4096),
        provider_config: Some(ProviderSpecificConfig::OpenRouter(OpenRouterProviderConfig {
            site_url: Some("https://example.com".to_string()),
            app_name: Some("AutoRun".to_string()),
            custom_headers: None,
            timeout_seconds: Some(30),
            enable_streaming: Some(true),
        })),
        rate_limits: Some(RateLimitConfig {
            requests_per_minute: Some(60),
            requests_per_hour: Some(1000),
            requests_per_day: None,
            backoff_strategy: Some(BackoffStrategy::Exponential {
                initial_delay_seconds: 1,
                max_delay_seconds: 60,
            }),
            enabled: true,
        }),
        health_check: Some(HealthCheckConfig {
            enabled: true,
            interval_seconds: Some(300),
            timeout_seconds: Some(10),
            endpoint: None,
            failure_threshold: Some(3),
        }),
        fallback_providers: Some(vec\!["anthropic".to_string()]),
        api_key_sources: Some(vec\![
            ApiKeySource::Environment { var_name: "OPENROUTER_API_KEY".to_string() },
            ApiKeySource::Config { key: "api_key".to_string() },
        ]),
    };

    // Test TOML serialization
    let toml_str = toml::to_string_pretty(&config).expect("Failed to serialize to TOML");
    println\!("Enhanced LLMConfig TOML:");
    println\!("{}", toml_str);

    // Test TOML deserialization
    let _deserialized: LLMConfig = toml::from_str(&toml_str).expect("Failed to deserialize from TOML");
    println\!("✅ TOML serialization/deserialization successful\!");

    // Test backward compatibility with old config format
    let old_config_toml = r#"
        provider = "anthropic"
        model = "claude-3-5-sonnet"
        temperature = 0.8
        max_tokens = 2048
    "#;

    let _old_config: LLMConfig = toml::from_str(old_config_toml).expect("Failed to parse old config format");
    println\!("✅ Backward compatibility test successful\!");

    println\!("All tests passed\! Enhanced configuration system is working correctly.");
}
EOF < /dev/null