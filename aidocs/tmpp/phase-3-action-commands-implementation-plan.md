# Phase 3 Action Commands Implementation Plan

## Overview
Implement a comprehensive `/` command system for action and tool execution, building on the existing Phase 1 & 2 infrastructure. The system will provide dynamic MCP tool discovery, memory management interface, and custom command loading with variable substitution.

## Concurrent Sub-Agent Delegation Strategy

### Primary Decomposition for Maximum Concurrency
This implementation is designed for maximum parallelization using sub-agents working concurrently on independent components:

#### Batch 1: Core Infrastructure (Parallel)
- **Agent A**: Action Engine Core (`src/commands/action_engine.rs`)
- **Agent B**: Template Engine (`src/templates/` module)
- **Agent C**: Memory Management System (`src/commands/memory/` module)
- **Agent D**: Action Completion Provider (`src/ui/completion/action_completion.rs`)

#### Batch 2: MCP Integration (Parallel, after Batch 1 Core)
- **Agent E**: MCP Discovery (`src/commands/mcp_actions/mcp_discovery.rs`)
- **Agent F**: MCP Tool Executor (`src/commands/mcp_actions/mcp_tool_executor.rs`)
- **Agent G**: MCP Prompt Handler (`src/commands/mcp_actions/mcp_prompt_handler.rs`)
- **Agent H**: MCP Resource Manager (`src/commands/mcp_actions/mcp_resource_manager.rs`)

#### Batch 3: Custom Commands (Parallel, after Template Engine)
- **Agent I**: Command Loader (`src/commands/custom/command_loader.rs`)
- **Agent J**: Template Parser (`src/commands/custom/template_parser.rs`)
- **Agent K**: Variable Substitution (`src/commands/custom/variable_substitution.rs`)
- **Agent L**: Command Executor (`src/commands/custom/command_executor.rs`)

### Dependency Graph for Coordination
```
Template Engine → Template Parser, Variable Substitution
Action Engine → All MCP Components, Memory System
Memory System → Independent
MCP Discovery → MCP Tool Executor, MCP Prompt Handler, MCP Resource Manager
Command Loader → Template Parser (depends on Template Engine)
```

## Implementation Structure

### 1. Action Command Engine (`src/commands/action_engine.rs`)
- **Core Engine**: Main orchestrator for all action commands
- **Memory Commands**: Interactive CRUD operations for `/memory` commands  
- **MCP Integration**: Dynamic discovery and execution of MCP tools
- **Custom Commands**: Loading and execution of user-defined commands
- **Template Processing**: Variable substitution and template execution

### 2. MCP Action Integration (`src/commands/mcp_actions/`)
- **`mcp_discovery.rs`**: Real-time MCP server and tool discovery
- **`mcp_tool_executor.rs`**: Dynamic tool execution with parameter mapping
- **`mcp_prompt_handler.rs`**: MCP prompt execution with context injection
- **`mcp_resource_manager.rs`**: MCP resource access and management

### 3. Memory Management System (`src/commands/memory/`)
- **`memory_editor.rs`**: Interactive memory CRUD interface
- **`memory_search.rs`**: Full-text search with fuzzy matching
- **`memory_tags.rs`**: Tag-based organization and filtering
- **`memory_export.rs`**: Export/import functionality

### 4. Custom Command System (`src/commands/custom/`)
- **`command_loader.rs`**: Discover and load commands from filesystem
- **`template_parser.rs`**: Parse markdown templates with variable extraction
- **`variable_substitution.rs`**: Variable replacement with interactive prompts
- **`command_executor.rs`**: Execute custom commands with context injection

### 5. Template Engine (`src/templates/`)
- **`template_engine.rs`**: Core template processing with variable support
- **`variable_resolver.rs`**: Resolve system and user variables
- **`template_cache.rs`**: Compiled template caching for performance
- **`template_validator.rs`**: Template syntax validation and error reporting

### 6. Action Completion Provider (`src/ui/completion/action_completion.rs`)
- **Memory Completions**: Dynamic completion for `/memory` operations
- **MCP Completions**: Real-time MCP tool/prompt/resource completion
- **Custom Command Completions**: Discovery and parameter completion

## Key Features

### Memory Management (`/memory`)
- CRUD operations: create, read, update, delete, search memories
- Full-text search with SQLite FTS5 (already implemented in existing memory_store.rs)
- Tag-based organization and filtering
- Export/import functionality
- Interactive editing interface

### Dynamic MCP Tool Execution
- Automatic discovery of available MCP servers and tools
- Dynamic completion of tool names and parameters
- Real-time execution with progress reporting
- Resource management and quota handling

### Custom Command System
- Scan `.autorun/commands/` and `.claude/commands/` directories
- Parse markdown templates with YAML frontmatter
- Variable substitution with interactive prompts
- Template caching for performance
- File watching for real-time command updates

### Template Engine
- Support for `$VAR`, `${VAR}`, and environment variables
- Interactive variable prompts for missing values
- Template compilation and caching
- Syntax validation and error reporting

## Integration Points

### Existing Infrastructure
- **Commands System**: Extend existing parser and registry in `src/commands/`
- **Memory Store**: Leverage existing `src/storage/memory_store.rs`
- **MCP Client**: Build on existing `src/mcp/client.rs`
- **Context System**: Integrate with Phase 2 context providers
- **UI Completion**: Extend existing `src/ui/completion/` system

### Dependencies
- **Tera**: Already included for template processing
- **SQLite**: Already integrated via tokio-rusqlite for memory storage
- **MCP Framework**: Already included via rmcp crate
- **File Watching**: Use notify crate (already included)
- **Fuzzy Matching**: Use fuzzy-matcher crate (already included)

## Concurrent Implementation Execution Plan

### Phase 3A: Core Infrastructure (4 Parallel Agents)
**Agent A - Action Engine Core:**
```rust
// Create src/commands/action_engine.rs
pub struct ActionEngine {
    memory_store: Arc<RwLock<MemoryStore>>,
    mcp_client: Arc<McpClient>,
    template_engine: Arc<TemplateEngine>,
    custom_commands: Arc<RwLock<HashMap<String, CustomCommand>>>,
}
```

**Agent B - Template Engine:**
```rust
// Create src/templates/mod.rs and components
pub struct TemplateEngine {
    tera: Tera,
    cache: Arc<RwLock<TemplateCache>>,
    variable_resolver: VariableResolver,
}
```

**Agent C - Memory Management:**
```rust
// Create src/commands/memory/ module
pub struct MemoryEditor {
    store: Arc<RwLock<MemoryStore>>,
    search_engine: MemorySearch,
    tag_manager: MemoryTags,
}
```

**Agent D - Action Completion:**
```rust
// Create src/ui/completion/action_completion.rs
pub struct ActionCompletionProvider {
    memory_completions: MemoryCompletionEngine,
    mcp_completions: McpCompletionEngine,
    custom_completions: CustomCommandCompletionEngine,
}
```

### Phase 3B: MCP Integration (4 Parallel Agents)
**Agent E - MCP Discovery:**
```rust
// Create src/commands/mcp_actions/mcp_discovery.rs
pub struct McpDiscovery {
    client_manager: Arc<McpClientManager>,
    server_registry: Arc<RwLock<HashMap<String, McpServerInfo>>>,
}
```

**Agent F - MCP Tool Executor:**
```rust
// Create src/commands/mcp_actions/mcp_tool_executor.rs
pub struct McpToolExecutor {
    client_manager: Arc<McpClientManager>,
    execution_tracker: Arc<RwLock<HashMap<Uuid, ExecutionStatus>>>,
}
```

**Agent G - MCP Prompt Handler:**
```rust
// Create src/commands/mcp_actions/mcp_prompt_handler.rs
pub struct McpPromptHandler {
    client_manager: Arc<McpClientManager>,
    context_injector: ContextInjector,
}
```

**Agent H - MCP Resource Manager:**
```rust
// Create src/commands/mcp_actions/mcp_resource_manager.rs
pub struct McpResourceManager {
    client_manager: Arc<McpClientManager>,
    resource_cache: Arc<RwLock<HashMap<String, CachedResource>>>,
}
```

### Phase 3C: Custom Commands (4 Parallel Agents)
**Agent I - Command Loader:**
```rust
// Create src/commands/custom/command_loader.rs
pub struct CommandLoader {
    file_watcher: notify::RecommendedWatcher,
    commands: Arc<RwLock<HashMap<String, CustomCommand>>>,
}
```

**Agent J - Template Parser:**
```rust
// Create src/commands/custom/template_parser.rs
pub struct TemplateParser {
    yaml_parser: serde_yaml::Deserializer,
    markdown_parser: pulldown_cmark::Parser,
}
```

**Agent K - Variable Substitution:**
```rust
// Create src/commands/custom/variable_substitution.rs
pub struct VariableSubstitution {
    resolver: VariableResolver,
    interactive_prompter: InteractivePrompter,
}
```

**Agent L - Command Executor:**
```rust
// Create src/commands/custom/command_executor.rs
pub struct CommandExecutor {
    template_engine: Arc<TemplateEngine>,
    context_injector: ContextInjector,
    execution_tracker: Arc<RwLock<HashMap<Uuid, ExecutionStatus>>>,
}
```

## Quality Assurance
- **Testing**: Comprehensive unit and integration tests for each component
- **Error Handling**: Robust error handling with clear user messages
- **Security**: Input sanitization and safe command execution
- **Performance**: Template caching and efficient database operations
- **Documentation**: Inline documentation and usage examples

## Coordination Protocol
1. **Interface Contracts**: Each agent works to well-defined trait interfaces
2. **Shared State**: Use Arc<RwLock<T>> for thread-safe shared state
3. **Event System**: Implement async event system for component communication
4. **Integration Points**: Clear handoff protocols between agent deliverables
5. **Testing Integration**: Each agent provides integration test harness

This implementation maximizes concurrency by having 12 agents working in parallel across 3 phases, with clear dependency management and coordination protocols.