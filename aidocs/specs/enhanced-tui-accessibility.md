# Enhanced TUI Accessibility Guide

## Overview

The Enhanced TUI is designed with accessibility as a core principle, ensuring that all users, including those with disabilities, can effectively use AutoRun-RS. This guide covers accessibility features, configuration, and best practices.

## Accessibility Features

### Screen Reader Support

#### Supported Screen Readers
- **NVDA** (Windows) - Full support
- **JAWS** (Windows) - Full support  
- **VoiceOver** (macOS) - Full support
- **Orca** (Linux) - Full support
- **TalkBack** (Android/Termux) - Basic support

#### Screen Reader Configuration
```bash
# Enable screen reader mode
cargo run -- --screen-reader

# Or via configuration
echo 'screen_reader = true' >> ~/.config/autorun/accessibility.toml
```

#### ARIA Support
All UI elements include proper ARIA labels and roles:
- **Landmarks**: Main content, navigation, complementary
- **Roles**: Button, textbox, listbox, option, dialog
- **Properties**: aria-label, aria-describedby, aria-expanded
- **States**: aria-selected, aria-checked, aria-disabled

### Keyboard Navigation

#### Full Keyboard Access
Every feature is accessible via keyboard:
- **Tab Navigation**: Logical tab order through all interactive elements
- **Arrow Keys**: Navigate within components (lists, menus, content blocks)
- **Enter/Space**: Activate buttons and select items
- **Escape**: Cancel operations and close dialogs

#### Skip Links
Quick navigation shortcuts:
- **Ctrl+1**: Skip to main content area
- **Ctrl+2**: Skip to input area
- **Ctrl+3**: Skip to status bar
- **Ctrl+4**: Skip to widget area

#### Focus Indicators
Clear visual and programmatic focus indicators:
- **High Contrast Borders**: Visible focus rectangles
- **Color Changes**: Focus state color changes
- **Screen Reader Announcements**: Focus changes announced
- **Focus History**: Return to previous focus on dialog close

### Visual Accessibility

#### High Contrast Themes
Built-in high contrast themes for better visibility:
- **High Contrast Dark**: White text on black background
- **High Contrast Light**: Black text on white background
- **Custom Contrast**: User-configurable contrast ratios

```bash
# Enable high contrast theme
:theme high_contrast_dark

# Or via configuration
echo 'theme = "high_contrast_dark"' >> ~/.config/autorun/ui.toml
```

#### Font and Size Options
Configurable text display:
- **Font Size**: Adjustable from 8pt to 24pt
- **Font Family**: Monospace fonts optimized for terminals
- **Line Height**: Adjustable line spacing
- **Character Spacing**: Adjustable letter spacing

```toml
# ~/.config/autorun/ui.toml
[display]
font_size = 16
line_height = 1.2
character_spacing = 0.1
```

#### Color Customization
Comprehensive color configuration:
- **Foreground/Background**: Primary text and background colors
- **Accent Colors**: Highlighting and selection colors
- **Status Colors**: Error, warning, success indicators
- **Syntax Colors**: Code highlighting colors

### Motor Accessibility

#### Reduced Motion
Options for users sensitive to motion:
- **Disable Animations**: Turn off all UI animations
- **Reduce Transitions**: Minimize visual transitions
- **Static Indicators**: Use static instead of animated progress indicators

```toml
# ~/.config/autorun/accessibility.toml
[motion]
animations = false
transitions = false
static_progress = true
```

#### Sticky Keys Support
Enhanced keyboard handling:
- **Modifier Key Handling**: Proper handling of sticky modifier keys
- **Key Repeat**: Configurable key repeat rates
- **Chord Alternatives**: Alternative key combinations for complex shortcuts

#### Mouse Alternatives
Complete mouse-free operation:
- **Keyboard Selection**: Select text and content blocks with keyboard
- **Context Menus**: Access all context menus via keyboard
- **Drag and Drop**: Keyboard alternatives for drag and drop operations

## Configuration

### Accessibility Configuration File

#### Location: `~/.config/autorun/accessibility.toml`
```toml
[screen_reader]
enabled = true
announcement_level = "verbose"  # verbose, normal, minimal
announce_focus_changes = true
announce_content_changes = true
announce_status_changes = true

[keyboard]
tab_navigation = true
arrow_navigation = true
skip_links = true
focus_indicators = "high_contrast"  # high_contrast, normal, custom

[visual]
high_contrast = true
large_text = false
reduced_motion = true
focus_outline_width = 2

[motor]
sticky_keys = true
slow_keys = false
bounce_keys = false
key_repeat_delay = 500  # milliseconds
key_repeat_rate = 30    # keys per second

[audio]
sound_feedback = false
error_sounds = true
success_sounds = true
```

### Theme Accessibility

#### High Contrast Theme Configuration
```toml
# themes/high_contrast.toml
name = "High Contrast"

[colors]
background = "#000000"
foreground = "#ffffff"
primary = "#ffffff"
secondary = "#ffff00"
accent = "#00ffff"
error = "#ff0000"
warning = "#ffff00"
success = "#00ff00"

[accessibility]
contrast_ratio = 21.0  # WCAG AAA level
focus_outline = "#ffff00"
focus_outline_width = 3
selection_background = "#0000ff"
selection_foreground = "#ffffff"
```

### Keybinding Accessibility

#### Alternative Keybindings
```toml
# keybindings/accessible.toml
[accessibility]
# Alternative shortcuts for users who can't use standard combinations
quit_alt = "F10"
help_alt = "F1"
menu_alt = "F2"

# Single-key alternatives for common actions
[single_key]
next_block = "Tab"
previous_block = "Shift+Tab"
select_block = "Space"
activate = "Enter"

# Chord alternatives (sequential key presses)
[chords]
copy = ["c", "p"]  # Press 'c' then 'p' instead of Ctrl+C
paste = ["p", "s"]  # Press 'p' then 's' instead of Ctrl+V
```

## Screen Reader Integration

### Content Announcements

#### Content Block Announcements
When navigating content blocks, screen readers announce:
- **Block Type**: "User message", "Assistant response", "Code block"
- **Content Preview**: First line or summary of content
- **Position**: "Block 3 of 15"
- **Metadata**: Timestamp, tool name (for tool outputs)

#### Status Announcements
Screen readers announce important status changes:
- **Processing Status**: "Processing user request"
- **Completion Status**: "Request completed successfully"
- **Error Status**: "Error occurred: [error message]"
- **Mode Changes**: "Entered insert mode", "Returned to normal mode"

#### Input Announcements
During text input, screen readers announce:
- **Completion Suggestions**: Available auto-completions
- **Validation Errors**: Input validation feedback
- **Character Echo**: Characters as they're typed
- **Word Echo**: Words as they're completed

### ARIA Implementation

#### Landmark Roles
```rust
// Main content area
aria_role = "main"
aria_label = "Conversation content"

// Input area
aria_role = "form"
aria_label = "Message input"

// Widget area
aria_role = "complementary"
aria_label = "Tool widgets"

// Status bar
aria_role = "status"
aria_label = "Application status"
```

#### Interactive Elements
```rust
// Content blocks
aria_role = "article"
aria_label = "User message from [timestamp]"

// Completion popup
aria_role = "listbox"
aria_label = "Auto-completion suggestions"

// Buttons
aria_role = "button"
aria_label = "Send message"
aria_describedby = "send-button-help"
```

## Testing Accessibility

### Automated Testing

#### Screen Reader Testing
```bash
# Run with screen reader simulation
cargo test --features accessibility -- --screen-reader-sim

# Test ARIA compliance
cargo test accessibility::aria_compliance

# Test keyboard navigation
cargo test accessibility::keyboard_navigation
```

#### Accessibility Audit
```rust
#[cfg(test)]
mod accessibility_tests {
    use super::*;
    
    #[tokio::test]
    async fn test_aria_compliance() {
        let tui = EnhancedTuiInterface::new(TuiConfig::default()).await.unwrap();
        let audit = AccessibilityAuditor::new();
        
        let results = audit.audit_interface(&tui).await;
        assert!(results.is_compliant());
        assert!(results.contrast_ratio >= 4.5); // WCAG AA
    }
    
    #[tokio::test]
    async fn test_keyboard_navigation() {
        let mut tui = EnhancedTuiInterface::new(TuiConfig::default()).await.unwrap();
        
        // Test tab navigation
        let result = tui.handle_event(Event::Key(KeyEvent::new(
            KeyCode::Tab,
            KeyModifiers::NONE,
        ))).await.unwrap();
        
        assert!(matches!(result, Some(Action::FocusNext)));
    }
}
```

### Manual Testing

#### Screen Reader Testing Checklist
- [ ] All content is announced when focused
- [ ] Navigation between elements is logical
- [ ] Status changes are announced appropriately
- [ ] Form controls have proper labels
- [ ] Error messages are announced
- [ ] Help text is available and announced

#### Keyboard Testing Checklist
- [ ] All functionality accessible via keyboard
- [ ] Tab order is logical and complete
- [ ] Focus indicators are visible
- [ ] Keyboard shortcuts work as expected
- [ ] No keyboard traps exist

#### Visual Testing Checklist
- [ ] High contrast themes provide sufficient contrast
- [ ] Focus indicators are clearly visible
- [ ] Text is readable at all supported sizes
- [ ] Color is not the only way to convey information

## Best Practices

### Development Guidelines

#### ARIA Best Practices
1. **Use semantic HTML/TUI elements** when possible
2. **Provide descriptive labels** for all interactive elements
3. **Use landmarks** to structure content
4. **Announce dynamic content changes**
5. **Provide alternative text** for visual elements

#### Keyboard Navigation Best Practices
1. **Ensure logical tab order** through all interactive elements
2. **Provide skip links** for efficient navigation
3. **Use standard keyboard conventions** (Tab, Arrow keys, Enter, Escape)
4. **Avoid keyboard traps** where users can't navigate away
5. **Provide keyboard alternatives** for mouse-only actions

#### Visual Design Best Practices
1. **Maintain sufficient color contrast** (4.5:1 minimum for normal text)
2. **Don't rely solely on color** to convey information
3. **Provide clear focus indicators** for all interactive elements
4. **Support user customization** of visual elements
5. **Test with high contrast modes** and screen magnifiers

### Content Guidelines

#### Writing Accessible Content
1. **Use clear, simple language** when possible
2. **Provide context** for abbreviations and technical terms
3. **Structure content** with proper headings and landmarks
4. **Describe visual elements** that convey important information
5. **Provide alternative formats** for complex visual content

#### Error Handling
1. **Provide clear error messages** that explain what went wrong
2. **Suggest specific solutions** when possible
3. **Announce errors** to screen reader users
4. **Position error messages** near the relevant form fields
5. **Use appropriate ARIA roles** for error messages

## Troubleshooting

### Common Issues

#### Screen Reader Not Working
1. **Check screen reader mode**: Ensure `screen_reader = true` in config
2. **Verify screen reader software**: Ensure compatible screen reader is running
3. **Check terminal compatibility**: Some terminals have limited accessibility support
4. **Update configuration**: Try resetting accessibility configuration

#### Keyboard Navigation Issues
1. **Check keybinding configuration**: Verify keybindings are properly configured
2. **Test in different terminals**: Some terminals handle keys differently
3. **Disable conflicting software**: Check for software that might intercept keys
4. **Reset to defaults**: Try resetting keybindings to default configuration

#### Visual Accessibility Issues
1. **Try high contrast theme**: Switch to a high contrast theme
2. **Adjust font size**: Increase font size in configuration
3. **Check terminal settings**: Verify terminal supports required features
4. **Update graphics drivers**: Ensure graphics drivers are up to date

### Getting Help

#### Accessibility Support
- **Documentation**: Comprehensive accessibility documentation
- **Community**: Active community support for accessibility questions
- **Bug Reports**: Dedicated accessibility bug reporting process
- **Feature Requests**: Accessibility feature request process

#### Testing Resources
- **Screen Reader Testing**: Guidelines for testing with screen readers
- **Keyboard Testing**: Comprehensive keyboard testing procedures
- **Visual Testing**: Tools and procedures for visual accessibility testing
- **Automated Testing**: Automated accessibility testing tools and scripts

## Compliance

### WCAG 2.1 Compliance

#### Level AA Compliance
The Enhanced TUI meets WCAG 2.1 Level AA standards:
- **Perceivable**: Content is presentable in multiple ways
- **Operable**: Interface components are operable via multiple methods
- **Understandable**: Information and UI operation are understandable
- **Robust**: Content is robust enough for various assistive technologies

#### Specific Guidelines Met
- **1.4.3 Contrast (Minimum)**: 4.5:1 contrast ratio for normal text
- **2.1.1 Keyboard**: All functionality available via keyboard
- **2.1.2 No Keyboard Trap**: No keyboard traps exist
- **2.4.3 Focus Order**: Logical focus order maintained
- **2.4.7 Focus Visible**: Focus indicators are visible
- **3.2.2 On Input**: No unexpected context changes on input
- **4.1.2 Name, Role, Value**: All components have accessible names and roles

### Section 508 Compliance

The Enhanced TUI also meets Section 508 requirements for federal accessibility:
- **Software Applications**: Meets software application requirements
- **Web-based Intranet**: Meets web-based application requirements
- **Telecommunications**: Meets telecommunications product requirements
