# Specification: Phase 1 - Foundation Enhancement

## 1. Goal/Objective

Enhance the existing LLMProvider infrastructure to support advanced multi-provider features, improved error handling, and provider-specific configurations. This phase establishes the foundational capabilities required for advanced LLM provider management, rate limiting, health monitoring, and provider-specific optimizations while maintaining full backward compatibility with the current codebase.

## 2. Input

**Current AutoRun-RS codebase with:**
- Basic `LLMProvider` trait with `complete()`, `supports_tools()`, and capability detection
- Factory pattern implementation with `ProviderBuilder` and `ProviderFactory`  
- Simple configuration system with `LLMConfig` struct
- Existing error handling via `AutorunError` enum with thiserror integration
- Provider detection system with environment variable auto-detection
- Tool support detection for various LLM models
- Current providers: Anthropic (direct) and OpenAI-compatible (OpenRouter)

**Key Files:**
- `src/llm/mod.rs` - Core LLM types and provider trait
- `src/llm/client.rs` - Provider implementations
- `src/llm/factory.rs` - Provider factory pattern
- `src/config/mod.rs` - Configuration management
- `src/config/registry.rs` - Provider registry
- `src/errors.rs` - Error handling

## 3. Output

**Enhanced LLMProvider Infrastructure:**

### 3.1 Core Files to Modify:
- `src/llm/mod.rs` - Enhanced LLMProvider trait with new methods
- `src/llm/client.rs` - Provider implementations with new capabilities
- `src/llm/factory.rs` - Extended factory with advanced configuration
- `src/config/mod.rs` - Provider-specific configuration structures
- `src/config/registry.rs` - Enhanced registry with provider metadata
- `src/errors.rs` - Extended error types for provider operations

### 3.2 New Components:
- Rate limiting infrastructure for each provider
- Health monitoring and provider status tracking
- API key resolution system with fallback mechanisms
- Provider-specific configuration validation
- Enhanced error handling with provider-specific error codes

### 3.3 Deliverables:
1. **Enhanced LLMProvider trait** with rate limiting, health checks, and metadata
2. **Provider-specific configuration system** supporting per-provider settings
3. **Comprehensive error handling** with provider-specific error types
4. **API key resolution system** with environment variable fallbacks
5. **Rate limiting infrastructure** with configurable limits per provider
6. **Health monitoring system** for provider availability and performance
7. **Backward compatibility layer** ensuring existing code continues to work
8. **Unit tests** for all new functionality
9. **Integration tests** for multi-provider scenarios

## 4. Constraints

- **Backward Compatibility**: Must maintain full compatibility with existing `LLMProvider` trait usage
- **Rust Best Practices**: Follow async patterns, proper error handling, and idiomatic Rust code
- **Error Handling**: Use existing `AutorunError` pattern with thiserror for consistency
- **Configuration Integration**: Extend existing configuration system without breaking changes
- **Performance**: Minimize overhead for basic operations while adding advanced features
- **Testing**: All new functionality must include comprehensive unit and integration tests
- **Documentation**: Code must be well-documented with examples and usage patterns

## 5. Implementation Details

### 5.1 Enhanced LLMProvider Trait

```rust
#[async_trait::async_trait]
pub trait LLMProvider: Send + Sync {
    // Existing methods (maintained for backward compatibility)
    async fn complete(&self, messages: Vec<Message>) -> Result<String>;
    fn supports_tools(&self) -> ToolSupport;
    fn model_name(&self) -> &str;
    fn model_capabilities(&self) -> Vec<ModelCapability>;
    fn has_thinking_capability(&self) -> bool;
    fn as_any(&self) -> Option<&dyn std::any::Any>;

    // New methods for enhanced functionality
    /// Get provider metadata (name, version, capabilities)
    fn provider_info(&self) -> ProviderInfo;
    
    /// Check provider health and availability
    async fn health_check(&self) -> Result<ProviderHealthStatus>;
    
    /// Get current rate limit status
    fn rate_limit_status(&self) -> RateLimitStatus;
    
    /// Validate API key and provider configuration
    async fn validate_configuration(&self) -> Result<ConfigValidationResult>;
    
    /// Get provider-specific metrics
    fn get_metrics(&self) -> ProviderMetrics;
    
    /// Check if provider supports streaming responses
    fn supports_streaming(&self) -> bool { false }
    
    /// Get estimated cost for a request (if supported)
    fn estimate_cost(&self, messages: &[Message]) -> Option<CostEstimate>;
}
```

### 5.2 Provider Information Structures

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProviderInfo {
    pub name: String,
    pub version: String,
    pub description: String,
    pub supported_models: Vec<String>,
    pub capabilities: Vec<ProviderCapability>,
    pub rate_limits: RateLimitInfo,
    pub cost_info: Option<CostInfo>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ProviderCapability {
    TextGeneration,
    ToolCalling,
    Streaming,
    Vision,
    CodeGeneration,
    Thinking,
    Custom(String),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitInfo {
    pub requests_per_minute: Option<u32>,
    pub requests_per_hour: Option<u32>,
    pub requests_per_day: Option<u32>,
    pub tokens_per_minute: Option<u32>,
    pub tokens_per_hour: Option<u32>,
    pub concurrent_requests: Option<u32>,
}

#[derive(Debug, Clone)]
pub struct RateLimitStatus {
    pub requests_remaining: Option<u32>,
    pub tokens_remaining: Option<u32>,
    pub reset_time: Option<SystemTime>,
    pub is_rate_limited: bool,
}

#[derive(Debug, Clone)]
pub struct ProviderHealthStatus {
    pub is_healthy: bool,
    pub response_time_ms: Option<u64>,
    pub last_error: Option<String>,
    pub last_check: SystemTime,
    pub api_status: ApiStatus,
}

#[derive(Debug, Clone)]
pub enum ApiStatus {
    Operational,
    Degraded,
    Outage,
    Maintenance,
    Unknown,
}

#[derive(Debug, Clone)]
pub struct ConfigValidationResult {
    pub is_valid: bool,
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
    pub api_key_valid: bool,
    pub base_url_reachable: bool,
}

#[derive(Debug, Clone, Default)]
pub struct ProviderMetrics {
    pub total_requests: u64,
    pub successful_requests: u64,
    pub failed_requests: u64,
    pub total_tokens_used: u64,
    pub average_response_time_ms: f64,
    pub last_request_time: Option<SystemTime>,
}

#[derive(Debug, Clone)]
pub struct CostEstimate {
    pub input_tokens: u32,
    pub estimated_output_tokens: u32,
    pub cost_usd: f64,
    pub model: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CostInfo {
    pub input_cost_per_token: Option<f64>,
    pub output_cost_per_token: Option<f64>,
    pub currency: String,
    pub billing_unit: String,
}
```

### 5.3 Enhanced Configuration System

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMConfig {
    // Existing fields (maintained for compatibility)
    pub provider: String,
    pub api_key: Option<String>,
    pub base_url: Option<String>,
    pub model: String,
    pub temperature: Option<f32>,
    pub max_tokens: Option<u32>,
    
    // New provider-specific configuration
    pub provider_config: ProviderSpecificConfig,
    pub rate_limits: Option<RateLimitConfig>,
    pub health_check: HealthCheckConfig,
    pub fallback_providers: Vec<String>,
    pub api_key_sources: Vec<ApiKeySource>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ProviderSpecificConfig {
    Anthropic(AnthropicProviderConfig),
    OpenRouter(OpenRouterProviderConfig),
    OpenAI(OpenAIProviderConfig),
    Custom(HashMap<String, serde_json::Value>),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnthropicProviderConfig {
    pub anthropic_version: Option<String>,
    pub anthropic_beta: Option<Vec<String>>,
    pub max_retries: Option<u32>,
    pub timeout_seconds: Option<u64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OpenRouterProviderConfig {
    pub site_url: Option<String>,
    pub app_name: Option<String>,
    pub model_preferences: Option<Vec<String>>,
    pub cost_threshold_usd: Option<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OpenAIProviderConfig {
    pub organization: Option<String>,
    pub project: Option<String>,
    pub max_retries: Option<u32>,
    pub timeout_seconds: Option<u64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitConfig {
    pub enable_rate_limiting: bool,
    pub requests_per_minute: Option<u32>,
    pub requests_per_hour: Option<u32>,
    pub tokens_per_minute: Option<u32>,
    pub burst_allowance: Option<u32>,
    pub backoff_strategy: BackoffStrategy,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BackoffStrategy {
    Linear { delay_ms: u64 },
    Exponential { base_delay_ms: u64, max_delay_ms: u64 },
    Fixed { delay_ms: u64 },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheckConfig {
    pub enable_health_checks: bool,
    pub check_interval_seconds: u64,
    pub timeout_seconds: u64,
    pub max_failures_before_disable: u32,
    pub auto_recovery: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ApiKeySource {
    Environment { var_name: String },
    File { path: String },
    Config { value: String },
    KeychainService { service: String, account: String },
}
```

### 5.4 Enhanced Error Handling

```rust
// Add to existing AutorunError enum in src/errors.rs
#[derive(Error, Debug)]
pub enum AutorunError {
    // ... existing variants ...
    
    #[error("Provider error: {0}")]
    Provider(#[from] ProviderError),
    
    #[error("Rate limit exceeded: {0}")]
    RateLimitExceeded(String),
    
    #[error("Provider health check failed: {0}")]
    ProviderHealthCheck(String),
    
    #[error("API key resolution failed: {0}")]
    ApiKeyResolution(String),
    
    #[error("Configuration validation failed: {0}")]
    ConfigValidation(String),
}

#[derive(Error, Debug)]
pub enum ProviderError {
    #[error("Authentication failed: {0}")]
    Authentication(String),
    
    #[error("Rate limit exceeded: requests={requests_remaining:?}, tokens={tokens_remaining:?}, reset_time={reset_time:?}")]
    RateLimitExceeded {
        requests_remaining: Option<u32>,
        tokens_remaining: Option<u32>,
        reset_time: Option<SystemTime>,
    },
    
    #[error("Invalid model: {model}")]
    InvalidModel { model: String },
    
    #[error("Provider unavailable: {provider}")]
    ProviderUnavailable { provider: String },
    
    #[error("Configuration error: {0}")]
    Configuration(String),
    
    #[error("Network error: {0}")]
    Network(String),
    
    #[error("Timeout: operation took longer than {timeout_ms}ms")]
    Timeout { timeout_ms: u64 },
    
    #[error("Cost limit exceeded: estimated=${estimated_cost}, limit=${cost_limit}")]
    CostLimitExceeded { estimated_cost: f64, cost_limit: f64 },
    
    #[error("Provider-specific error: {provider} - {message}")]
    ProviderSpecific { provider: String, message: String },
}
```

### 5.5 Rate Limiting Infrastructure

```rust
use std::time::{Duration, SystemTime};
use tokio::sync::RwLock;
use std::collections::HashMap;

#[derive(Debug)]
pub struct RateLimiter {
    limits: RateLimitConfig,
    state: RwLock<RateLimitState>,
}

#[derive(Debug, Default)]
struct RateLimitState {
    request_timestamps: Vec<SystemTime>,
    token_usage: Vec<(SystemTime, u32)>,
    last_reset: SystemTime,
}

impl RateLimiter {
    pub fn new(config: RateLimitConfig) -> Self {
        Self {
            limits: config,
            state: RwLock::new(RateLimitState::default()),
        }
    }
    
    pub async fn check_rate_limit(&self, tokens: u32) -> Result<(), ProviderError> {
        if !self.limits.enable_rate_limiting {
            return Ok(());
        }
        
        let mut state = self.state.write().await;
        let now = SystemTime::now();
        
        // Clean old entries
        self.cleanup_old_entries(&mut state, now);
        
        // Check request rate limit
        if let Some(rpm) = self.limits.requests_per_minute {
            if state.request_timestamps.len() >= rpm as usize {
                return Err(ProviderError::RateLimitExceeded {
                    requests_remaining: Some(0),
                    tokens_remaining: None,
                    reset_time: Some(now + Duration::from_secs(60)),
                });
            }
        }
        
        // Check token rate limit
        if let Some(tpm) = self.limits.tokens_per_minute {
            let current_tokens: u32 = state.token_usage.iter().map(|(_, t)| *t).sum();
            if current_tokens + tokens > tpm {
                return Err(ProviderError::RateLimitExceeded {
                    requests_remaining: None,
                    tokens_remaining: Some(tpm.saturating_sub(current_tokens)),
                    reset_time: Some(now + Duration::from_secs(60)),
                });
            }
        }
        
        // Record this request
        state.request_timestamps.push(now);
        state.token_usage.push((now, tokens));
        
        Ok(())
    }
    
    pub async fn get_status(&self) -> RateLimitStatus {
        let state = self.state.read().await;
        let now = SystemTime::now();
        
        let requests_remaining = self.limits.requests_per_minute.map(|rpm| {
            rpm.saturating_sub(state.request_timestamps.len() as u32)
        });
        
        let tokens_remaining = self.limits.tokens_per_minute.map(|tpm| {
            let used: u32 = state.token_usage.iter().map(|(_, t)| *t).sum();
            tpm.saturating_sub(used)
        });
        
        RateLimitStatus {
            requests_remaining,
            tokens_remaining,
            reset_time: Some(now + Duration::from_secs(60)),
            is_rate_limited: requests_remaining == Some(0) || tokens_remaining == Some(0),
        }
    }
    
    fn cleanup_old_entries(&self, state: &mut RateLimitState, now: SystemTime) {
        let one_minute_ago = now - Duration::from_secs(60);
        let one_hour_ago = now - Duration::from_secs(3600);
        
        state.request_timestamps.retain(|&timestamp| timestamp > one_minute_ago);
        state.token_usage.retain(|(timestamp, _)| *timestamp > one_minute_ago);
    }
}
```

### 5.6 API Key Resolution System

```rust
use std::path::Path;
use std::collections::HashMap;

#[derive(Debug)]
pub struct ApiKeyResolver {
    sources: Vec<ApiKeySource>,
    cache: RwLock<HashMap<String, String>>,
}

impl ApiKeyResolver {
    pub fn new(sources: Vec<ApiKeySource>) -> Self {
        Self {
            sources,
            cache: RwLock::new(HashMap::new()),
        }
    }
    
    pub async fn resolve_api_key(&self, provider: &str) -> Result<String, ProviderError> {
        // Check cache first
        {
            let cache = self.cache.read().await;
            if let Some(key) = cache.get(provider) {
                return Ok(key.clone());
            }
        }
        
        // Try each source in order
        for source in &self.sources {
            match self.try_source(source, provider).await {
                Ok(Some(key)) => {
                    // Cache the key
                    let mut cache = self.cache.write().await;
                    cache.insert(provider.to_string(), key.clone());
                    return Ok(key);
                }
                Ok(None) => continue,
                Err(e) => {
                    log::warn!("Failed to resolve API key from source {:?}: {}", source, e);
                    continue;
                }
            }
        }
        
        Err(ProviderError::Authentication(
            format!("No API key found for provider: {}", provider)
        ))
    }
    
    async fn try_source(&self, source: &ApiKeySource, provider: &str) -> Result<Option<String>, Box<dyn std::error::Error + Send + Sync>> {
        match source {
            ApiKeySource::Environment { var_name } => {
                let var_name = var_name.replace("{PROVIDER}", &provider.to_uppercase());
                Ok(std::env::var(&var_name).ok())
            }
            ApiKeySource::File { path } => {
                if Path::new(path).exists() {
                    let content = tokio::fs::read_to_string(path).await?;
                    // Parse file content as JSON and look for provider key
                    let keys: HashMap<String, String> = serde_json::from_str(&content)?;
                    Ok(keys.get(provider).cloned())
                } else {
                    Ok(None)
                }
            }
            ApiKeySource::Config { value } => {
                Ok(Some(value.clone()))
            }
            ApiKeySource::KeychainService { service, account } => {
                // Platform-specific keychain integration would go here
                // For now, return None to indicate this source is not available
                Ok(None)
            }
        }
    }
    
    pub async fn clear_cache(&self) {
        let mut cache = self.cache.write().await;
        cache.clear();
    }
}
```

### 5.7 Enhanced Provider Factory

```rust
impl ProviderFactory {
    pub async fn create_provider_with_config(
        &self,
        config: &LLMConfig,
    ) -> Result<Arc<dyn LLMProvider>> {
        // Validate configuration first
        self.validate_enhanced_config(config)?;
        
        // Resolve API key
        let api_key = self.resolve_api_key_for_provider(config).await?;
        
        // Create rate limiter if configured
        let rate_limiter = config.rate_limits.as_ref().map(|limits| {
            Arc::new(RateLimiter::new(limits.clone()))
        });
        
        // Create provider with enhanced configuration
        match config.provider.as_str() {
            "anthropic" => {
                let anthropic_config = match &config.provider_config {
                    ProviderSpecificConfig::Anthropic(ac) => ac.clone(),
                    _ => AnthropicProviderConfig::default(),
                };
                
                Ok(Arc::new(EnhancedAnthropicClient::new(
                    config.clone(),
                    anthropic_config,
                    api_key,
                    rate_limiter,
                )?))
            }
            "openrouter" => {
                let openrouter_config = match &config.provider_config {
                    ProviderSpecificConfig::OpenRouter(orc) => orc.clone(),
                    _ => OpenRouterProviderConfig::default(),
                };
                
                Ok(Arc::new(EnhancedOpenRouterClient::new(
                    config.clone(),
                    openrouter_config,
                    api_key,
                    rate_limiter,
                )?))
            }
            _ => Err(AutorunError::Config(format!(
                "Unsupported LLM provider: {}",
                config.provider
            )))
        }
    }
    
    async fn resolve_api_key_for_provider(&self, config: &LLMConfig) -> Result<String> {
        // If API key is directly configured, use it
        if let Some(key) = &config.api_key {
            if !key.is_empty() {
                return Ok(key.clone());
            }
        }
        
        // Otherwise, use the resolver
        let resolver = ApiKeyResolver::new(config.api_key_sources.clone());
        resolver.resolve_api_key(&config.provider)
            .await
            .map_err(|e| AutorunError::ApiKeyResolution(e.to_string()))
    }
    
    fn validate_enhanced_config(&self, config: &LLMConfig) -> Result<()> {
        // Validate provider
        self.validate_provider(&config.provider)?;
        
        // Validate model for provider
        if !self.is_model_supported(&config.provider, &config.model) {
            return Err(AutorunError::ConfigValidation(
                format!("Model {} is not supported by provider {}", config.model, config.provider)
            ));
        }
        
        // Validate rate limit configuration
        if let Some(rate_limits) = &config.rate_limits {
            if rate_limits.enable_rate_limiting {
                if rate_limits.requests_per_minute.is_none() && 
                   rate_limits.requests_per_hour.is_none() && 
                   rate_limits.tokens_per_minute.is_none() {
                    return Err(AutorunError::ConfigValidation(
                        "Rate limiting enabled but no limits specified".to_string()
                    ));
                }
            }
        }
        
        Ok(())
    }
    
    fn is_model_supported(&self, provider: &str, model: &str) -> bool {
        if let Some(provider_info) = registry::get_provider_info(provider) {
            provider_info.supported_models.contains(&model.to_string()) ||
            provider_info.supported_models.iter().any(|pattern| {
                // Support wildcard matching for model patterns
                self.matches_pattern(model, pattern)
            })
        } else {
            // Fallback to existing detection logic
            true
        }
    }
    
    fn matches_pattern(&self, model: &str, pattern: &str) -> bool {
        if pattern.contains('*') {
            // Simple wildcard matching
            let pattern_parts: Vec<&str> = pattern.split('*').collect();
            if pattern_parts.len() == 2 {
                model.starts_with(pattern_parts[0]) && model.ends_with(pattern_parts[1])
            } else {
                false
            }
        } else {
            model == pattern
        }
    }
}
```

### 5.8 Implementation Plan

**Week 1: Core Infrastructure**
1. **Day 1-2**: Extend `LLMProvider` trait with new methods and create supporting data structures
2. **Day 3-4**: Implement rate limiting infrastructure and API key resolution system
3. **Day 5-7**: Create enhanced configuration system with provider-specific configs

**Week 2: Provider Integration & Testing**
1. **Day 8-10**: Update existing provider implementations (Anthropic, OpenRouter) with new capabilities
2. **Day 11-12**: Implement health monitoring and metrics collection
3. **Day 13-14**: Comprehensive testing, documentation, and backward compatibility verification

### 5.9 Testing Strategy

**Unit Tests:**
- Test all new trait methods with mock implementations
- Validate rate limiting logic with various scenarios
- Test API key resolution from different sources
- Verify configuration validation logic

**Integration Tests:**
- Test multi-provider scenarios with fallbacks
- Validate rate limiting across concurrent requests
- Test health monitoring with simulated provider failures
- Verify backward compatibility with existing code

**Example Test Structure:**
```rust
#[tokio::test]
async fn test_enhanced_provider_creation() {
    let config = LLMConfig {
        provider: "anthropic".to_string(),
        api_key: Some("test-key".to_string()),
        // ... enhanced config fields
        rate_limits: Some(RateLimitConfig {
            enable_rate_limiting: true,
            requests_per_minute: Some(60),
            // ...
        }),
        // ...
    };
    
    let factory = ProviderFactory::new();
    let provider = factory.create_provider_with_config(&config).await.unwrap();
    
    // Test that provider implements enhanced capabilities
    assert!(provider.provider_info().name == "anthropic");
    assert!(provider.rate_limit_status().requests_remaining.is_some());
    
    // Test backward compatibility
    let messages = vec![Message::user("Hello")];
    let response = provider.complete(messages).await.unwrap();
    assert!(!response.is_empty());
}
```

This specification provides a comprehensive foundation for Phase 1 enhancement while maintaining full backward compatibility and following established patterns in the codebase. The implementation can proceed incrementally, testing each component thoroughly before moving to the next phase.