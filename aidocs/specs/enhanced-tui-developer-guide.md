# Enhanced TUI Developer Guide

## Overview

This guide provides comprehensive information for developers working on or extending the Enhanced TUI system in AutoRun-RS.

## Architecture Overview

### Module Structure

```
src/ui/enhanced/
├── mod.rs                    # Main enhanced UI module
├── interface.rs              # Enhanced TUI interface
├── state.rs                  # UI state management
├── content_blocks/           # Content block system
│   ├── mod.rs
│   ├── block.rs             # Content block definition
│   ├── renderer.rs          # Block rendering
│   ├── navigator.rs         # Block navigation
│   └── types.rs             # Block type definitions
├── input/                    # Input handling system
│   ├── mod.rs
│   ├── handler.rs           # Input event handling
│   ├── vim.rs               # Vim keybinding system
│   ├── completion.rs        # Auto-completion engine
│   └── history.rs           # Command history
├── components/               # Reusable UI components
│   ├── mod.rs
│   ├── combobox.rs          # Dropdown/combobox
│   ├── progress.rs          # Progress indicators
│   ├── dialog.rs            # Modal dialogs
│   └── status_bar.rs        # Enhanced status bar
├── config/                   # Configuration system
│   ├── mod.rs
│   ├── theme.rs             # Theme management
│   ├── keybindings.rs       # Keybinding configuration
│   └── hot_reload.rs        # Hot-reloading system
├── accessibility/            # Accessibility features
│   ├── mod.rs
│   ├── screen_reader.rs     # Screen reader support
│   └── keyboard.rs          # Keyboard navigation
└── utils/                    # Utility functions
    ├── mod.rs
    ├── layout.rs            # Layout utilities
    └── colors.rs            # Color utilities
```

### Core Components

#### Enhanced TUI Interface
```rust
pub struct EnhancedTuiInterface {
    state: TuiState,
    content_navigator: ContentNavigator,
    input_handler: InputHandler,
    component_registry: ComponentRegistry,
    config_manager: ConfigManager,
    accessibility: AccessibilityManager,
}
```

#### Content Block System
```rust
pub struct ContentBlock {
    pub id: Uuid,
    pub block_type: ContentBlockType,
    pub content: String,
    pub metadata: ContentMetadata,
    pub timestamp: DateTime<Utc>,
    pub render_state: RenderState,
}

pub trait ContentBlockRenderer {
    fn render(&self, block: &ContentBlock, area: Rect, frame: &mut Frame);
    fn get_height(&self, block: &ContentBlock, width: u16) -> u16;
    fn handle_interaction(&self, block: &ContentBlock, event: &Event) -> Option<Action>;
}
```

## Development Setup

### Prerequisites

```bash
# Install Rust (1.70+)
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# Install development dependencies
cargo install cargo-watch
cargo install cargo-tarpaulin  # For coverage
cargo install cargo-audit      # For security audits
```

### Building Enhanced TUI

```bash
# Build with enhanced TUI features
cargo build --features enhanced-tui

# Run with enhanced TUI
cargo run --features enhanced-tui

# Run tests
cargo test --features enhanced-tui

# Run with hot-reloading for development
cargo watch -x "run --features enhanced-tui"
```

### Feature Flags

```toml
[features]
default = ["enhanced-tui"]
enhanced-tui = ["ratatui/enhanced", "crossterm/events"]
vim-bindings = ["enhanced-tui"]
accessibility = ["enhanced-tui", "screen-reader"]
hot-reload = ["enhanced-tui", "notify"]
```

## Component Development

### Creating Custom Components

#### Component Trait
```rust
pub trait Component: Send + Sync {
    type State: ComponentState;
    
    fn render(&self, state: &Self::State, area: Rect, frame: &mut Frame);
    fn handle_event(&self, state: &mut Self::State, event: &Event) -> ComponentResult;
    fn get_constraints(&self, state: &Self::State) -> Vec<Constraint>;
    fn is_focusable(&self) -> bool { true }
}

pub trait ComponentState: Clone + Send + Sync {
    fn reset(&mut self);
    fn is_dirty(&self) -> bool;
    fn mark_clean(&mut self);
}
```

#### Example: Custom Button Component
```rust
pub struct Button {
    label: String,
    style: ButtonStyle,
    action: Option<Action>,
}

#[derive(Clone)]
pub struct ButtonState {
    is_focused: bool,
    is_pressed: bool,
    is_dirty: bool,
}

impl Component for Button {
    type State = ButtonState;
    
    fn render(&self, state: &Self::State, area: Rect, frame: &mut Frame) {
        let style = if state.is_focused {
            self.style.focused
        } else {
            self.style.normal
        };
        
        let button = Paragraph::new(self.label.as_str())
            .style(style)
            .block(Block::default().borders(Borders::ALL));
            
        frame.render_widget(button, area);
    }
    
    fn handle_event(&self, state: &mut Self::State, event: &Event) -> ComponentResult {
        match event {
            Event::Key(KeyEvent { code: KeyCode::Enter, .. }) if state.is_focused => {
                state.is_pressed = true;
                state.is_dirty = true;
                ComponentResult::Action(self.action.clone())
            }
            Event::Focus => {
                state.is_focused = true;
                state.is_dirty = true;
                ComponentResult::Handled
            }
            Event::Blur => {
                state.is_focused = false;
                state.is_dirty = true;
                ComponentResult::Handled
            }
            _ => ComponentResult::NotHandled,
        }
    }
}
```

### Content Block Renderers

#### Creating Custom Block Renderers
```rust
pub struct CodeBlockRenderer {
    syntax_highlighter: SyntaxHighlighter,
    theme: SyntaxTheme,
}

impl ContentBlockRenderer for CodeBlockRenderer {
    fn render(&self, block: &ContentBlock, area: Rect, frame: &mut Frame) {
        if let ContentBlockType::CodeBlock { language } = &block.block_type {
            let highlighted = self.syntax_highlighter.highlight(
                &block.content,
                language.as_deref(),
                &self.theme,
            );
            
            let code_widget = Paragraph::new(highlighted)
                .block(Block::default()
                    .borders(Borders::ALL)
                    .title(format!("Code: {}", language.as_deref().unwrap_or("text")))
                    .border_style(Style::default().fg(Color::Magenta)))
                .wrap(Wrap { trim: false });
                
            frame.render_widget(code_widget, area);
        }
    }
    
    fn get_height(&self, block: &ContentBlock, width: u16) -> u16 {
        let lines = block.content.lines().count() as u16;
        let wrapped_lines = block.content.lines()
            .map(|line| (line.len() as u16 / width.saturating_sub(4)) + 1)
            .sum::<u16>();
        wrapped_lines + 2 // +2 for borders
    }
}
```

## Input System Development

### Custom Input Handlers

#### Input Handler Trait
```rust
pub trait InputHandler: Send + Sync {
    fn can_handle(&self, event: &Event, context: &InputContext) -> bool;
    fn handle(&self, event: &Event, context: &mut InputContext) -> InputResult;
    fn get_priority(&self) -> u8;
}

pub struct InputContext {
    pub mode: InputMode,
    pub vim_state: Option<VimState>,
    pub completion_state: Option<CompletionState>,
    pub focus_state: FocusState,
}
```

#### Example: Custom Command Handler
```rust
pub struct CustomCommandHandler;

impl InputHandler for CustomCommandHandler {
    fn can_handle(&self, event: &Event, context: &InputContext) -> bool {
        matches!(event, Event::Key(KeyEvent { 
            code: KeyCode::Char(':'), 
            modifiers: KeyModifiers::NONE 
        })) && matches!(context.mode, InputMode::Normal)
    }
    
    fn handle(&self, event: &Event, context: &mut InputContext) -> InputResult {
        context.mode = InputMode::Command;
        InputResult::ModeChange(InputMode::Command)
    }
    
    fn get_priority(&self) -> u8 { 100 }
}
```

### Vim Keybinding Extension

#### Custom Vim Commands
```rust
pub struct CustomVimCommand {
    name: String,
    keys: Vec<KeyEvent>,
    action: VimAction,
}

impl VimCommand for CustomVimCommand {
    fn matches(&self, keys: &[KeyEvent]) -> bool {
        keys.ends_with(&self.keys)
    }
    
    fn execute(&self, context: &mut VimContext) -> VimResult {
        match &self.action {
            VimAction::Custom(action) => {
                // Execute custom action
                VimResult::Action(action.clone())
            }
            _ => VimResult::Continue,
        }
    }
}
```

## Configuration System

### Theme Development

#### Theme Structure
```rust
#[derive(Serialize, Deserialize, Clone)]
pub struct Theme {
    pub name: String,
    pub colors: ColorScheme,
    pub styles: StyleConfig,
    pub borders: BorderConfig,
    pub syntax: SyntaxTheme,
}

#[derive(Serialize, Deserialize, Clone)]
pub struct ColorScheme {
    pub background: Color,
    pub foreground: Color,
    pub primary: Color,
    pub secondary: Color,
    pub accent: Color,
    pub error: Color,
    pub warning: Color,
    pub success: Color,
}
```

#### Creating Custom Themes
```toml
# themes/custom.toml
name = "Custom Theme"

[colors]
background = "#1e1e1e"
foreground = "#d4d4d4"
primary = "#007acc"
secondary = "#6a9955"
accent = "#f44747"
error = "#f44747"
warning = "#ffcc02"
success = "#4ec9b0"

[styles]
bold = true
italic = false
underline = false

[borders]
style = "rounded"
thickness = 1

[syntax]
keyword = "#569cd6"
string = "#ce9178"
comment = "#6a9955"
function = "#dcdcaa"
variable = "#9cdcfe"
```

### Hot-reloading Implementation

#### Config Watcher
```rust
pub struct ConfigWatcher {
    watcher: RecommendedWatcher,
    receiver: mpsc::Receiver<ConfigEvent>,
}

impl ConfigWatcher {
    pub async fn new(config_dir: PathBuf) -> Result<Self> {
        let (tx, rx) = mpsc::channel(100);
        
        let mut watcher = notify::recommended_watcher(move |res| {
            if let Ok(event) = res {
                let _ = tx.try_send(ConfigEvent::from(event));
            }
        })?;
        
        watcher.watch(&config_dir, RecursiveMode::Recursive)?;
        
        Ok(Self {
            watcher,
            receiver: rx,
        })
    }
    
    pub async fn next_event(&mut self) -> Option<ConfigEvent> {
        self.receiver.recv().await
    }
}
```

## Testing

### Unit Testing

#### Component Testing
```rust
#[cfg(test)]
mod tests {
    use super::*;
    use ratatui::backend::TestBackend;
    use ratatui::Terminal;
    
    #[tokio::test]
    async fn test_button_component() {
        let mut terminal = Terminal::new(TestBackend::new(20, 5)).unwrap();
        let button = Button::new("Test".to_string());
        let mut state = ButtonState::default();
        
        terminal.draw(|frame| {
            button.render(&state, frame.area(), frame);
        }).unwrap();
        
        // Test event handling
        let result = button.handle_event(&mut state, &Event::Focus);
        assert!(matches!(result, ComponentResult::Handled));
        assert!(state.is_focused);
    }
}
```

#### Integration Testing
```rust
#[tokio::test]
async fn test_enhanced_tui_integration() {
    let config = TuiConfig::default();
    let mut tui = EnhancedTuiInterface::new(config).await.unwrap();
    
    // Test content block addition
    let block = ContentBlock::new(
        ContentBlockType::UserMessage,
        "Test message".to_string(),
    );
    
    tui.add_content_block(block).await.unwrap();
    assert_eq!(tui.get_content_blocks().len(), 1);
    
    // Test navigation
    let result = tui.handle_event(Event::Key(KeyEvent::new(
        KeyCode::Char('j'),
        KeyModifiers::NONE,
    ))).await.unwrap();
    
    assert!(matches!(result, Some(Action::Navigate(_))));
}
```

### Performance Testing

#### Benchmark Setup
```rust
use criterion::{black_box, criterion_group, criterion_main, Criterion};

fn benchmark_content_rendering(c: &mut Criterion) {
    let mut group = c.benchmark_group("content_rendering");
    
    group.bench_function("render_1000_blocks", |b| {
        let blocks = create_test_blocks(1000);
        let renderer = ContentBlockRenderer::new();
        
        b.iter(|| {
            for block in &blocks {
                renderer.render(black_box(block), Rect::default(), &mut frame);
            }
        });
    });
    
    group.finish();
}

criterion_group!(benches, benchmark_content_rendering);
criterion_main!(benches);
```

## Accessibility Development

### Screen Reader Support

#### ARIA Implementation
```rust
pub struct AriaLabel {
    text: String,
    role: AriaRole,
    properties: HashMap<String, String>,
}

impl AriaLabel {
    pub fn new(text: String, role: AriaRole) -> Self {
        Self {
            text,
            role,
            properties: HashMap::new(),
        }
    }
    
    pub fn with_property(mut self, key: String, value: String) -> Self {
        self.properties.insert(key, value);
        self
    }
}

pub trait AccessibleComponent {
    fn get_aria_label(&self) -> Option<AriaLabel>;
    fn get_description(&self) -> Option<String>;
    fn is_focusable(&self) -> bool;
    fn get_role(&self) -> AriaRole;
}
```

### Keyboard Navigation

#### Focus Management
```rust
pub struct FocusManager {
    focus_chain: Vec<ComponentId>,
    current_focus: Option<ComponentId>,
    focus_history: VecDeque<ComponentId>,
}

impl FocusManager {
    pub fn next_focus(&mut self) -> Option<ComponentId> {
        if let Some(current) = self.current_focus {
            if let Some(pos) = self.focus_chain.iter().position(|&id| id == current) {
                let next_pos = (pos + 1) % self.focus_chain.len();
                self.current_focus = self.focus_chain.get(next_pos).copied();
            }
        }
        self.current_focus
    }
    
    pub fn previous_focus(&mut self) -> Option<ComponentId> {
        if let Some(current) = self.current_focus {
            if let Some(pos) = self.focus_chain.iter().position(|&id| id == current) {
                let prev_pos = if pos == 0 {
                    self.focus_chain.len() - 1
                } else {
                    pos - 1
                };
                self.current_focus = self.focus_chain.get(prev_pos).copied();
            }
        }
        self.current_focus
    }
}
```

## Debugging and Profiling

### Debug Tools

#### Debug Overlay
```rust
pub struct DebugOverlay {
    enabled: bool,
    info: DebugInfo,
}

#[derive(Default)]
pub struct DebugInfo {
    pub frame_time: Duration,
    pub render_time: Duration,
    pub event_count: usize,
    pub memory_usage: usize,
    pub active_components: usize,
}

impl DebugOverlay {
    pub fn render(&self, frame: &mut Frame, area: Rect) {
        if !self.enabled {
            return;
        }
        
        let debug_text = format!(
            "Frame: {:.2}ms | Render: {:.2}ms | Events: {} | Memory: {}KB | Components: {}",
            self.info.frame_time.as_secs_f64() * 1000.0,
            self.info.render_time.as_secs_f64() * 1000.0,
            self.info.event_count,
            self.info.memory_usage / 1024,
            self.info.active_components,
        );
        
        let debug_widget = Paragraph::new(debug_text)
            .style(Style::default().bg(Color::Black).fg(Color::Yellow));
            
        frame.render_widget(debug_widget, area);
    }
}
```

### Performance Monitoring

#### Metrics Collection
```rust
pub struct PerformanceMetrics {
    frame_times: VecDeque<Duration>,
    render_times: VecDeque<Duration>,
    event_processing_times: VecDeque<Duration>,
}

impl PerformanceMetrics {
    pub fn record_frame_time(&mut self, duration: Duration) {
        self.frame_times.push_back(duration);
        if self.frame_times.len() > 100 {
            self.frame_times.pop_front();
        }
    }
    
    pub fn get_average_frame_time(&self) -> Duration {
        if self.frame_times.is_empty() {
            return Duration::ZERO;
        }
        
        let total: Duration = self.frame_times.iter().sum();
        total / self.frame_times.len() as u32
    }
    
    pub fn get_fps(&self) -> f64 {
        let avg_frame_time = self.get_average_frame_time();
        if avg_frame_time.is_zero() {
            return 0.0;
        }
        1.0 / avg_frame_time.as_secs_f64()
    }
}
```

## Contributing

### Code Style

Follow the Rust guidelines in `.claude/code-guidelines/rust.md`:

1. **Use proper error handling** with `Result` types
2. **Avoid unwrap()** in production code
3. **Use iterators** instead of manual loops
4. **Implement proper traits** for custom types
5. **Use async/await** for I/O operations

### Pull Request Process

1. **Create feature branch** from main
2. **Implement changes** with tests
3. **Update documentation** as needed
4. **Run full test suite** including accessibility tests
5. **Submit PR** with detailed description

### Testing Requirements

- **Unit tests** for all new components
- **Integration tests** for component interactions
- **Performance tests** for rendering-critical code
- **Accessibility tests** for UI components
- **Documentation tests** for code examples
