# Specification: Phase 3 - Action Commands (/)

## 1. Goal/Objective

Implement the complete / command system for action and tool execution. Create dynamic MCP tool discovery, memory management interface, and custom command loading system with variable substitution. Enable users to execute tools, manage memory, and run custom prompt templates with full parameter support.

## 2. Input

* **Phase 1 & 2 Output**: Core command infrastructure and context system
* **MCP Framework**: Existing `McpClientManager` and MCP integration
* **Memory System**: Memory storage from Phase 2
* **Tool Registry**: Existing tool execution framework
* **File System**: Access to `.autorun/commands/` and `.claude/commands/` directories

## 3. Output

* **Action Command Engine**: `src/commands/action_engine.rs`
  - `/memory` - Interactive memory editing with CRUD operations
  - Dynamic MCP command discovery and execution
  - Custom command loading with template processing

* **MCP Action Integration**: `src/commands/mcp_actions/`
  - `mcp_discovery.rs` - Real-time MCP server and tool discovery
  - `mcp_tool_executor.rs` - Dynamic tool execution with parameter mapping
  - `mcp_prompt_handler.rs` - MCP prompt execution with context injection
  - `mcp_resource_manager.rs` - MCP resource access and management

* **Memory Management System**: `src/commands/memory/`
  - `memory_editor.rs` - Interactive memory CRUD interface
  - `memory_search.rs` - Full-text search with fuzzy matching
  - `memory_tags.rs` - Tag-based organization and filtering
  - `memory_export.rs` - Export/import functionality

* **Custom Command System**: `src/commands/custom/`
  - `command_loader.rs` - Discover and load commands from filesystem
  - `template_parser.rs` - Parse markdown templates with variable extraction
  - `variable_substitution.rs` - Variable replacement with interactive prompts
  - `command_executor.rs` - Execute custom commands with context injection

* **Template Engine**: `src/templates/`
  - `template_engine.rs` - Core template processing with variable support
  - `variable_resolver.rs` - Resolve system and user variables
  - `template_cache.rs` - Compiled template caching for performance
  - `template_validator.rs` - Template syntax validation and error reporting

* **Action Completion Provider**: `src/ui/completion/action_completion.rs`
  - Dynamic completion for `/memory` operations
  - Real-time MCP tool/prompt/resource completion
  - Custom command discovery and parameter completion

## 4. Constraints

<!-- * **Real-Time Discovery**: MCP tools must be discovered and available within 1 second -->
<!-- * **Template Performance**: Template compilation and execution must complete within 500ms -->
* **Memory Safety**: All memory operations must be atomic with proper error handling
* **File Watching**: Custom command directories must be monitored for changes
* **Variable Security**: User input variables must be sanitized to prevent injection attacks
* **Error Handling**: Network failures in MCP operations must provide clear error messages
* **Async Execution**: All action commands must support cancellation and progress reporting
* **Resource Management**: MCP resource access must respect server limitations and quotas
* **Template Syntax**: Support standard variable syntax ($VAR, ${VAR}, environment variables)
* **Command Isolation**: Custom commands must execute in isolated contexts
* **Caching Strategy**: Frequently used templates and MCP metadata must be cached effectively