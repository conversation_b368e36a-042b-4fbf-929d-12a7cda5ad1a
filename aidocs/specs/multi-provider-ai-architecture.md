# Specification: Multi-Provider AI/LLM Architecture for AutoRun-RS

## 1. Goal/Objective

Design and implement a comprehensive multi-provider AI/LLM architecture for AutoRun-RS that supports multiple AI providers with tool/function calling capabilities. The system should be extensible, robust, and follow Rust best practices while providing a unified interface for AI interactions across different providers.

## 2. Input

### 2.1 Reference Analysis

Based on analysis of the Cline implementation in `aidocs/reference/cline/src/`, key architectural patterns identified:

- **Provider Factory Pattern**: Central `buildApiHandler()` function with switch-based provider selection
- **Unified Interface**: Common `ApiHandler` interface with `createMessage()` and `getModel()` methods
- **Stream-based Architecture**: Async generators for streaming responses
- **Configuration Management**: Provider-specific options merged into unified configuration
- **Error Handling**: Provider-specific error handling with retry mechanisms

### 2.2 Current AutoRun-RS State

- Existing `LLMProvider` trait in `src/llm/mod.rs`
- Factory pattern in `src/llm/factory.rs` with `ProviderBuilder` trait
- Configuration system in `src/config/mod.rs` with `LLMConfig`
- Model registry in `src/config/registry.rs`
- Tool calling support detection via `ToolSupport` enum

## 3. Output

### 3.1 Core Architecture Files

- **Enhanced Provider Factory**: `src/llm/factory.rs` (extend existing)
- **Provider Implementations**: `src/llm/providers/` (new directory)
  - `src/llm/providers/mod.rs`
  - `src/llm/providers/openrouter.rs`
  - `src/llm/providers/requesty.rs`
  - `src/llm/providers/anthropic.rs`
  - `src/llm/providers/gemini.rs`
  - `src/llm/providers/openai.rs`
  - `src/llm/providers/ollama.rs`
  - `src/llm/providers/base.rs`
- **Configuration Extensions**: `src/config/providers.rs` (new)
- **Error Handling**: `src/llm/errors.rs` (new)

### 3.2 Configuration Files

- **Provider Configurations**: `config/providers/` (new directory)
- **Model Registry Updates**: Enhanced `src/config/registry.rs`

### 3.3 Documentation

- **Implementation Guide**: This specification document
- **Provider Integration Guide**: `aidocs/provider-integration-guide.md`

## 4. Constraints

- **Tool Calling Requirement**: Only integrate models that support tool/function calling
- **Rust Best Practices**: Follow guidelines in `.claude/code-guidelines/rust.md`
- **Async Architecture**: All providers must support async/await patterns
- **Error Propagation**: Use `Result<T, AutorunError>` pattern consistently
- **No Breaking Changes**: Maintain compatibility with existing `LLMProvider` trait
- **Memory Efficiency**: Use `Arc<dyn Trait>` for shared ownership patterns
- **Configuration Compatibility**: Extend existing TOML configuration format

## 5. Architecture Overview

### 5.1 Design Principles

1. **Unified Interface**: Single `LLMProvider` trait for all providers
2. **Plugin Architecture**: Easy addition of new providers via `ProviderBuilder` trait
3. **Capability Detection**: Runtime validation of tool calling support
4. **Graceful Degradation**: Clear error messages for unsupported features
5. **Configuration Flexibility**: Provider-specific settings with sensible defaults
6. **Async-First**: Non-blocking operations throughout the stack

### 5.2 Core Components

```rust
// Enhanced LLMProvider trait (extend existing)
#[async_trait::async_trait]
pub trait LLMProvider: Send + Sync {
    async fn complete(&self, messages: Vec<Message>) -> Result<String>;
    async fn complete_with_tools(&self, messages: Vec<Message>, tools: Vec<Tool>) -> Result<ToolCallResponse>;
    async fn stream_complete(&self, messages: Vec<Message>) -> Result<Pin<Box<dyn Stream<Item = Result<StreamChunk>>>>>;

    fn supports_tools(&self) -> ToolSupport;
    fn supports_streaming(&self) -> bool;
    fn model_name(&self) -> &str;
    fn provider_name(&self) -> &str;
    fn model_capabilities(&self) -> Vec<ModelCapability>;

    // Provider-specific configuration validation
    fn validate_config(&self, config: &LLMConfig) -> Result<()>;

    // Health check for provider availability
    async fn health_check(&self) -> Result<ProviderHealth>;
}

// Provider factory with enhanced capabilities
pub struct ProviderFactory {
    builders: HashMap<String, Box<dyn ProviderBuilder>>,
}

impl ProviderFactory {
    pub fn new() -> Self {
        let mut factory = Self {
            builders: HashMap::new(),
        };

        // Register all providers
        factory.register("openrouter", Box::new(OpenRouterBuilder));
        factory.register("requesty", Box::new(RequestyBuilder));
        factory.register("anthropic", Box::new(AnthropicBuilder));
        factory.register("gemini", Box::new(GeminiBuilder));
        factory.register("openai", Box::new(OpenAIBuilder));
        factory.register("ollama", Box::new(OllamaBuilder));

        factory
    }

    pub async fn create_provider(&self, config: &LLMConfig) -> Result<Arc<dyn LLMProvider>> {
        let builder = self.builders.get(&config.provider)
            .ok_or_else(|| AutorunError::Config(format!("Unsupported provider: {}", config.provider)))?;

        // Validate configuration before building
        builder.validate_config(config)?;

        // Build provider instance
        let provider = builder.build(config).await?;

        // Validate tool calling support if required
        if config.require_tools.unwrap_or(true) {
            match provider.supports_tools() {
                ToolSupport::None => {
                    return Err(AutorunError::Config(format!(
                        "Provider {} model {} does not support tool calling",
                        config.provider, config.model
                    )));
                }
                ToolSupport::Limited => {
                    log::warn!("Provider {} has limited tool support", config.provider);
                }
                ToolSupport::Full => {}
            }
        }

        Ok(provider)
    }
}
```

### 5.3 Provider Implementation Pattern

Each provider follows a consistent implementation pattern:

```rust
// Base provider trait for common functionality
#[async_trait::async_trait]
pub trait BaseProvider: LLMProvider {
    async fn make_request(&self, request: ProviderRequest) -> Result<ProviderResponse>;
    fn get_client(&self) -> &dyn HttpClient;
    fn get_config(&self) -> &ProviderConfig;

    // Default implementations for common operations
    async fn health_check(&self) -> Result<ProviderHealth> {
        // Default health check implementation
    }

    fn validate_config(&self, config: &LLMConfig) -> Result<()> {
        // Common validation logic
    }
}

// Example provider implementation
pub struct OpenRouterProvider {
    client: reqwest::Client,
    config: OpenRouterConfig,
    model_info: ModelInfo,
}

#[async_trait::async_trait]
impl LLMProvider for OpenRouterProvider {
    async fn complete(&self, messages: Vec<Message>) -> Result<String> {
        let request = self.build_completion_request(messages, None)?;
        let response = self.make_request(request).await?;
        self.parse_completion_response(response)
    }

    async fn complete_with_tools(&self, messages: Vec<Message>, tools: Vec<Tool>) -> Result<ToolCallResponse> {
        if self.supports_tools() == ToolSupport::None {
            return Err(AutorunError::LlmApi("Tool calling not supported".to_string()));
        }

        let request = self.build_completion_request(messages, Some(tools))?;
        let response = self.make_request(request).await?;
        self.parse_tool_response(response)
    }

    fn supports_tools(&self) -> ToolSupport {
        detect_tool_support(&self.config.provider, &self.config.model)
    }

    fn provider_name(&self) -> &str {
        "openrouter"
    }

    fn model_name(&self) -> &str {
        &self.config.model
    }
}
```

## 6. Configuration Management

### 6.1 Enhanced Configuration Structure

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMConfig {
    pub provider: String,
    pub model: String,
    pub api_key: Option<String>,
    pub base_url: Option<String>,
    pub temperature: Option<f32>,
    pub max_tokens: Option<u32>,
    pub require_tools: Option<bool>,

    // Provider-specific configurations
    pub openrouter: Option<OpenRouterConfig>,
    pub requesty: Option<RequestyConfig>,
    pub anthropic: Option<AnthropicConfig>,
    pub gemini: Option<GeminiConfig>,
    pub openai: Option<OpenAIConfig>,
    pub ollama: Option<OllamaConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OpenRouterConfig {
    pub site_url: Option<String>,
    pub app_name: Option<String>,
    pub transforms: Option<Vec<String>>,
    pub models: Option<Vec<String>>,
    pub route: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnthropicConfig {
    pub version: Option<String>,
    pub beta_features: Option<Vec<String>>,
    pub max_retries: Option<u32>,
}
```

### 6.2 Automatic API Key Detection

The system implements a hierarchical API key detection strategy to provide seamless configuration:

```rust
#[derive(Debug, Clone)]
pub struct ApiKeyResolver {
    provider: String,
}

impl ApiKeyResolver {
    pub fn new(provider: &str) -> Self {
        Self {
            provider: provider.to_string(),
        }
    }

    /// Resolve API key with fallback hierarchy
    pub fn resolve_api_key(&self, config_key: Option<&str>) -> Option<String> {
        // 1. Explicit configuration value (highest priority)
        if let Some(key) = config_key {
            if !key.is_empty() && !key.starts_with("${") {
                return Some(key.to_string());
            }
        }

        // 2. Provider-specific environment variable
        let provider_env_key = format!("{}_API_KEY", self.provider.to_uppercase());
        if let Ok(key) = std::env::var(&provider_env_key) {
            return Some(key);
        }

        // 3. Generic LLM API key environment variable
        if let Ok(key) = std::env::var("LLM_API_KEY") {
            return Some(key);
        }

        // 4. Provider-specific alternative names
        match self.provider.as_str() {
            "openrouter" => {
                std::env::var("OPENROUTER_KEY").ok()
                    .or_else(|| std::env::var("OR_API_KEY").ok())
            }
            "anthropic" => {
                std::env::var("ANTHROPIC_KEY").ok()
                    .or_else(|| std::env::var("CLAUDE_API_KEY").ok())
            }
            "openai" => {
                std::env::var("OPENAI_KEY").ok()
                    .or_else(|| std::env::var("GPT_API_KEY").ok())
            }
            "gemini" => {
                std::env::var("GOOGLE_API_KEY").ok()
                    .or_else(|| std::env::var("GEMINI_KEY").ok())
            }
            _ => None,
        }
    }

    /// Validate that an API key is available
    pub fn validate_api_key_available(&self, config_key: Option<&str>) -> Result<String> {
        self.resolve_api_key(config_key)
            .ok_or_else(|| AutorunError::Config(format!(
                "No API key found for provider '{}'. Set {} or LLM_API_KEY environment variable",
                self.provider,
                format!("{}_API_KEY", self.provider.to_uppercase())
            )))
    }
}
```

### 6.3 Default Provider and Model Configuration

AutoRun-RS uses intelligent defaults to minimize configuration overhead:

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMConfig {
    #[serde(default = "default_provider")]
    pub provider: String,

    #[serde(default = "default_model")]
    pub model: String,

    pub api_key: Option<String>,
    pub base_url: Option<String>,

    #[serde(default = "default_temperature")]
    pub temperature: Option<f32>,

    #[serde(default = "default_max_tokens")]
    pub max_tokens: Option<u32>,

    #[serde(default = "default_require_tools")]
    pub require_tools: Option<bool>,

    // Task-specific model configurations
    pub task_models: Option<TaskModelConfig>,

    // Provider-specific configurations
    pub openrouter: Option<OpenRouterConfig>,
    pub anthropic: Option<AnthropicConfig>,
    pub gemini: Option<GeminiConfig>,
    pub openai: Option<OpenAIConfig>,
    pub ollama: Option<OllamaConfig>,
    pub requesty: Option<RequestyConfig>,
}

fn default_provider() -> String {
    "openrouter".to_string()
}

fn default_model() -> String {
    // Use latest Claude Sonnet 4 model as default
    "anthropic/claude-3-5-sonnet-20241022".to_string()
}

fn default_temperature() -> Option<f32> {
    Some(0.7)
}

fn default_max_tokens() -> Option<u32> {
    Some(4096)
}

fn default_require_tools() -> Option<bool> {
    Some(true)
}

impl Default for LLMConfig {
    fn default() -> Self {
        Self {
            provider: default_provider(),
            model: default_model(),
            api_key: None,
            base_url: None,
            temperature: default_temperature(),
            max_tokens: default_max_tokens(),
            require_tools: default_require_tools(),
            task_models: None,
            openrouter: None,
            anthropic: None,
            gemini: None,
            openai: None,
            ollama: None,
            requesty: None,
        }
    }
}
```

### 6.4 Configuration File Example

```toml
[llm]
# Optional: provider defaults to "openrouter"
provider = "openrouter"
# Optional: model defaults to "anthropic/claude-3-5-sonnet-20241022"
model = "anthropic/claude-3-5-sonnet-20241022"
# Optional: API key auto-detected from environment variables
# api_key = "${OPENROUTER_API_KEY}"
temperature = 0.7
max_tokens = 4096
require_tools = true

[llm.task_models]
# High-capability models for complex tasks
primary = "anthropic/claude-3-5-sonnet-20241022"
reasoning = "anthropic/claude-3-5-sonnet-20241022"
coding = "anthropic/claude-3-5-sonnet-20241022"

# Lightweight models for simple tasks
lightweight = "anthropic/claude-3-haiku-20240307"
formatting = "anthropic/claude-3-haiku-20240307"
diff_analysis = "anthropic/claude-3-haiku-20240307"

[llm.openrouter]
site_url = "https://autorun-rs.dev"
app_name = "AutoRun-RS"
transforms = ["middle-out"]

[llm.anthropic]
version = "2023-06-01"
beta_features = ["tools-2024-04-04"]
max_retries = 3
```

## 7. Task-Specific Model Selection

### 7.1 Multi-Model Architecture

AutoRun-RS implements intelligent model selection based on task complexity and requirements:

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskModelConfig {
    // High-capability models for complex tasks
    pub primary: Option<String>,
    pub reasoning: Option<String>,
    pub coding: Option<String>,
    pub analysis: Option<String>,

    // Lightweight models for simple tasks
    pub lightweight: Option<String>,
    pub formatting: Option<String>,
    pub diff_analysis: Option<String>,
    pub commit_messages: Option<String>,
    pub text_processing: Option<String>,

    // Fallback configurations
    pub fallback_primary: Option<String>,
    pub fallback_lightweight: Option<String>,
}

impl Default for TaskModelConfig {
    fn default() -> Self {
        Self {
            // High-capability defaults
            primary: Some("anthropic/claude-3-5-sonnet-20241022".to_string()),
            reasoning: Some("anthropic/claude-3-5-sonnet-20241022".to_string()),
            coding: Some("anthropic/claude-3-5-sonnet-20241022".to_string()),
            analysis: Some("anthropic/claude-3-5-sonnet-20241022".to_string()),

            // Lightweight defaults
            lightweight: Some("anthropic/claude-3-haiku-20240307".to_string()),
            formatting: Some("anthropic/claude-3-haiku-20240307".to_string()),
            diff_analysis: Some("anthropic/claude-3-haiku-20240307".to_string()),
            commit_messages: Some("anthropic/claude-3-haiku-20240307".to_string()),
            text_processing: Some("anthropic/claude-3-haiku-20240307".to_string()),

            // Fallbacks
            fallback_primary: Some("openai/gpt-4".to_string()),
            fallback_lightweight: Some("openai/gpt-3.5-turbo".to_string()),
        }
    }
}

#[derive(Debug, Clone, PartialEq)]
pub enum TaskType {
    // High-capability tasks
    PrimaryInteraction,
    ComplexCoding,
    LogicalReasoning,
    CodeAnalysis,
    ProblemSolving,

    // Lightweight tasks
    DiffAnalysis,
    CommitMessageGeneration,
    CodeFormatting,
    TextProcessing,
    SimpleQueries,

    // Custom task types
    Custom(String),
}

impl TaskType {
    /// Determine if this task requires high-capability model
    pub fn requires_high_capability(&self) -> bool {
        matches!(self,
            TaskType::PrimaryInteraction |
            TaskType::ComplexCoding |
            TaskType::LogicalReasoning |
            TaskType::CodeAnalysis |
            TaskType::ProblemSolving
        )
    }

    /// Get the appropriate model configuration key for this task
    pub fn get_model_key(&self) -> &str {
        match self {
            TaskType::PrimaryInteraction => "primary",
            TaskType::ComplexCoding => "coding",
            TaskType::LogicalReasoning => "reasoning",
            TaskType::CodeAnalysis => "analysis",
            TaskType::ProblemSolving => "primary",
            TaskType::DiffAnalysis => "diff_analysis",
            TaskType::CommitMessageGeneration => "commit_messages",
            TaskType::CodeFormatting => "formatting",
            TaskType::TextProcessing => "text_processing",
            TaskType::SimpleQueries => "lightweight",
            TaskType::Custom(key) => key,
        }
    }
}
```

### 7.2 Task Classification and Model Selection

```rust
pub struct TaskClassifier;

impl TaskClassifier {
    /// Classify a task based on context and content
    pub fn classify_task(context: &TaskContext) -> TaskType {
        // Analyze task characteristics
        if context.involves_code_generation() || context.involves_complex_logic() {
            if context.is_primary_user_interaction() {
                TaskType::PrimaryInteraction
            } else if context.involves_complex_algorithms() {
                TaskType::ComplexCoding
            } else {
                TaskType::CodeAnalysis
            }
        } else if context.is_diff_related() {
            TaskType::DiffAnalysis
        } else if context.is_commit_message_generation() {
            TaskType::CommitMessageGeneration
        } else if context.is_formatting_task() {
            TaskType::CodeFormatting
        } else if context.is_simple_text_processing() {
            TaskType::TextProcessing
        } else {
            // Default to primary for unknown tasks
            TaskType::PrimaryInteraction
        }
    }
}

#[derive(Debug)]
pub struct TaskContext {
    pub user_input: String,
    pub conversation_history: Vec<Message>,
    pub file_context: Option<Vec<String>>,
    pub tool_calls_required: bool,
    pub estimated_complexity: ComplexityLevel,
}

#[derive(Debug, PartialEq)]
pub enum ComplexityLevel {
    Low,
    Medium,
    High,
}

impl TaskContext {
    pub fn involves_code_generation(&self) -> bool {
        let keywords = ["implement", "create", "write code", "generate", "build"];
        keywords.iter().any(|&keyword|
            self.user_input.to_lowercase().contains(keyword)
        )
    }

    pub fn involves_complex_logic(&self) -> bool {
        self.estimated_complexity == ComplexityLevel::High ||
        self.tool_calls_required ||
        self.user_input.len() > 500
    }

    pub fn is_primary_user_interaction(&self) -> bool {
        self.conversation_history.len() <= 2 ||
        self.user_input.starts_with("@") // Direct user command
    }

    pub fn is_diff_related(&self) -> bool {
        let keywords = ["diff", "compare", "changes", "delta"];
        keywords.iter().any(|&keyword|
            self.user_input.to_lowercase().contains(keyword)
        )
    }

    pub fn is_commit_message_generation(&self) -> bool {
        self.user_input.to_lowercase().contains("commit message") ||
        self.user_input.to_lowercase().contains("git commit")
    }

    pub fn is_formatting_task(&self) -> bool {
        let keywords = ["format", "style", "lint", "prettier"];
        keywords.iter().any(|&keyword|
            self.user_input.to_lowercase().contains(keyword)
        )
    }

    pub fn is_simple_text_processing(&self) -> bool {
        self.estimated_complexity == ComplexityLevel::Low &&
        !self.tool_calls_required &&
        self.user_input.len() < 200
    }
}
```

### 7.3 Enhanced Provider Factory with Task-Aware Model Selection

```rust
pub struct TaskAwareProviderFactory {
    base_factory: ProviderFactory,
    task_config: TaskModelConfig,
}

impl TaskAwareProviderFactory {
    pub fn new(config: &LLMConfig) -> Self {
        Self {
            base_factory: ProviderFactory::new(),
            task_config: config.task_models.clone().unwrap_or_default(),
        }
    }

    /// Create provider for specific task type
    pub async fn create_provider_for_task(
        &self,
        base_config: &LLMConfig,
        task_type: TaskType
    ) -> Result<Arc<dyn LLMProvider>> {
        let model = self.select_model_for_task(&task_type, base_config)?;

        let task_config = LLMConfig {
            model,
            ..base_config.clone()
        };

        self.base_factory.create_provider(&task_config).await
    }

    /// Select appropriate model for task type
    fn select_model_for_task(&self, task_type: &TaskType, base_config: &LLMConfig) -> Result<String> {
        let model_key = task_type.get_model_key();

        // Try task-specific model first
        if let Some(model) = self.get_task_model(model_key) {
            return Ok(model);
        }

        // Fall back to capability-based selection
        if task_type.requires_high_capability() {
            if let Some(model) = self.task_config.primary.as_ref() {
                return Ok(model.clone());
            }
            if let Some(model) = self.task_config.fallback_primary.as_ref() {
                return Ok(model.clone());
            }
        } else {
            if let Some(model) = self.task_config.lightweight.as_ref() {
                return Ok(model.clone());
            }
            if let Some(model) = self.task_config.fallback_lightweight.as_ref() {
                return Ok(model.clone());
            }
        }

        // Final fallback to base config model
        Ok(base_config.model.clone())
    }

    fn get_task_model(&self, key: &str) -> Option<String> {
        match key {
            "primary" => self.task_config.primary.clone(),
            "reasoning" => self.task_config.reasoning.clone(),
            "coding" => self.task_config.coding.clone(),
            "analysis" => self.task_config.analysis.clone(),
            "lightweight" => self.task_config.lightweight.clone(),
            "formatting" => self.task_config.formatting.clone(),
            "diff_analysis" => self.task_config.diff_analysis.clone(),
            "commit_messages" => self.task_config.commit_messages.clone(),
            "text_processing" => self.task_config.text_processing.clone(),
            _ => None,
        }
    }
}
```

## 8. Error Handling Strategy

### 7.1 Provider-Specific Error Types

```rust
#[derive(Error, Debug)]
pub enum ProviderError {
    #[error("Authentication failed: {0}")]
    Authentication(String),

    #[error("Rate limit exceeded: {retry_after:?}")]
    RateLimit { retry_after: Option<Duration> },

    #[error("Model not found: {model}")]
    ModelNotFound { model: String },

    #[error("Tool calling not supported by model: {model}")]
    ToolsNotSupported { model: String },

    #[error("Invalid request: {0}")]
    InvalidRequest(String),

    #[error("Provider unavailable: {0}")]
    Unavailable(String),

    #[error("Network error: {0}")]
    Network(#[from] reqwest::Error),

    #[error("Parsing error: {0}")]
    Parsing(String),
}

// Integration with existing error system
impl From<ProviderError> for AutorunError {
    fn from(err: ProviderError) -> Self {
        AutorunError::LlmApi(err.to_string())
    }
}
```

### 7.2 Retry and Circuit Breaker Pattern

```rust
pub struct RetryConfig {
    pub max_retries: u32,
    pub base_delay: Duration,
    pub max_delay: Duration,
    pub backoff_multiplier: f64,
}

#[async_trait::async_trait]
pub trait RetryableProvider {
    async fn execute_with_retry<T, F, Fut>(&self, operation: F) -> Result<T>
    where
        F: Fn() -> Fut + Send + Sync,
        Fut: Future<Output = Result<T>> + Send,
        T: Send;
}
```

## 8. Tool Calling Implementation

### 8.1 Tool Support Detection

```rust
pub fn detect_tool_support(provider: &str, model: &str) -> ToolSupport {
    match provider {
        "openrouter" => detect_openrouter_tool_support(model),
        "anthropic" => ToolSupport::Full,
        "openai" => detect_openai_tool_support(model),
        "gemini" => detect_gemini_tool_support(model),
        "ollama" => detect_ollama_tool_support(model),
        "requesty" => ToolSupport::Full, // Assume full support
        _ => ToolSupport::None,
    }
}

fn detect_openrouter_tool_support(model: &str) -> ToolSupport {
    // Tool calling support matrix for OpenRouter models
    match model {
        m if m.contains("claude") => ToolSupport::Full,
        m if m.contains("gpt-4") => ToolSupport::Full,
        m if m.contains("gpt-3.5") => ToolSupport::Limited,
        m if m.contains("gemini") => ToolSupport::Full,
        _ => ToolSupport::None,
    }
}
```

### 8.2 Tool Call Response Handling

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolCallResponse {
    pub content: String,
    pub tool_calls: Vec<ToolCall>,
    pub finish_reason: Option<String>,
    pub usage: Option<TokenUsage>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolCall {
    pub id: String,
    pub name: String,
    pub arguments: serde_json::Value,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenUsage {
    pub prompt_tokens: u32,
    pub completion_tokens: u32,
    pub total_tokens: u32,
}
```

## 9. Implementation Roadmap

### Phase 1: Foundation (Week 1-2)

- [ ] Enhance existing `LLMProvider` trait with new methods
- [ ] Implement base provider infrastructure
- [ ] Create provider-specific error types
- [ ] Set up configuration extensions

### Phase 2: Core Providers (Week 3-4)

- [ ] Implement OpenRouter provider
- [ ] Implement Anthropic provider
- [ ] Implement OpenAI provider
- [ ] Add comprehensive tool calling support

### Phase 3: Extended Providers (Week 5-6)

- [ ] Implement Gemini provider
- [ ] Implement Ollama provider
- [ ] Implement Requesty provider
- [ ] Add streaming support for all providers

### Phase 4: Advanced Features (Week 7-8)

- [ ] Implement retry mechanisms and circuit breakers
- [ ] Add health checking and monitoring
- [ ] Performance optimization and caching
- [ ] Comprehensive testing and documentation

### Phase 5: Integration and Polish (Week 9-10)

- [ ] Integration with existing AutoRun-RS components
- [ ] Configuration migration tools
- [ ] Performance benchmarking
- [ ] Production readiness assessment

## 10. Future Extensibility

### 10.1 Plugin Architecture

The provider system is designed for easy extension:

```rust
// Adding a new provider requires implementing ProviderBuilder
pub struct NewProviderBuilder;

#[async_trait::async_trait]
impl ProviderBuilder for NewProviderBuilder {
    async fn build(&self, config: &LLMConfig) -> Result<Arc<dyn LLMProvider>> {
        // Implementation
    }

    fn validate_config(&self, config: &LLMConfig) -> Result<()> {
        // Validation logic
    }

    fn provider_name(&self) -> &'static str {
        "new_provider"
    }
}

// Register in factory
factory.register("new_provider", Box::new(NewProviderBuilder));
```

### 10.2 Configuration Schema Evolution

- Backward-compatible configuration changes
- Migration tools for configuration updates
- Versioned configuration schemas

### 10.3 Monitoring and Observability

- Provider performance metrics
- Error rate tracking
- Usage analytics
- Health monitoring dashboards

## 11. Provider-Specific Implementation Details

### 11.1 OpenRouter Provider

OpenRouter acts as a meta-provider, supporting multiple underlying models. Key implementation considerations:

```rust
pub struct OpenRouterProvider {
    client: reqwest::Client,
    config: OpenRouterConfig,
    base_url: String,
    api_key: String,
    model: String,
}

impl OpenRouterProvider {
    pub fn new(config: &LLMConfig) -> Result<Self> {
        let openrouter_config = config.openrouter.as_ref()
            .ok_or_else(|| AutorunError::Config("OpenRouter config missing".to_string()))?;

        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(60))
            .default_headers({
                let mut headers = HeaderMap::new();
                headers.insert("Authorization", format!("Bearer {}", config.api_key.as_ref().unwrap()).parse()?);
                headers.insert("HTTP-Referer", openrouter_config.site_url.as_ref().unwrap_or(&"https://autorun-rs.dev".to_string()).parse()?);
                headers.insert("X-Title", openrouter_config.app_name.as_ref().unwrap_or(&"AutoRun-RS".to_string()).parse()?);
                headers
            })
            .build()?;

        Ok(Self {
            client,
            config: openrouter_config.clone(),
            base_url: config.base_url.as_ref().unwrap_or(&"https://openrouter.ai/api/v1".to_string()).clone(),
            api_key: config.api_key.as_ref().unwrap().clone(),
            model: config.model.clone(),
        })
    }

    async fn make_completion_request(&self, messages: Vec<Message>, tools: Option<Vec<Tool>>) -> Result<serde_json::Value> {
        let mut request_body = json!({
            "model": self.model,
            "messages": self.convert_messages(messages)?,
            "temperature": 0.7,
            "max_tokens": 4096,
        });

        if let Some(tools) = tools {
            request_body["tools"] = json!(self.convert_tools(tools)?);
            request_body["tool_choice"] = json!("auto");
        }

        let response = self.client
            .post(&format!("{}/chat/completions", self.base_url))
            .json(&request_body)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(AutorunError::LlmApi(format!("OpenRouter API error: {}", error_text)));
        }

        let response_json: serde_json::Value = response.json().await?;
        Ok(response_json)
    }
}
```

### 11.2 Anthropic Provider

Direct integration with Anthropic's Claude models:

```rust
pub struct AnthropicProvider {
    client: anthropic::Client,
    model: String,
    config: AnthropicConfig,
}

impl AnthropicProvider {
    pub fn new(config: &LLMConfig) -> Result<Self> {
        let anthropic_config = config.anthropic.as_ref()
            .unwrap_or(&AnthropicConfig::default());

        let client = anthropic::Client::builder()
            .api_key(config.api_key.as_ref().unwrap())
            .base_url(config.base_url.as_ref().unwrap_or(&"https://api.anthropic.com".to_string()))
            .build()?;

        Ok(Self {
            client,
            model: config.model.clone(),
            config: anthropic_config.clone(),
        })
    }

    async fn create_message_request(&self, messages: Vec<Message>, tools: Option<Vec<Tool>>) -> Result<anthropic::MessageRequest> {
        let mut request = anthropic::MessageRequest::builder()
            .model(&self.model)
            .max_tokens(4096)
            .messages(self.convert_messages(messages)?)
            .build()?;

        if let Some(tools) = tools {
            request = request.tools(self.convert_tools_to_anthropic(tools)?);
        }

        Ok(request)
    }
}
```

### 11.3 Gemini Provider

Google's Gemini models with specific API patterns:

```rust
pub struct GeminiProvider {
    client: reqwest::Client,
    api_key: String,
    model: String,
    base_url: String,
}

impl GeminiProvider {
    pub fn new(config: &LLMConfig) -> Result<Self> {
        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(60))
            .build()?;

        Ok(Self {
            client,
            api_key: config.api_key.as_ref().unwrap().clone(),
            model: config.model.clone(),
            base_url: config.base_url.as_ref()
                .unwrap_or(&"https://generativelanguage.googleapis.com/v1beta".to_string())
                .clone(),
        })
    }

    async fn generate_content(&self, messages: Vec<Message>, tools: Option<Vec<Tool>>) -> Result<serde_json::Value> {
        let mut request_body = json!({
            "contents": self.convert_messages_to_gemini(messages)?,
            "generationConfig": {
                "temperature": 0.7,
                "maxOutputTokens": 4096,
            }
        });

        if let Some(tools) = tools {
            request_body["tools"] = json!([{
                "functionDeclarations": self.convert_tools_to_gemini(tools)?
            }]);
        }

        let url = format!("{}/models/{}:generateContent?key={}",
                         self.base_url, self.model, self.api_key);

        let response = self.client
            .post(&url)
            .json(&request_body)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(AutorunError::LlmApi(format!("Gemini API error: {}", error_text)));
        }

        let response_json: serde_json::Value = response.json().await?;
        Ok(response_json)
    }
}
```

### 11.4 Ollama Provider

Local model hosting with Ollama:

```rust
pub struct OllamaProvider {
    client: reqwest::Client,
    base_url: String,
    model: String,
}

impl OllamaProvider {
    pub fn new(config: &LLMConfig) -> Result<Self> {
        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(120)) // Longer timeout for local models
            .build()?;

        Ok(Self {
            client,
            base_url: config.base_url.as_ref()
                .unwrap_or(&"http://localhost:11434".to_string())
                .clone(),
            model: config.model.clone(),
        })
    }

    async fn health_check(&self) -> Result<ProviderHealth> {
        let response = self.client
            .get(&format!("{}/api/tags", self.base_url))
            .send()
            .await?;

        if response.status().is_success() {
            let models: serde_json::Value = response.json().await?;
            let available_models: Vec<String> = models["models"]
                .as_array()
                .unwrap_or(&vec![])
                .iter()
                .filter_map(|m| m["name"].as_str().map(|s| s.to_string()))
                .collect();

            if available_models.contains(&self.model) {
                Ok(ProviderHealth::Healthy)
            } else {
                Ok(ProviderHealth::Degraded(format!("Model {} not available", self.model)))
            }
        } else {
            Ok(ProviderHealth::Unhealthy("Ollama server not responding".to_string()))
        }
    }
}
```

## 12. Comprehensive Streaming Implementation

### 12.1 Unified Streaming Interface

All providers must implement a consistent streaming interface that supports both regular completion and tool-calling scenarios:

```rust
use futures::stream::Stream;
use tokio::sync::mpsc;
use std::pin::Pin;

#[derive(Debug, Clone)]
pub enum StreamChunk {
    Content {
        delta: String,
        role: Option<MessageRole>,
    },
    ToolCallStart {
        id: String,
        function_name: String,
    },
    ToolCallDelta {
        id: String,
        arguments_delta: String,
    },
    ToolCallEnd {
        id: String,
        complete_arguments: String,
    },
    Metadata {
        usage: Option<TokenUsage>,
        model: Option<String>,
        finish_reason: Option<String>,
    },
    Error {
        error: ProviderError,
        recoverable: bool,
    },
}

#[derive(Debug, Clone)]
pub struct TokenUsage {
    pub prompt_tokens: u32,
    pub completion_tokens: u32,
    pub total_tokens: u32,
}

#[async_trait]
pub trait StreamingProvider: LLMProvider {
    /// Stream completion with real-time chunks
    async fn stream_completion(
        &self,
        messages: Vec<Message>,
        tools: Option<Vec<Tool>>,
    ) -> Result<Pin<Box<dyn Stream<Item = Result<StreamChunk>> + Send>>>;

    /// Stream completion with tool calling support
    async fn stream_completion_with_tools(
        &self,
        messages: Vec<Message>,
        tools: Vec<Tool>,
    ) -> Result<Pin<Box<dyn Stream<Item = Result<StreamChunk>> + Send>>>;

    /// Check if provider supports streaming
    fn supports_streaming(&self) -> bool;

    /// Get streaming configuration
    fn streaming_config(&self) -> StreamingConfig;

    /// Handle streaming errors with recovery
    async fn handle_streaming_error(&self, error: &ProviderError) -> StreamingRecoveryAction;
}

#[derive(Debug, Clone)]
pub struct StreamingConfig {
    pub buffer_size: usize,
    pub timeout: Duration,
    pub max_retries: u32,
    pub backpressure_threshold: usize,
    pub enable_partial_tool_calls: bool,
    pub chunk_aggregation_timeout: Duration,
}

impl Default for StreamingConfig {
    fn default() -> Self {
        Self {
            buffer_size: 1024,
            timeout: Duration::from_secs(30),
            max_retries: 3,
            backpressure_threshold: 10000,
            enable_partial_tool_calls: true,
            chunk_aggregation_timeout: Duration::from_millis(100),
        }
    }
}

#[derive(Debug, Clone)]
pub enum StreamingRecoveryAction {
    Retry,
    RetryWithBackoff(Duration),
    Abort,
    SwitchToNonStreaming,
}
```

### 12.2 Provider-Specific Streaming Implementations

#### 12.2.1 OpenRouter Streaming (OpenAI-Compatible SSE)

```rust
impl StreamingProvider for OpenRouterProvider {
    async fn stream_completion(
        &self,
        messages: Vec<Message>,
        tools: Option<Vec<Tool>>,
    ) -> Result<Pin<Box<dyn Stream<Item = Result<StreamChunk>> + Send>>> {
        // Reference: aidocs/reference/cline/src/api/providers/openrouter.ts
        let request = self.build_streaming_request(messages, tools)?;
        let response = self.client.post(&self.api_url)
            .json(&request)
            .header("Accept", "text/event-stream")
            .header("Cache-Control", "no-cache")
            .send()
            .await?;

        if !response.status().is_success() {
            return Err(self.handle_error_response(response).await?);
        }

        let stream = self.parse_openrouter_sse_stream(response).await?;
        Ok(Box::pin(stream))
    }

    fn supports_streaming(&self) -> bool {
        true
    }

    fn streaming_config(&self) -> StreamingConfig {
        StreamingConfig {
            buffer_size: 2048,
            timeout: Duration::from_secs(60),
            enable_partial_tool_calls: true,
            ..Default::default()
        }
    }

    async fn handle_streaming_error(&self, error: &ProviderError) -> StreamingRecoveryAction {
        match error {
            ProviderError::RateLimit { retry_after } => {
                StreamingRecoveryAction::RetryWithBackoff(
                    retry_after.unwrap_or(Duration::from_secs(1))
                )
            }
            ProviderError::Timeout => StreamingRecoveryAction::Retry,
            ProviderError::ConnectionFailed(_) => StreamingRecoveryAction::Retry,
            _ => StreamingRecoveryAction::Abort,
        }
    }
}

impl OpenRouterProvider {
    async fn parse_openrouter_sse_stream(
        &self,
        response: reqwest::Response,
    ) -> Result<impl Stream<Item = Result<StreamChunk>>> {
        let (tx, rx) = mpsc::unbounded_channel();
        let mut stream = response.bytes_stream();
        let mut buffer = String::new();
        let mut tool_call_accumulator = ToolCallAccumulator::new();

        tokio::spawn(async move {
            while let Some(chunk_result) = stream.next().await {
                match chunk_result {
                    Ok(bytes) => {
                        if let Ok(text) = String::from_utf8(bytes.to_vec()) {
                            buffer.push_str(&text);

                            // Process complete SSE events
                            while let Some(event_end) = buffer.find("\n\n") {
                                let event_text = buffer[..event_end].to_string();
                                buffer = buffer[event_end + 2..].to_string();

                                match Self::parse_openrouter_event(&event_text, &mut tool_call_accumulator) {
                                    Ok(chunks) => {
                                        for chunk in chunks {
                                            if tx.send(Ok(chunk)).is_err() {
                                                return;
                                            }
                                        }
                                    }
                                    Err(e) => {
                                        let _ = tx.send(Err(e));
                                    }
                                }
                            }
                        }
                    }
                    Err(e) => {
                        let _ = tx.send(Err(AutorunError::LlmApi(e.to_string())));
                        return;
                    }
                }
            }
        });

        Ok(tokio_stream::wrappers::UnboundedReceiverStream::new(rx))
    }
}
```

### 12.3 Tool Call Accumulation in Streaming Context

```rust
#[derive(Debug, Default)]
pub struct ToolCallAccumulator {
    active_calls: HashMap<String, PartialToolCall>,
}

#[derive(Debug, Clone)]
struct PartialToolCall {
    id: String,
    function_name: String,
    arguments: String,
    is_complete: bool,
}

impl ToolCallAccumulator {
    pub fn new() -> Self {
        Self {
            active_calls: HashMap::new(),
        }
    }

    pub fn process_tool_call_delta(
        &mut self,
        id: &str,
        function_name: Option<&str>,
        arguments_delta: Option<&str>,
    ) -> Vec<StreamChunk> {
        let mut chunks = Vec::new();

        // Get or create partial tool call
        let partial_call = self.active_calls.entry(id.to_string()).or_insert_with(|| {
            PartialToolCall {
                id: id.to_string(),
                function_name: String::new(),
                arguments: String::new(),
                is_complete: false,
            }
        });

        // Handle function name
        if let Some(name) = function_name {
            if partial_call.function_name.is_empty() {
                partial_call.function_name = name.to_string();
                chunks.push(StreamChunk::ToolCallStart {
                    id: id.to_string(),
                    function_name: name.to_string(),
                });
            }
        }

        // Handle arguments delta
        if let Some(args_delta) = arguments_delta {
            partial_call.arguments.push_str(args_delta);
            chunks.push(StreamChunk::ToolCallDelta {
                id: id.to_string(),
                arguments_delta: args_delta.to_string(),
            });
        }

        chunks
    }

    pub fn complete_tool_call(&mut self, id: &str) -> Option<StreamChunk> {
        if let Some(mut partial_call) = self.active_calls.remove(id) {
            partial_call.is_complete = true;
            Some(StreamChunk::ToolCallEnd {
                id: partial_call.id,
                complete_arguments: partial_call.arguments,
            })
        } else {
            None
        }
    }
}

impl OpenRouterProvider {
    fn parse_openrouter_event(
        event_text: &str,
        tool_accumulator: &mut ToolCallAccumulator,
    ) -> Result<Vec<StreamChunk>> {
        let mut chunks = Vec::new();

        // Parse OpenAI-compatible SSE format
        for line in event_text.lines() {
            if let Some(data) = line.strip_prefix("data: ") {
                if data == "[DONE]" {
                    chunks.push(StreamChunk::Metadata {
                        usage: None,
                        model: None,
                        finish_reason: Some("stop".to_string()),
                    });
                    continue;
                }

                let json: serde_json::Value = serde_json::from_str(data)?;
                chunks.extend(Self::parse_openrouter_delta(&json, tool_accumulator)?);
            }
        }

        Ok(chunks)
    }

    fn parse_openrouter_delta(
        json: &serde_json::Value,
        tool_accumulator: &mut ToolCallAccumulator,
    ) -> Result<Vec<StreamChunk>> {
        let mut chunks = Vec::new();

        let choices = json["choices"].as_array()
            .ok_or_else(|| AutorunError::LlmApi("No choices in response".to_string()))?;

        if let Some(choice) = choices.first() {
            let delta = &choice["delta"];

            // Handle content delta
            if let Some(content) = delta["content"].as_str() {
                chunks.push(StreamChunk::Content {
                    delta: content.to_string(),
                    role: delta["role"].as_str().map(|r| MessageRole::from_str(r)),
                });
            }

            // Handle tool call delta
            if let Some(tool_calls) = delta["tool_calls"].as_array() {
                for tool_call in tool_calls {
                    let id = tool_call["id"].as_str().unwrap_or_default();
                    let function = tool_call["function"].as_object();

                    let function_name = function
                        .and_then(|f| f["name"].as_str());
                    let arguments_delta = function
                        .and_then(|f| f["arguments"].as_str());

                    chunks.extend(tool_accumulator.process_tool_call_delta(
                        id,
                        function_name,
                        arguments_delta,
                    ));
                }
            }

            // Handle finish reason and complete tool calls
            if let Some(finish_reason) = choice["finish_reason"].as_str() {
                if finish_reason == "tool_calls" {
                    // Complete all active tool calls
                    for id in tool_accumulator.active_calls.keys().cloned().collect::<Vec<_>>() {
                        if let Some(chunk) = tool_accumulator.complete_tool_call(&id) {
                            chunks.push(chunk);
                        }
                    }
                }

                chunks.push(StreamChunk::Metadata {
                    usage: json["usage"].as_object().map(|u| TokenUsage {
                        prompt_tokens: u["prompt_tokens"].as_u64().unwrap_or(0) as u32,
                        completion_tokens: u["completion_tokens"].as_u64().unwrap_or(0) as u32,
                        total_tokens: u["total_tokens"].as_u64().unwrap_or(0) as u32,
                    }),
                    model: json["model"].as_str().map(|s| s.to_string()),
                    finish_reason: Some(finish_reason.to_string()),
                });
            }
        }

        Ok(chunks)
    }
}
```

### 12.4 Anthropic Streaming Implementation

```rust
impl StreamingProvider for AnthropicProvider {
    async fn stream_completion(
        &self,
        messages: Vec<Message>,
        tools: Option<Vec<Tool>>,
    ) -> Result<Pin<Box<dyn Stream<Item = Result<StreamChunk>> + Send>>> {
        // Reference: aidocs/reference/cline/src/api/providers/anthropic.ts
        let request = self.build_anthropic_streaming_request(messages, tools)?;
        let response = self.client.post(&self.api_url)
            .json(&request)
            .header("Accept", "text/event-stream")
            .header("anthropic-version", &self.version)
            .send()
            .await?;

        let stream = self.parse_anthropic_sse_stream(response).await?;
        Ok(Box::pin(stream))
    }

    fn supports_streaming(&self) -> bool {
        true
    }

    fn streaming_config(&self) -> StreamingConfig {
        StreamingConfig {
            buffer_size: 1024,
            timeout: Duration::from_secs(45),
            enable_partial_tool_calls: true,
            ..Default::default()
        }
    }
}

impl AnthropicProvider {
    async fn parse_anthropic_sse_stream(
        &self,
        response: reqwest::Response,
    ) -> Result<impl Stream<Item = Result<StreamChunk>>> {
        let (tx, rx) = mpsc::unbounded_channel();
        let mut stream = response.bytes_stream();
        let mut buffer = String::new();

        tokio::spawn(async move {
            while let Some(chunk_result) = stream.next().await {
                match chunk_result {
                    Ok(bytes) => {
                        if let Ok(text) = String::from_utf8(bytes.to_vec()) {
                            buffer.push_str(&text);

                            // Process Anthropic SSE events
                            while let Some(event_end) = buffer.find("\n\n") {
                                let event_text = buffer[..event_end].to_string();
                                buffer = buffer[event_end + 2..].to_string();

                                match Self::parse_anthropic_event(&event_text) {
                                    Ok(Some(chunk)) => {
                                        if tx.send(Ok(chunk)).is_err() {
                                            return;
                                        }
                                    }
                                    Ok(None) => {} // Skip empty events
                                    Err(e) => {
                                        let _ = tx.send(Err(e));
                                    }
                                }
                            }
                        }
                    }
                    Err(e) => {
                        let _ = tx.send(Err(AutorunError::LlmApi(e.to_string())));
                        return;
                    }
                }
            }
        });

        Ok(tokio_stream::wrappers::UnboundedReceiverStream::new(rx))
    }

    fn parse_anthropic_event(event_text: &str) -> Result<Option<StreamChunk>> {
        let mut event_type = None;
        let mut data = None;

        for line in event_text.lines() {
            if let Some(event) = line.strip_prefix("event: ") {
                event_type = Some(event);
            } else if let Some(event_data) = line.strip_prefix("data: ") {
                data = Some(event_data);
            }
        }

        match (event_type, data) {
            (Some("content_block_delta"), Some(data)) => {
                let json: serde_json::Value = serde_json::from_str(data)?;
                if let Some(text) = json["delta"]["text"].as_str() {
                    Ok(Some(StreamChunk::Content {
                        delta: text.to_string(),
                        role: Some(MessageRole::Assistant),
                    }))
                } else {
                    Ok(None)
                }
            }
            (Some("message_stop"), _) => {
                Ok(Some(StreamChunk::Metadata {
                    usage: None,
                    model: None,
                    finish_reason: Some("stop".to_string()),
                }))
            }
            _ => Ok(None),
        }
    }
}
```

## 13. Testing Strategy

### 12.5 Streaming Error Handling and Recovery

```rust
pub struct StreamingErrorHandler {
    max_retries: u32,
    base_delay: Duration,
    max_delay: Duration,
}

impl StreamingErrorHandler {
    pub fn new() -> Self {
        Self {
            max_retries: 3,
            base_delay: Duration::from_millis(100),
            max_delay: Duration::from_secs(10),
        }
    }

    pub async fn handle_streaming_error<F, Fut>(
        &self,
        operation: F,
        error_context: &str,
    ) -> Result<Pin<Box<dyn Stream<Item = Result<StreamChunk>> + Send>>>
    where
        F: Fn() -> Fut + Send + 'static,
        Fut: Future<Output = Result<Pin<Box<dyn Stream<Item = Result<StreamChunk>> + Send>>>> + Send,
    {
        let mut delay = self.base_delay;
        let mut last_error = None;

        for attempt in 0..=self.max_retries {
            match operation().await {
                Ok(stream) => {
                    return Ok(self.wrap_stream_with_error_recovery(stream));
                }
                Err(e) => {
                    last_error = Some(e);

                    if attempt < self.max_retries {
                        tracing::warn!(
                            "Streaming attempt {} failed for {}: {:?}. Retrying in {:?}",
                            attempt + 1,
                            error_context,
                            last_error,
                            delay
                        );

                        tokio::time::sleep(delay).await;
                        delay = std::cmp::min(delay * 2, self.max_delay);
                    }
                }
            }
        }

        Err(last_error.unwrap_or_else(|| {
            AutorunError::LlmApi("Max streaming retries exceeded".to_string())
        }))
    }

    fn wrap_stream_with_error_recovery(
        &self,
        stream: Pin<Box<dyn Stream<Item = Result<StreamChunk>> + Send>>,
    ) -> Pin<Box<dyn Stream<Item = Result<StreamChunk>> + Send>> {
        let error_recovery_stream = stream.map(|result| {
            match result {
                Ok(chunk) => Ok(chunk),
                Err(e) => {
                    // Log error but continue stream
                    tracing::error!("Stream chunk error: {:?}", e);
                    Err(e)
                }
            }
        });

        Box::pin(error_recovery_stream)
    }
}
```

### 12.6 Backpressure and Flow Control

```rust
pub struct StreamingFlowController {
    buffer_size: usize,
    backpressure_threshold: usize,
    drop_policy: DropPolicy,
}

#[derive(Debug, Clone)]
pub enum DropPolicy {
    DropOldest,
    DropNewest,
    Block,
}

impl StreamingFlowController {
    pub fn new(config: &StreamingConfig) -> Self {
        Self {
            buffer_size: config.buffer_size,
            backpressure_threshold: config.backpressure_threshold,
            drop_policy: DropPolicy::DropOldest,
        }
    }

    pub fn create_controlled_stream<S>(
        &self,
        source_stream: S,
    ) -> Pin<Box<dyn Stream<Item = Result<StreamChunk>> + Send>>
    where
        S: Stream<Item = Result<StreamChunk>> + Send + 'static,
    {
        let (tx, rx) = mpsc::channel(self.buffer_size);
        let backpressure_threshold = self.backpressure_threshold;
        let drop_policy = self.drop_policy.clone();

        tokio::spawn(async move {
            let mut source_stream = Box::pin(source_stream);
            let mut buffer = VecDeque::new();

            loop {
                tokio::select! {
                    // Receive from source stream
                    chunk_result = source_stream.next() => {
                        match chunk_result {
                            Some(Ok(chunk)) => {
                                // Apply backpressure management
                                if buffer.len() >= backpressure_threshold {
                                    match drop_policy {
                                        DropPolicy::DropOldest => {
                                            buffer.pop_front();
                                        }
                                        DropPolicy::DropNewest => {
                                            continue; // Skip this chunk
                                        }
                                        DropPolicy::Block => {
                                            // Wait for buffer space
                                            while buffer.len() >= backpressure_threshold {
                                                tokio::time::sleep(Duration::from_millis(10)).await;
                                            }
                                        }
                                    }
                                }

                                buffer.push_back(chunk);
                            }
                            Some(Err(e)) => {
                                let _ = tx.send(Err(e)).await;
                                break;
                            }
                            None => break,
                        }
                    }

                    // Send buffered chunks
                    _ = async {
                        if let Some(chunk) = buffer.pop_front() {
                            if tx.send(Ok(chunk)).await.is_err() {
                                return;
                            }
                        }
                    }, if !buffer.is_empty() => {}
                }
            }
        });

        Box::pin(tokio_stream::wrappers::ReceiverStream::new(rx))
    }
}
```

### 12.7 Stream Cancellation and Cleanup

```rust
pub struct CancellableStream {
    stream: Pin<Box<dyn Stream<Item = Result<StreamChunk>> + Send>>,
    cancellation_token: tokio_util::sync::CancellationToken,
}

impl CancellableStream {
    pub fn new(
        stream: Pin<Box<dyn Stream<Item = Result<StreamChunk>> + Send>>,
    ) -> (Self, tokio_util::sync::CancellationToken) {
        let cancellation_token = tokio_util::sync::CancellationToken::new();
        let stream_with_cancellation = Self {
            stream,
            cancellation_token: cancellation_token.clone(),
        };

        (stream_with_cancellation, cancellation_token)
    }
}

impl Stream for CancellableStream {
    type Item = Result<StreamChunk>;

    fn poll_next(
        mut self: Pin<&mut Self>,
        cx: &mut Context<'_>,
    ) -> Poll<Option<Self::Item>> {
        // Check for cancellation
        if self.cancellation_token.is_cancelled() {
            return Poll::Ready(None);
        }

        // Poll the underlying stream
        self.stream.as_mut().poll_next(cx)
    }
}

// Usage example
impl StreamingProvider for OpenRouterProvider {
    async fn stream_completion_with_cancellation(
        &self,
        messages: Vec<Message>,
        tools: Option<Vec<Tool>>,
    ) -> Result<(Pin<Box<dyn Stream<Item = Result<StreamChunk>> + Send>>, tokio_util::sync::CancellationToken)> {
        let base_stream = self.stream_completion(messages, tools).await?;
        let (cancellable_stream, cancellation_token) = CancellableStream::new(base_stream);

        Ok((Box::pin(cancellable_stream), cancellation_token))
    }
}
```

### 12.8 Reference Implementation Mapping

The streaming implementation closely follows patterns from Cline's proven architecture:

#### Key Reference Files:

- **Base Stream Handling**: `aidocs/reference/cline/src/api/transform/stream.ts` - Core SSE parsing patterns
- **OpenRouter Streaming**: `aidocs/reference/cline/src/api/providers/openrouter.ts` - OpenAI-compatible streaming
- **Anthropic Streaming**: `aidocs/reference/cline/src/api/providers/anthropic.ts` - Anthropic-specific event handling
- **Error Recovery**: `aidocs/reference/cline/src/api/providers/types.ts` - Error handling patterns

#### Streaming Format Compatibility:

- **OpenRouter/OpenAI**: Uses `data: {...}` SSE format with `[DONE]` termination
- **Anthropic**: Uses `event: type` and `data: {...}` format with specific event types
- **Gemini**: Uses custom streaming format with `candidates` array
- **Ollama**: Supports both OpenAI-compatible and native streaming formats

#### Tool Calling in Streaming:

- **Partial Tool Calls**: Accumulate function arguments across multiple chunks
- **Tool Call Completion**: Detect when tool calls are complete and ready for execution
- **Interleaved Content**: Handle mixed content and tool call streams

### 13.1 Unit Tests

```rust
#[cfg(test)]
mod tests {
    use super::*;
    use tokio_test;

    #[tokio::test]
    async fn test_openrouter_provider_creation() {
        let config = LLMConfig {
            provider: "openrouter".to_string(),
            model: "anthropic/claude-3-5-sonnet-20241022".to_string(),
            api_key: Some("test-key".to_string()),
            base_url: Some("https://openrouter.ai/api/v1".to_string()),
            openrouter: Some(OpenRouterConfig {
                site_url: Some("https://test.com".to_string()),
                app_name: Some("Test App".to_string()),
                ..Default::default()
            }),
            ..Default::default()
        };

        let provider = OpenRouterProvider::new(&config).unwrap();
        assert_eq!(provider.provider_name(), "openrouter");
        assert_eq!(provider.model_name(), "anthropic/claude-3-5-sonnet-20241022");
        assert!(provider.supports_tools() != ToolSupport::None);
    }

    #[tokio::test]
    async fn test_tool_support_detection() {
        assert_eq!(detect_tool_support("openrouter", "anthropic/claude-3-5-sonnet-20241022"), ToolSupport::Full);
        assert_eq!(detect_tool_support("openrouter", "meta-llama/llama-2-7b-chat"), ToolSupport::None);
        assert_eq!(detect_tool_support("anthropic", "claude-3-5-sonnet-20241022"), ToolSupport::Full);
    }

    #[tokio::test]
    async fn test_provider_factory() {
        let factory = ProviderFactory::new();

        let config = LLMConfig {
            provider: "anthropic".to_string(),
            model: "claude-3-5-sonnet-20241022".to_string(),
            api_key: Some("test-key".to_string()),
            ..Default::default()
        };

        let provider = factory.create_provider(&config).await.unwrap();
        assert_eq!(provider.provider_name(), "anthropic");
    }
}
```

### 13.2 Integration Tests

```rust
#[cfg(test)]
mod integration_tests {
    use super::*;

    #[tokio::test]
    #[ignore] // Requires API keys
    async fn test_real_openrouter_completion() {
        let config = LLMConfig {
            provider: "openrouter".to_string(),
            model: "anthropic/claude-3-5-sonnet-20241022".to_string(),
            api_key: std::env::var("OPENROUTER_API_KEY").ok(),
            ..Default::default()
        };

        if config.api_key.is_none() {
            return; // Skip test if no API key
        }

        let provider = OpenRouterProvider::new(&config).unwrap();
        let messages = vec![Message::user("Hello, how are you?")];

        let response = provider.complete(messages).await.unwrap();
        assert!(!response.is_empty());
    }

    #[tokio::test]
    #[ignore] // Requires API keys
    async fn test_tool_calling_integration() {
        // Test actual tool calling with real providers
    }
}
```

## 14. Implementation Guidelines

### 14.1 Development Workflow

1. **Start with Base Infrastructure**

   - Implement enhanced `LLMProvider` trait
   - Create base provider infrastructure
   - Set up error handling and configuration extensions
   - Implement task classification system

2. **Implement Task-Aware Model Selection**

   - Create `TaskClassifier` for automatic task type detection
   - Implement `TaskAwareProviderFactory` with model routing
   - Add configuration support for task-specific models
   - Test model selection logic with various task types

3. **Implement One Provider at a Time**

   - Begin with OpenRouter (most versatile)
   - Add comprehensive tests for each provider
   - Validate tool calling capabilities
   - Test with both high-capability and lightweight models

4. **Follow Rust Best Practices**

   - Use `#![deny(clippy::unwrap_used)]` to enforce proper error handling
   - Implement `From` traits for error conversions
   - Use `Arc<dyn Trait>` for shared ownership
   - Prefer borrowing over cloning

5. **Testing Strategy**
   - Unit tests for each provider
   - Integration tests with real APIs (behind feature flags)
   - Mock tests for error scenarios
   - Performance benchmarks
   - Task classification accuracy tests
   - Model selection validation tests

### 14.2 Code Organization

```
src/llm/
├── mod.rs                 # Main module with traits and factory
├── factory.rs             # Enhanced provider factory
├── task_factory.rs        # Task-aware provider factory
├── errors.rs              # Provider-specific errors
├── streaming.rs           # Streaming utilities
├── task_classification/
│   ├── mod.rs            # Task classification module exports
│   ├── classifier.rs     # Task type classification logic
│   ├── context.rs        # Task context analysis
│   └── types.rs          # Task type definitions
├── providers/
│   ├── mod.rs            # Provider module exports
│   ├── base.rs           # Base provider trait and utilities
│   ├── openrouter.rs     # OpenRouter implementation
│   ├── anthropic.rs      # Anthropic implementation
│   ├── gemini.rs         # Gemini implementation
│   ├── openai.rs         # OpenAI implementation
│   ├── ollama.rs         # Ollama implementation
│   └── requesty.rs       # Requesty implementation
└── utils/
    ├── mod.rs            # Utility module exports
    ├── tool_detection.rs # Tool support detection
    ├── message_conversion.rs # Message format conversion
    ├── api_key_resolver.rs # API key resolution logic
    └── retry.rs          # Retry mechanisms
```

### 14.3 Configuration Best Practices

1. **Environment Variable Support**

   ```rust
   pub fn load_api_key(provider: &str, config_key: Option<&str>) -> Option<String> {
       config_key.map(|k| k.to_string())
           .or_else(|| std::env::var(format!("{}_API_KEY", provider.to_uppercase())).ok())
           .or_else(|| std::env::var("LLM_API_KEY").ok())
   }
   ```

2. **Configuration Validation**

   ```rust
   impl LLMConfig {
       pub fn validate(&self) -> Result<()> {
           if self.provider.is_empty() {
               return Err(AutorunError::Config("Provider cannot be empty".to_string()));
           }

           if self.model.is_empty() {
               return Err(AutorunError::Config("Model cannot be empty".to_string()));
           }

           // Provider-specific validation
           match self.provider.as_str() {
               "openrouter" => self.validate_openrouter_config(),
               "anthropic" => self.validate_anthropic_config(),
               "gemini" => self.validate_gemini_config(),
               _ => Ok(()),
           }
       }
   }
   ```

3. **Secure API Key Handling**

   ```rust
   use secrecy::{Secret, ExposeSecret};

   #[derive(Debug, Clone)]
   pub struct SecureConfig {
       pub provider: String,
       pub model: String,
       pub api_key: Option<Secret<String>>,
       // ... other fields
   }

   impl SecureConfig {
       pub fn get_api_key(&self) -> Option<&str> {
           self.api_key.as_ref().map(|k| k.expose_secret())
       }
   }
   ```

### 14.4 Error Handling Patterns

1. **Provider-Specific Error Mapping**

   ```rust
   impl From<reqwest::Error> for ProviderError {
       fn from(err: reqwest::Error) -> Self {
           if err.is_timeout() {
               ProviderError::Timeout
           } else if err.is_connect() {
               ProviderError::ConnectionFailed(err.to_string())
           } else {
               ProviderError::Network(err)
           }
       }
   }
   ```

2. **Retry Logic with Exponential Backoff**

   ```rust
   pub async fn retry_with_backoff<T, F, Fut>(
       operation: F,
       max_retries: u32,
       base_delay: Duration,
   ) -> Result<T>
   where
       F: Fn() -> Fut,
       Fut: Future<Output = Result<T>>,
   {
       let mut delay = base_delay;

       for attempt in 0..max_retries {
           match operation().await {
               Ok(result) => return Ok(result),
               Err(err) if attempt == max_retries - 1 => return Err(err),
               Err(ProviderError::RateLimit { retry_after }) => {
                   let wait_time = retry_after.unwrap_or(delay);
                   tokio::time::sleep(wait_time).await;
                   delay = std::cmp::min(delay * 2, Duration::from_secs(60));
               }
               Err(_) => {
                   tokio::time::sleep(delay).await;
                   delay = std::cmp::min(delay * 2, Duration::from_secs(60));
               }
           }
       }

       unreachable!()
   }
   ```

### 14.5 Performance Considerations

1. **Connection Pooling**

   ```rust
   pub fn create_http_client() -> reqwest::Client {
       reqwest::Client::builder()
           .pool_max_idle_per_host(10)
           .pool_idle_timeout(Duration::from_secs(30))
           .timeout(Duration::from_secs(60))
           .build()
           .expect("Failed to create HTTP client")
   }
   ```

2. **Async Streaming**

   ```rust
   use futures::stream::{Stream, StreamExt};
   use tokio::sync::mpsc;

   pub async fn create_streaming_response(
       response: reqwest::Response,
   ) -> Result<impl Stream<Item = Result<StreamChunk>>> {
       let (tx, rx) = mpsc::unbounded_channel();

       tokio::spawn(async move {
           let mut stream = response.bytes_stream();
           while let Some(chunk) = stream.next().await {
               match chunk {
                   Ok(bytes) => {
                       if let Ok(chunk) = parse_sse_chunk(bytes) {
                           if tx.send(Ok(chunk)).is_err() {
                               break;
                           }
                       }
                   }
                   Err(e) => {
                       let _ = tx.send(Err(AutorunError::LlmApi(e.to_string())));
                       break;
                   }
               }
           }
       });

       Ok(tokio_stream::wrappers::UnboundedReceiverStream::new(rx))
   }
   ```

### 14.6 Security Considerations

1. **API Key Protection**

   - Never log API keys
   - Use secure storage for configuration
   - Support environment variable injection
   - Implement key rotation capabilities

2. **Request Validation**

   - Validate all input parameters
   - Sanitize user-provided content
   - Implement request size limits
   - Add timeout protections

3. **Response Handling**
   - Validate response formats
   - Handle malformed responses gracefully
   - Implement content filtering if needed
   - Log security-relevant events

### 14.7 Monitoring and Observability

1. **Metrics Collection**

   ```rust
   use prometheus::{Counter, Histogram, register_counter, register_histogram};

   lazy_static! {
       static ref REQUEST_COUNTER: Counter = register_counter!(
           "llm_requests_total",
           "Total number of LLM requests"
       ).unwrap();

       static ref REQUEST_DURATION: Histogram = register_histogram!(
           "llm_request_duration_seconds",
           "Duration of LLM requests"
       ).unwrap();
   }

   pub async fn make_request_with_metrics<T, F, Fut>(operation: F) -> Result<T>
   where
       F: FnOnce() -> Fut,
       Fut: Future<Output = Result<T>>,
   {
       REQUEST_COUNTER.inc();
       let timer = REQUEST_DURATION.start_timer();

       let result = operation().await;
       timer.observe_duration();

       result
   }
   ```

2. **Structured Logging**

   ```rust
   use tracing::{info, warn, error, instrument};

   #[instrument(skip(self, messages))]
   pub async fn complete(&self, messages: Vec<Message>) -> Result<String> {
       info!(
           provider = self.provider_name(),
           model = self.model_name(),
           message_count = messages.len(),
           "Starting completion request"
       );

       match self.make_request(messages).await {
           Ok(response) => {
               info!("Completion request successful");
               Ok(response)
           }
           Err(e) => {
               error!(error = %e, "Completion request failed");
               Err(e)
           }
       }
   }
   ```

## 15. Reference Implementation Examples

### 15.1 Cline Provider Implementations

For detailed examples of request/response formats and provider-specific implementations, refer to the Cline reference code in `aidocs/reference/cline/src/api/providers/`:

#### Request/Response Format References:

- **OpenRouter**: `aidocs/reference/cline/src/api/providers/openrouter.ts`
- **Anthropic**: `aidocs/reference/cline/src/api/providers/anthropic.ts`
- **Gemini**: `aidocs/reference/cline/src/api/providers/gemini.ts`
- **OpenAI**: `aidocs/reference/cline/src/api/providers/openai.ts`
- **Ollama**: `aidocs/reference/cline/src/api/providers/ollama.ts`
- **Requesty**: `aidocs/reference/cline/src/api/providers/requesty.ts`

#### Key Reference Files:

- **Base API Handler**: `aidocs/reference/cline/src/api/index.ts` - Shows the unified interface pattern
- **Provider Types**: `aidocs/reference/cline/src/api/providers/types.ts` - Error handling and type definitions
- **Stream Handling**: `aidocs/reference/cline/src/api/transform/stream.ts` - Streaming response patterns
- **Configuration**: `aidocs/reference/cline/src/shared/api.ts` - Provider configuration structures

### 15.2 Message Format Conversion Examples

When implementing message conversion between AutoRun-RS format and provider-specific formats, refer to these Cline examples:

```rust
// Example conversion patterns based on Cline implementations
impl OpenRouterProvider {
    fn convert_messages(&self, messages: Vec<Message>) -> Result<Vec<serde_json::Value>> {
        // Reference: aidocs/reference/cline/src/api/providers/openrouter.ts
        // OpenRouter uses OpenAI-compatible format
        messages.into_iter().map(|msg| {
            match msg.role {
                MessageRole::User => json!({
                    "role": "user",
                    "content": msg.content
                }),
                MessageRole::Assistant => json!({
                    "role": "assistant",
                    "content": msg.content
                }),
                MessageRole::System => json!({
                    "role": "system",
                    "content": msg.content
                }),
                // Handle tool calls and results
                _ => self.convert_tool_message(msg)
            }
        }).collect()
    }
}

impl AnthropicProvider {
    fn convert_messages(&self, messages: Vec<Message>) -> Result<Vec<anthropic::Message>> {
        // Reference: aidocs/reference/cline/src/api/providers/anthropic.ts
        // Anthropic has specific message format requirements
        // System messages are handled separately in Anthropic API
    }
}

impl GeminiProvider {
    fn convert_messages_to_gemini(&self, messages: Vec<Message>) -> Result<Vec<serde_json::Value>> {
        // Reference: aidocs/reference/cline/src/api/providers/gemini.ts
        // Gemini uses "contents" array with "parts" structure
        messages.into_iter().map(|msg| {
            json!({
                "role": match msg.role {
                    MessageRole::User => "user",
                    MessageRole::Assistant => "model",
                    _ => "user" // Gemini only supports user/model roles
                },
                "parts": [{"text": msg.content}]
            })
        }).collect()
    }
}
```

### 15.3 Tool Calling Format References

Each provider has different tool calling formats. Reference the Cline implementations for exact schemas:

- **OpenRouter/OpenAI Format**: Uses `tools` array with `function` objects
- **Anthropic Format**: Uses `tools` array with specific Claude tool schema
- **Gemini Format**: Uses `functionDeclarations` within `tools` array
- **Ollama Format**: Varies by model, some support OpenAI-compatible format

### 15.4 Error Handling Patterns

Refer to `aidocs/reference/cline/src/api/providers/types.ts` for comprehensive error handling patterns:

```rust
// Based on Cline's OpenRouter error handling
#[derive(Debug, Deserialize)]
pub struct OpenRouterErrorResponse {
    pub error: OpenRouterError,
}

#[derive(Debug, Deserialize)]
pub struct OpenRouterError {
    pub message: String,
    pub code: i32,
    pub metadata: Option<serde_json::Value>,
}

// Provider-specific error mapping
impl From<OpenRouterErrorResponse> for ProviderError {
    fn from(err: OpenRouterErrorResponse) -> Self {
        match err.error.code {
            429 => ProviderError::RateLimit {
                retry_after: extract_retry_after(&err.error.metadata)
            },
            401 => ProviderError::Authentication(err.error.message),
            404 => ProviderError::ModelNotFound {
                model: extract_model_from_metadata(&err.error.metadata)
            },
            _ => ProviderError::InvalidRequest(err.error.message),
        }
    }
}
```

## 16. Conclusion

This specification provides a comprehensive foundation for implementing a robust, extensible multi-provider AI architecture in AutoRun-RS. The design emphasizes:

- **Extensibility**: Easy addition of new providers through the plugin architecture
- **Reliability**: Comprehensive error handling and retry mechanisms
- **Performance**: Async-first design with streaming support
- **Security**: Secure API key handling and request validation
- **Maintainability**: Clear separation of concerns and consistent patterns
- **Compatibility**: Backward compatibility with existing code

The implementation should be done incrementally, starting with the base infrastructure and adding providers one by one, with comprehensive testing at each step. This approach ensures a stable, production-ready multi-provider system that can grow with the project's needs.
