# Specification: Phase 2 - Context Commands (@)

## 1. Goal/Objective

Implement the complete @ command system for context and reference injection. Extend the existing mention completion with new context types including folder, codebase, MCP servers, symbols, web resources, sessions, workspaces, git references, and memory. Create persistent storage systems to support session and workspace management.

## 2. Input

* **Phase 1 Output**: Core command infrastructure with unified completion engine
* **MCP Integration**: Existing `McpClientManager` in `src/mcp/client.rs`
* **Project Structure**: Existing file system and project organization
* **Git Integration**: Access to git repository information

## 3. Output

* **Extended Context Engine**: `src/ui/completion/context_engine.rs`
  - `@file` - File traversal and selection with fuzzy matching
  - `@folder` - Directory traversal and selection with fuzzy matching
  - `@codebase` - Full project analysis and file indexing
  - `@mcp` - MCP server discovery and selection interface
  - `@symbol` - Code symbol discovery using AST parsing or LSP integration
  - `@web` - Web search integration with configurable search engines
  - `@session <id>` - Reference to previous conversation sessions
  - `@workspace <id>` - Workspace configuration and state references
  - `@git` - Git branch, commit, and PR references
  - `@memory` - Persistent memory storage with tagging and search

* **Storage Systems**: `src/storage/`
  - `session_store.rs` - Session persistence with SQLite backend
  - `memory_store.rs` - Memory storage with full-text search capabilities
  - `workspace_store.rs` - Workspace configuration management
  - `git_integration.rs` - Git repository analysis and reference extraction

* **Context Providers**: `src/context/providers/`
  - `folder_provider.rs` - Directory analysis and navigation
  - `codebase_provider.rs` - Project-wide code analysis and indexing
  - `symbol_provider.rs` - AST-based symbol extraction and search
  - `web_provider.rs` - Web search integration with rate limiting
  - `git_provider.rs` - Git metadata extraction and branch analysis

* **Enhanced Mention Types**: Update `src/ui/completion/mention_completion.rs`
  - New `MentionType` variants for all @ command types
  - Extended metadata support for complex context objects
  - Improved caching with invalidation strategies

## 4. Constraints

* **Performance**: Full codebase indexing must complete within 30 seconds for large projects
* **Memory Usage**: Context caching must not exceed 100MB total memory footprint
* **Storage**: Session and memory data must be stored efficiently with compression
* **Security**: Web search integration must respect rate limits and API keys
* **Git Integration**: Must handle repositories without git gracefully
* **Incremental Updates**: Codebase and symbol indexing must support incremental updates
* **Cross-Platform**: All file system operations must work on Windows, macOS, and Linux
* **Error Recovery**: Network failures and missing dependencies must not crash the system
* **Privacy**: Session data must be stored locally with optional encryption
* **Scalability**: System must handle projects with >10,000 files efficiently