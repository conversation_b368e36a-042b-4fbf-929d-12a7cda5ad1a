# Specification: Phase 4 - Extended Provider Support

## 1. Goal/Objective

Add comprehensive support for remaining major AI providers (Gemini, Ollama, Requesty) with full feature parity including streaming, tool calling, and advanced capabilities. This phase focuses on expanding the provider ecosystem while maintaining unified interfaces and consistent user experience across all providers.

The system will provide:
- **GeminiProvider**: Google AI Studio/Vertex AI integration with Gemini-specific optimizations
- **OllamaProvider**: Local model hosting with connectivity resilience and model management
- **RequestyProvider**: Custom API endpoint support with flexible authentication
- **Unified Tool Calling**: Consistent tool interface across different provider formats
- **Provider Health Monitoring**: Robust health checks and fallback mechanisms
- **Streaming Consistency**: Uniform streaming experience across all providers

## 2. Input

**Enhanced Provider Infrastructure from Previous Phases:**
- Phase 1: Enhanced `LLMProvider` trait with rate limiting, health checks, and metadata (`src/llm/mod.rs`, `src/llm/client.rs`)
- Phase 2: Task-aware model selection and routing (`src/llm/task_aware_factory.rs`, `src/llm/model_selector.rs`)
- Phase 3: Advanced streaming infrastructure with tool support (`src/llm/streaming.rs`, `src/llm/streaming_tools.rs`)
- Existing provider implementations: `AnthropicClient`, `OpenAICompatibleClient`
- Tool execution framework (`src/tools/executor.rs`, `src/tools/mod.rs`)
- Configuration and registry system (`src/config/registry.rs`, `src/config/mod.rs`)

**Provider-Specific Requirements:**
- Google AI Studio/Vertex AI API specifications and authentication patterns
- Ollama local API and model management capabilities
- Requesty custom endpoint patterns and authentication methods
- Tool calling format variations across providers
- Streaming protocol differences and capabilities

## 3. Output

### 3.1 Core Provider Implementations

**`src/llm/providers/gemini.rs`** - Gemini Provider Implementation
- `GeminiClient` struct with Google AI-specific configurations
- `GeminiConfig` for API keys, regions, and model preferences
- `GeminiRequest`/`GeminiResponse` structs matching Google's API format
- Gemini-specific tool calling format conversion
- Safety settings and content filtering integration
- Multi-modal support (text, images) when applicable

**`src/llm/providers/ollama.rs`** - Ollama Local Provider
- `OllamaClient` with local server connectivity management
- `OllamaConfig` for server URL, model management, and performance settings
- Model downloading and management through Ollama API
- Connection resilience with retry logic and timeout handling
- Local model discovery and capability detection
- Resource usage monitoring and limits

**`src/llm/providers/requesty.rs`** - Custom API Provider
- `RequestyClient` for flexible custom endpoint integration
- `RequestyConfig` with customizable authentication and request patterns
- Dynamic request/response mapping based on API specifications
- Custom authentication methods (API key, Bearer token, custom headers)
- Flexible tool calling format configuration
- Rate limiting and error handling for custom APIs

### 3.2 Unified Tool Calling System

**`src/llm/tool_calling/mod.rs`** - Tool Calling Abstraction
- `ToolCallFormat` enum for different provider formats
- `ToolCallConverter` trait for format transformation
- `UnifiedToolCall` struct as internal representation
- Format detection and automatic conversion

**`src/llm/tool_calling/formats/`** - Provider-Specific Formats
- `anthropic.rs` - Anthropic's tool calling format
- `openai.rs` - OpenAI-compatible format (OpenRouter, etc.)
- `gemini.rs` - Google's function calling format
- `ollama.rs` - Ollama's tool calling format
- `requesty.rs` - Configurable format for custom APIs

### 3.3 Enhanced Provider Factory and Registry

**`src/llm/factory.rs`** - Extended Factory (Enhanced)
- Updated `ProviderFactory` with new provider support
- Provider-specific builder patterns for each new provider
- Enhanced configuration validation for all providers
- Unified health check integration across providers

**`src/config/providers/`** - Provider-Specific Configurations
- `gemini.rs` - Gemini configuration structures and defaults
- `ollama.rs` - Ollama configuration with model management
- `requesty.rs` - Requesty flexible configuration system
- Provider capability detection and validation

### 3.4 Streaming Infrastructure Extensions

**`src/llm/streaming/provider_adapters/`** - Provider Streaming Adapters
- `gemini_streaming.rs` - Gemini streaming protocol adapter
- `ollama_streaming.rs` - Ollama streaming with local optimizations
- `requesty_streaming.rs` - Custom streaming format support
- Unified streaming interface across all providers

### 3.5 Provider Health and Monitoring

**`src/llm/health/`** - Enhanced Health Monitoring
- Provider-specific health check implementations
- Local provider connectivity monitoring (Ollama)
- API quota and rate limit tracking
- Automated fallback and recovery mechanisms

## 4. Constraints

- **Interface Compatibility**: All new providers must fully implement the `LLMProvider` trait from Phase 1
- **Tool Calling Parity**: Each provider must support the unified tool calling interface
- **Streaming Consistency**: Streaming behavior must be consistent across all providers
- **Local Provider Resilience**: Ollama integration must handle offline/connection issues gracefully
- **Configuration Flexibility**: Each provider must support extensive configuration options
- **Performance Parity**: New providers should not introduce significant performance degradation
- **Error Handling Consistency**: All providers must use the unified error handling system
- **Testing Coverage**: Each provider requires comprehensive unit and integration tests
- **Documentation Standards**: Each provider needs complete API documentation and usage examples

## 5. Detailed Implementation Specifications

### 5.1 Gemini Provider Implementation

#### Core Configuration Structure
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeminiConfig {
    pub api_key: String,
    pub project_id: Option<String>,
    pub location: String, // e.g., "us-central1"
    pub endpoint_type: GeminiEndpointType,
    pub model: String,
    pub temperature: Option<f32>,
    pub top_p: Option<f32>,
    pub top_k: Option<i32>,
    pub max_output_tokens: Option<u32>,
    pub safety_settings: Vec<SafetySetting>,
    pub generation_config: Option<GenerationConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum GeminiEndpointType {
    AIStudio,      // generativelanguage.googleapis.com
    VertexAI,      // {location}-aiplatform.googleapis.com
    Custom(String), // Custom endpoint URL
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SafetySetting {
    pub category: SafetyCategory,
    pub threshold: SafetyThreshold,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SafetyCategory {
    HateSpeech,
    Harassment,
    SexuallyExplicit,
    DangerousContent,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SafetyThreshold {
    BlockNone,
    BlockOnlyHigh,
    BlockMediumAndAbove,
    BlockLowAndAbove,
}
```

#### Gemini Request/Response Structures
```rust
#[derive(Debug, Serialize)]
pub struct GeminiRequest {
    pub contents: Vec<GeminiContent>,
    pub tools: Option<Vec<GeminiTool>>,
    pub generation_config: Option<GenerationConfig>,
    pub safety_settings: Option<Vec<SafetySetting>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GeminiContent {
    pub role: String, // "user" or "model"
    pub parts: Vec<GeminiPart>,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(untagged)]
pub enum GeminiPart {
    Text { text: String },
    FunctionCall { function_call: GeminiFunctionCall },
    FunctionResponse { function_response: GeminiFunctionResponse },
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GeminiFunctionCall {
    pub name: String,
    pub args: serde_json::Value,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GeminiFunctionResponse {
    pub name: String,
    pub response: serde_json::Value,
}

#[derive(Debug, Deserialize)]
pub struct GeminiResponse {
    pub candidates: Vec<GeminiCandidate>,
    pub usage_metadata: Option<GeminiUsageMetadata>,
}

#[derive(Debug, Deserialize)]
pub struct GeminiCandidate {
    pub content: GeminiContent,
    pub finish_reason: Option<String>,
    pub safety_ratings: Option<Vec<SafetyRating>>,
}
```

#### Gemini Tool Calling Format Converter
```rust
impl ToolCallConverter for GeminiToolCallConverter {
    fn convert_to_provider_format(
        &self,
        tools: &[UnifiedToolCall],
    ) -> Result<serde_json::Value> {
        let gemini_tools: Vec<GeminiTool> = tools
            .iter()
            .map(|tool| GeminiTool {
                function_declarations: vec![GeminiFunctionDeclaration {
                    name: tool.name.clone(),
                    description: tool.description.clone(),
                    parameters: tool.parameters.clone(),
                }],
            })
            .collect();
        
        Ok(serde_json::to_value(gemini_tools)?)
    }
    
    fn extract_tool_calls(
        &self,
        response: &serde_json::Value,
    ) -> Result<Vec<UnifiedToolCall>> {
        // Extract function calls from Gemini response format
        let response: GeminiResponse = serde_json::from_value(response.clone())?;
        
        let mut tool_calls = Vec::new();
        for candidate in response.candidates {
            for part in candidate.content.parts {
                if let GeminiPart::FunctionCall { function_call } = part {
                    tool_calls.push(UnifiedToolCall {
                        id: format!("gemini_{}", uuid::Uuid::new_v4()),
                        name: function_call.name,
                        arguments: function_call.args,
                    });
                }
            }
        }
        
        Ok(tool_calls)
    }
}
```

### 5.2 Ollama Provider Implementation

#### Ollama Configuration and Client
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OllamaConfig {
    pub base_url: String, // Default: "http://localhost:11434"
    pub model: String,
    pub temperature: Option<f32>,
    pub top_p: Option<f32>,
    pub top_k: Option<i32>,
    pub num_ctx: Option<i32>, // Context window size
    pub num_gpu: Option<i32>, // GPU layers
    pub num_thread: Option<i32>, // CPU threads
    pub repeat_penalty: Option<f32>,
    pub seed: Option<i32>,
    pub timeout_seconds: Option<u64>,
    pub keep_alive: Option<String>, // "5m", "10s", etc.
    pub auto_pull_models: bool,
    pub model_management: OllamaModelManagement,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OllamaModelManagement {
    pub auto_download: bool,
    pub storage_path: Option<String>,
    pub max_models: Option<usize>,
    pub cleanup_policy: CleanupPolicy,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CleanupPolicy {
    None,
    LeastRecentlyUsed { max_size_gb: f64 },
    TimeBasedDeletion { max_age_days: u32 },
}

pub struct OllamaClient {
    config: OllamaConfig,
    client: reqwest::Client,
    rate_limiter: Option<Arc<RateLimiter>>,
    health_monitor: Arc<RwLock<ProviderHealthStatus>>,
    model_manager: OllamaModelManager,
    metrics: Arc<RwLock<ProviderMetrics>>,
}
```

#### Ollama Health Monitoring and Connection Resilience
```rust
impl OllamaClient {
    async fn check_ollama_server_health(&self) -> Result<ProviderHealthStatus> {
        let start_time = std::time::Instant::now();
        
        // Check basic connectivity
        let version_response = self.client
            .get(&format!("{}/api/version", self.config.base_url))
            .timeout(Duration::from_secs(5))
            .send()
            .await;
        
        match version_response {
            Ok(response) if response.status().is_success() => {
                let response_time = start_time.elapsed().as_millis() as u64;
                
                // Additional checks: model availability, disk space, etc.
                let model_status = self.check_model_availability().await?;
                let system_resources = self.check_system_resources().await?;
                
                Ok(ProviderHealthStatus {
                    is_healthy: model_status && system_resources,
                    response_time_ms: Some(response_time),
                    last_error: None,
                    last_check: SystemTime::now(),
                    api_status: ApiStatus::Operational,
                })
            }
            Ok(response) => {
                Ok(ProviderHealthStatus {
                    is_healthy: false,
                    response_time_ms: Some(start_time.elapsed().as_millis() as u64),
                    last_error: Some(format!("Server returned: {}", response.status())),
                    last_check: SystemTime::now(),
                    api_status: ApiStatus::Degraded,
                })
            }
            Err(e) => {
                Ok(ProviderHealthStatus {
                    is_healthy: false,
                    response_time_ms: None,
                    last_error: Some(e.to_string()),
                    last_check: SystemTime::now(),
                    api_status: ApiStatus::Outage,
                })
            }
        }
    }
    
    async fn ensure_model_available(&self) -> Result<()> {
        if !self.config.model_management.auto_download {
            return Ok(());
        }
        
        // Check if model is already available
        let models_response = self.client
            .get(&format!("{}/api/tags", self.config.base_url))
            .send()
            .await
            .map_err(|e| ProviderError::Network(e.to_string()))?;
        
        let models: OllamaModelsResponse = models_response
            .json()
            .await
            .map_err(|e| ProviderError::Network(e.to_string()))?;
        
        let model_exists = models.models.iter().any(|m| m.name == self.config.model);
        
        if !model_exists {
            log::info!("Model {} not found locally, pulling...", self.config.model);
            self.pull_model(&self.config.model).await?;
        }
        
        Ok(())
    }
    
    async fn pull_model(&self, model: &str) -> Result<()> {
        let pull_request = json!({
            "name": model,
            "stream": false
        });
        
        let response = self.client
            .post(&format!("{}/api/pull", self.config.base_url))
            .json(&pull_request)
            .timeout(Duration::from_secs(300)) // 5 minutes for model download
            .send()
            .await
            .map_err(|e| ProviderError::Network(e.to_string()))?;
        
        if !response.status().is_success() {
            return Err(ProviderError::ProviderSpecific {
                provider: "ollama".to_string(),
                message: format!("Failed to pull model {}: {}", model, response.status()),
            });
        }
        
        log::info!("Successfully pulled model: {}", model);
        Ok(())
    }
}
```

### 5.3 Requesty Provider Implementation

#### Flexible Configuration System
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RequestyConfig {
    pub base_url: String,
    pub authentication: RequestyAuthentication,
    pub request_format: RequestFormat,
    pub response_format: ResponseFormat,
    pub tool_calling: Option<ToolCallingConfig>,
    pub streaming: Option<StreamingConfig>,
    pub rate_limits: Option<RateLimitConfig>,
    pub headers: HashMap<String, String>,
    pub timeout_seconds: Option<u64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RequestyAuthentication {
    None,
    ApiKey { 
        header_name: String, 
        prefix: Option<String>, // "Bearer ", "API-Key ", etc.
        key: String 
    },
    BasicAuth { username: String, password: String },
    CustomHeaders { headers: HashMap<String, String> },
    OAuth2 { token_url: String, client_id: String, client_secret: String },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RequestFormat {
    pub template: String, // JSON template with placeholders
    pub message_path: String, // JSONPath for messages
    pub tool_path: Option<String>, // JSONPath for tools
    pub parameter_mapping: HashMap<String, String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResponseFormat {
    pub content_path: String, // JSONPath to extract content
    pub tool_calls_path: Option<String>, // JSONPath to extract tool calls
    pub error_path: Option<String>, // JSONPath to detect errors
    pub usage_path: Option<String>, // JSONPath to extract usage stats
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolCallingConfig {
    pub format: ToolCallFormat,
    pub function_name_field: String,
    pub arguments_field: String,
    pub result_format: String,
}
```

#### Dynamic Request/Response Processing
```rust
impl RequestyClient {
    async fn build_request(&self, messages: &[Message]) -> Result<reqwest::Request> {
        // Apply request template with dynamic values
        let mut request_body = self.apply_template(&self.config.request_format.template, messages)?;
        
        // Add authentication
        let mut headers = reqwest::header::HeaderMap::new();
        self.apply_authentication(&mut headers)?;
        
        // Add custom headers
        for (key, value) in &self.config.headers {
            headers.insert(
                reqwest::header::HeaderName::from_str(key)?,
                reqwest::header::HeaderValue::from_str(value)?,
            );
        }
        
        let request = self.client
            .post(&self.config.base_url)
            .headers(headers)
            .json(&request_body)
            .build()?;
        
        Ok(request)
    }
    
    fn apply_template(&self, template: &str, messages: &[Message]) -> Result<serde_json::Value> {
        let mut template_value: serde_json::Value = serde_json::from_str(template)?;
        
        // Apply message mapping
        let messages_json = serde_json::to_value(messages)?;
        self.set_json_path(&mut template_value, &self.config.request_format.message_path, messages_json)?;
        
        // Apply parameter mapping
        for (param, value_path) in &self.config.request_format.parameter_mapping {
            if let Some(value) = self.get_config_value(value_path) {
                self.set_json_path(&mut template_value, param, value)?;
            }
        }
        
        Ok(template_value)
    }
    
    fn extract_response_content(&self, response: &serde_json::Value) -> Result<String> {
        self.get_json_path(response, &self.config.response_format.content_path)
            .and_then(|v| v.as_str().map(|s| s.to_string()))
            .ok_or_else(|| ProviderError::ProviderSpecific {
                provider: "requesty".to_string(),
                message: "Failed to extract content from response".to_string(),
            })
    }
}
```

### 5.4 Unified Tool Calling System

#### Core Tool Call Abstraction
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UnifiedToolCall {
    pub id: String,
    pub name: String,
    pub arguments: serde_json::Value,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UnifiedToolResult {
    pub id: String,
    pub name: String,
    pub result: serde_json::Value,
    pub error: Option<String>,
}

#[async_trait::async_trait]
pub trait ToolCallConverter: Send + Sync {
    /// Convert unified tool definitions to provider-specific format
    fn convert_tools_to_provider_format(
        &self,
        tools: &[ToolDefinition],
    ) -> Result<serde_json::Value>;
    
    /// Extract tool calls from provider response
    fn extract_tool_calls(
        &self,
        response: &serde_json::Value,
    ) -> Result<Vec<UnifiedToolCall>>;
    
    /// Convert tool results to provider-specific format for continuation
    fn format_tool_results(
        &self,
        results: &[UnifiedToolResult],
    ) -> Result<serde_json::Value>;
}

pub enum ToolCallFormat {
    OpenAI,      // OpenAI/OpenRouter format
    Anthropic,   // Anthropic Claude format
    Gemini,      // Google Gemini format
    Ollama,      // Ollama format
    Custom(String), // Custom format specification
}
```

#### Provider-Specific Tool Call Converters
```rust
// Anthropic tool calling format
impl ToolCallConverter for AnthropicToolCallConverter {
    fn convert_tools_to_provider_format(
        &self,
        tools: &[ToolDefinition],
    ) -> Result<serde_json::Value> {
        let anthropic_tools: Vec<AnthropicTool> = tools
            .iter()
            .map(|tool| AnthropicTool {
                name: tool.name.clone(),
                description: tool.description.clone(),
                input_schema: tool.parameters.clone(),
            })
            .collect();
        
        Ok(serde_json::to_value(anthropic_tools)?)
    }
    
    fn extract_tool_calls(
        &self,
        response: &serde_json::Value,
    ) -> Result<Vec<UnifiedToolCall>> {
        // Parse Anthropic's tool_use content blocks
        let content = response
            .get("content")
            .and_then(|c| c.as_array())
            .unwrap_or(&vec![]);
        
        let mut tool_calls = Vec::new();
        for block in content {
            if block.get("type").and_then(|t| t.as_str()) == Some("tool_use") {
                let id = block.get("id").and_then(|i| i.as_str()).unwrap_or("unknown");
                let name = block.get("name").and_then(|n| n.as_str()).unwrap_or("unknown");
                let input = block.get("input").cloned().unwrap_or(json!({}));
                
                tool_calls.push(UnifiedToolCall {
                    id: id.to_string(),
                    name: name.to_string(),
                    arguments: input,
                });
            }
        }
        
        Ok(tool_calls)
    }
    
    fn format_tool_results(
        &self,
        results: &[UnifiedToolResult],
    ) -> Result<serde_json::Value> {
        let tool_results: Vec<serde_json::Value> = results
            .iter()
            .map(|result| {
                json!({
                    "type": "tool_result",
                    "tool_use_id": result.id,
                    "content": result.result.to_string(),
                    "is_error": result.error.is_some()
                })
            })
            .collect();
        
        Ok(serde_json::Value::Array(tool_results))
    }
}
```

### 5.5 Enhanced Streaming Support

#### Provider-Specific Streaming Adapters
```rust
#[async_trait::async_trait]
pub trait StreamingAdapter: Send + Sync {
    async fn create_stream(
        &self,
        request: serde_json::Value,
    ) -> Result<Pin<Box<dyn Stream<Item = Result<StreamChunk>> + Send>>>;
    
    fn parse_stream_chunk(&self, data: &str) -> Result<Option<StreamChunk>>;
    
    fn supports_tool_calling_in_stream(&self) -> bool;
}

// Gemini streaming adapter
pub struct GeminiStreamingAdapter {
    client: reqwest::Client,
    config: GeminiConfig,
}

#[async_trait::async_trait]
impl StreamingAdapter for GeminiStreamingAdapter {
    async fn create_stream(
        &self,
        request: serde_json::Value,
    ) -> Result<Pin<Box<dyn Stream<Item = Result<StreamChunk>> + Send>>> {
        let mut request_with_stream = request;
        request_with_stream["stream"] = json!(true);
        
        let response = self.client
            .post(&format!("{}/v1/models/{}:streamGenerateContent", 
                   self.get_base_url(), self.config.model))
            .json(&request_with_stream)
            .send()
            .await?;
        
        let stream = response
            .bytes_stream()
            .map_err(|e| AutorunError::Network(e.to_string()))
            .map_ok(|chunk| String::from_utf8_lossy(&chunk).into_owned())
            .try_filter_map(|line| async move {
                if line.starts_with("data: ") {
                    let data = &line[6..];
                    if data == "[DONE]" {
                        return Ok(None);
                    }
                    
                    match self.parse_stream_chunk(data) {
                        Ok(Some(chunk)) => Ok(Some(chunk)),
                        Ok(None) => Ok(None),
                        Err(e) => {
                            log::warn!("Failed to parse Gemini stream chunk: {}", e);
                            Ok(None)
                        }
                    }
                } else {
                    Ok(None)
                }
            });
        
        Ok(Box::pin(stream))
    }
    
    fn parse_stream_chunk(&self, data: &str) -> Result<Option<StreamChunk>> {
        let chunk: serde_json::Value = serde_json::from_str(data)?;
        
        // Extract content from Gemini streaming format
        if let Some(candidates) = chunk.get("candidates").and_then(|c| c.as_array()) {
            if let Some(candidate) = candidates.first() {
                if let Some(content) = candidate.get("content") {
                    if let Some(parts) = content.get("parts").and_then(|p| p.as_array()) {
                        for part in parts {
                            if let Some(text) = part.get("text").and_then(|t| t.as_str()) {
                                return Ok(Some(StreamChunk {
                                    content: Some(text.to_string()),
                                    tool_calls: vec![], // Handle tool calls separately
                                    finish_reason: candidate
                                        .get("finishReason")
                                        .and_then(|r| r.as_str())
                                        .map(|s| s.to_string()),
                                    usage: None, // Usage typically comes at the end
                                }));
                            }
                            
                            // Handle function calls in streaming
                            if let Some(function_call) = part.get("functionCall") {
                                // Convert to unified tool call format
                                // Implementation details...
                            }
                        }
                    }
                }
            }
        }
        
        Ok(None)
    }
    
    fn supports_tool_calling_in_stream(&self) -> bool {
        true
    }
}
```

### 5.6 Enhanced Provider Registry and Factory

#### Provider Registration System
```rust
// In src/config/registry.rs - Enhanced provider registry
impl ProviderRegistry {
    pub fn register_gemini_provider(&mut self) {
        self.providers.insert("gemini".to_string(), ProviderInfo {
            name: "Google Gemini".to_string(),
            version: "1.0.0".to_string(),
            description: "Google's Gemini AI models via AI Studio or Vertex AI".to_string(),
            supported_models: vec![
                "gemini-1.5-pro".to_string(),
                "gemini-1.5-flash".to_string(),
                "gemini-1.0-pro".to_string(),
                "gemini-pro-vision".to_string(),
            ],
            capabilities: vec![
                ProviderCapability::TextGeneration,
                ProviderCapability::ToolCalling,
                ProviderCapability::Streaming,
                ProviderCapability::Vision,
                ProviderCapability::CodeGeneration,
            ],
            rate_limits: RateLimitInfo {
                requests_per_minute: Some(60),
                requests_per_hour: Some(1000),
                tokens_per_minute: Some(32000),
                ..Default::default()
            },
            cost_info: Some(CostInfo {
                input_cost_per_token: Some(0.00125 / 1000.0), // $1.25 per 1M tokens
                output_cost_per_token: Some(0.00375 / 1000.0), // $3.75 per 1M tokens
                currency: "USD".to_string(),
                billing_unit: "token".to_string(),
            }),
        });
    }
    
    pub fn register_ollama_provider(&mut self) {
        self.providers.insert("ollama".to_string(), ProviderInfo {
            name: "Ollama Local".to_string(),
            version: "1.0.0".to_string(),
            description: "Local LLM hosting via Ollama".to_string(),
            supported_models: vec![
                "llama2".to_string(),
                "codellama".to_string(),
                "mistral".to_string(),
                "mixtral".to_string(),
                "qwen".to_string(),
                "gemma".to_string(),
                // Dynamic model detection
            ],
            capabilities: vec![
                ProviderCapability::TextGeneration,
                ProviderCapability::ToolCalling,
                ProviderCapability::Streaming,
                ProviderCapability::CodeGeneration,
            ],
            rate_limits: RateLimitInfo {
                // Local provider - high limits
                requests_per_minute: Some(1000),
                concurrent_requests: Some(10),
                ..Default::default()
            },
            cost_info: None, // No cost for local models
        });
    }
    
    pub fn register_requesty_provider(&mut self) {
        self.providers.insert("requesty".to_string(), ProviderInfo {
            name: "Custom API (Requesty)".to_string(),
            version: "1.0.0".to_string(),
            description: "Flexible integration with custom AI API endpoints".to_string(),
            supported_models: vec!["*".to_string()], // Wildcard - depends on endpoint
            capabilities: vec![
                ProviderCapability::TextGeneration,
                ProviderCapability::Custom("configurable".to_string()),
            ],
            rate_limits: RateLimitInfo::default(), // Configurable per endpoint
            cost_info: None, // Depends on the custom endpoint
        });
    }
}
```

#### Enhanced Provider Factory
```rust
// In src/llm/factory.rs - Extended factory implementation
impl ProviderFactory {
    pub async fn create_provider_with_enhanced_config(
        &self,
        config: &LLMConfig,
    ) -> Result<Arc<dyn LLMProvider>> {
        match config.provider.as_str() {
            "gemini" => {
                let gemini_config = self.extract_gemini_config(config)?;
                let api_key = self.resolve_api_key_for_provider(config).await?;
                let rate_limiter = self.create_rate_limiter(config)?;
                
                Ok(Arc::new(GeminiClient::new(
                    gemini_config,
                    api_key,
                    rate_limiter,
                )?))
            }
            "ollama" => {
                let ollama_config = self.extract_ollama_config(config)?;
                let rate_limiter = self.create_rate_limiter(config)?;
                
                // No API key needed for local Ollama
                Ok(Arc::new(OllamaClient::new(
                    ollama_config,
                    rate_limiter,
                ).await?))
            }
            "requesty" => {
                let requesty_config = self.extract_requesty_config(config)?;
                let rate_limiter = self.create_rate_limiter(config)?;
                
                Ok(Arc::new(RequestyClient::new(
                    requesty_config,
                    rate_limiter,
                )?))
            }
            // ... existing providers (anthropic, openrouter)
            _ => Err(AutorunError::Config(format!(
                "Unsupported LLM provider: {}",
                config.provider
            )))
        }
    }
}
```

### 5.7 Implementation Timeline

**Week 6: Core Provider Implementations**
- **Days 1-2**: Implement `GeminiClient` with Google AI Studio integration
- **Days 3-4**: Implement `OllamaClient` with local server management
- **Days 5-7**: Implement `RequestyClient` with flexible configuration system

**Week 7: Integration and Optimization**
- **Days 8-9**: Implement unified tool calling system and format converters
- **Days 10-11**: Add streaming support for all new providers
- **Days 12-13**: Enhanced health monitoring and provider registry updates
- **Day 14**: Comprehensive testing and documentation

### 5.8 Testing Strategy

**Unit Tests for Each Provider:**
```rust
#[tokio::test]
async fn test_gemini_provider_creation() {
    let config = GeminiConfig {
        api_key: "test-key".to_string(),
        project_id: Some("test-project".to_string()),
        location: "us-central1".to_string(),
        endpoint_type: GeminiEndpointType::AIStudio,
        model: "gemini-1.5-pro".to_string(),
        // ... other config
    };
    
    let client = GeminiClient::new(config, None).await.unwrap();
    
    // Test provider info
    let info = client.provider_info();
    assert_eq!(info.name, "Google Gemini");
    assert!(info.capabilities.contains(&ProviderCapability::ToolCalling));
    
    // Test health check
    let health = client.health_check().await.unwrap();
    assert!(health.is_healthy);
}

#[tokio::test]
async fn test_ollama_model_management() {
    let mut config = OllamaConfig::default();
    config.model = "llama2".to_string();
    config.model_management.auto_download = true;
    
    let client = OllamaClient::new(config, None).await.unwrap();
    
    // Test model availability check
    let available = client.check_model_availability().await.unwrap();
    assert!(available);
    
    // Test model pulling (if not available)
    client.ensure_model_available().await.unwrap();
}

#[tokio::test]
async fn test_requesty_custom_format() {
    let config = RequestyConfig {
        base_url: "https://api.example.com/v1/chat".to_string(),
        authentication: RequestyAuthentication::ApiKey {
            header_name: "Authorization".to_string(),
            prefix: Some("Bearer ".to_string()),
            key: "test-key".to_string(),
        },
        request_format: RequestFormat {
            template: r#"{"messages": [], "model": "custom-model"}"#.to_string(),
            message_path: "$.messages".to_string(),
            tool_path: Some("$.tools".to_string()),
            parameter_mapping: HashMap::new(),
        },
        response_format: ResponseFormat {
            content_path: "$.choices[0].message.content".to_string(),
            tool_calls_path: Some("$.choices[0].message.tool_calls".to_string()),
            error_path: Some("$.error".to_string()),
            usage_path: Some("$.usage".to_string()),
        },
        // ... other config
    };
    
    let client = RequestyClient::new(config, None).unwrap();
    
    // Test request building
    let messages = vec![Message::user("Hello")];
    let request = client.build_request(&messages).await.unwrap();
    
    // Verify request format
    assert!(request.url().as_str().contains("api.example.com"));
    assert!(request.headers().contains_key("authorization"));
}
```

**Integration Tests:**
```rust
#[tokio::test]
async fn test_cross_provider_tool_calling() {
    let providers = vec![
        create_test_anthropic_provider().await,
        create_test_gemini_provider().await,
        create_test_ollama_provider().await,
    ];
    
    let tool_def = ToolDefinition {
        name: "get_weather".to_string(),
        description: "Get weather information".to_string(),
        parameters: json!({
            "type": "object",
            "properties": {
                "location": {"type": "string"}
            }
        }),
    };
    
    for provider in providers {
        // Test tool calling works consistently across providers
        let messages = vec![
            Message::user("What's the weather in New York?"),
        ];
        
        let response = provider.complete_with_tools(messages, vec![tool_def.clone()]).await.unwrap();
        
        // Verify tool call was made
        assert!(!response.tool_calls.is_empty());
        assert_eq!(response.tool_calls[0].name, "get_weather");
    }
}
```

## 6. Success Criteria

- **All three new providers** (Gemini, Ollama, Requesty) fully implement the `LLMProvider` trait
- **Tool calling works consistently** across all providers with unified interface
- **Streaming support** is available for all providers that support it
- **Health monitoring** accurately detects provider availability and issues
- **Configuration system** supports provider-specific settings and validation
- **Local provider resilience** handles Ollama connectivity issues gracefully
- **Performance parity** with existing providers (no significant degradation)
- **Comprehensive test coverage** (>90%) for all new functionality
- **Complete documentation** with usage examples for each provider

This specification provides a comprehensive roadmap for Phase 4, extending AutoRun's provider ecosystem while maintaining consistency, reliability, and ease of use across all supported AI providers.