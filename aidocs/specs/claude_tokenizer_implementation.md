# Claude Tokenizer Implementation

## Overview

This document describes the implementation of `ClaudeTokenizerProvider` for Anthrop<PERSON>'s Claude models in the AutoRun tokenization system.

## Implementation Details

### Location
- **Main Implementation**: `src/prompts/tokenization/claude.rs`
- **Integration**: `src/prompts/tokenization/mod.rs`
- **Tests**: `tests/claude_tokenizer_tests.rs`
- **Examples**: `examples/claude_tokenizer_demo.rs`, `examples/test_claude_simple.rs`

### Features

#### 1. **ClaudeTokenizerProvider Struct**
```rust
pub struct ClaudeTokenizerProvider;
```
- Zero-cost abstraction using module-level functions from `claude-tokenizer` crate
- Implements `TokenizerProvider` trait with full async support

#### 2. **Supported Models**
- **Claude 3 Models**: Opus, Sonnet, Haiku (200K token context window)
- **Claude 2**: 100K token context window
- **<PERSON> Instant**: 100K token context window

#### 3. **Token Limits and Pricing**
- **Claude 3 Opus**: 200K context, $0.015/1K input, $0.075/1K output
- **Claude 3 Sonnet**: 200K context, $0.003/1K input, $0.015/1K output  
- **Claude 3 Haiku**: 200K context, $0.00025/1K input, $0.00125/1K output
- **Claude 2/Instant**: 100K context, no pricing info stored

#### 4. **Core Functionality**

##### Token Counting
```rust
async fn count_tokens(&self, text: &str, config: &TokenizerConfig) -> TokenizationResult<usize>
```
- Uses `claude_tokenizer::count_tokens()` for accurate token counting
- Handles Claude-specific message formatting with Human:/Assistant: prefixes

##### Text Encoding
```rust
async fn encode(&self, text: &str, config: &TokenizerConfig) -> TokenizationResult<Vec<u32>>
```
- Uses `claude_tokenizer::tokenize()` to get token ID/text pairs
- Extracts token IDs and converts from `usize` to `u32`

##### Token Decoding
```rust
async fn decode(&self, tokens: &[u32], _config: &TokenizerConfig) -> TokenizationResult<String>
```
- **Note**: Claude tokenizer doesn't provide direct decode functionality
- Returns placeholder text indicating number of tokens
- Production usage may require custom token-to-text mapping

##### Model Information
```rust
async fn get_model_info(&self, model: &str) -> TokenizationResult<ModelTokenInfo>
```
- Returns context limits, output limits, and pricing information
- Automatically detects model variant from model string

#### 5. **Message Formatting**
```rust
fn format_for_claude(&self, text: &str, config: &TokenizerConfig) -> String
```
- Adds Claude-specific Human:/Assistant: formatting when `include_special_tokens` is enabled
- Ensures accurate token counting for actual Claude API usage

#### 6. **Advanced Features**
- **Usage Calculation**: Computes token usage percentages and remaining capacity
- **Token Validation**: Checks if text exceeds model limits
- **Text Truncation**: Supports multiple truncation strategies (End, Beginning, Both, None)
- **Text Splitting**: Splits large texts into token-limited chunks

### Factory Integration

The `TokenizerFactory` automatically detects Claude models:

```rust
impl TokenizerFactory {
    pub fn create_provider(model: &str) -> TokenizationResult<Box<dyn TokenizerProvider>> {
        let model_lower = model.to_lowercase();
        
        if model_lower.contains("claude") {
            let provider = claude::ClaudeTokenizerProvider::new()?;
            return Ok(Box::new(provider));
        }
        // ... other providers
    }
    
    pub fn is_available(model: &str) -> bool {
        model.to_lowercase().contains("claude")
    }
}
```

### Usage Examples

#### Basic Usage
```rust
use autorun::prompts::tokenization::{TokenizerFactory, TokenizerConfig};

// Create provider
let provider = TokenizerFactory::create_provider("claude-3-sonnet")?;

// Configure
let config = TokenizerConfig {
    model: "claude-3-sonnet".to_string(),
    max_tokens: 200_000,
    include_special_tokens: true,
    ..Default::default()
};

// Count tokens
let count = provider.count_tokens("Hello, Claude!", &config).await?;
println!("Token count: {}", count);
```

#### Advanced Usage
```rust
// Get model information
let info = provider.get_model_info("claude-3-opus").await?;
println!("Max context: {} tokens", info.max_context_tokens);

// Calculate usage
let usage = provider.calculate_usage(text, &config).await?;
println!("Usage: {:.2}%", usage.usage_percentage);

// Validate token limits
let is_valid = provider.validate_token_limit(text, &config).await?;

// Split large text
let chunks = provider.split_by_tokens(large_text, 1000, &config).await?;
```

### Dependencies

- **claude-tokenizer**: `0.2.0` - Core tokenization functionality
- **async-trait**: For async trait implementation
- **serde**: For serialization support

### Limitations

1. **No Direct Decoding**: The `claude-tokenizer` crate doesn't provide token-to-text decoding
2. **Simplified Decode**: Current implementation returns placeholder text
3. **Format Assumptions**: Message formatting is simplified for the Human:/Assistant: pattern

### Testing

The implementation includes comprehensive tests:

- **Unit Tests**: Model variant detection, token limits, pricing
- **Integration Tests**: Full tokenization pipeline
- **Example Applications**: Demonstrating real-world usage

Run tests:
```bash
# All tokenization tests
cargo test tokenization

# Claude-specific tests  
cargo test claude

# Example applications
cargo run --example claude_tokenizer_demo
cargo run --example test_claude_simple
```

### Performance

- **Zero-cost Abstraction**: No runtime overhead for provider creation
- **Async Optimized**: All operations are async-first
- **Memory Efficient**: Uses references and borrows where possible
- **Caching Ready**: Model info and limits are computed once per call

### Future Enhancements

1. **Proper Decoding**: Implement token-to-text mapping storage during encoding
2. **Batch Operations**: Support batch tokenization for multiple texts
3. **Streaming**: Support streaming tokenization for large texts
4. **Cache Integration**: Add token count caching for frequently used texts
5. **Model Updates**: Support for newer Claude model variants

## Integration Status

✅ **Complete** - ClaudeTokenizerProvider is fully implemented and integrated with the tokenization system
✅ **Tested** - Comprehensive test coverage with working examples
✅ **Factory Integration** - Automatic provider selection for Claude models
✅ **Documentation** - Complete API documentation and usage examples