# Specification: Phase 5 - Template Engine

## 1. Goal/Objective

Create a comprehensive template engine with advanced variable substitution capabilities for custom commands. Support dynamic variable resolution, interactive prompts, conditional logic, and template inheritance. Enable powerful prompt engineering workflows with reusable template components and sophisticated parameter handling.

## 2. Input

* **Phase 1-4 Output**: Complete command system with custom command loading from Phase 3
* **Custom Commands**: Markdown files in `.autorun/commands/` and `.claude/commands/`
* **Context System**: Full context injection capabilities from Phase 2
* **Configuration**: Runtime configuration system from Phase 4

## 3. Output

* **Core Template Engine**: `src/templates/engine/`
  - `template_engine.rs` - Main template processing engine with async support
  - `ast_parser.rs` - Template AST parsing for complex expressions
  - `expression_evaluator.rs` - Variable expression evaluation with functions
  - `template_compiler.rs` - Template compilation to bytecode for performance

* **Variable System**: `src/templates/variables/`
  - `variable_resolver.rs` - Multi-source variable resolution with priority
  - `system_variables.rs` - Built-in system variables ($WORKING_DIR, $SESSION_ID, etc.)
  - `user_variables.rs` - User-defined variables with persistence
  - `environment_variables.rs` - Environment variable integration
  - `context_variables.rs` - Dynamic context injection from @ commands
  - `interactive_prompts.rs` - Interactive variable collection with validation

* **Template Features**: `src/templates/features/`
  - `conditional_logic.rs` - If/else/elif conditional processing
  - `loop_processing.rs` - For/while loop support for lists and maps
  - `template_inheritance.rs` - Template extends/includes with block overrides
  - `macro_system.rs` - Reusable macro definitions and expansions
  - `filter_functions.rs` - Text transformation filters (uppercase, trim, etc.)
  - `function_library.rs` - Built-in functions (date, random, math, etc.)

* **Template Language**: `src/templates/language/`
  - `syntax_parser.rs` - Custom template syntax parsing
  - `lexer.rs` - Template tokenization and syntax highlighting
  - `semantic_analyzer.rs` - Template validation and type checking
  - `error_reporter.rs` - Detailed error reporting with line numbers

* **Template Management**: `src/templates/management/`
  - `template_cache.rs` - Intelligent caching with dependency tracking
  - `template_registry.rs` - Template discovery and metadata management
  - `template_validator.rs` - Template syntax and semantic validation
  - `template_debugger.rs` - Template execution debugging and profiling

* **Integration Layer**: `src/templates/integration/`
  - `command_integration.rs` - Integration with custom command system
  - `context_injection.rs` - Automatic context variable injection
  - `output_formatting.rs` - Template output formatting and escape handling
  - `streaming_processor.rs` - Streaming template processing for large outputs

* **Template Syntax Support**:
  - Variables: `$VAR`, `${VAR}`, `${VAR:default}`
  - Expressions: `${VAR + 1}`, `${func(VAR)}`
  - Conditionals: `{% if condition %}...{% endif %}`
  - Loops: `{% for item in list %}...{% endfor %}`
  - Includes: `{% include "template.md" %}`
  - Macros: `{% macro name(args) %}...{% endmacro %}`

## 4. Constraints

* **Performance**: Template compilation must complete within 1 second for complex templates
* **Memory Efficiency**: Template cache must not exceed 50MB with LRU eviction
* **Syntax Compatibility**: Must support Jinja2-like syntax for familiarity
* **Error Handling**: Detailed error messages with line/column information
* **Security**: Template execution must be sandboxed to prevent code injection
* **Recursive Limits**: Template includes and macro calls must have recursion limits
* **Streaming Support**: Large template outputs must support streaming to prevent memory issues
* **Unicode Support**: Full Unicode support for international templates
* **Watch Mode**: Template files must be monitored for changes with hot reloading
* **Debugging**: Template execution must provide step-by-step debugging capabilities
* **Type Safety**: Variable types must be checked at compile time where possible
* **Integration**: Must seamlessly integrate with existing command and context systems