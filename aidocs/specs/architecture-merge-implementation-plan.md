# Architecture Merge Implementation Plan

## 1. Executive Summary

This document outlines the comprehensive plan for merging the AutoRun-RS Agentic Core Architecture with the Multi-Provider AI Architecture into a unified, cohesive system. The merge will create a powerful agentic coding assistant that leverages multiple AI providers while maintaining the existing TUI interface, CLI functionality, and MCP protocol support.

## 2. Analysis Phase

### 2.1 Current State Assessment

**Agentic Core Architecture (`aidocs/autorun-agentic-core-architecture.md`)**:

- **Focus**: Autonomous task execution, tool orchestration, state management
- **Key Components**: AgentCore trait, TaskManager, ExecutionLoop, StateManager, ContextManager
- **Integration Points**: TUI interface, MCP protocol, tool execution framework
- **Implementation Timeline**: 10-week phased approach

**Multi-Provider AI Architecture (`aidocs/multi-provider-ai-architecture.md`)**:

- **Focus**: LLM provider abstraction, tool calling support, streaming capabilities
- **Key Components**: LLMProvider trait, ProviderFactory, provider implementations
- **Supported Providers**: <PERSON>R<PERSON><PERSON>, Anthropic, OpenAI, <PERSON>, <PERSON><PERSON><PERSON>, Requesty
- **Implementation Timeline**: 10-week phased approach

### 2.2 Overlapping Concepts

| Concept                    | Agentic Core             | Multi-Provider AI              | Resolution Strategy                    |
| -------------------------- | ------------------------ | ------------------------------ | -------------------------------------- |
| **LLM Provider Interface** | `Arc<dyn LLMProvider>`   | Enhanced `LLMProvider` trait   | Use enhanced trait from multi-provider |
| **Tool Calling**           | Agent tool orchestration | Provider-specific tool calling | Integrate both layers                  |
| **Streaming**              | Real-time agent updates  | Provider streaming support     | Combine for end-to-end streaming       |
| **Error Handling**         | Agent-specific errors    | Provider-specific errors       | Create unified error hierarchy         |
| **Configuration**          | Agent settings           | Provider configurations        | Merge into unified config system       |
| **Async Architecture**     | Agent execution patterns | Provider async patterns        | Align on consistent async patterns     |

### 2.3 Conflicting Approaches

**Configuration Management**:

- **Conflict**: Separate config structures for agent and provider settings
- **Resolution**: Create unified configuration hierarchy with clear separation of concerns

**Error Handling**:

- **Conflict**: Different error type hierarchies
- **Resolution**: Implement unified error system with provider and agent error variants

**State Management**:

- **Conflict**: Agent state vs provider state management
- **Resolution**: Clear separation with agent state managing provider instances

## 3. Merge Strategy

### 3.1 Unified Architecture Design

```rust
// Unified architecture components
pub struct UnifiedAgentSystem {
    // Multi-provider AI layer
    provider_factory: Arc<ProviderFactory>,
    active_provider: Arc<dyn LLMProvider>,

    // Agentic core layer
    agent_core: Arc<dyn AgentCore>,
    task_manager: Arc<TaskManager>,
    state_manager: Arc<RwLock<StateManager>>,

    // Integration layer
    tool_executor: Arc<ToolExecutor>,
    context_manager: Arc<RwLock<ContextManager>>,

    // Configuration
    config: UnifiedConfig,
}
```

### 3.2 Integration Layers

**Layer 1: Provider Foundation**

- Enhanced LLMProvider trait with tool calling and streaming
- ProviderFactory with all supported providers
- Unified error handling and configuration

**Layer 2: Agentic Core**

- AgentCore implementation using provider layer
- Task management and execution loop
- State persistence and recovery

**Layer 3: Integration**

- TUI integration with real-time updates
- CLI mode support
- MCP protocol integration

### 3.3 Configuration Unification

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UnifiedConfig {
    // Provider configuration
    pub llm: LLMConfig,
    pub providers: HashMap<String, ProviderConfig>,

    // Agent configuration
    pub agent: AgentConfig,
    pub task: TaskConfig,
    pub state: StateConfig,

    // Integration configuration
    pub tui: TuiConfig,
    pub cli: CliConfig,
    pub mcp: McpConfig,
}
```

## 4. Implementation Roadmap

### Phase 1: Foundation Merge (Week 1-2)

**Week 1: Provider Foundation**

- [ ] **Enhance LLMProvider Trait** (`src/llm/mod.rs`)

  - Merge provider trait enhancements from multi-provider architecture
  - Add streaming support and health checking methods
  - Implement tool calling capabilities across all providers
  - **Dependencies**: None
  - **Testing**: Unit tests for trait implementations

- [ ] **Implement ProviderFactory** (`src/llm/factory.rs`)
  - Create enhanced provider factory with all supported providers
  - Add provider registration and validation
  - Implement provider health checking
  - **Dependencies**: Enhanced LLMProvider trait
  - **Testing**: Factory creation and provider instantiation tests

**Week 2: Configuration Unification**

- [ ] **Create Unified Configuration** (`src/config/unified.rs`)

  - Merge agent and provider configuration structures
  - Implement configuration validation and migration
  - Add TOML serialization/deserialization
  - **Dependencies**: Provider factory
  - **Testing**: Configuration loading and validation tests

- [ ] **Update Error Handling** (`src/errors/mod.rs`)
  - Create unified error hierarchy
  - Add provider-specific and agent-specific error variants
  - Implement error conversion traits
  - **Dependencies**: Provider implementations
  - **Testing**: Error propagation and conversion tests

### Phase 2: Core Integration (Week 3-4)

**Week 3: Agent Core Implementation**

- [ ] **Implement AgentCore** (`src/agent/core.rs`)

  - Create AgentCore trait implementation
  - Integrate with enhanced provider factory
  - Add task execution and state management
  - **Dependencies**: Unified configuration, provider factory
  - **Testing**: Agent execution tests with mock providers

- [ ] **Implement TaskManager** (`src/agent/task.rs`)
  - Create task lifecycle management
  - Add checkpoint and recovery mechanisms
  - Implement task persistence
  - **Dependencies**: AgentCore, unified configuration
  - **Testing**: Task creation, execution, and recovery tests

**Week 4: Execution Engine**

- [ ] **Implement AgentExecutor** (`src/agent/executor.rs`)

  - Create recursive request loop
  - Add streaming response processing
  - Integrate tool calling with provider layer
  - **Dependencies**: TaskManager, provider streaming
  - **Testing**: Execution loop tests with various providers

- [ ] **Implement StateManager** (`src/agent/state.rs`)
  - Create state persistence and recovery
  - Add conversation history management
  - Implement checkpoint system
  - **Dependencies**: AgentExecutor
  - **Testing**: State persistence and recovery tests

### Phase 3: Provider Implementation (Week 5-6)

**Week 5: Core Providers**

- [ ] **Implement OpenRouter Provider** (`src/llm/providers/openrouter.rs`)

  - Add OpenRouter-specific implementation
  - Implement tool calling and streaming
  - Add error handling and retry logic
  - **Dependencies**: Enhanced LLMProvider trait
  - **Testing**: OpenRouter integration tests

- [ ] **Implement Anthropic Provider** (`src/llm/providers/anthropic.rs`)
  - Add Anthropic-specific implementation
  - Implement Claude tool calling format
  - Add streaming support
  - **Dependencies**: Enhanced LLMProvider trait
  - **Testing**: Anthropic integration tests

**Week 6: Extended Providers**

- [ ] **Implement OpenAI Provider** (`src/llm/providers/openai.rs`)

  - Add OpenAI-specific implementation
  - Implement function calling support
  - Add streaming and error handling
  - **Dependencies**: Enhanced LLMProvider trait
  - **Testing**: OpenAI integration tests

- [ ] **Implement Remaining Providers** (`src/llm/providers/`)
  - Add Gemini, Ollama, and Requesty providers
  - Implement provider-specific features
  - Add comprehensive error handling
  - **Dependencies**: Enhanced LLMProvider trait
  - **Testing**: Provider-specific integration tests

### Phase 4: TUI Integration (Week 7-8)

**Week 7: TUI Component Updates**

- [ ] **Update TUI Components** (`src/tui/components/`)

  - Modify existing components for agent integration
  - Add real-time streaming display
  - Implement task progress indicators
  - **Dependencies**: AgentExecutor, streaming support
  - **Testing**: TUI component tests with mock agent

- [ ] **Implement Agent TUI Mode** (`src/tui/modes/agent.rs`)
  - Create dedicated agent interaction mode
  - Add task creation and monitoring interface
  - Implement streaming response display
  - **Dependencies**: Updated TUI components
  - **Testing**: Agent mode integration tests

**Week 8: Context and Tool Integration**

- [ ] **Implement ContextManager** (`src/agent/context.rs`)

  - Create dynamic context assembly
  - Add workspace tracking and file system integration
  - Implement context window management
  - **Dependencies**: AgentExecutor, TUI integration
  - **Testing**: Context assembly and management tests

- [ ] **Integrate Tool Execution** (`src/agent/tools.rs`)
  - Bridge agent reasoning with existing tool executor
  - Implement approval workflow management
  - Add tool result processing
  - **Dependencies**: ContextManager, existing tool executor
  - **Testing**: Tool integration and approval workflow tests

### Phase 5: Advanced Features (Week 9-10)

**Week 9: CLI and MCP Integration**

- [ ] **Implement CLI Agent Mode** (`src/cli/agent.rs`)

  - Add agent execution to CLI interface
  - Implement non-interactive agent mode
  - Create batch processing capabilities
  - **Dependencies**: AgentCore, unified configuration
  - **Testing**: CLI agent mode tests

- [ ] **Enhance MCP Integration** (`src/mcp/agent.rs`)
  - Integrate agent with MCP protocol
  - Add agent-specific MCP tools
  - Implement MCP server communication for agent tasks
  - **Dependencies**: AgentCore, existing MCP framework
  - **Testing**: MCP agent integration tests

**Week 10: Polish and Documentation**

- [ ] **Performance Optimization**

  - Optimize memory usage for long conversations
  - Implement efficient context window management
  - Add caching for repeated operations
  - **Dependencies**: All previous implementations
  - **Testing**: Performance benchmarks and stress tests

- [ ] **Comprehensive Testing**
  - Create integration tests for complete system
  - Add end-to-end tests with real providers
  - Implement mock provider for testing
  - **Dependencies**: Complete implementation
  - **Testing**: Full test suite execution

## 5. Reference Updates

### 5.1 Documentation Updates Required

**File: `aidocs/autorun-agentic-core-architecture.md`**

- Update integration points to reference unified provider system
- Modify implementation roadmap to reflect merged approach
- Update code examples to use unified configuration

**File: `aidocs/multi-provider-ai-architecture.md`**

- Add agent integration examples
- Update provider implementations to support agent use cases
- Modify configuration examples for unified system

**File: `README.md`**

- Update architecture overview to reflect unified system
- Add agent capabilities to feature list
- Update configuration examples

### 5.2 Code Reference Updates

**Module Structure Updates**:

```
src/
├── agent/                 # New agentic core module
│   ├── mod.rs            # Agent module exports
│   ├── core.rs           # AgentCore implementation
│   ├── task.rs           # TaskManager implementation
│   ├── executor.rs       # AgentExecutor implementation
│   ├── state.rs          # StateManager implementation
│   ├── context.rs        # ContextManager implementation
│   └── tools.rs          # Tool integration bridge
├── llm/                  # Enhanced LLM module
│   ├── mod.rs            # Enhanced LLMProvider trait
│   ├── factory.rs        # Enhanced ProviderFactory
│   ├── streaming.rs      # Streaming utilities
│   └── providers/        # All provider implementations
├── config/               # Enhanced configuration
│   ├── mod.rs            # Configuration module
│   └── unified.rs        # Unified configuration structure
├── tui/                  # Enhanced TUI
│   ├── modes/
│   │   └── agent.rs      # Agent interaction mode
│   └── components/       # Updated components for agent
├── cli/                  # Enhanced CLI
│   └── agent.rs          # CLI agent mode
└── mcp/                  # Enhanced MCP
    └── agent.rs          # MCP agent integration
```

**Configuration File Updates**:

```toml
# autorun.toml - Unified configuration example
[llm]
provider = "anthropic"
model = "claude-3-5-sonnet-20241022"

[llm.providers.anthropic]
api_key = "${ANTHROPIC_API_KEY}"
max_tokens = 4096

[agent]
max_iterations = 50
checkpoint_interval = 10
auto_approve_tools = false

[agent.task]
timeout_seconds = 3600
max_context_tokens = 100000

[tui]
agent_mode_enabled = true
streaming_enabled = true

[cli]
agent_mode_enabled = true
batch_processing = true
```

## 6. Validation Criteria

### 6.1 Functional Validation

**Provider Integration**:

- [ ] All providers (OpenRouter, Anthropic, OpenAI, Gemini, Ollama, Requesty) work with agent
- [ ] Tool calling functions correctly across all providers
- [ ] Streaming responses display properly in TUI and CLI
- [ ] Provider switching works seamlessly during agent execution

**Agent Functionality**:

- [ ] Agent can execute complex multi-step tasks
- [ ] Task persistence and recovery work correctly
- [ ] Context management maintains relevant information
- [ ] Tool approval workflow functions properly

**Integration Compatibility**:

- [ ] TUI interface remains responsive during agent execution
- [ ] CLI mode supports both interactive and batch agent execution
- [ ] MCP protocol integration works with agent tasks
- [ ] Configuration system supports all unified settings

### 6.2 Performance Validation

**Memory Usage**:

- [ ] Long conversations don't cause memory leaks
- [ ] Context window management prevents excessive memory usage
- [ ] Provider switching doesn't accumulate memory

**Response Times**:

- [ ] Agent responses stream in real-time
- [ ] Tool execution doesn't block UI updates
- [ ] Provider health checks complete within reasonable time

**Scalability**:

- [ ] System handles multiple concurrent agent tasks
- [ ] Large file operations don't block agent execution
- [ ] Context assembly scales with workspace size

### 6.3 Reliability Validation

**Error Handling**:

- [ ] Provider failures gracefully degrade
- [ ] Agent errors don't crash the application
- [ ] Recovery mechanisms restore agent state correctly

**State Persistence**:

- [ ] Agent state survives application restarts
- [ ] Conversation history persists correctly
- [ ] Checkpoints enable reliable recovery

**Configuration Robustness**:

- [ ] Invalid configurations provide clear error messages
- [ ] Configuration migration works from existing setups
- [ ] Default values provide working system out-of-box

## 7. Risk Mitigation

### 7.1 Technical Risks

**Integration Complexity**:

- **Risk**: Merging two complex architectures may introduce bugs
- **Mitigation**: Phased implementation with comprehensive testing at each stage

**Performance Degradation**:

- **Risk**: Unified system may be slower than individual components
- **Mitigation**: Performance benchmarking and optimization in final phase

**Configuration Complexity**:

- **Risk**: Unified configuration may be too complex for users
- **Mitigation**: Sensible defaults and clear documentation

### 7.2 Implementation Risks

**Timeline Overrun**:

- **Risk**: 10-week timeline may be insufficient for complete integration
- **Mitigation**: Prioritize core functionality, defer advanced features if needed

**Provider Compatibility**:

- **Risk**: Some providers may not support all required features
- **Mitigation**: Graceful degradation and clear capability documentation

**Breaking Changes**:

- **Risk**: Unified system may break existing functionality
- **Mitigation**: Maintain backward compatibility where possible, provide migration guide

## 8. Success Metrics

### 8.1 Completion Criteria

- [ ] All 6 providers integrated and tested
- [ ] Agent can execute complex coding tasks autonomously
- [ ] TUI and CLI modes support agent functionality
- [ ] Configuration system unified and documented
- [ ] Performance meets or exceeds current system
- [ ] Comprehensive test suite passes
- [ ] Documentation updated and complete

### 8.2 Quality Metrics

- **Test Coverage**: >90% for all new agent modules
- **Performance**: <10% degradation from current system
- **Memory Usage**: <20% increase for equivalent operations
- **Error Rate**: <1% for provider operations
- **Documentation**: Complete API documentation and user guides

## 9. Conclusion

This implementation plan provides a structured approach to merging the AutoRun-RS Agentic Core and Multi-Provider AI architectures into a unified, powerful system. The phased approach ensures stability while building comprehensive capabilities, and the validation criteria ensure the final system meets quality and performance standards.

The merged architecture will provide AutoRun-RS users with a sophisticated agentic coding assistant that can work with multiple AI providers while maintaining the familiar TUI interface and extending CLI capabilities for autonomous operation.
