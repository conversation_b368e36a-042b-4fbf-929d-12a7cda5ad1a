[package]
name = "autorun"
version = "0.1.0"
edition = "2021"
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
description = "High-performance Rust CLI TUI application serving as an agentic coding assistant"
license = "MIT OR Apache-2.0"
default-run = "autorun"

[dependencies]
# TUI and terminal handling
ratatui = { version = "0.29", features = ["crossterm"] }
crossterm = "0.28"
tui-textarea = "0.7"
is-terminal = "0.4"

# Async runtime
tokio = { version = "1.42", features = ["full", "macros", "rt-multi-thread"] }
async-trait = "0.1"

# Concurrent data structures
dashmap = "6.1"

# CLI parsing
clap = { version = "4.5", features = ["derive", "env", "wrap_help"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Error handling
thiserror = "2.0"
anyhow = "1.0"

# Logging and tracing
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "fmt", "json"] }
tracing-log = "0.2"
tui-logger = "0.17.3"
log = "0.4"

# HTTP client for LLM APIs
reqwest = { version = "0.12", features = ["json", "stream"] }

# Async stream utilities
futures-util = "0.3"
bytes = "1.5"

# File system utilities
directories = "5.0"
tempfile = "3.14"
dirs = "5.0"

# Configuration management
config = "0.14"

# Lazy static initialization
once_cell = "1.20"
toml = "0.8"
dotenvy = "0.15"

# UUID generation for sessions
uuid = { version = "1.11", features = ["v4", "serde"] }

# Time handling
chrono = { version = "0.4", features = ["serde"] }

# Log viewer dependencies
colored = "2.1"
notify = "6.1"

# Cryptographic hashing
sha2 = "0.10"

# MCP (Model Context Protocol) client/server
rmcp = { version = "0.1", features = ["client", "server", "transport-io", "transport-child-process"] }

# Regular expressions for pattern matching
regex = "1.11"
rand = "0.8"

# Shell-like string parsing for command arguments
shlex = "1.3"

# File pattern matching and searching
glob = "0.3"
ignore = "0.4"

# Fuzzy matching for completions
fuzzy-matcher = "0.3"

# Path difference calculation
pathdiff = "0.2"

# Template engine for prompt system
tera = "1.19"

# Walking directory trees
walkdir = "2.5"

# YAML support for prompts
serde_yaml = "0.9"

# Tokenization libraries
tiktoken-rs = "0.7.0"
claude-tokenizer = "0.2.0"
sentencepiece = { version = "0.11.3", optional = true }

[features]
default = []
advanced-tokenization = ["sentencepiece"]

[dev-dependencies]
tokio-test = "0.4"
mockall = "0.13"
pretty_assertions = "1.4"

[profile.release]
lto = true
codegen-units = 1
strip = true
opt-level = "z"
