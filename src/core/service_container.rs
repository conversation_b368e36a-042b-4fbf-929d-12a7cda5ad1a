use crate::agent::core::ToolExecutor;
use crate::commands::handler::CommandProcessor;
use crate::config::{registry::ConfigRegistry, Config};
use crate::errors::Result;
use crate::llm::ProviderFactory;
use crate::mcp::client::McpClientManager;
use crate::tools::ToolRegistry;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, info};

/// Service container for dependency injection and lifecycle management
///
/// The ServiceContainer centralizes the creation and management of all major
/// application services, replacing scattered service creation throughout the codebase.
/// It implements the dependency injection pattern to improve testability and
/// reduce coupling between components.
pub struct ServiceContainer {
    /// Provider factory for creating LLM clients
    provider_factory: Arc<ProviderFactory>,

    /// Configuration registry for centralized config management
    config_registry: Arc<ConfigRegistry>,

    /// Tool executor for running local and MCP tools
    tool_executor: Arc<ToolExecutor>,

    /// Command processor for CLI subcommands
    command_processor: Arc<CommandProcessor>,

    /// Tool registry for local tool management
    tool_registry: Arc<ToolRegistry>,

    /// MCP client manager for external tool integration
    mcp_client_manager: Arc<RwLock<McpClientManager>>,

    /// Application configuration
    config: Config,
}

impl ServiceContainer {
    /// Create a new service container with all services initialized
    pub async fn new(config: Config) -> Result<Self> {
        info!("Initializing service container");

        // Initialize core services
        let provider_factory = Arc::new(ProviderFactory::new());
        let config_registry = Arc::new(ConfigRegistry::new());

        debug!("Initializing tool registry");
        let tool_registry = Arc::new(ToolRegistry::new());

        // Register all local tools
        crate::tools::local_tools::register_all_local_tools(&tool_registry)?;
        info!("Registered {} tools", tool_registry.list_tools().len());

        debug!("Initializing MCP client manager");
        let mcp_client_manager = Arc::new(RwLock::new(McpClientManager::new()));

        debug!("Creating tool executor");
        let tool_executor = Arc::new(ToolExecutor::new(
            tool_registry.clone(),
            mcp_client_manager.clone(),
        ));

        debug!("Initializing command processor");
        let command_processor = Arc::new(CommandProcessor::new());

        info!("Service container initialized successfully");

        Ok(Self {
            provider_factory,
            config_registry,
            tool_executor,
            command_processor,
            tool_registry,
            mcp_client_manager,
            config,
        })
    }

    /// Create a new service container with custom config path and working directory
    pub async fn new_with_paths(
        config_path: Option<&std::path::Path>,
        workdir: Option<&std::path::Path>,
    ) -> Result<Self> {
        info!("Loading configuration for service container");
        let config = Config::load_with_override(workdir, config_path).await?;
        Self::new(config).await
    }

    /// Get the provider factory service
    pub fn get_provider_factory(&self) -> Arc<ProviderFactory> {
        self.provider_factory.clone()
    }

    /// Get the configuration registry service
    pub fn get_config_registry(&self) -> Arc<ConfigRegistry> {
        self.config_registry.clone()
    }

    /// Get the tool executor service
    pub fn get_tool_executor(&self) -> Arc<ToolExecutor> {
        self.tool_executor.clone()
    }

    /// Get the command processor service
    pub fn get_command_processor(&self) -> Arc<CommandProcessor> {
        self.command_processor.clone()
    }

    /// Get the tool registry service
    pub fn get_tool_registry(&self) -> Arc<ToolRegistry> {
        self.tool_registry.clone()
    }

    /// Get the MCP client manager service
    pub fn get_mcp_client_manager(&self) -> Arc<RwLock<McpClientManager>> {
        self.mcp_client_manager.clone()
    }

    /// Get the application configuration
    pub fn get_config(&self) -> &Config {
        &self.config
    }

    /// Update the application configuration
    pub fn update_config(&mut self, config: Config) {
        debug!("Updating service container configuration");
        self.config = config;
    }

    /// Perform service lifecycle startup operations
    pub async fn startup(&self) -> Result<()> {
        info!("Starting up service container");

        // Validate configuration
        self.validate_services().await?;

        // Initialize any services that require startup operations
        // This could include warming up caches, establishing connections, etc.

        info!("Service container startup completed");
        Ok(())
    }

    /// Perform service lifecycle shutdown operations
    pub async fn shutdown(&self) -> Result<()> {
        info!("Shutting down service container");

        // Perform graceful shutdown of services
        // This could include closing connections, flushing caches, etc.

        // MCP clients shutdown
        {
            let mut mcp_manager = self.mcp_client_manager.write().await;
            // MCP manager doesn't have explicit shutdown, but we could add it
        }

        info!("Service container shutdown completed");
        Ok(())
    }

    /// Validate that all services are properly configured
    async fn validate_services(&self) -> Result<()> {
        debug!("Validating service configuration");

        // Validate provider factory can handle current configuration
        if let Err(e) = self
            .provider_factory
            .validate_provider(&self.config.llm.provider)
        {
            return Err(crate::errors::AutorunError::Config(format!(
                "Provider validation failed: {}",
                e
            ))
            .into());
        }

        // Validate tool registry has tools registered
        if self.tool_registry.list_tools().is_empty() {
            return Err(crate::errors::AutorunError::Config(
                "No tools registered in tool registry".to_string(),
            )
            .into());
        }

        debug!("Service validation completed successfully");
        Ok(())
    }
}

/// Service trait for components that can be managed by the container
///
/// This trait allows for future extensibility where additional services
/// can be added to the container with consistent lifecycle management.
pub trait Service: Send + Sync {
    /// Get the service name for logging and debugging
    fn name(&self) -> &str;

    /// Initialize the service (called during container startup)
    async fn initialize(&self) -> Result<()> {
        Ok(())
    }

    /// Shutdown the service (called during container shutdown)
    async fn shutdown(&self) -> Result<()> {
        Ok(())
    }
}

impl Default for ServiceContainer {
    /// Create a default service container with minimal configuration
    ///
    /// This is primarily used for testing purposes. In production,
    /// use `new()` or `new_with_paths()` to ensure proper configuration.
    fn default() -> Self {
        // This is a fallback implementation for testing
        // In practice, the async `new()` method should be used
        let provider_factory = Arc::new(ProviderFactory::new());
        let config_registry = Arc::new(ConfigRegistry::new());
        let tool_registry = Arc::new(ToolRegistry::new());
        let mcp_client_manager = Arc::new(RwLock::new(McpClientManager::new()));
        let tool_executor = Arc::new(ToolExecutor::new(
            tool_registry.clone(),
            mcp_client_manager.clone(),
        ));
        let command_processor = Arc::new(CommandProcessor::new());

        // Use minimal default configuration
        let config = Config::default();

        Self {
            provider_factory,
            config_registry,
            tool_executor,
            command_processor,
            tool_registry,
            mcp_client_manager,
            config,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[tokio::test]
    async fn test_service_container_creation() {
        let config = Config::default();
        let container = ServiceContainer::new(config).await;
        assert!(container.is_ok());
    }

    #[test]
    fn test_service_container_default() {
        let container = ServiceContainer::default();
        assert!(!container.get_provider_factory().is_null());
        assert!(!container.get_config_registry().is_null());
        assert!(!container.get_tool_executor().is_null());
    }

    #[tokio::test]
    async fn test_service_container_lifecycle() {
        let config = Config::default();
        let container = ServiceContainer::new(config).await.unwrap();

        // Test startup
        assert!(container.startup().await.is_ok());

        // Test shutdown
        assert!(container.shutdown().await.is_ok());
    }

    #[tokio::test]
    async fn test_service_validation() {
        let config = Config::default();
        let container = ServiceContainer::new(config).await.unwrap();

        // Validation should pass with default config
        assert!(container.validate_services().await.is_ok());
    }
}
