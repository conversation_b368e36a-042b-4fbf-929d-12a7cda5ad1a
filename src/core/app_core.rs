use crate::agent::core::{<PERSON><PERSON><PERSON>, Agent<PERSON>pdate, ToolExecutor};
use crate::commands::{CommandRegistry, CommandResponse};
use crate::config::{Config, LLMConfig};
use crate::errors::Result;
use crate::llm::{LLMProvider, ProviderFactory};
use crate::mcp::client::McpClientManager;
use crate::tools::{ExecutionContext, ToolRegistry};
use std::path::PathBuf;
use std::sync::Arc;
use tokio::sync::{mpsc, RwLock};
use tracing::{error, info};
use uuid::Uuid;

#[derive(Debug, Clone)]
pub enum AppCoreMessage {
    UserInput(String),
    AgentResponse(String),
    Error(String),
    ProcessingStatus(String),
    Exit,
    // Widget-related messages
    WidgetGeneration(crate::agent::core::WidgetGenerationRequest),
    WidgetUpdate(crate::agent::core::WidgetUpdateEvent),
}

/// Core application logic separated from UI concerns
pub struct AppCore {
    pub config: Config,
    pub messages: Vec<String>,
    pub agent_core: Option<AgentCore>,
    pub command_registry: Arc<CommandRegistry>,
    pub tool_executor: Arc<ToolExecutor>,
    pub tool_registry: Arc<ToolRegistry>,
    pub mcp_client_manager: Arc<RwLock<McpClientManager>>,
    pub is_processing: bool,
    pub processing_status: String,
    workdir: PathBuf,
}

impl AppCore {
    pub async fn new(
        config: Config,
        workdir: Option<PathBuf>,
        tool_registry: Arc<ToolRegistry>,
        mcp_client_manager: Arc<RwLock<McpClientManager>>,
    ) -> Result<Self> {
        let workdir = workdir.unwrap_or_else(|| std::env::current_dir().unwrap());

        // Create tool executor
        let tool_executor = Arc::new(ToolExecutor::new(
            tool_registry.clone(),
            mcp_client_manager.clone(),
        ));

        // Create command registry
        let command_registry = Arc::new(CommandRegistry::new());

        Ok(Self {
            config,
            messages: Vec::new(),
            agent_core: None,
            command_registry,
            tool_executor,
            tool_registry,
            mcp_client_manager,
            is_processing: false,
            processing_status: String::new(),
            workdir,
        })
    }

    /// Initialize the agent core with the configured LLM provider
    pub async fn initialize_agent(&mut self) -> Result<()> {
        info!(
            "Initializing agent with provider: {}",
            self.config.llm.provider
        );

        // Create LLM provider using factory
        let factory = ProviderFactory::new();
        let llm_provider = factory.create_provider(&self.config.llm).await?;

        // Create execution context
        let execution_context = ExecutionContext::with_config(
            self.workdir.clone(),
            self.config.clone(),
            Uuid::new_v4().to_string(),
        );

        // Create agent core with proper tool support
        let agent_core = Self::create_agent_core_with_tool_support(
            llm_provider,
            self.tool_executor.clone(),
            execution_context,
            &self.config.llm.provider,
        );

        self.agent_core = Some(agent_core);
        Ok(())
    }

    /// Create an AgentCore with proper tool support detection
    pub fn create_agent_core_with_tool_support(
        llm_provider: Arc<dyn LLMProvider>,
        tool_executor: Arc<ToolExecutor>,
        execution_context: ExecutionContext,
        _provider_name: &str,
    ) -> AgentCore {
        use crate::agent::AgentCoreFactory;

        // Use the centralized factory to create AgentCore with tool support
        let factory = AgentCoreFactory::new();
        factory.create_agent_core_from_provider(llm_provider, tool_executor, execution_context)
    }

    /// Process a user message
    pub async fn process_user_message(
        &mut self,
        message: String,
        tx: mpsc::Sender<AppCoreMessage>,
    ) -> Result<()> {
        if let Some(agent_core) = &mut self.agent_core {
            self.messages.push(format!("You: {}", message));
            self.is_processing = true;
            self.processing_status = "Processing...".to_string();

            // Send the message to the agent
            agent_core.add_user_message(message).await?;

            // Run the agent loop
            match agent_core.agent_loop().await {
                Ok(()) => {
                    let _ = tx
                        .send(AppCoreMessage::AgentResponse(
                            "Agent completed processing".to_string(),
                        ))
                        .await;
                }
                Err(e) => {
                    let _ = tx
                        .send(AppCoreMessage::Error(format!("Agent error: {}", e)))
                        .await;
                }
            }
        }
        Ok(())
    }

    /// Handle a message from the agent
    pub fn handle_agent_message(&mut self, message: AppCoreMessage) {
        match message {
            AppCoreMessage::AgentResponse(msg) => {
                self.messages.push(format!("Assistant: {}", msg));
                self.is_processing = false;
                self.processing_status.clear();
            }
            AppCoreMessage::Error(err) => {
                self.messages.push(format!("Error: {}", err));
                self.is_processing = false;
                self.processing_status.clear();
            }
            AppCoreMessage::ProcessingStatus(status) => {
                self.processing_status = status;
            }
            _ => {}
        }
    }

    /// Override the LLM configuration
    pub fn override_llm_config(&mut self, llm_config: LLMConfig) -> Result<()> {
        self.config.llm = llm_config;
        Ok(())
    }

    /// Get the current messages
    pub fn get_messages(&self) -> &[String] {
        &self.messages
    }

    /// Check if the agent is processing
    pub fn is_processing(&self) -> bool {
        self.is_processing
    }

    /// Get the processing status
    pub fn get_processing_status(&self) -> &str {
        &self.processing_status
    }
}
