use anyhow::Result;
use chrono::Utc;
use clap::Parser;
use std::io::Write;
use std::path::PathBuf;
use std::sync::Arc;
use tracing::{debug, error, info, Level};
use tracing_subscriber::{fmt, layer::SubscriberExt, util::SubscriberInitExt, EnvFilter, Layer};
use uuid::Uuid;

use autorun::app::App;
use autorun::cli::{Cli, Commands, ConfigCommands, McpCommands, OutputFormat};
use autorun::commands::handler::{CommandProcessor, CommandResponse};
use autorun::config::{parse_model_string, Config, LLMConfig};
use autorun::llm::create_llm_provider;

#[tokio::main]
async fn main() -> Result<()> {
    // Load .env file if it exists (ignore errors if file doesn't exist)
    let _ = dotenvy::dotenv();

    let cli = Cli::parse();

    // Validate CLI arguments
    if let Err(e) = cli.validate() {
        eprintln!("Error: {}", e);
        std::process::exit(1);
    }

    // Initialize logging with tracing_subscriber and optionally tui-logger
    let filter_level = if cli.debug { Level::DEBUG } else { Level::INFO };

    // Create logs directory if it doesn't exist
    let logs_dir = PathBuf::from("logs");
    if !logs_dir.exists() {
        std::fs::create_dir_all(&logs_dir)?;
    }

    // Create timestamped log file
    let timestamp = Utc::now().format("%Y%m%d-%H%M%S");
    let log_filename = format!("autorun-{}.log", timestamp);
    let log_file_path = logs_dir.join(&log_filename);

    // Initialize tracing subscriber with tui-logger and file outputs
    let file_appender = std::fs::OpenOptions::new()
        .create(true)
        .append(true)
        .open(&log_file_path)?;

    // Create EnvFilter for console/tui output
    let tui_env_filter = EnvFilter::from_default_env()
        .add_directive(format!("autorun={}", filter_level.as_str().to_lowercase()).parse()?)
        .add_directive(format!("rmcp={}", filter_level.as_str().to_lowercase()).parse()?)
        .add_directive(format!("reqwest={}", if cli.debug { "debug" } else { "info" }).parse()?)
        .add_directive(format!("h2={}", if cli.debug { "debug" } else { "warn" }).parse()?)
        .add_directive(format!("hyper={}", if cli.debug { "debug" } else { "warn" }).parse()?)
        .add_directive(format!("tokio={}", if cli.debug { "debug" } else { "warn" }).parse()?);

    // Create EnvFilter for file output
    let file_env_filter = EnvFilter::from_default_env()
        .add_directive(format!("autorun={}", filter_level.as_str().to_lowercase()).parse()?)
        .add_directive(format!("rmcp={}", filter_level.as_str().to_lowercase()).parse()?)
        .add_directive(format!("reqwest={}", if cli.debug { "debug" } else { "info" }).parse()?)
        .add_directive(format!("h2={}", if cli.debug { "debug" } else { "warn" }).parse()?)
        .add_directive(format!("hyper={}", if cli.debug { "debug" } else { "warn" }).parse()?)
        .add_directive(format!("tokio={}", if cli.debug { "debug" } else { "warn" }).parse()?);

    // Console layer (only enabled for non-interactive mode or when explicitly forced)
    // In interactive TUI mode, console output would break the terminal UI
    let enable_console =
        cli.is_non_interactive() || std::env::var("AUTORUN_FORCE_CONSOLE_LOG").is_ok();

    let console_layer = if enable_console {
        // Use stderr for non-interactive mode
        Some(
            fmt::layer()
                .with_writer(std::io::stderr)
                .with_ansi(true)
                .with_target(true)
                .with_thread_ids(false)
                .with_line_number(true)
                .with_file(false)
                .compact()
                .with_filter(tui_env_filter),
        )
    } else {
        // In TUI mode, don't create a console layer at all to prevent UI corruption
        None
    };

    // File layer (always enabled)
    let file_layer = fmt::layer()
        .with_writer(file_appender)
        .with_ansi(false)
        .with_target(true)
        .with_thread_ids(true)
        .with_line_number(true)
        .with_file(true)
        .json()
        .with_filter(file_env_filter);

    // Setup logging layers
    let mut layers = Vec::new();

    // Add file layer (always present)
    layers.push(file_layer.boxed());

    // Add console layer if enabled
    if let Some(console) = console_layer {
        layers.push(console.boxed());
    }

    // Handle logging initialization
    if cli.show_logs && cli.is_interactive() {
        // For show-logs mode, initialize tui-logger FIRST before any other loggers
        tui_logger::init_logger(if cli.debug {
            log::LevelFilter::Debug
        } else {
            log::LevelFilter::Info
        })
        .unwrap();
        tui_logger::set_default_level(if cli.debug {
            log::LevelFilter::Debug
        } else {
            log::LevelFilter::Info
        });

        // Initialize tracing-log bridge to forward tracing events to tui-logger
        tracing_log::LogTracer::init().unwrap_or_else(|_| {
            // If LogTracer fails, we'll continue but warn the user
            eprintln!("Warning: Could not bridge tracing to tui-logger");
        });

        // CRITICAL: We still need tracing-subscriber for file logging
        // But we can't call .init() again as tui-logger owns the global logger
        // Instead, we'll set up tracing but NOT initialize it globally
        let subscriber = tracing_subscriber::registry().with(layers);

        // Set as global default using try_init to avoid conflicts
        if let Err(e) = tracing::subscriber::set_global_default(subscriber) {
            eprintln!("Warning: Could not set tracing as global default: {:?}", e);
        }

        // Test both systems
        log::info!("TUI Logger initialized - logs will appear in panel");
        tracing::info!("Tracing system active - logs will go to file and panel");
    } else {
        // Normal initialization when tui-logger is not used
        tracing_subscriber::registry().with(layers).init();
    }

    // Now all logging uses tracing macros - they will be bridged to tui-logger when needed
    info!("Starting AutoRun CLI");
    info!("Log file created: {}", log_file_path.display());
    info!(
        "Console logging: {}",
        if enable_console {
            "enabled"
        } else {
            "disabled (use log viewer or set AUTORUN_FORCE_CONSOLE_LOG=1)"
        }
    );
    info!(
        "CLI arguments: debug={}, print={}, prompt={:?}",
        cli.debug, cli.print, cli.prompt
    );

    if cli.show_logs && cli.is_interactive() {
        info!("TUI log panel enabled - logs will appear in live panel");
    }

    // Handle subcommands first
    let result = if let Some(command) = &cli.command {
        handle_subcommand(command).await
    } else {
        // Setup panic handler to restore terminal on panic for TUI mode
        if cli.is_interactive() {
            setup_panic_handler();
        }

        // Route to appropriate mode
        if cli.is_non_interactive() {
            handle_non_interactive_mode(&cli).await
        } else {
            handle_interactive_mode(&cli).await
        }
    };

    // Ensure all logs are flushed before exiting
    info!("Shutting down AutoRun CLI");

    result
}

fn setup_panic_handler() {
    let original_panic = std::panic::take_hook();
    std::panic::set_hook(Box::new(move |panic_info| {
        // Try to restore terminal
        let _ = crossterm::terminal::disable_raw_mode();
        let _ = crossterm::execute!(std::io::stdout(), crossterm::terminal::LeaveAlternateScreen);

        // Call the original panic handler
        original_panic(panic_info);
    }));
}

async fn handle_subcommand(command: &Commands) -> Result<()> {
    // Create service container with minimal configuration for subcommands
    let service_container = autorun::core::ServiceContainer::new_with_paths(None, None).await?;
    let processor = service_container.get_command_processor();

    match processor.process(command).await? {
        CommandResponse::Success(Some(output)) => {
            println!("{}", output);
        }
        CommandResponse::Success(None) => {
            // Command succeeded with no output
        }
        CommandResponse::Error(error) => {
            eprintln!("Error: {}", error);
            std::process::exit(1);
        }
    }

    Ok(())
}

async fn handle_non_interactive_mode(cli: &Cli) -> Result<()> {
    info!("Running in non-interactive mode");

    if let Some(prompt) = &cli.prompt {
        // Log user input
        info!("User prompt: {}", prompt);

        // Parse model to determine provider and add to output
        let (provider, model) = parse_model_string(&cli.model);
        info!("Using model: {} (Provider: {})", model, provider);

        // Create service container with configuration
        let mut service_container = autorun::core::ServiceContainer::new_with_paths(
            cli.config.as_deref(),
            cli.workdir.as_deref(),
        )
        .await?;

        // Apply CLI model override if different from default
        if cli.model != "claude-3.5-sonnet" {
            info!(
                "CLI model override: {} -> {}/{}",
                cli.model, provider, model
            );
            let mut config = service_container.get_config().clone();
            config.llm.provider = provider.clone();
            config.llm.model = model.clone();
            service_container.update_config(config);
        }

        let config = service_container.get_config();
        info!(
            "Final LLM config: provider={}, model={}, has_api_key={}",
            config.llm.provider,
            config.llm.model,
            config.llm.has_api_key()
        );

        // Get services from container
        let provider_factory = service_container.get_provider_factory();
        let tool_executor = service_container.get_tool_executor();

        // Create LLM provider using factory
        let llm_provider = provider_factory.create_provider(&config.llm).await?;

        // Store provider name before moving config
        let provider_name = config.llm.provider.clone();

        // Create execution context with FULL permissions for non-interactive mode
        let working_dir = cli
            .workdir
            .as_ref()
            .map(|p| p.clone())
            .unwrap_or_else(|| std::env::current_dir().unwrap());
        let execution_context = autorun::tools::ExecutionContext::with_config(
            working_dir,
            config.clone(),
            Uuid::new_v4().to_string(),
        )
        .with_permissions(autorun::tools::context::Permission {
            read: true,
            write: true,
            execute: true, // Allow command execution in non-interactive mode
            network: true, // Allow network access in non-interactive mode
            system: true,  // Allow system operations in non-interactive mode
        });

        // Create agent core with proper tool support using AgentCoreFactory
        use autorun::agent::AgentCoreFactory;
        let factory = AgentCoreFactory::new();
        let mut agent_core =
            factory.create_agent_core_from_provider(llm_provider, tool_executor, execution_context);

        // Add the user's prompt as a message
        agent_core.add_user_message(prompt.clone()).await?;

        // Run the agent loop to process the message
        info!("Running agent loop for non-interactive mode...");

        // Keep running agent loop until we get a text response
        let mut response = String::new();
        let max_iterations = 10; // Prevent infinite loops
        let mut iterations = 0;

        while iterations < max_iterations {
            iterations += 1;
            info!("Agent loop iteration {}", iterations);

            match agent_core.agent_loop().await {
                Ok(()) => {
                    // Check if we have a text response
                    if let Some(last_response) = agent_core.get_last_assistant_response() {
                        if !last_response.is_empty() {
                            response = last_response;
                            break;
                        }
                    }

                    // Check if we need to continue (e.g., after tool execution)
                    if agent_core.needs_continuation() {
                        info!("Tool execution completed, continuing for final response...");
                        // Continue to next iteration
                    } else {
                        // No continuation needed, we're done
                        break;
                    }
                }
                Err(e) => {
                    error!("Agent loop failed: {}", e);
                    return Err(e.into());
                }
            }
        }

        if response.is_empty() && iterations >= max_iterations {
            response = "Failed to get response after maximum iterations".to_string();
        }

        info!("LLM response received: {} characters", response.len());
        if cli.debug {
            debug!("LLM response content: {}", response);
        }

        match cli.output_format {
            OutputFormat::Text => {
                println!("{}", response);
            }
            OutputFormat::Json => {
                let json_response = serde_json::json!({
                    "response": response,
                    "model": config.llm.model,
                    "provider": config.llm.provider
                });
                println!("{}", serde_json::to_string_pretty(&json_response)?);
            }
            OutputFormat::StreamJson => {
                let stream_response = serde_json::json!({
                    "type": "response",
                    "content": response,
                    "model": config.llm.model,
                    "provider": config.llm.provider
                });
                println!("{}", serde_json::to_string(&stream_response)?);
            }
        }
    } else {
        eprintln!("Error: No prompt provided for non-interactive mode");
        std::process::exit(1);
    }

    Ok(())
}

async fn handle_interactive_mode(cli: &Cli) -> Result<()> {
    info!("Running in interactive TUI mode");
    info!("Config file: {:?}", cli.config);
    info!("Working directory: {:?}", cli.workdir);

    // Create config override from CLI if model is not default
    let mut app = if cli.model != "claude-3.5-sonnet" {
        // Parse model string to determine provider and model
        let (provider, model) = parse_model_string(&cli.model);
        let llm_override = LLMConfig {
            provider,
            model,
            api_key: None,  // Will be determined by provider defaults
            base_url: None, // Will be determined by provider defaults
            temperature: Some(0.7),
            max_tokens: Some(4096),
        };

        App::new_with_llm_override_and_options(
            cli.config.clone(),
            cli.workdir.clone(),
            llm_override,
            cli.show_logs,
        )
        .await?
    } else {
        App::new_with_options(cli.config.clone(), cli.workdir.clone(), cli.show_logs).await?
    };

    let result = app.run().await;

    // Reset panic handler
    let _ = std::panic::take_hook();

    Ok(result?)
}

/// A writer that discards all output (like /dev/null)
struct NullWriter;

impl Write for NullWriter {
    fn write(&mut self, buf: &[u8]) -> std::io::Result<usize> {
        Ok(buf.len())
    }

    fn flush(&mut self) -> std::io::Result<()> {
        Ok(())
    }
}

impl tracing_subscriber::fmt::MakeWriter<'_> for NullWriter {
    type Writer = NullWriter;

    fn make_writer(&self) -> Self::Writer {
        NullWriter
    }
}
