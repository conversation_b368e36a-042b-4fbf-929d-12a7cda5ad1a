//! Advanced session management system
//! 
//! This module provides comprehensive session lifecycle management
//! with context serialization, backup, and search capabilities.

pub mod session_controller;
pub mod context_serializer;
pub mod backup_manager;
pub mod session_metadata;

// Re-export main types
pub use session_controller::{
    SessionController, SessionInstance, SessionMetadata, SessionContext,
    SessionState, SessionStats, ChatMessage, MessageRole, ConversationThread,
    FileContext, ToolExecution, ToolResult, SessionCategory, SessionStatus,
};
pub use context_serializer::{ContextSerializer, SerializationFormat, CompressionLevel};
pub use backup_manager::{BackupManager, BackupStrategy, BackupMetadata};
pub use session_metadata::{SessionMetadataStore, SessionIndex, SearchQuery};

use crate::errors::Result;
use uuid::Uuid;

/// Initialize the session management system
pub async fn initialize_session_system() -> Result<SessionController> {
    let controller = SessionController::new();
    
    // TODO: Load existing sessions from storage
    // TODO: Initialize session index
    // TODO: Set up automatic backup schedule
    
    Ok(controller)
}

/// Session event for real-time updates
#[derive(Debug, Clone)]
pub enum SessionEvent {
    /// New session created
    SessionCreated {
        session_id: Uuid,
        name: String,
    },
    
    /// Session loaded
    SessionLoaded {
        session_id: Uuid,
        name: String,
    },
    
    /// Session saved
    SessionSaved {
        session_id: Uuid,
        name: String,
    },
    
    /// Session deleted
    SessionDeleted {
        session_id: Uuid,
        name: String,
    },
    
    /// Message added to session
    MessageAdded {
        session_id: Uuid,
        message_id: Uuid,
        role: MessageRole,
    },
    
    /// Tool executed in session
    ToolExecuted {
        session_id: Uuid,
        tool_name: String,
        success: bool,
    },
    
    /// Session archived
    SessionArchived {
        session_id: Uuid,
        name: String,
    },
}

/// Session query for advanced search
#[derive(Debug, Clone)]
pub struct SessionQuery {
    /// Text search in session content
    pub text_search: Option<String>,
    
    /// Filter by tags
    pub tags: Option<Vec<String>>,
    
    /// Filter by category
    pub category: Option<SessionCategory>,
    
    /// Filter by date range
    pub date_range: Option<(chrono::DateTime<chrono::Utc>, chrono::DateTime<chrono::Utc>)>,
    
    /// Filter by workspace
    pub workspace: Option<String>,
    
    /// Minimum message count
    pub min_messages: Option<u32>,
    
    /// Sort order
    pub sort_by: SortField,
    
    /// Result limit
    pub limit: Option<usize>,
}

/// Session sort fields
#[derive(Debug, Clone)]
pub enum SortField {
    Created,
    LastModified,
    LastAccessed,
    MessageCount,
    Name,
    Relevance,
}

/// Session analytics data
#[derive(Debug, Clone)]
pub struct SessionAnalytics {
    /// Total number of sessions
    pub total_sessions: u32,
    
    /// Active sessions
    pub active_sessions: u32,
    
    /// Average session duration
    pub avg_session_duration: chrono::Duration,
    
    /// Most used commands
    pub popular_commands: Vec<(String, u32)>,
    
    /// File types accessed
    pub file_type_stats: std::collections::HashMap<String, u32>,
    
    /// User productivity metrics
    pub productivity_metrics: ProductivityMetrics,
}

/// Productivity metrics
#[derive(Debug, Clone)]
pub struct ProductivityMetrics {
    /// Messages per session
    pub avg_messages_per_session: f64,
    
    /// Tools used per session
    pub avg_tools_per_session: f64,
    
    /// Success rate
    pub success_rate: f64,
    
    /// Time to completion for tasks
    pub avg_completion_time: chrono::Duration,
}