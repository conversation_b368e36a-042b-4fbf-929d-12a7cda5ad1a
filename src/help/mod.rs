//! Help and documentation system
//! 
//! This module provides dynamic help content generation,
//! contextual assistance, and documentation management.

pub mod help_provider;
pub mod command_documentation;
pub mod contextual_help;

// Re-export main types
pub use help_provider::{
    HelpProvider, HelpContext, HelpResponse, HelpMetadata, HelpSuggestion,
    RelatedCommand, HelpExample, ContextualTip, ExternalLink, OnboardingStep,
};
pub use command_documentation::{CommandDocumentation, CommandHelp, HelpCategory};
pub use contextual_help::{ContextualHelp, ContextAnalyzer, HelpPersonalization};

use crate::errors::Result;
use crate::ui::enhanced::state::UiMode;

/// Initialize the help system
pub async fn initialize_help_system() -> Result<HelpProvider> {
    let provider = HelpProvider::new();
    
    // TODO: Load documentation content
    // TODO: Initialize context analyzer
    // TODO: Set up help caching
    
    Ok(provider)
}

/// Help request with full context
#[derive(Debug, Clone)]
pub struct HelpRequest {
    /// What the user is asking about
    pub query: String,
    
    /// Current context
    pub context: HelpContext,
    
    /// Requested help type
    pub help_type: HelpType,
    
    /// User preferences
    pub preferences: HelpPreferences,
}

/// Type of help requested
#[derive(Debug, Clone)]
pub enum HelpType {
    /// General overview
    General,
    
    /// Specific command help
    Command(String),
    
    /// Error explanation
    Error(String),
    
    /// Feature tutorial
    Tutorial(String),
    
    /// Troubleshooting
    Troubleshooting,
    
    /// Best practices
    BestPractices,
}

/// User help preferences
#[derive(Debug, Clone)]
pub struct HelpPreferences {
    /// Preferred detail level
    pub detail_level: DetailLevel,
    
    /// Show examples
    pub show_examples: bool,
    
    /// Show related commands
    pub show_related: bool,
    
    /// Include external links
    pub include_links: bool,
    
    /// Preferred format
    pub format: HelpFormat,
}

/// Help detail level
#[derive(Debug, Clone)]
pub enum DetailLevel {
    Brief,
    Standard,
    Detailed,
    Comprehensive,
}

/// Help format
#[derive(Debug, Clone)]
pub enum HelpFormat {
    Text,
    Markdown,
    Interactive,
    Visual,
}

/// Help analytics for improvement
#[derive(Debug, Clone)]
pub struct HelpAnalytics {
    /// Most requested help topics
    pub popular_topics: Vec<(String, u32)>,
    
    /// User satisfaction ratings
    pub satisfaction_scores: Vec<f32>,
    
    /// Common help patterns
    pub help_patterns: Vec<HelpPattern>,
    
    /// Performance metrics
    pub performance: HelpPerformanceMetrics,
}

/// Help usage pattern
#[derive(Debug, Clone)]
pub struct HelpPattern {
    /// Pattern description
    pub pattern: String,
    
    /// Frequency
    pub frequency: u32,
    
    /// Success rate
    pub success_rate: f32,
    
    /// Associated user actions
    pub user_actions: Vec<String>,
}

/// Help system performance metrics
#[derive(Debug, Clone)]
pub struct HelpPerformanceMetrics {
    /// Average response time
    pub avg_response_time_ms: f64,
    
    /// Cache hit ratio
    pub cache_hit_ratio: f32,
    
    /// Content freshness
    pub content_freshness: f32,
    
    /// User engagement time
    pub avg_engagement_time: chrono::Duration,
}