use crate::errors::{AutorunError, Result};
use crate::config::Config;
use crate::ui::enhanced::state::UiMode;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio::sync::mpsc;
use tracing::{info, warn, error};

/// Configuration command engine for TUI `:` commands
/// <PERSON>les runtime configuration modification and UI mode switching
#[derive(Debug)]
pub struct ConfigEngine {
    /// Current configuration state
    config: Config,
    
    /// Runtime configuration manager
    runtime_config: RuntimeConfigManager,
    
    /// Mode manager for UI mode switching  
    mode_manager: ModeManager,
    
    /// Theme manager for theme switching
    theme_manager: ThemeManager,
    
    /// Session manager for session operations
    session_manager: SessionManager,
    
    /// Help system
    help_system: HelpSystem,
    
    /// Event channel for notifying UI of changes
    ui_event_tx: Option<mpsc::UnboundedSender<ConfigEvent>>,
}

/// Events that can be sent to the UI for immediate updates
#[derive(Debug, Clone)]
pub enum ConfigEvent {
    /// Mode changed
    ModeChanged { new_mode: UiMode, old_mode: UiMode },
    
    /// Theme changed
    ThemeChanged { theme_name: String },
    
    /// Configuration setting changed
    SettingChanged { key: String, value: String },
    
    /// Provider changed
    ProviderChanged { provider: String },
    
    /// Workspace changed
    WorkspaceChanged { path: String },
    
    /// Session operation completed
    SessionOperation { action: String, result: String },
    
    /// Log level changed
    LogLevelChanged { level: String },
    
    /// Configuration error
    Error { message: String },
}

/// Result of executing a configuration command
#[derive(Debug, Clone)]
pub struct ConfigCommandResult {
    /// Success status
    pub success: bool,
    
    /// Response message
    pub message: String,
    
    /// Optional data payload
    pub data: Option<serde_json::Value>,
    
    /// Whether UI refresh is needed
    pub needs_refresh: bool,
}

impl ConfigEngine {
    /// Create a new configuration engine
    pub fn new(config: Config) -> Self {
        Self {
            runtime_config: RuntimeConfigManager::new(),
            mode_manager: ModeManager::new(),
            theme_manager: ThemeManager::new(),
            session_manager: SessionManager::new(),
            help_system: HelpSystem::new(),
            ui_event_tx: None,
            config,
        }
    }
    
    /// Set the UI event channel for immediate updates
    pub fn set_ui_channel(&mut self, tx: mpsc::UnboundedSender<ConfigEvent>) {
        self.ui_event_tx = Some(tx);
    }
    
    /// Execute a configuration command
    pub async fn execute_command(&mut self, command: &str) -> Result<ConfigCommandResult> {
        let trimmed = command.trim();
        
        if !trimmed.starts_with(':') {
            return Err(AutorunError::CommandParse(
                "Configuration commands must start with ':'".to_string()
            ));
        }
        
        let command = &trimmed[1..]; // Remove the ':'
        let parts: Vec<&str> = command.split_whitespace().collect();
        
        if parts.is_empty() {
            return Ok(ConfigCommandResult {
                success: false,
                message: "Empty command".to_string(),
                data: None,
                needs_refresh: false,
            });
        }
        
        match parts[0] {
            "mode" => self.handle_mode_command(&parts[1..]).await,
            "set" => self.handle_set_command(&parts[1..]).await,
            "theme" => self.handle_theme_command(&parts[1..]).await,
            "provider" => self.handle_provider_command(&parts[1..]).await,
            "workspace" => self.handle_workspace_command(&parts[1..]).await,
            "session" => self.handle_session_command(&parts[1..]).await,
            "config" => self.handle_config_command(&parts[1..]).await,
            "log" => self.handle_log_command(&parts[1..]).await,
            "help" => self.handle_help_command(&parts[1..]).await,
            "exit" => self.handle_exit_command().await,
            "clear" => self.handle_clear_command().await,
            "save" => self.handle_save_command(&parts[1..]).await,
            "load" => self.handle_load_command(&parts[1..]).await,
            "export" => self.handle_export_command(&parts[1..]).await,
            _ => Ok(ConfigCommandResult {
                success: false,
                message: format!("Unknown command: {}", parts[0]),
                data: None,
                needs_refresh: false,
            }),
        }
    }
    
    /// Handle mode switching command
    async fn handle_mode_command(&mut self, args: &[&str]) -> Result<ConfigCommandResult> {
        if args.is_empty() {
            return Ok(ConfigCommandResult {
                success: false,
                message: "Usage: :mode <mode> (normal|vim|debug)".to_string(),
                data: None,
                needs_refresh: false,
            });
        }
        
        let mode = match args[0] {
            "normal" => UiMode::Normal,
            "vim" => UiMode::Vim,
            "debug" => UiMode::Debug,
            _ => {
                return Ok(ConfigCommandResult {
                    success: false,
                    message: format!("Unknown mode: {}. Available: normal, vim, debug", args[0]),
                    data: None,
                    needs_refresh: false,
                });
            }
        };
        
        let old_mode = self.mode_manager.current_mode();
        self.mode_manager.switch_mode(mode.clone()).await?;
        
        // Notify UI of mode change
        if let Some(ref tx) = self.ui_event_tx {
            let _ = tx.send(ConfigEvent::ModeChanged { 
                new_mode: mode.clone(), 
                old_mode 
            });
        }
        
        Ok(ConfigCommandResult {
            success: true,
            message: format!("Switched to {} mode", args[0]),
            data: None,
            needs_refresh: true,
        })
    }
    
    /// Handle setting modification command
    async fn handle_set_command(&mut self, args: &[&str]) -> Result<ConfigCommandResult> {
        if args.is_empty() {
            return Ok(ConfigCommandResult {
                success: false,
                message: "Usage: :set <setting>=<value>".to_string(),
                data: None,
                needs_refresh: false,
            });
        }
        
        let setting_arg = args.join(" ");
        let parts: Vec<&str> = setting_arg.splitn(2, '=').collect();
        
        if parts.len() != 2 {
            return Ok(ConfigCommandResult {
                success: false,
                message: "Usage: :set <setting>=<value>".to_string(),
                data: None,
                needs_refresh: false,
            });
        }
        
        let key = parts[0].trim();
        let value = parts[1].trim();
        
        // Validate and apply the setting
        match self.runtime_config.set_setting(key, value).await {
            Ok(()) => {
                // Notify UI of setting change
                if let Some(ref tx) = self.ui_event_tx {
                    let _ = tx.send(ConfigEvent::SettingChanged { 
                        key: key.to_string(), 
                        value: value.to_string() 
                    });
                }
                
                Ok(ConfigCommandResult {
                    success: true,
                    message: format!("Set {} = {}", key, value),
                    data: None,
                    needs_refresh: false,
                })
            }
            Err(e) => Ok(ConfigCommandResult {
                success: false,
                message: format!("Failed to set {}: {}", key, e),
                data: None,
                needs_refresh: false,
            }),
        }
    }
    
    /// Handle theme switching command
    async fn handle_theme_command(&mut self, args: &[&str]) -> Result<ConfigCommandResult> {
        if args.is_empty() {
            // List available themes
            let themes = self.theme_manager.list_themes().await?;
            let current = self.theme_manager.current_theme();
            
            let mut message = format!("Current theme: {}\nAvailable themes:\n", current);
            for theme in themes {
                message.push_str(&format!("  {}\n", theme));
            }
            
            return Ok(ConfigCommandResult {
                success: true,
                message,
                data: None,
                needs_refresh: false,
            });
        }
        
        let theme_name = args[0];
        
        match self.theme_manager.switch_theme(theme_name).await {
            Ok(()) => {
                // Notify UI of theme change
                if let Some(ref tx) = self.ui_event_tx {
                    let _ = tx.send(ConfigEvent::ThemeChanged { 
                        theme_name: theme_name.to_string() 
                    });
                }
                
                Ok(ConfigCommandResult {
                    success: true,
                    message: format!("Switched to theme: {}", theme_name),
                    data: None,
                    needs_refresh: true,
                })
            }
            Err(e) => Ok(ConfigCommandResult {
                success: false,
                message: format!("Failed to switch theme: {}", e),
                data: None,
                needs_refresh: false,
            }),
        }
    }
    
    /// Handle provider switching command
    async fn handle_provider_command(&mut self, args: &[&str]) -> Result<ConfigCommandResult> {
        if args.is_empty() {
            return Ok(ConfigCommandResult {
                success: false,
                message: "Usage: :provider <provider_name>".to_string(),
                data: None,
                needs_refresh: false,
            });
        }
        
        let provider = args[0];
        
        // Validate provider availability
        let available_providers = vec!["anthropic", "openrouter", "openai", "ollama"];
        if !available_providers.contains(&provider) {
            return Ok(ConfigCommandResult {
                success: false,
                message: format!("Unknown provider: {}. Available: {}", 
                               provider, 
                               available_providers.join(", ")),
                data: None,
                needs_refresh: false,
            });
        }
        
        // Update configuration
        self.runtime_config.set_setting("llm.provider", provider).await?;
        
        // Notify UI of provider change
        if let Some(ref tx) = self.ui_event_tx {
            let _ = tx.send(ConfigEvent::ProviderChanged { 
                provider: provider.to_string() 
            });
        }
        
        Ok(ConfigCommandResult {
            success: true,
            message: format!("Switched to provider: {}", provider),
            data: None,
            needs_refresh: false,
        })
    }
    
    /// Handle workspace switching command
    async fn handle_workspace_command(&mut self, args: &[&str]) -> Result<ConfigCommandResult> {
        if args.is_empty() {
            return Ok(ConfigCommandResult {
                success: false,
                message: "Usage: :workspace <path>".to_string(),
                data: None,
                needs_refresh: false,
            });
        }
        
        let path = args.join(" ");
        
        // Validate path exists
        if !std::path::Path::new(&path).exists() {
            return Ok(ConfigCommandResult {
                success: false,
                message: format!("Path does not exist: {}", path),
                data: None,
                needs_refresh: false,
            });
        }
        
        // Switch workspace
        // TODO: Implement workspace switching logic
        
        // Notify UI of workspace change
        if let Some(ref tx) = self.ui_event_tx {
            let _ = tx.send(ConfigEvent::WorkspaceChanged { 
                path: path.clone() 
            });
        }
        
        Ok(ConfigCommandResult {
            success: true,
            message: format!("Switched workspace to: {}", path),
            data: None,
            needs_refresh: true,
        })
    }
    
    /// Handle session management commands
    async fn handle_session_command(&mut self, args: &[&str]) -> Result<ConfigCommandResult> {
        if args.is_empty() {
            return Ok(ConfigCommandResult {
                success: false,
                message: "Usage: :session <action> [args...] (save|load|new|list)".to_string(),
                data: None,
                needs_refresh: false,
            });
        }
        
        match args[0] {
            "save" => {
                let name = if args.len() > 1 { args[1] } else { "default" };
                self.session_manager.save_session(name).await?;
                
                Ok(ConfigCommandResult {
                    success: true,
                    message: format!("Session saved as: {}", name),
                    data: None,
                    needs_refresh: false,
                })
            }
            "load" => {
                if args.len() < 2 {
                    return Ok(ConfigCommandResult {
                        success: false,
                        message: "Usage: :session load <name>".to_string(),
                        data: None,
                        needs_refresh: false,
                    });
                }
                
                self.session_manager.load_session(args[1]).await?;
                
                Ok(ConfigCommandResult {
                    success: true,
                    message: format!("Loaded session: {}", args[1]),
                    data: None,
                    needs_refresh: true,
                })
            }
            "new" => {
                self.session_manager.new_session().await?;
                
                Ok(ConfigCommandResult {
                    success: true,
                    message: "Started new session".to_string(),
                    data: None,
                    needs_refresh: true,
                })
            }
            "list" => {
                let sessions = self.session_manager.list_sessions().await?;
                let mut message = "Available sessions:\n".to_string();
                for session in sessions {
                    message.push_str(&format!("  {}\n", session));
                }
                
                Ok(ConfigCommandResult {
                    success: true,
                    message,
                    data: None,
                    needs_refresh: false,
                })
            }
            _ => Ok(ConfigCommandResult {
                success: false,
                message: format!("Unknown session action: {}", args[0]),
                data: None,
                needs_refresh: false,
            }),
        }
    }
    
    /// Handle configuration management commands
    async fn handle_config_command(&mut self, args: &[&str]) -> Result<ConfigCommandResult> {
        if args.is_empty() {
            return Ok(ConfigCommandResult {
                success: false,
                message: "Usage: :config <action> (import|export|reset)".to_string(),
                data: None,
                needs_refresh: false,
            });
        }
        
        match args[0] {
            "import" => {
                if args.len() < 2 {
                    return Ok(ConfigCommandResult {
                        success: false,
                        message: "Usage: :config import <file>".to_string(),
                        data: None,
                        needs_refresh: false,
                    });
                }
                
                self.runtime_config.import_config(args[1]).await?;
                
                Ok(ConfigCommandResult {
                    success: true,
                    message: format!("Imported configuration from: {}", args[1]),
                    data: None,
                    needs_refresh: true,
                })
            }
            "export" => {
                if args.len() < 2 {
                    return Ok(ConfigCommandResult {
                        success: false,
                        message: "Usage: :config export <file>".to_string(),
                        data: None,
                        needs_refresh: false,
                    });
                }
                
                self.runtime_config.export_config(args[1]).await?;
                
                Ok(ConfigCommandResult {
                    success: true,
                    message: format!("Exported configuration to: {}", args[1]),
                    data: None,
                    needs_refresh: false,
                })
            }
            "reset" => {
                self.runtime_config.reset_config().await?;
                
                Ok(ConfigCommandResult {
                    success: true,
                    message: "Configuration reset to defaults".to_string(),
                    data: None,
                    needs_refresh: true,
                })
            }
            _ => Ok(ConfigCommandResult {
                success: false,
                message: format!("Unknown config action: {}", args[0]),
                data: None,
                needs_refresh: false,
            }),
        }
    }
    
    /// Handle log level adjustment command
    async fn handle_log_command(&mut self, args: &[&str]) -> Result<ConfigCommandResult> {
        if args.is_empty() {
            return Ok(ConfigCommandResult {
                success: false,
                message: "Usage: :log <level> (trace|debug|info|warn|error)".to_string(),
                data: None,
                needs_refresh: false,
            });
        }
        
        let level = args[0];
        let valid_levels = vec!["trace", "debug", "info", "warn", "error"];
        
        if !valid_levels.contains(&level) {
            return Ok(ConfigCommandResult {
                success: false,
                message: format!("Invalid log level: {}. Valid levels: {}", 
                               level, 
                               valid_levels.join(", ")),
                data: None,
                needs_refresh: false,
            });
        }
        
        // Update log level
        // TODO: Implement dynamic log level changing
        
        // Notify UI of log level change
        if let Some(ref tx) = self.ui_event_tx {
            let _ = tx.send(ConfigEvent::LogLevelChanged { 
                level: level.to_string() 
            });
        }
        
        Ok(ConfigCommandResult {
            success: true,
            message: format!("Log level set to: {}", level),
            data: None,
            needs_refresh: false,
        })
    }
    
    /// Handle help command
    async fn handle_help_command(&mut self, args: &[&str]) -> Result<ConfigCommandResult> {
        let help_text = if args.is_empty() {
            self.help_system.get_general_help().await?
        } else {
            self.help_system.get_command_help(args[0]).await?
        };
        
        Ok(ConfigCommandResult {
            success: true,
            message: help_text,
            data: None,
            needs_refresh: false,
        })
    }
    
    /// Handle exit command
    async fn handle_exit_command(&mut self) -> Result<ConfigCommandResult> {
        Ok(ConfigCommandResult {
            success: true,
            message: "Exiting application...".to_string(),
            data: Some(serde_json::json!({"action": "exit"})),
            needs_refresh: false,
        })
    }
    
    /// Handle clear command
    async fn handle_clear_command(&mut self) -> Result<ConfigCommandResult> {
        Ok(ConfigCommandResult {
            success: true,
            message: "Chat history cleared".to_string(),
            data: Some(serde_json::json!({"action": "clear"})),
            needs_refresh: true,
        })
    }
    
    /// Handle save command
    async fn handle_save_command(&mut self, args: &[&str]) -> Result<ConfigCommandResult> {
        let name = if args.is_empty() { "default" } else { args[0] };
        
        // TODO: Implement context saving
        
        Ok(ConfigCommandResult {
            success: true,
            message: format!("Context saved as: {}", name),
            data: None,
            needs_refresh: false,
        })
    }
    
    /// Handle load command
    async fn handle_load_command(&mut self, args: &[&str]) -> Result<ConfigCommandResult> {
        if args.is_empty() {
            return Ok(ConfigCommandResult {
                success: false,
                message: "Usage: :load <name>".to_string(),
                data: None,
                needs_refresh: false,
            });
        }
        
        // TODO: Implement context loading
        
        Ok(ConfigCommandResult {
            success: true,
            message: format!("Context loaded: {}", args[0]),
            data: None,
            needs_refresh: true,
        })
    }
    
    /// Handle export command
    async fn handle_export_command(&mut self, args: &[&str]) -> Result<ConfigCommandResult> {
        if args.is_empty() {
            return Ok(ConfigCommandResult {
                success: false,
                message: "Usage: :export <format> [file] (json|yaml|toml)".to_string(),
                data: None,
                needs_refresh: false,
            });
        }
        
        let format = args[0];
        let file = if args.len() > 1 { Some(args[1]) } else { None };
        
        // TODO: Implement data export
        
        Ok(ConfigCommandResult {
            success: true,
            message: format!("Data exported in {} format", format),
            data: None,
            needs_refresh: false,
        })
    }
}

// Placeholder implementations for the manager components
// These will be implemented in separate files

#[derive(Debug)]
struct RuntimeConfigManager;

impl RuntimeConfigManager {
    fn new() -> Self { Self }
    async fn set_setting(&mut self, _key: &str, _value: &str) -> Result<()> { Ok(()) }
    async fn import_config(&mut self, _file: &str) -> Result<()> { Ok(()) }
    async fn export_config(&mut self, _file: &str) -> Result<()> { Ok(()) }
    async fn reset_config(&mut self) -> Result<()> { Ok(()) }
}

#[derive(Debug)]
struct ModeManager {
    current_mode: UiMode,
}

impl ModeManager {
    fn new() -> Self { 
        Self { current_mode: UiMode::Normal }
    }
    fn current_mode(&self) -> UiMode { self.current_mode.clone() }
    async fn switch_mode(&mut self, mode: UiMode) -> Result<()> { 
        self.current_mode = mode;
        Ok(()) 
    }
}

#[derive(Debug)]
struct ThemeManager;

impl ThemeManager {
    fn new() -> Self { Self }
    async fn list_themes(&self) -> Result<Vec<String>> { 
        Ok(vec!["default".to_string(), "dark".to_string(), "light".to_string()]) 
    }
    fn current_theme(&self) -> String { "default".to_string() }
    async fn switch_theme(&mut self, _theme: &str) -> Result<()> { Ok(()) }
}

#[derive(Debug)]
struct SessionManager;

impl SessionManager {
    fn new() -> Self { Self }
    async fn save_session(&mut self, _name: &str) -> Result<()> { Ok(()) }
    async fn load_session(&mut self, _name: &str) -> Result<()> { Ok(()) }
    async fn new_session(&mut self) -> Result<()> { Ok(()) }
    async fn list_sessions(&self) -> Result<Vec<String>> { 
        Ok(vec!["default".to_string(), "work".to_string()]) 
    }
}

#[derive(Debug)]
struct HelpSystem;

impl HelpSystem {
    fn new() -> Self { Self }
    
    async fn get_general_help(&self) -> Result<String> {
        Ok(r#"Configuration Commands:
:mode <mode>        - Switch UI mode (normal|vim|debug)
:set <key>=<value>  - Set configuration value
:theme <theme>      - Switch theme
:provider <name>    - Switch LLM provider
:workspace <path>   - Switch workspace
:session <action>   - Session management (save|load|new|list)
:config <action>    - Config management (import|export|reset)
:log <level>        - Set log level
:help [command]     - Show help
:exit               - Exit application
:clear              - Clear chat history
:save <name>        - Save context
:load <name>        - Load context
:export <format>    - Export data"#.to_string())
    }
    
    async fn get_command_help(&self, command: &str) -> Result<String> {
        let help = match command {
            "mode" => ":mode <mode> - Switch UI mode\nAvailable modes: normal, vim, debug",
            "set" => ":set <key>=<value> - Set configuration value\nExample: :set llm.temperature=0.7",
            "theme" => ":theme <theme> - Switch theme\nUse :theme without args to list available themes",
            "provider" => ":provider <name> - Switch LLM provider\nAvailable: anthropic, openrouter, openai, ollama",
            "session" => ":session <action> - Session management\nActions: save, load, new, list",
            _ => "Unknown command. Use :help for general help.",
        };
        
        Ok(help.to_string())
    }
}