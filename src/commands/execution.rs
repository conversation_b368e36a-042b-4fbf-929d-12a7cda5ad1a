// Command Execution Framework with async execution, validation, and result handling

use super::parser::{CommandType, ParsedCommand};
use super::registry::{CommandRegistry, ExecutionContext, ExecutionResult};
use crate::errors::{AutorunError, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{RwLock, Semaphore};
use tokio::time::timeout;
use uuid::Uuid;

/// Command execution engine
pub struct CommandExecutor {
    /// Command registry for execution
    registry: Arc<CommandRegistry>,
    /// Active executions
    active_executions: RwLock<HashMap<ExecutionId, ActiveExecution>>,
    /// Execution semaphore for concurrency control
    execution_semaphore: Arc<Semaphore>,
    /// Executor configuration
    config: ExecutorConfig,
    /// Execution history for analysis
    execution_history: RwLock<Vec<ExecutionRecord>>,
}

/// Unique execution identifier
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, <PERSON>h, <PERSON>ialEq, Eq, Serialize, Deserialize)]
pub struct ExecutionId(Uuid);

impl ExecutionId {
    pub fn new() -> Self {
        Self(Uuid::new_v4())
    }
}

impl std::fmt::Display for ExecutionId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

/// Active execution tracking
#[derive(Debug)]
struct ActiveExecution {
    /// Execution ID
    id: ExecutionId,
    /// Command being executed
    command: ParsedCommand,
    /// Execution context
    context: ExecutionContext,
    /// Start time
    start_time: Instant,
    /// Cancellation token
    cancel_token: tokio_util::sync::CancellationToken,
    /// Progress callback
    progress_callback: Option<Box<dyn Fn(ExecutionProgress) + Send + Sync>>,
}

/// Execution progress information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionProgress {
    /// Execution ID
    pub id: ExecutionId,
    /// Current stage
    pub stage: ExecutionStage,
    /// Progress percentage (0-100)
    pub progress: u8,
    /// Current message
    pub message: String,
    /// Elapsed time
    pub elapsed: Duration,
}

/// Execution stages
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ExecutionStage {
    /// Validating command
    Validating,
    /// Preparing execution
    Preparing,
    /// Executing command
    Executing,
    /// Processing results
    Processing,
    /// Completed
    Completed,
    /// Failed
    Failed,
    /// Cancelled
    Cancelled,
}

/// Execution record for history and analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionRecord {
    /// Execution ID
    pub id: ExecutionId,
    /// Command that was executed
    pub command: ParsedCommand,
    /// Execution result
    pub result: ExecutionResult,
    /// Start time
    pub start_time: std::time::SystemTime,
    /// Duration
    pub duration: Duration,
    /// User who executed the command
    pub user: Option<String>,
    /// Session ID
    pub session_id: Option<String>,
}

/// Executor configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutorConfig {
    /// Maximum concurrent executions
    pub max_concurrent_executions: usize,
    /// Default execution timeout
    pub default_timeout: Duration,
    /// Enable execution history
    pub enable_history: bool,
    /// Maximum history entries
    pub max_history_entries: usize,
    /// Enable progress tracking
    pub enable_progress_tracking: bool,
    /// Validate commands before execution
    pub validate_before_execution: bool,
    /// Allow dangerous commands
    pub allow_dangerous_commands: bool,
    /// Execution retry attempts
    pub retry_attempts: u32,
    /// Retry delay
    pub retry_delay: Duration,
}

impl Default for ExecutorConfig {
    fn default() -> Self {
        Self {
            max_concurrent_executions: 10,
            default_timeout: Duration::from_secs(30),
            enable_history: true,
            max_history_entries: 1000,
            enable_progress_tracking: true,
            validate_before_execution: true,
            allow_dangerous_commands: false,
            retry_attempts: 3,
            retry_delay: Duration::from_millis(1000),
        }
    }
}

/// Command execution options
#[derive(Debug, Clone)]
pub struct ExecutionOptions {
    /// Execution timeout override
    pub timeout: Option<Duration>,
    /// Enable progress callbacks
    pub enable_progress: bool,
    /// Progress callback
    pub progress_callback: Option<Box<dyn Fn(ExecutionProgress) + Send + Sync>>,
    /// Execution priority
    pub priority: ExecutionPriority,
    /// Whether to retry on failure
    pub retry_on_failure: bool,
    /// Custom retry attempts
    pub retry_attempts: Option<u32>,
    /// Background execution (don't block)
    pub background: bool,
}

impl Default for ExecutionOptions {
    fn default() -> Self {
        Self {
            timeout: None,
            enable_progress: true,
            progress_callback: None,
            priority: ExecutionPriority::Normal,
            retry_on_failure: true,
            retry_attempts: None,
            background: false,
        }
    }
}

/// Execution priority levels
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum ExecutionPriority {
    Low,
    Normal,
    High,
    Critical,
}

impl CommandExecutor {
    /// Create a new command executor
    pub fn new(registry: Arc<CommandRegistry>, config: ExecutorConfig) -> Self {
        let execution_semaphore = Arc::new(Semaphore::new(config.max_concurrent_executions));

        Self {
            registry,
            active_executions: RwLock::new(HashMap::new()),
            execution_semaphore,
            config,
            execution_history: RwLock::new(Vec::new()),
        }
    }

    /// Execute a command with default options
    pub async fn execute(
        &self,
        command: ParsedCommand,
        context: ExecutionContext,
    ) -> Result<ExecutionResult> {
        self.execute_with_options(command, context, ExecutionOptions::default())
            .await
    }

    /// Execute a command with custom options
    pub async fn execute_with_options(
        &self,
        command: ParsedCommand,
        context: ExecutionContext,
        options: ExecutionOptions,
    ) -> Result<ExecutionResult> {
        let execution_id = ExecutionId::new();

        // Validate command if enabled
        if self.config.validate_before_execution {
            self.report_progress(
                execution_id,
                ExecutionStage::Validating,
                0,
                "Validating command".to_string(),
                &options,
            )
            .await;

            let validation_errors = self.registry.validate_command(&command).await?;
            if !validation_errors.is_empty() {
                return Ok(ExecutionResult {
                    success: false,
                    output: String::new(),
                    error: Some(format!(
                        "Validation failed: {}",
                        validation_errors.join(", ")
                    )),
                    data: HashMap::new(),
                    display: true,
                });
            }
        }

        // Check for dangerous commands
        if !self.config.allow_dangerous_commands && self.is_dangerous_command(&command)? {
            return Ok(ExecutionResult {
                success: false,
                output: String::new(),
                error: Some("Dangerous command execution is disabled".to_string()),
                data: HashMap::new(),
                display: true,
            });
        }

        // Execute with retries if configured
        let max_attempts = options
            .retry_attempts
            .unwrap_or(self.config.retry_attempts)
            .max(1);
        let mut last_error = None;

        for attempt in 1..=max_attempts {
            match self
                .execute_single_attempt(execution_id, &command, &context, &options)
                .await
            {
                Ok(result) => {
                    if result.success || !options.retry_on_failure {
                        // Record successful execution
                        if self.config.enable_history {
                            self.record_execution(
                                execution_id,
                                command.clone(),
                                result.clone(),
                                &context,
                            )
                            .await;
                        }
                        return Ok(result);
                    } else {
                        last_error = result.error.clone();
                    }
                }
                Err(e) => {
                    last_error = Some(e.to_string());
                }
            }

            // Wait before retry if not the last attempt
            if attempt < max_attempts {
                tokio::time::sleep(self.config.retry_delay).await;
            }
        }

        // All attempts failed
        let result = ExecutionResult {
            success: false,
            output: String::new(),
            error: last_error.or_else(|| Some("Command execution failed".to_string())),
            data: HashMap::new(),
            display: true,
        };

        if self.config.enable_history {
            self.record_execution(execution_id, command, result.clone(), &context)
                .await;
        }

        Ok(result)
    }

    /// Execute a single attempt
    async fn execute_single_attempt(
        &self,
        execution_id: ExecutionId,
        command: &ParsedCommand,
        context: &ExecutionContext,
        options: &ExecutionOptions,
    ) -> Result<ExecutionResult> {
        // Acquire semaphore permit
        let _permit = self.execution_semaphore.acquire().await.map_err(|_| {
            AutorunError::CommandError("Failed to acquire execution permit".to_string())
        })?;

        // Create cancellation token
        let cancel_token = tokio_util::sync::CancellationToken::new();

        // Track active execution
        {
            let mut active = self.active_executions.write().await;
            active.insert(
                execution_id,
                ActiveExecution {
                    id: execution_id,
                    command: command.clone(),
                    context: context.clone(),
                    start_time: Instant::now(),
                    cancel_token: cancel_token.clone(),
                    progress_callback: None, // TODO: Clone boxed callback properly
                },
            );
        }

        self.report_progress(
            execution_id,
            ExecutionStage::Preparing,
            10,
            "Preparing execution".to_string(),
            options,
        )
        .await;

        // Execute with timeout
        let execution_timeout = options.timeout.unwrap_or(self.config.default_timeout);

        let execution_future = async {
            self.report_progress(
                execution_id,
                ExecutionStage::Executing,
                25,
                "Executing command".to_string(),
                options,
            )
            .await;

            // Execute the command through the registry
            let result = self.registry.execute_command(command, context).await?;

            self.report_progress(
                execution_id,
                ExecutionStage::Processing,
                80,
                "Processing results".to_string(),
                options,
            )
            .await;

            Ok::<ExecutionResult, AutorunError>(result)
        };

        let result = tokio::select! {
            result = execution_future => {
                match result {
                    Ok(exec_result) => {
                        self.report_progress(execution_id, ExecutionStage::Completed, 100, "Execution completed".to_string(), options).await;
                        Ok(exec_result)
                    }
                    Err(e) => {
                        self.report_progress(execution_id, ExecutionStage::Failed, 100, format!("Execution failed: {}", e), options).await;
                        Err(e)
                    }
                }
            }
            _ = timeout(execution_timeout, cancel_token.cancelled()) => {
                self.report_progress(execution_id, ExecutionStage::Cancelled, 100, "Execution cancelled".to_string(), options).await;
                Err(AutorunError::CommandError("Execution cancelled".to_string()))
            }
            _ = tokio::time::sleep(execution_timeout) => {
                self.report_progress(execution_id, ExecutionStage::Failed, 100, "Execution timed out".to_string(), options).await;
                Err(AutorunError::CommandError("Execution timed out".to_string()))
            }
        };

        // Clean up active execution
        {
            let mut active = self.active_executions.write().await;
            active.remove(&execution_id);
        }

        result
    }

    /// Cancel an active execution
    pub async fn cancel_execution(&self, execution_id: ExecutionId) -> Result<bool> {
        let active = self.active_executions.read().await;

        if let Some(execution) = active.get(&execution_id) {
            execution.cancel_token.cancel();
            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// Get active executions
    pub async fn get_active_executions(&self) -> Vec<ExecutionId> {
        let active = self.active_executions.read().await;
        active.keys().copied().collect()
    }

    /// Get execution history
    pub async fn get_execution_history(&self, limit: Option<usize>) -> Vec<ExecutionRecord> {
        let history = self.execution_history.read().await;
        let mut records = history.clone();

        // Sort by start time (most recent first)
        records.sort_by(|a, b| b.start_time.cmp(&a.start_time));

        if let Some(limit) = limit {
            records.truncate(limit);
        }

        records
    }

    /// Clear execution history
    pub async fn clear_history(&self) {
        let mut history = self.execution_history.write().await;
        history.clear();
    }

    /// Get executor statistics
    pub async fn get_stats(&self) -> ExecutorStats {
        let active = self.active_executions.read().await;
        let history = self.execution_history.read().await;

        let successful_executions = history.iter().filter(|r| r.result.success).count();
        let failed_executions = history.iter().filter(|r| !r.result.success).count();

        let avg_duration = if !history.is_empty() {
            history.iter().map(|r| r.duration.as_millis()).sum::<u128>() / history.len() as u128
        } else {
            0
        };

        ExecutorStats {
            active_executions: active.len(),
            total_executions: history.len(),
            successful_executions,
            failed_executions,
            average_duration_ms: avg_duration,
            available_permits: self.execution_semaphore.available_permits(),
        }
    }

    // Private helper methods

    /// Report execution progress
    async fn report_progress(
        &self,
        execution_id: ExecutionId,
        stage: ExecutionStage,
        progress: u8,
        message: String,
        options: &ExecutionOptions,
    ) {
        if !self.config.enable_progress_tracking || !options.enable_progress {
            return;
        }

        let elapsed = {
            let active = self.active_executions.read().await;
            active
                .get(&execution_id)
                .map(|exec| exec.start_time.elapsed())
                .unwrap_or_default()
        };

        let progress_info = ExecutionProgress {
            id: execution_id,
            stage,
            progress,
            message,
            elapsed,
        };

        // Call progress callback if provided
        if let Some(callback) = &options.progress_callback {
            callback(progress_info);
        }
    }

    /// Check if command is considered dangerous
    fn is_dangerous_command(&self, command: &ParsedCommand) -> Result<bool> {
        // Define dangerous command patterns
        let dangerous_patterns = vec![
            // File operations
            "delete",
            "remove",
            "rm",
            // System operations
            "shutdown",
            "reboot",
            "halt",
            // Network operations
            "curl",
            "wget",
            "ssh",
            // Process operations
            "kill",
            "killall",
            // Package management
            "install",
            "uninstall",
            "remove",
        ];

        let command_name = command.name.to_lowercase();

        // Check for dangerous command names
        if dangerous_patterns
            .iter()
            .any(|&pattern| command_name.contains(pattern))
        {
            return Ok(true);
        }

        // Check for dangerous flags or options
        for arg in &command.args {
            let arg_lower = arg.to_lowercase();
            if arg_lower.contains("--force")
                || arg_lower.contains("-f")
                || arg_lower.contains("--recursive")
                || arg_lower.contains("-r")
            {
                return Ok(true);
            }
        }

        Ok(false)
    }

    /// Record execution in history
    async fn record_execution(
        &self,
        execution_id: ExecutionId,
        command: ParsedCommand,
        result: ExecutionResult,
        context: &ExecutionContext,
    ) {
        let mut history = self.execution_history.write().await;

        let record = ExecutionRecord {
            id: execution_id,
            command,
            result,
            start_time: std::time::SystemTime::now(),
            duration: Duration::from_secs(0), // TODO: Calculate actual duration
            user: None,                       // TODO: Get from context
            session_id: None,                 // TODO: Get from context
        };

        history.push(record);

        // Trim history if it exceeds maximum entries
        if history.len() > self.config.max_history_entries {
            history.remove(0);
        }
    }
}

/// Executor statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutorStats {
    pub active_executions: usize,
    pub total_executions: usize,
    pub successful_executions: usize,
    pub failed_executions: usize,
    pub average_duration_ms: u128,
    pub available_permits: usize,
}

impl Default for CommandExecutor {
    fn default() -> Self {
        // This requires a registry, so we can't provide a meaningful default
        panic!("CommandExecutor requires a registry - use CommandExecutor::new() instead")
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::commands::registry::{CommandRegistry, RegistryConfig};
    use crate::tools::PermissionLevel;

    #[tokio::test]
    async fn test_executor_creation() {
        let registry = Arc::new(CommandRegistry::new(RegistryConfig::default()));
        let executor = CommandExecutor::new(registry, ExecutorConfig::default());

        let stats = executor.get_stats().await;
        assert_eq!(stats.active_executions, 0);
        assert_eq!(stats.total_executions, 0);
    }

    #[tokio::test]
    async fn test_dangerous_command_detection() {
        let registry = Arc::new(CommandRegistry::new(RegistryConfig::default()));
        let executor = CommandExecutor::new(registry, ExecutorConfig::default());

        let mut command = ParsedCommand::new(CommandType::Action, "/delete --force".to_string(), 0);
        command.set_name("delete".to_string());
        command.add_arg("--force".to_string());

        assert!(executor.is_dangerous_command(&command).unwrap());
    }

    #[tokio::test]
    async fn test_execution_id_generation() {
        let id1 = ExecutionId::new();
        let id2 = ExecutionId::new();

        assert_ne!(id1, id2);
        assert!(!id1.to_string().is_empty());
    }
}
