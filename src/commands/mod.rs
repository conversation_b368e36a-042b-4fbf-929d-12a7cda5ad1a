// Unified command system for @, /, and : commands
// Provides command parsing, validation, and execution infrastructure

pub mod action_engine;
pub mod completion;
pub mod config_engine;
pub mod custom;
pub mod execution;
pub mod mcp_actions;
pub mod parser;
pub mod registry;
pub mod types;

#[cfg(test)]
mod tests;

// Re-export commonly used types
pub use action_engine::{
    ActionEngine, ExecutionStatus, ExecutionState,
};
pub use completion::{CompletionConfig, CompletionStats, UnifiedCompletionProvider};
pub use execution::{
    CommandExecutor, ExecutionId, ExecutionOptions, ExecutionPriority, ExecutionProgress,
    ExecutionStage, ExecutorConfig,
};
pub use parser::{
    CommandCompletion, CommandParser, CommandType, ParseResult, ParsedCommand, ParsingContext,
    UnifiedCommandParser,
};
pub use registry::{
    CommandInfo, CommandProvider, CommandRegistry, ExecutionContext, ExecutionResult,
};
pub use mcp_actions::{
    McpToolExecutor, McpToolExecutorConfig, McpExecutionProgress, McpExecutionStatus, ParameterConfig,
};
pub use custom::{
    CommandEvent, CommandFile, CommandLoader, CommandLoaderConfig, CommandMetadata,
    CommandParameter, LoaderStats, ValidationLevel,
};
pub use config_engine::{
    ConfigEngine, ConfigEvent, ConfigCommandResult,
};

use crate::errors::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Command system configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommandConfig {
    /// Enable command system
    pub enabled: bool,

    /// Enable auto-completion
    pub completion_enabled: bool,

    /// Enable command validation
    pub validation_enabled: bool,

    /// Maximum command history
    pub max_history: usize,

    /// Command execution timeout (seconds)
    pub execution_timeout: u64,

    /// Custom command directories
    pub custom_command_paths: Vec<String>,

    /// Enable MCP integration
    pub mcp_integration: bool,

    /// Enable web search for @ commands
    pub web_search_enabled: bool,

    /// Default provider configurations
    pub providers: HashMap<String, serde_json::Value>,
}

impl Default for CommandConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            completion_enabled: true,
            validation_enabled: true,
            max_history: 1000,
            execution_timeout: 30,
            custom_command_paths: vec![
                ".autorun/commands".to_string(),
                ".claude/commands".to_string(),
            ],
            mcp_integration: true,
            web_search_enabled: false, // Disabled by default for privacy
            providers: HashMap::new(),
        }
    }
}

/// Initialize the command system
pub async fn initialize_command_system(config: CommandConfig) -> Result<CommandRegistry> {
    let registry_config = registry::RegistryConfig {
        enable_caching: config.completion_enabled,
        cache_ttl: 300,
        max_cache_size: config.max_history,
        enable_validation: config.validation_enabled,
        default_timeout: config.execution_timeout,
    };

    let registry = CommandRegistry::new(registry_config);

    // Register built-in providers
    registry.register_builtin_providers().await?;

    // Load custom commands if enabled
    if !config.custom_command_paths.is_empty() {
        registry.load_custom_commands().await?;
    }

    // Initialize MCP integration if enabled
    if config.mcp_integration {
        registry.initialize_mcp_providers().await?;
    }

    Ok(registry)
}
