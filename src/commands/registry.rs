// Enhanced Command Registry with dynamic discovery and provider management

use super::parser::{
    CommandCompletion, CommandType, MainCommandParser, ParseR<PERSON>ult, ParsedCommand, Parsing<PERSON>ontext,
    UnifiedCommandParser,
};
use crate::errors::{AutorunError, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

/// Command provider trait for extensible command registration
#[async_trait::async_trait]
pub trait CommandProvider: Send + Sync {
    /// Get the name of this provider
    fn name(&self) -> &str;

    /// Get the command types this provider supports
    fn supported_types(&self) -> Vec<CommandType>;

    /// Get all commands provided by this provider
    async fn get_commands(&self) -> Result<Vec<CommandInfo>>;

    /// Execute a command
    async fn execute(
        &self,
        command: &ParsedCommand,
        context: &ExecutionContext,
    ) -> Result<ExecutionResult>;

    /// Get completions for a partial command
    async fn get_completions(&self, partial: &ParsedCommand) -> Result<Vec<CommandCompletion>>;

    /// Validate a command
    async fn validate(&self, command: &ParsedCommand) -> Result<Vec<String>>;
}

/// Information about a registered command
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommandInfo {
    /// Command name
    pub name: String,
    /// Command type (@, /, :)
    pub command_type: CommandType,
    /// Brief description
    pub description: String,
    /// Detailed help text
    pub help: Option<String>,
    /// Required arguments
    pub required_args: Vec<ArgumentInfo>,
    /// Optional arguments
    pub optional_args: Vec<ArgumentInfo>,
    /// Available flags
    pub flags: Vec<FlagInfo>,
    /// Example usage
    pub examples: Vec<String>,
    /// Provider that registered this command
    pub provider: String,
    /// Command category/group
    pub category: String,
    /// Priority for completion ordering
    pub priority: u8,
    /// Whether command is currently available
    pub enabled: bool,
}

/// Argument information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ArgumentInfo {
    /// Argument name
    pub name: String,
    /// Argument description
    pub description: String,
    /// Argument type for validation and completion
    pub arg_type: ArgumentType,
    /// Default value if optional
    pub default: Option<String>,
    /// Validation pattern (regex)
    pub validation: Option<String>,
}

/// Flag information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FlagInfo {
    /// Flag name (without --)
    pub name: String,
    /// Flag description
    pub description: String,
    /// Flag type
    pub flag_type: FlagType,
    /// Conflicts with other flags
    pub conflicts: Vec<String>,
    /// Requires other flags
    pub requires: Vec<String>,
}

/// Types of command arguments
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ArgumentType {
    /// String value
    String,
    /// Integer number
    Integer,
    /// Floating point number
    Float,
    /// Boolean value
    Boolean,
    /// File path
    FilePath,
    /// Directory path
    DirectoryPath,
    /// URL
    Url,
    /// Email address
    Email,
    /// One of predefined choices
    Choice(Vec<String>),
    /// Custom type with validation
    Custom(String),
}

/// Types of command flags
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FlagType {
    /// Boolean flag (present/absent)
    Boolean,
    /// Flag with value
    Value(ArgumentType),
}

/// Execution context for commands
#[derive(Debug, Clone)]
pub struct ExecutionContext {
    /// Current working directory
    pub working_directory: std::path::PathBuf,
    /// User permissions
    pub permissions: crate::tools::PermissionLevel,
    /// Session data
    pub session_data: HashMap<String, String>,
    /// Environment variables
    pub environment: HashMap<String, String>,
    /// Tool registry reference
    pub tool_registry: Option<Arc<crate::tools::ToolRegistry>>,
}

/// Result of command execution
#[derive(Debug, Clone)]
pub struct ExecutionResult {
    /// Whether execution was successful
    pub success: bool,
    /// Output message
    pub output: String,
    /// Error message if failed
    pub error: Option<String>,
    /// Additional data
    pub data: HashMap<String, serde_json::Value>,
    /// Whether to display result in UI
    pub display: bool,
}

/// Enhanced command registry
pub struct CommandRegistry {
    /// Registered command providers
    providers: RwLock<HashMap<String, Arc<dyn CommandProvider>>>,
    /// Cached command information
    command_cache: RwLock<HashMap<String, CommandInfo>>,
    /// Main command parser
    parser: Arc<MainCommandParser>,
    /// Registry configuration
    config: RegistryConfig,
}

/// Registry configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegistryConfig {
    /// Enable caching of command information
    pub enable_caching: bool,
    /// Cache expiration time in seconds
    pub cache_ttl: u64,
    /// Maximum number of cached commands
    pub max_cache_size: usize,
    /// Enable command validation
    pub enable_validation: bool,
    /// Default execution timeout in seconds
    pub default_timeout: u64,
}

impl Default for RegistryConfig {
    fn default() -> Self {
        Self {
            enable_caching: true,
            cache_ttl: 300, // 5 minutes
            max_cache_size: 1000,
            enable_validation: true,
            default_timeout: 30,
        }
    }
}

impl CommandRegistry {
    /// Create a new command registry
    pub fn new(config: RegistryConfig) -> Self {
        Self {
            providers: RwLock::new(HashMap::new()),
            command_cache: RwLock::new(HashMap::new()),
            parser: Arc::new(MainCommandParser::new()),
            config,
        }
    }

    /// Register a command provider
    pub async fn register_provider(&self, provider: Arc<dyn CommandProvider>) -> Result<()> {
        let name = provider.name().to_string();

        // Load commands from provider
        let commands = provider.get_commands().await?;

        // Add to cache if caching is enabled
        if self.config.enable_caching {
            let mut cache = self.command_cache.write().await;
            for command in commands {
                cache.insert(
                    format!("{}:{}", command.command_type.prefix(), command.name),
                    command,
                );
            }
        }

        // Register provider
        let mut providers = self.providers.write().await;
        providers.insert(name, provider);

        Ok(())
    }

    /// Unregister a command provider
    pub async fn unregister_provider(&self, name: &str) -> Result<()> {
        let mut providers = self.providers.write().await;

        if let Some(provider) = providers.remove(name) {
            // Remove commands from cache
            if self.config.enable_caching {
                let mut cache = self.command_cache.write().await;
                let commands = provider.get_commands().await?;
                for command in commands {
                    cache.remove(&format!(
                        "{}:{}",
                        command.command_type.prefix(),
                        command.name
                    ));
                }
            }
        }

        Ok(())
    }

    /// Get all registered commands
    pub async fn get_all_commands(&self) -> Result<Vec<CommandInfo>> {
        if self.config.enable_caching {
            let cache = self.command_cache.read().await;
            Ok(cache.values().cloned().collect())
        } else {
            let mut all_commands = Vec::new();
            let providers = self.providers.read().await;

            for provider in providers.values() {
                let commands = provider.get_commands().await?;
                all_commands.extend(commands);
            }

            Ok(all_commands)
        }
    }

    /// Get commands by type
    pub async fn get_commands_by_type(
        &self,
        command_type: CommandType,
    ) -> Result<Vec<CommandInfo>> {
        let all_commands = self.get_all_commands().await?;
        Ok(all_commands
            .into_iter()
            .filter(|cmd| cmd.command_type == command_type)
            .collect())
    }

    /// Get command information
    pub async fn get_command_info(
        &self,
        command_type: CommandType,
        name: &str,
    ) -> Result<Option<CommandInfo>> {
        let key = format!("{}:{}", command_type.prefix(), name);

        if self.config.enable_caching {
            let cache = self.command_cache.read().await;
            Ok(cache.get(&key).cloned())
        } else {
            let providers = self.providers.read().await;

            for provider in providers.values() {
                if provider.supported_types().contains(&command_type) {
                    let commands = provider.get_commands().await?;
                    if let Some(command) = commands.into_iter().find(|cmd| cmd.name == name) {
                        return Ok(Some(command));
                    }
                }
            }

            Ok(None)
        }
    }

    /// Parse command from input
    pub async fn parse_command(&self, input: &str, cursor_pos: usize) -> Result<ParseResult> {
        let context = ParsingContext::new(input.to_string(), cursor_pos);
        self.parser.parse_any(&context).await
    }

    /// Get completions for partial input
    pub async fn get_completions(
        &self,
        input: &str,
        cursor_pos: usize,
    ) -> Result<Vec<CommandCompletion>> {
        let context = ParsingContext::new(input.to_string(), cursor_pos);

        // Get parser completions
        let mut completions = self.parser.get_completions(&context).await?;

        // Get provider-specific completions
        let parse_result = self.parser.parse_any(&context).await?;
        if let ParseResult::Success(command) | ParseResult::Partial(command) = parse_result {
            let providers = self.providers.read().await;

            for provider in providers.values() {
                if provider.supported_types().contains(&command.command_type) {
                    let provider_completions = provider.get_completions(&command).await?;
                    completions.extend(provider_completions);
                }
            }
        }

        // Sort and deduplicate
        completions.sort_by(|a, b| b.priority.cmp(&a.priority));
        completions.dedup_by(|a, b| a.text == b.text);

        Ok(completions)
    }

    /// Validate a command
    pub async fn validate_command(&self, command: &ParsedCommand) -> Result<Vec<String>> {
        if !self.config.enable_validation {
            return Ok(Vec::new());
        }

        let mut errors = Vec::new();

        // Basic parser validation
        let parser_errors = self.parser.validate(command).await?;
        errors.extend(parser_errors);

        // Provider-specific validation
        let providers = self.providers.read().await;

        for provider in providers.values() {
            if provider.supported_types().contains(&command.command_type) {
                let provider_errors = provider.validate(command).await?;
                errors.extend(provider_errors);
            }
        }

        Ok(errors)
    }

    /// Execute a command
    pub async fn execute_command(
        &self,
        command: &ParsedCommand,
        context: &ExecutionContext,
    ) -> Result<ExecutionResult> {
        // Validate command first
        if self.config.enable_validation {
            let errors = self.validate_command(command).await?;
            if !errors.is_empty() {
                return Ok(ExecutionResult {
                    success: false,
                    output: String::new(),
                    error: Some(format!("Validation errors: {}", errors.join(", "))),
                    data: HashMap::new(),
                    display: true,
                });
            }
        }

        // Find provider that can execute this command
        let providers = self.providers.read().await;

        for provider in providers.values() {
            if provider.supported_types().contains(&command.command_type) {
                // Check if provider has this command
                let commands = provider.get_commands().await?;
                if commands
                    .iter()
                    .any(|cmd| cmd.name == command.name && cmd.enabled)
                {
                    return provider.execute(command, context).await;
                }
            }
        }

        Err(AutorunError::CommandError(format!(
            "No provider found for command: {}",
            command.name
        )))
    }

    /// Register built-in providers
    pub async fn register_builtin_providers(&self) -> Result<()> {
        // Context provider
        let context_provider = Arc::new(BuiltinContextProvider::new());
        self.register_provider(context_provider).await?;

        // Action provider
        let action_provider = Arc::new(BuiltinActionProvider::new());
        self.register_provider(action_provider).await?;

        // Config provider
        let config_provider = Arc::new(BuiltinConfigProvider::new());
        self.register_provider(config_provider).await?;

        Ok(())
    }

    /// Load custom commands from configuration
    pub async fn load_custom_commands(&self) -> Result<()> {
        use crate::commands::custom::{CommandLoader, CommandLoaderConfig};
        use crate::templates::TemplateEngine;
        use std::sync::Arc;
        
        tracing::info!("Loading custom commands from filesystem");
        
        // Create template engine for command processing
        let template_engine = Arc::new(TemplateEngine::new()?);
        
        // Configure command loader with default paths
        let config = CommandLoaderConfig::default();
        
        // Initialize command loader
        let mut loader = CommandLoader::new(config, template_engine);
        loader.initialize().await?;
        
        // Get all loaded commands
        let custom_commands = loader.get_commands().await;
        
        tracing::info!("Loaded {} custom commands", custom_commands.len());
        
        // Register commands with the registry
        for (name, command_file) in custom_commands {
            if command_file.is_valid {
                let command_info = self.create_command_info_from_file(&command_file);
                self.register_custom_command(name, command_info).await?;
            } else {
                tracing::warn!("Skipping invalid command: {} (validation errors: {:?})", 
                    name, command_file.validation_errors);
            }
        }
        
        // Store the loader for potential hot reloading
        // Note: In a full implementation, you'd want to store this somewhere accessible
        // for hot reloading functionality
        
        Ok(())
    }

    /// Create CommandInfo from a loaded command file
    fn create_command_info_from_file(&self, command_file: &crate::commands::custom::CommandFile) -> CommandInfo {
        use crate::commands::custom::{CommandMetadata, CommandParameter as CustomParam};
        
        let metadata = &command_file.metadata;
        
        // Convert custom parameters to registry arguments
        let arguments = metadata.parameters
            .iter()
            .map(|param| ArgumentInfo {
                name: param.name.clone(),
                description: param.description.clone(),
                arg_type: self.map_param_type(&param.param_type),
                required: param.required,
                default_value: param.default.clone(),
                validation_pattern: param.validation.clone(),
                possible_values: if param.options.is_empty() { 
                    None 
                } else { 
                    Some(param.options.clone()) 
                },
            })
            .collect();

        CommandInfo {
            name: metadata.name.clone(),
            description: metadata.description.clone().unwrap_or_else(|| 
                format!("Custom {} command", metadata.command_type)
            ),
            category: metadata.command_type.clone(),
            arguments,
            flags: Vec::new(), // Custom commands don't use flags currently
            aliases: metadata.aliases.clone(),
            permissions: metadata.permissions.clone(),
            tags: metadata.tags.clone(),
            requires_confirmation: metadata.requires_confirmation,
            priority: metadata.priority,
            enabled: metadata.enabled,
            source: format!("custom:{}", command_file.path.display()),
        }
    }

    /// Map custom parameter type to registry argument type
    fn map_param_type(&self, param_type: &str) -> ArgumentType {
        match param_type.to_lowercase().as_str() {
            "string" | "text" => ArgumentType::String,
            "integer" | "int" | "number" => ArgumentType::Integer,
            "boolean" | "bool" => ArgumentType::Boolean,
            "file" | "path" => ArgumentType::File,
            "directory" | "dir" => ArgumentType::Directory,
            "url" => ArgumentType::Url,
            "enum" | "choice" => ArgumentType::Choice,
            _ => ArgumentType::String, // Default to string for unknown types
        }
    }

    /// Register a custom command with the registry
    async fn register_custom_command(&self, name: String, command_info: CommandInfo) -> Result<()> {
        use std::collections::HashMap;
        
        tracing::debug!("Registering custom command: {}", name);
        
        // Add to command cache
        self.command_cache.insert(name.clone(), command_info.clone());
        
        // Create a custom command provider for this command
        let custom_provider = CustomCommandProvider::new(name.clone(), command_info);
        
        // Register the provider
        self.providers.insert(
            format!("custom_{}", name),
            Box::new(custom_provider)
        );
        
        tracing::info!("Successfully registered custom command: {}", name);
        Ok(())
    }

    /// Initialize MCP providers
    pub async fn initialize_mcp_providers(&self) -> Result<()> {
        // TODO: Implement MCP provider initialization
        // This would connect to configured MCP servers and register their tools as commands
        Ok(())
    }

    /// Clear command cache
    pub async fn clear_cache(&self) {
        if self.config.enable_caching {
            let mut cache = self.command_cache.write().await;
            cache.clear();
        }
    }

    /// Get registry statistics
    pub async fn get_stats(&self) -> RegistryStats {
        let providers = self.providers.read().await;
        let cache = self.command_cache.read().await;

        RegistryStats {
            provider_count: providers.len(),
            command_count: cache.len(),
            cache_enabled: self.config.enable_caching,
            validation_enabled: self.config.enable_validation,
        }
    }
}

/// Custom command provider for individual commands loaded from filesystem
#[derive(Debug)]
pub struct CustomCommandProvider {
    name: String,
    command_info: CommandInfo,
}

impl CustomCommandProvider {
    pub fn new(name: String, command_info: CommandInfo) -> Self {
        Self { name, command_info }
    }
}

#[async_trait::async_trait]
impl CommandProvider for CustomCommandProvider {
    fn name(&self) -> &str {
        &self.name
    }

    fn description(&self) -> &str {
        &self.command_info.description
    }

    fn supported_types(&self) -> Vec<CommandType> {
        // Map command category to CommandType
        match self.command_info.category.as_str() {
            "action" => vec![CommandType::Action],
            "context" => vec![CommandType::Context],
            "config" | "configuration" => vec![CommandType::Configuration],
            _ => vec![CommandType::Action], // Default to action
        }
    }

    async fn get_commands(&self) -> Result<Vec<CommandInfo>> {
        Ok(vec![self.command_info.clone()])
    }

    async fn get_completions(&self, _command: &ParsedCommand) -> Result<Vec<CommandCompletion>> {
        // Custom commands use basic completion based on parameters
        let mut completions = Vec::new();
        
        for arg in &self.command_info.arguments {
            if let Some(possible_values) = &arg.possible_values {
                for value in possible_values {
                    completions.push(CommandCompletion {
                        text: value.clone(),
                        description: arg.description.clone(),
                        priority: if arg.required { 100 } else { 50 },
                        completion_type: "argument_value".to_string(),
                    });
                }
            } else {
                completions.push(CommandCompletion {
                    text: format!("<{}>", arg.name),
                    description: arg.description.clone(),
                    priority: if arg.required { 100 } else { 50 },
                    completion_type: "argument".to_string(),
                });
            }
        }
        
        Ok(completions)
    }

    async fn validate(&self, command: &ParsedCommand) -> Result<Vec<String>> {
        let mut errors = Vec::new();
        
        // Validate required arguments
        for arg in &self.command_info.arguments {
            if arg.required {
                let arg_present = command.arguments.iter()
                    .any(|cmd_arg| cmd_arg.name == arg.name);
                
                if !arg_present {
                    errors.push(format!("Required argument '{}' is missing", arg.name));
                }
            }
        }
        
        // Validate argument types and patterns
        for cmd_arg in &command.arguments {
            if let Some(arg_info) = self.command_info.arguments.iter()
                .find(|a| a.name == cmd_arg.name) {
                
                // Validate against pattern if provided
                if let Some(pattern) = &arg_info.validation_pattern {
                    if let Ok(regex) = regex::Regex::new(pattern) {
                        if !regex.is_match(&cmd_arg.value) {
                            errors.push(format!(
                                "Argument '{}' does not match required pattern: {}",
                                cmd_arg.name, pattern
                            ));
                        }
                    }
                }
                
                // Validate against possible values if provided
                if let Some(possible_values) = &arg_info.possible_values {
                    if !possible_values.contains(&cmd_arg.value) {
                        errors.push(format!(
                            "Argument '{}' must be one of: {}",
                            cmd_arg.name,
                            possible_values.join(", ")
                        ));
                    }
                }
            }
        }
        
        Ok(errors)
    }

    async fn execute(&self, command: &ParsedCommand, context: &ExecutionContext) -> Result<ExecutionResult> {
        use crate::templates::{TemplateEngine, TemplateContext};
        use std::collections::HashMap;
        
        // This is a simplified execution - in a full implementation, you'd want to:
        // 1. Load the template content from the command file
        // 2. Process variables from command arguments
        // 3. Execute the rendered template
        
        tracing::info!("Executing custom command: {}", self.command_info.name);
        
        // For now, return a success result with basic information
        let mut data = HashMap::new();
        data.insert("command_name".to_string(), serde_json::Value::String(self.command_info.name.clone()));
        data.insert("command_type".to_string(), serde_json::Value::String(self.command_info.category.clone()));
        
        // Add arguments to data
        for arg in &command.arguments {
            data.insert(arg.name.clone(), serde_json::Value::String(arg.value.clone()));
        }
        
        Ok(ExecutionResult {
            success: true,
            output: format!("Custom command '{}' executed successfully", self.command_info.name),
            error: None,
            data,
            display: true,
        })
    }
}

/// Registry statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegistryStats {
    pub provider_count: usize,
    pub command_count: usize,
    pub cache_enabled: bool,
    pub validation_enabled: bool,
}

// Built-in providers (placeholder implementations)

struct BuiltinContextProvider;

impl BuiltinContextProvider {
    fn new() -> Self {
        Self
    }
}

#[async_trait::async_trait]
impl CommandProvider for BuiltinContextProvider {
    fn name(&self) -> &str {
        "builtin_context"
    }

    fn supported_types(&self) -> Vec<CommandType> {
        vec![CommandType::Context]
    }

    async fn get_commands(&self) -> Result<Vec<CommandInfo>> {
        Ok(vec![CommandInfo {
            name: "file".to_string(),
            command_type: CommandType::Context,
            description: "Reference a file".to_string(),
            help: Some("Reference a file in the project".to_string()),
            required_args: vec![ArgumentInfo {
                name: "path".to_string(),
                description: "File path".to_string(),
                arg_type: ArgumentType::FilePath,
                default: None,
                validation: None,
            }],
            optional_args: vec![],
            flags: vec![],
            examples: vec!["@file src/main.rs".to_string()],
            provider: "builtin_context".to_string(),
            category: "reference".to_string(),
            priority: 10,
            enabled: true,
        }])
    }

    async fn execute(
        &self,
        _command: &ParsedCommand,
        _context: &ExecutionContext,
    ) -> Result<ExecutionResult> {
        Ok(ExecutionResult {
            success: true,
            output: "Context command executed".to_string(),
            error: None,
            data: HashMap::new(),
            display: false,
        })
    }

    async fn get_completions(&self, _partial: &ParsedCommand) -> Result<Vec<CommandCompletion>> {
        Ok(Vec::new())
    }

    async fn validate(&self, _command: &ParsedCommand) -> Result<Vec<String>> {
        Ok(Vec::new())
    }
}

struct BuiltinActionProvider;

impl BuiltinActionProvider {
    fn new() -> Self {
        Self
    }
}

#[async_trait::async_trait]
impl CommandProvider for BuiltinActionProvider {
    fn name(&self) -> &str {
        "builtin_action"
    }

    fn supported_types(&self) -> Vec<CommandType> {
        vec![CommandType::Action]
    }

    async fn get_commands(&self) -> Result<Vec<CommandInfo>> {
        Ok(vec![CommandInfo {
            name: "edit".to_string(),
            command_type: CommandType::Action,
            description: "Edit a file".to_string(),
            help: Some("Open a file for editing".to_string()),
            required_args: vec![ArgumentInfo {
                name: "file".to_string(),
                description: "File to edit".to_string(),
                arg_type: ArgumentType::FilePath,
                default: None,
                validation: None,
            }],
            optional_args: vec![],
            flags: vec![],
            examples: vec!["/edit src/main.rs".to_string()],
            provider: "builtin_action".to_string(),
            category: "file".to_string(),
            priority: 10,
            enabled: true,
        }])
    }

    async fn execute(
        &self,
        _command: &ParsedCommand,
        _context: &ExecutionContext,
    ) -> Result<ExecutionResult> {
        Ok(ExecutionResult {
            success: true,
            output: "Action command executed".to_string(),
            error: None,
            data: HashMap::new(),
            display: true,
        })
    }

    async fn get_completions(&self, _partial: &ParsedCommand) -> Result<Vec<CommandCompletion>> {
        Ok(Vec::new())
    }

    async fn validate(&self, _command: &ParsedCommand) -> Result<Vec<String>> {
        Ok(Vec::new())
    }
}

struct BuiltinConfigProvider;

impl BuiltinConfigProvider {
    fn new() -> Self {
        Self
    }
}

#[async_trait::async_trait]
impl CommandProvider for BuiltinConfigProvider {
    fn name(&self) -> &str {
        "builtin_config"
    }

    fn supported_types(&self) -> Vec<CommandType> {
        vec![CommandType::Config]
    }

    async fn get_commands(&self) -> Result<Vec<CommandInfo>> {
        Ok(vec![CommandInfo {
            name: "set".to_string(),
            command_type: CommandType::Config,
            description: "Set configuration value".to_string(),
            help: Some("Set a configuration key to a value".to_string()),
            required_args: vec![
                ArgumentInfo {
                    name: "key".to_string(),
                    description: "Configuration key".to_string(),
                    arg_type: ArgumentType::String,
                    default: None,
                    validation: None,
                },
                ArgumentInfo {
                    name: "value".to_string(),
                    description: "Configuration value".to_string(),
                    arg_type: ArgumentType::String,
                    default: None,
                    validation: None,
                },
            ],
            optional_args: vec![],
            flags: vec![],
            examples: vec![":set theme dark".to_string()],
            provider: "builtin_config".to_string(),
            category: "configuration".to_string(),
            priority: 10,
            enabled: true,
        }])
    }

    async fn execute(
        &self,
        _command: &ParsedCommand,
        _context: &ExecutionContext,
    ) -> Result<ExecutionResult> {
        Ok(ExecutionResult {
            success: true,
            output: "Config command executed".to_string(),
            error: None,
            data: HashMap::new(),
            display: true,
        })
    }

    async fn get_completions(&self, _partial: &ParsedCommand) -> Result<Vec<CommandCompletion>> {
        Ok(Vec::new())
    }

    async fn validate(&self, _command: &ParsedCommand) -> Result<Vec<String>> {
        Ok(Vec::new())
    }
}

impl Default for CommandRegistry {
    fn default() -> Self {
        Self::new(RegistryConfig::default())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_registry_creation() {
        let registry = CommandRegistry::new(RegistryConfig::default());
        let stats = registry.get_stats().await;

        assert_eq!(stats.provider_count, 0);
        assert_eq!(stats.command_count, 0);
        assert!(stats.cache_enabled);
        assert!(stats.validation_enabled);
    }

    #[tokio::test]
    async fn test_provider_registration() {
        let registry = CommandRegistry::new(RegistryConfig::default());
        let provider = Arc::new(BuiltinContextProvider::new());

        registry.register_provider(provider).await.unwrap();

        let stats = registry.get_stats().await;
        assert_eq!(stats.provider_count, 1);
    }

    #[tokio::test]
    async fn test_command_parsing() {
        let registry = CommandRegistry::new(RegistryConfig::default());

        let result = registry
            .parse_command("@file src/main.rs", 17)
            .await
            .unwrap();

        match result {
            ParseResult::Success(command) => {
                assert_eq!(command.command_type, CommandType::Context);
                assert_eq!(command.name, "file");
            }
            _ => panic!("Expected successful parse"),
        }
    }
}
