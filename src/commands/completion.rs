// Unified Completion Engine with command-type routing and intelligent suggestions

use super::parser::{
    CommandCompletion, CommandType, CompletionType, ParsedCommand, ParsingContext,
};
use super::registry::{CommandRegistry, ExecutionContext};
use crate::errors::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

/// Unified completion provider that routes to appropriate command-specific engines
pub struct UnifiedCompletionProvider {
    /// Context completion engine (@)
    context_engine: Arc<ContextCompletionEngine>,
    /// Action completion engine (/)
    action_engine: Arc<ActionCompletionEngine>,
    /// Config completion engine (:)
    config_engine: Arc<ConfigCompletionEngine>,
    /// Command registry for command information
    registry: Arc<CommandRegistry>,
    /// Completion cache
    cache: RwLock<CompletionCache>,
    /// Configuration
    config: CompletionConfig,
}

/// Completion configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompletionConfig {
    /// Maximum number of suggestions to return
    pub max_suggestions: usize,
    /// Enable fuzzy matching
    pub fuzzy_matching: bool,
    /// Minimum score for fuzzy matches
    pub min_score: f64,
    /// Enable caching of completions
    pub enable_caching: bool,
    /// Cache TTL in seconds
    pub cache_ttl: u64,
    /// Enable context-aware completions
    pub context_aware: bool,
    /// Enable learning from user selections
    pub enable_learning: bool,
}

impl Default for CompletionConfig {
    fn default() -> Self {
        Self {
            max_suggestions: 10,
            fuzzy_matching: true,
            min_score: 0.3,
            enable_caching: true,
            cache_ttl: 300, // 5 minutes
            context_aware: true,
            enable_learning: true,
        }
    }
}

/// Completion cache
#[derive(Debug)]
struct CompletionCache {
    /// Cached completions by input hash
    entries: HashMap<u64, CacheEntry>,
    /// Learning data for user selections
    selection_history: HashMap<String, SelectionStats>,
}

/// Cache entry
#[derive(Debug, Clone)]
struct CacheEntry {
    /// Cached completions
    completions: Vec<CommandCompletion>,
    /// Timestamp when cached
    timestamp: std::time::Instant,
    /// Number of times this cache entry was used
    hit_count: u32,
}

/// Selection statistics for learning
#[derive(Debug, Clone, Serialize, Deserialize)]
struct SelectionStats {
    /// Number of times selected
    selection_count: u32,
    /// Last selection timestamp
    last_selected: std::time::SystemTime,
    /// Average position when selected
    avg_position: f64,
    /// Context patterns when selected
    contexts: Vec<String>,
}

impl UnifiedCompletionProvider {
    /// Create a new unified completion provider
    pub fn new(registry: Arc<CommandRegistry>, config: CompletionConfig) -> Self {
        Self {
            context_engine: Arc::new(ContextCompletionEngine::new()),
            action_engine: Arc::new(ActionCompletionEngine::new()),
            config_engine: Arc::new(ConfigCompletionEngine::new()),
            registry,
            cache: RwLock::new(CompletionCache {
                entries: HashMap::new(),
                selection_history: HashMap::new(),
            }),
            config,
        }
    }

    /// Get completions for input at cursor position
    pub async fn get_completions(
        &self,
        input: &str,
        cursor_pos: usize,
        context: Option<&ExecutionContext>,
    ) -> Result<Vec<CommandCompletion>> {
        // Create cache key
        let cache_key = self.create_cache_key(input, cursor_pos);

        // Check cache first
        if self.config.enable_caching {
            if let Some(cached) = self.get_cached_completions(cache_key).await {
                return Ok(cached);
            }
        }

        // Parse the current input to determine command type
        let parsing_context = ParsingContext::new(input.to_string(), cursor_pos);
        let parse_result = self.registry.parse_command(input, cursor_pos).await?;

        let mut completions = Vec::new();

        // Get registry completions
        let registry_completions = self.registry.get_completions(input, cursor_pos).await?;
        completions.extend(registry_completions);

        // Get engine-specific completions based on detected command type
        let engine_completions = match self.detect_command_type(input, cursor_pos) {
            Some(CommandType::Context) => {
                self.context_engine
                    .get_completions(&parsing_context, context)
                    .await?
            }
            Some(CommandType::Action) => {
                self.action_engine
                    .get_completions(&parsing_context, context)
                    .await?
            }
            Some(CommandType::Config) => {
                self.config_engine
                    .get_completions(&parsing_context, context)
                    .await?
            }
            None => {
                // No specific command type detected, provide general suggestions
                self.get_general_completions(input, cursor_pos).await?
            }
        };

        completions.extend(engine_completions);

        // Apply filtering and ranking
        completions = self
            .filter_and_rank_completions(completions, input, cursor_pos)
            .await?;

        // Limit results
        completions.truncate(self.config.max_suggestions);

        // Cache results
        if self.config.enable_caching {
            self.cache_completions(cache_key, completions.clone()).await;
        }

        Ok(completions)
    }

    /// Record user selection for learning
    pub async fn record_selection(
        &self,
        input: &str,
        selected: &CommandCompletion,
        position: usize,
    ) {
        if !self.config.enable_learning {
            return;
        }

        let mut cache = self.cache.write().await;
        let stats = cache
            .selection_history
            .entry(selected.text.clone())
            .or_insert_with(|| SelectionStats {
                selection_count: 0,
                last_selected: std::time::SystemTime::now(),
                avg_position: 0.0,
                contexts: Vec::new(),
            });

        stats.selection_count += 1;
        stats.last_selected = std::time::SystemTime::now();

        // Update average position
        let total_selections = stats.selection_count as f64;
        stats.avg_position =
            ((stats.avg_position * (total_selections - 1.0)) + position as f64) / total_selections;

        // Store context pattern
        let context_pattern = self.extract_context_pattern(input);
        if !stats.contexts.contains(&context_pattern) && stats.contexts.len() < 10 {
            stats.contexts.push(context_pattern);
        }
    }

    /// Clear completion cache
    pub async fn clear_cache(&self) {
        let mut cache = self.cache.write().await;
        cache.entries.clear();
    }

    /// Get completion statistics
    pub async fn get_stats(&self) -> CompletionStats {
        let cache = self.cache.read().await;
        CompletionStats {
            cache_entries: cache.entries.len(),
            total_selections: cache
                .selection_history
                .values()
                .map(|s| s.selection_count)
                .sum(),
            unique_selections: cache.selection_history.len(),
            cache_enabled: self.config.enable_caching,
            learning_enabled: self.config.enable_learning,
        }
    }

    // Private helper methods

    /// Detect command type from input and cursor position
    fn detect_command_type(&self, input: &str, cursor_pos: usize) -> Option<CommandType> {
        if cursor_pos == 0 || cursor_pos > input.len() {
            return None;
        }

        let before_cursor = &input[..cursor_pos];

        // Find the last command prefix before cursor
        for (i, ch) in before_cursor.char_indices().rev() {
            match ch {
                '@' => return Some(CommandType::Context),
                '/' => return Some(CommandType::Action),
                ':' => return Some(CommandType::Config),
                ' ' | '\t' | '\n' => continue,
                _ => break,
            }
        }

        None
    }

    /// Get general completions when no specific command type is detected
    async fn get_general_completions(
        &self,
        _input: &str,
        _cursor_pos: usize,
    ) -> Result<Vec<CommandCompletion>> {
        Ok(vec![
            CommandCompletion::new("@".to_string(), CompletionType::Command)
                .with_description("Context commands - reference files, tools, concepts".to_string())
                .with_priority(100),
            CommandCompletion::new("/".to_string(), CompletionType::Command)
                .with_description("Action commands - execute operations and workflows".to_string())
                .with_priority(100),
            CommandCompletion::new(":".to_string(), CompletionType::Command)
                .with_description("Config commands - manage settings and preferences".to_string())
                .with_priority(100),
        ])
    }

    /// Filter and rank completions based on relevance and user history
    async fn filter_and_rank_completions(
        &self,
        mut completions: Vec<CommandCompletion>,
        input: &str,
        cursor_pos: usize,
    ) -> Result<Vec<CommandCompletion>> {
        if completions.is_empty() {
            return Ok(completions);
        }

        // Extract query for scoring
        let query = self.extract_query(input, cursor_pos);

        // Score and filter completions
        let cache = self.cache.read().await;

        for completion in &mut completions {
            let mut score = completion.priority as f64;

            // Fuzzy matching score
            if self.config.fuzzy_matching && !query.is_empty() {
                let fuzzy_score = self.calculate_fuzzy_score(&completion.text, &query);
                if fuzzy_score < self.config.min_score {
                    score = 0.0; // Filter out low-scoring matches
                } else {
                    score += fuzzy_score * 100.0;
                }
            }

            // Learning-based score boost
            if let Some(stats) = cache.selection_history.get(&completion.text) {
                // Boost based on selection frequency
                score += (stats.selection_count as f64).ln() * 10.0;

                // Boost based on recency
                if let Ok(duration) = stats.last_selected.elapsed() {
                    let days_ago = duration.as_secs() as f64 / (24.0 * 3600.0);
                    score += (7.0 - days_ago.min(7.0)).max(0.0) * 5.0;
                }

                // Boost based on average position (lower position = higher boost)
                score += (10.0 - stats.avg_position).max(0.0) * 2.0;
            }

            completion.priority = score as u8;
        }

        // Filter out zero-scored completions
        completions.retain(|c| c.priority > 0);

        // Sort by score (priority)
        completions.sort_by(|a, b| b.priority.cmp(&a.priority));

        // Remove duplicates
        completions.dedup_by(|a, b| a.text == b.text);

        Ok(completions)
    }

    /// Create cache key for input and cursor position
    fn create_cache_key(&self, input: &str, cursor_pos: usize) -> u64 {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        input.hash(&mut hasher);
        cursor_pos.hash(&mut hasher);
        hasher.finish()
    }

    /// Get cached completions if available and not expired
    async fn get_cached_completions(&self, cache_key: u64) -> Option<Vec<CommandCompletion>> {
        let cache = self.cache.read().await;

        if let Some(entry) = cache.entries.get(&cache_key) {
            if entry.timestamp.elapsed().as_secs() < self.config.cache_ttl {
                return Some(entry.completions.clone());
            }
        }

        None
    }

    /// Cache completions
    async fn cache_completions(&self, cache_key: u64, completions: Vec<CommandCompletion>) {
        let mut cache = self.cache.write().await;

        // Clean expired entries if cache is getting large
        if cache.entries.len() > 1000 {
            cache
                .entries
                .retain(|_, entry| entry.timestamp.elapsed().as_secs() < self.config.cache_ttl);
        }

        cache.entries.insert(
            cache_key,
            CacheEntry {
                completions,
                timestamp: std::time::Instant::now(),
                hit_count: 0,
            },
        );
    }

    /// Extract query string for completion matching
    fn extract_query(&self, input: &str, cursor_pos: usize) -> String {
        if cursor_pos == 0 || cursor_pos > input.len() {
            return String::new();
        }

        let before_cursor = &input[..cursor_pos];

        // Find the start of the current "word"
        let start = before_cursor
            .rfind(|c: char| c.is_whitespace() || "@/:".contains(c))
            .map(|i| i + 1)
            .unwrap_or(0);

        before_cursor[start..].to_string()
    }

    /// Calculate fuzzy matching score
    fn calculate_fuzzy_score(&self, text: &str, query: &str) -> f64 {
        if query.is_empty() {
            return 1.0;
        }

        let text_lower = text.to_lowercase();
        let query_lower = query.to_lowercase();

        // Exact match
        if text_lower == query_lower {
            return 1.0;
        }

        // Prefix match
        if text_lower.starts_with(&query_lower) {
            return 0.9;
        }

        // Substring match
        if let Some(pos) = text_lower.find(&query_lower) {
            return 0.7 - (pos as f64 / text_lower.len() as f64) * 0.2;
        }

        // Character sequence match
        let mut text_chars = text_lower.chars();
        let mut score = 0.0;
        let mut matches = 0;

        for query_char in query_lower.chars() {
            if let Some(pos) = text_chars.position(|c| c == query_char) {
                matches += 1;
                score += 1.0 / (pos + 1) as f64;
            }
        }

        if matches == query_lower.chars().count() {
            score / query_lower.len() as f64 * 0.5
        } else {
            0.0
        }
    }

    /// Extract context pattern for learning
    fn extract_context_pattern(&self, input: &str) -> String {
        // Extract a simple pattern of prefixes and word boundaries
        input
            .chars()
            .map(|c| {
                if "@/:".contains(c) {
                    c
                } else if c.is_alphabetic() {
                    'w'
                } else if c.is_numeric() {
                    'n'
                } else if c.is_whitespace() {
                    ' '
                } else {
                    '_'
                }
            })
            .collect::<String>()
            .split_whitespace()
            .collect::<Vec<_>>()
            .join(" ")
    }
}

/// Completion statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompletionStats {
    pub cache_entries: usize,
    pub total_selections: u32,
    pub unique_selections: usize,
    pub cache_enabled: bool,
    pub learning_enabled: bool,
}

// Engine implementations

/// Context completion engine (@)
pub struct ContextCompletionEngine {
    // Add context-specific completion logic
}

impl ContextCompletionEngine {
    pub fn new() -> Self {
        Self {}
    }

    pub async fn get_completions(
        &self,
        _context: &ParsingContext,
        _exec_context: Option<&ExecutionContext>,
    ) -> Result<Vec<CommandCompletion>> {
        // Implementation would include file system scanning, tool enumeration, etc.
        Ok(vec![
            CommandCompletion::new(
                "file:".to_string(),
                CompletionType::Custom("context".to_string()),
            )
            .with_description("Reference a file".to_string())
            .with_priority(10),
            CommandCompletion::new(
                "tool:".to_string(),
                CompletionType::Custom("context".to_string()),
            )
            .with_description("Reference a tool".to_string())
            .with_priority(9),
        ])
    }
}

/// Action completion engine (/)
pub struct ActionCompletionEngine {
    // Add action-specific completion logic
}

impl ActionCompletionEngine {
    pub fn new() -> Self {
        Self {}
    }

    pub async fn get_completions(
        &self,
        _context: &ParsingContext,
        _exec_context: Option<&ExecutionContext>,
    ) -> Result<Vec<CommandCompletion>> {
        // Implementation would include tool scanning, macro enumeration, etc.
        Ok(vec![
            CommandCompletion::new("edit".to_string(), CompletionType::Command)
                .with_description("Edit a file".to_string())
                .with_priority(10),
            CommandCompletion::new("search".to_string(), CompletionType::Command)
                .with_description("Search for text".to_string())
                .with_priority(9),
        ])
    }
}

/// Config completion engine (:)
pub struct ConfigCompletionEngine {
    // Add config-specific completion logic
}

impl ConfigCompletionEngine {
    pub fn new() -> Self {
        Self {}
    }

    pub async fn get_completions(
        &self,
        _context: &ParsingContext,
        _exec_context: Option<&ExecutionContext>,
    ) -> Result<Vec<CommandCompletion>> {
        // Implementation would include config key enumeration, mode suggestions, etc.
        Ok(vec![
            CommandCompletion::new("set".to_string(), CompletionType::Command)
                .with_description("Set configuration value".to_string())
                .with_priority(10),
            CommandCompletion::new("theme".to_string(), CompletionType::Command)
                .with_description("Change theme".to_string())
                .with_priority(9),
        ])
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::commands::registry::RegistryConfig;

    #[tokio::test]
    async fn test_completion_provider_creation() {
        let registry = Arc::new(CommandRegistry::new(RegistryConfig::default()));
        let provider = UnifiedCompletionProvider::new(registry, CompletionConfig::default());

        let stats = provider.get_stats().await;
        assert_eq!(stats.cache_entries, 0);
        assert_eq!(stats.total_selections, 0);
    }

    #[tokio::test]
    async fn test_command_type_detection() {
        let registry = Arc::new(CommandRegistry::new(RegistryConfig::default()));
        let provider = UnifiedCompletionProvider::new(registry, CompletionConfig::default());

        assert_eq!(
            provider.detect_command_type("@file", 5),
            Some(CommandType::Context)
        );
        assert_eq!(
            provider.detect_command_type("/edit", 5),
            Some(CommandType::Action)
        );
        assert_eq!(
            provider.detect_command_type(":set", 4),
            Some(CommandType::Config)
        );
        assert_eq!(provider.detect_command_type("hello", 5), None);
    }

    #[tokio::test]
    async fn test_fuzzy_scoring() {
        let registry = Arc::new(CommandRegistry::new(RegistryConfig::default()));
        let provider = UnifiedCompletionProvider::new(registry, CompletionConfig::default());

        assert_eq!(provider.calculate_fuzzy_score("edit", "edit"), 1.0);
        assert!(provider.calculate_fuzzy_score("edit", "ed") > 0.8);
        assert!(provider.calculate_fuzzy_score("search", "sea") > 0.8);
        assert!(provider.calculate_fuzzy_score("configure", "conf") > 0.6);
    }
}
