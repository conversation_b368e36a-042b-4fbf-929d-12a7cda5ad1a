// Comprehensive unit tests for the command system

#[cfg(test)]
mod tests {
    use super::*;
    use crate::commands::completion::*;
    use crate::commands::execution::*;
    use crate::commands::parser::*;
    use crate::commands::registry::*;
    use crate::tools::PermissionLevel;
    use std::sync::Arc;
    use std::time::Duration;
    use tokio::time::timeout;

    // Test data setup
    fn create_test_registry() -> Arc<CommandRegistry> {
        Arc::new(CommandRegistry::new(RegistryConfig::default()))
    }

    fn create_test_context() -> ExecutionContext {
        ExecutionContext {
            working_directory: std::env::current_dir().unwrap_or_default(),
            permissions: PermissionLevel::Full,
            session_data: std::collections::HashMap::new(),
            environment: std::env::vars().collect(),
            tool_registry: None,
        }
    }

    // Parser Tests
    mod parser_tests {
        use super::*;

        #[tokio::test]
        async fn test_command_type_detection() {
            let parser = MainCommandParser::new();

            // Test context commands
            let context = ParsingContext::new("@file src/main.rs".to_string(), 17);
            let result = parser.parse(&context).await.unwrap();
            match result {
                ParseResult::Success(cmd) => {
                    assert_eq!(cmd.command_type, CommandType::Context);
                    assert_eq!(cmd.name, "file");
                }
                _ => panic!("Expected successful parse"),
            }

            // Test action commands
            let context = ParsingContext::new("/edit src/main.rs".to_string(), 16);
            let result = parser.parse(&context).await.unwrap();
            match result {
                ParseResult::Success(cmd) => {
                    assert_eq!(cmd.command_type, CommandType::Action);
                    assert_eq!(cmd.name, "edit");
                }
                _ => panic!("Expected successful parse"),
            }

            // Test config commands
            let context = ParsingContext::new(":set theme dark".to_string(), 15);
            let result = parser.parse(&context).await.unwrap();
            match result {
                ParseResult::Success(cmd) => {
                    assert_eq!(cmd.command_type, CommandType::Config);
                    assert_eq!(cmd.name, "set");
                }
                _ => panic!("Expected successful parse"),
            }
        }

        #[tokio::test]
        async fn test_command_parsing_with_arguments() {
            let parser = MainCommandParser::new();

            let context =
                ParsingContext::new("/search pattern --regex --case-sensitive".to_string(), 40);
            let result = parser.parse(&context).await.unwrap();

            match result {
                ParseResult::Success(cmd) => {
                    assert_eq!(cmd.command_type, CommandType::Action);
                    assert_eq!(cmd.name, "search");
                    assert_eq!(cmd.args.len(), 1);
                    assert_eq!(cmd.args[0], "pattern");
                    assert!(cmd.has_option("regex"));
                    assert!(cmd.has_option("case-sensitive"));
                }
                _ => panic!("Expected successful parse with options"),
            }
        }

        #[tokio::test]
        async fn test_partial_command_parsing() {
            let parser = MainCommandParser::new();

            let context = ParsingContext::new("@fil".to_string(), 4);
            let result = parser.parse(&context).await.unwrap();

            match result {
                ParseResult::Partial(cmd) => {
                    assert_eq!(cmd.command_type, CommandType::Context);
                    assert_eq!(cmd.name, "fil");
                    assert!(!cmd.is_complete);
                }
                _ => panic!("Expected partial parse"),
            }
        }

        #[tokio::test]
        async fn test_invalid_command_parsing() {
            let parser = MainCommandParser::new();

            let context = ParsingContext::new("@unknown_command".to_string(), 16);
            let result = parser.parse(&context).await.unwrap();

            // Should still parse successfully but validation would catch unknown commands
            match result {
                ParseResult::Success(cmd) | ParseResult::Partial(cmd) => {
                    assert_eq!(cmd.command_type, CommandType::Context);
                    assert_eq!(cmd.name, "unknown_command");
                }
                ParseResult::Invalid(_) => {
                    // This is also acceptable
                }
                _ => panic!("Expected parse result"),
            }
        }

        #[tokio::test]
        async fn test_multiple_command_detection() {
            let parser = MainCommandParser::new();

            let commands =
                parser.detect_commands("Check @file:src/main.rs and /edit it with :set theme dark");

            assert_eq!(commands.len(), 3);
            assert_eq!(commands[0].0, CommandType::Context);
            assert_eq!(commands[1].0, CommandType::Action);
            assert_eq!(commands[2].0, CommandType::Config);
        }

        #[tokio::test]
        async fn test_completion_suggestions() {
            let parser = MainCommandParser::new();

            let completions = parser.get_completions("@f", 2).await.unwrap();

            assert!(!completions.is_empty());
            // Should include suggestions for context commands starting with 'f'
            assert!(completions.iter().any(|c| c.text.contains("file")));
        }
    }

    // Registry Tests
    mod registry_tests {
        use super::*;

        #[tokio::test]
        async fn test_registry_creation() {
            let registry = create_test_registry();
            let stats = registry.get_stats().await;

            assert_eq!(stats.provider_count, 0);
            assert_eq!(stats.command_count, 0);
        }

        #[tokio::test]
        async fn test_builtin_provider_registration() {
            let registry = create_test_registry();
            registry.register_builtin_providers().await.unwrap();

            let stats = registry.get_stats().await;
            assert!(stats.provider_count > 0);
            assert!(stats.command_count > 0);
        }

        #[tokio::test]
        async fn test_command_lookup() {
            let registry = create_test_registry();
            registry.register_builtin_providers().await.unwrap();

            let file_cmd = registry
                .get_command_info(CommandType::Context, "file")
                .await
                .unwrap();
            assert!(file_cmd.is_some());

            let cmd = file_cmd.unwrap();
            assert_eq!(cmd.name, "file");
            assert_eq!(cmd.command_type, CommandType::Context);
            assert!(cmd.enabled);
        }

        #[tokio::test]
        async fn test_command_validation() {
            let registry = create_test_registry();
            registry.register_builtin_providers().await.unwrap();

            let mut command = ParsedCommand::new(CommandType::Context, "@file".to_string(), 5);
            command.set_name("file".to_string());

            // Missing required argument
            let errors = registry.validate_command(&command).await.unwrap();
            assert!(!errors.is_empty());

            // With required argument
            command.add_arg("src/main.rs".to_string());
            command.mark_complete();
            let errors = registry.validate_command(&command).await.unwrap();
            assert!(errors.is_empty());
        }

        #[tokio::test]
        async fn test_command_execution() {
            let registry = create_test_registry();
            registry.register_builtin_providers().await.unwrap();

            let mut command =
                ParsedCommand::new(CommandType::Context, "@file src/main.rs".to_string(), 17);
            command.set_name("file".to_string());
            command.add_arg("src/main.rs".to_string());
            command.mark_complete();

            let context = create_test_context();
            let result = registry.execute_command(&command, &context).await.unwrap();

            assert!(result.success);
        }

        #[tokio::test]
        async fn test_cache_functionality() {
            let config = RegistryConfig {
                enable_caching: true,
                cache_ttl: 1,
                max_cache_size: 100,
                enable_validation: true,
                default_timeout: 30,
            };

            let registry = CommandRegistry::new(config);
            registry.register_builtin_providers().await.unwrap();

            // Get commands (should cache)
            let commands1 = registry.get_all_commands().await.unwrap();
            let commands2 = registry.get_all_commands().await.unwrap();

            assert_eq!(commands1.len(), commands2.len());

            // Clear cache
            registry.clear_cache().await;

            let commands3 = registry.get_all_commands().await.unwrap();
            assert_eq!(commands1.len(), commands3.len());
        }
    }

    // Completion Tests
    mod completion_tests {
        use super::*;

        #[tokio::test]
        async fn test_completion_provider_creation() {
            let registry = create_test_registry();
            let provider = UnifiedCompletionProvider::new(registry, CompletionConfig::default());

            let stats = provider.get_stats().await;
            assert_eq!(stats.cache_entries, 0);
            assert_eq!(stats.total_selections, 0);
        }

        #[tokio::test]
        async fn test_context_completions() {
            let registry = create_test_registry();
            registry.register_builtin_providers().await.unwrap();

            let provider = UnifiedCompletionProvider::new(registry, CompletionConfig::default());
            let context = create_test_context();

            let completions = provider
                .get_completions("@f", 2, Some(&context))
                .await
                .unwrap();

            assert!(!completions.is_empty());
            assert!(completions.iter().any(|c| c.text.contains("file")));
        }

        #[tokio::test]
        async fn test_action_completions() {
            let registry = create_test_registry();
            registry.register_builtin_providers().await.unwrap();

            let provider = UnifiedCompletionProvider::new(registry, CompletionConfig::default());
            let context = create_test_context();

            let completions = provider
                .get_completions("/e", 2, Some(&context))
                .await
                .unwrap();

            assert!(!completions.is_empty());
            assert!(completions.iter().any(|c| c.text.contains("edit")));
        }

        #[tokio::test]
        async fn test_config_completions() {
            let registry = create_test_registry();
            registry.register_builtin_providers().await.unwrap();

            let provider = UnifiedCompletionProvider::new(registry, CompletionConfig::default());
            let context = create_test_context();

            let completions = provider
                .get_completions(":s", 2, Some(&context))
                .await
                .unwrap();

            assert!(!completions.is_empty());
            assert!(completions.iter().any(|c| c.text.contains("set")));
        }

        #[tokio::test]
        async fn test_completion_learning() {
            let registry = create_test_registry();
            let provider = UnifiedCompletionProvider::new(registry, CompletionConfig::default());

            let completion = CommandCompletion::new("test".to_string(), CompletionType::Command);

            // Record some selections
            provider
                .record_selection("test input", &completion, 0)
                .await;
            provider
                .record_selection("test input", &completion, 1)
                .await;

            let stats = provider.get_stats().await;
            assert_eq!(stats.total_selections, 2);
            assert_eq!(stats.unique_selections, 1);
        }

        #[tokio::test]
        async fn test_completion_caching() {
            let config = CompletionConfig {
                enable_caching: true,
                cache_ttl: 300,
                max_suggestions: 10,
                ..Default::default()
            };

            let registry = create_test_registry();
            let provider = UnifiedCompletionProvider::new(registry, config);

            // First call should populate cache
            let context = create_test_context();
            let _completions1 = provider
                .get_completions("@", 1, Some(&context))
                .await
                .unwrap();

            let stats1 = provider.get_stats().await;
            let cache_entries1 = stats1.cache_entries;

            // Second call should use cache
            let _completions2 = provider
                .get_completions("@", 1, Some(&context))
                .await
                .unwrap();

            let stats2 = provider.get_stats().await;
            assert_eq!(stats2.cache_entries, cache_entries1);
        }
    }

    // Execution Tests
    mod execution_tests {
        use super::*;

        #[tokio::test]
        async fn test_executor_creation() {
            let registry = create_test_registry();
            let executor = CommandExecutor::new(registry, ExecutorConfig::default());

            let stats = executor.get_stats().await;
            assert_eq!(stats.active_executions, 0);
            assert_eq!(stats.total_executions, 0);
        }

        #[tokio::test]
        async fn test_command_execution() {
            let registry = create_test_registry();
            registry.register_builtin_providers().await.unwrap();

            let executor = CommandExecutor::new(registry, ExecutorConfig::default());

            let mut command =
                ParsedCommand::new(CommandType::Context, "@file test.rs".to_string(), 12);
            command.set_name("file".to_string());
            command.add_arg("test.rs".to_string());
            command.mark_complete();

            let context = create_test_context();
            let result = executor.execute(command, context).await.unwrap();

            assert!(result.success);
        }

        #[tokio::test]
        async fn test_execution_timeout() {
            let config = ExecutorConfig {
                default_timeout: Duration::from_millis(100),
                ..Default::default()
            };

            let registry = create_test_registry();
            let executor = CommandExecutor::new(registry, config);

            let command = ParsedCommand::new(CommandType::Action, "/slow_command".to_string(), 13);
            let context = create_test_context();

            let start = std::time::Instant::now();
            let result = executor.execute(command, context).await.unwrap();
            let elapsed = start.elapsed();

            // Should timeout quickly
            assert!(elapsed < Duration::from_secs(1));
            assert!(!result.success);
            assert!(result.error.is_some());
        }

        #[tokio::test]
        async fn test_execution_validation() {
            let config = ExecutorConfig {
                validate_before_execution: true,
                ..Default::default()
            };

            let registry = create_test_registry();
            registry.register_builtin_providers().await.unwrap();

            let executor = CommandExecutor::new(registry, config);

            // Command with missing required arguments
            let mut command = ParsedCommand::new(CommandType::Context, "@file".to_string(), 5);
            command.set_name("file".to_string());
            // Not adding required argument

            let context = create_test_context();
            let result = executor.execute(command, context).await.unwrap();

            assert!(!result.success);
            assert!(result.error.is_some());
            assert!(result.error.unwrap().contains("Validation"));
        }

        #[tokio::test]
        async fn test_dangerous_command_detection() {
            let config = ExecutorConfig {
                allow_dangerous_commands: false,
                ..Default::default()
            };

            let registry = create_test_registry();
            let executor = CommandExecutor::new(registry, config);

            let mut command =
                ParsedCommand::new(CommandType::Action, "/delete --force".to_string(), 14);
            command.set_name("delete".to_string());
            command.add_arg("--force".to_string());

            let context = create_test_context();
            let result = executor.execute(command, context).await.unwrap();

            assert!(!result.success);
            assert!(result.error.is_some());
            assert!(result.error.unwrap().contains("Dangerous"));
        }

        #[tokio::test]
        async fn test_execution_history() {
            let config = ExecutorConfig {
                enable_history: true,
                max_history_entries: 10,
                ..Default::default()
            };

            let registry = create_test_registry();
            registry.register_builtin_providers().await.unwrap();

            let executor = CommandExecutor::new(registry, config);

            // Execute a few commands
            for i in 0..3 {
                let mut command = ParsedCommand::new(
                    CommandType::Context,
                    format!("@file test{}.rs", i),
                    12 + i.to_string().len(),
                );
                command.set_name("file".to_string());
                command.add_arg(format!("test{}.rs", i));
                command.mark_complete();

                let context = create_test_context();
                let _ = executor.execute(command, context).await.unwrap();
            }

            let history = executor.get_execution_history(None).await;
            assert_eq!(history.len(), 3);

            // Check that history is sorted by most recent first
            for i in 1..history.len() {
                assert!(history[i - 1].start_time >= history[i].start_time);
            }
        }

        #[tokio::test]
        async fn test_concurrent_execution() {
            let config = ExecutorConfig {
                max_concurrent_executions: 2,
                ..Default::default()
            };

            let registry = create_test_registry();
            registry.register_builtin_providers().await.unwrap();

            let executor = Arc::new(CommandExecutor::new(registry, config));

            // Start multiple executions concurrently
            let mut handles = Vec::new();

            for i in 0..3 {
                let executor_clone = executor.clone();
                let handle = tokio::spawn(async move {
                    let mut command = ParsedCommand::new(
                        CommandType::Context,
                        format!("@file test{}.rs", i),
                        12 + i.to_string().len(),
                    );
                    command.set_name("file".to_string());
                    command.add_arg(format!("test{}.rs", i));
                    command.mark_complete();

                    let context = create_test_context();
                    executor_clone.execute(command, context).await
                });
                handles.push(handle);
            }

            // Wait for all to complete
            let results: Vec<_> = futures::future::join_all(handles).await;

            // All should succeed
            for result in results {
                let exec_result = result.unwrap().unwrap();
                assert!(exec_result.success);
            }
        }

        #[tokio::test]
        async fn test_execution_cancellation() {
            let registry = create_test_registry();
            let executor = CommandExecutor::new(registry, ExecutorConfig::default());

            let command =
                ParsedCommand::new(CommandType::Action, "/long_running_command".to_string(), 21);
            let context = create_test_context();

            // Start execution in background
            let executor_ref = &executor;
            let execution_future = executor_ref.execute(command, context);

            // Let it start
            tokio::time::sleep(Duration::from_millis(10)).await;

            // Get active executions and cancel one
            let active = executor.get_active_executions().await;
            if let Some(execution_id) = active.first() {
                let cancelled = executor.cancel_execution(*execution_id).await.unwrap();
                assert!(cancelled);
            }

            // Wait for execution to complete (should be cancelled)
            let result = timeout(Duration::from_secs(1), execution_future).await;
            assert!(result.is_ok()); // Should complete quickly due to cancellation
        }
    }

    // Integration Tests
    mod integration_tests {
        use super::*;

        #[tokio::test]
        async fn test_full_command_flow() {
            // Initialize complete system
            let config = CommandConfig::default();
            let registry = Arc::new(initialize_command_system(config).await.unwrap());

            // Create completion provider
            let completion_provider =
                UnifiedCompletionProvider::new(registry.clone(), CompletionConfig::default());

            // Create executor
            let executor = CommandExecutor::new(registry.clone(), ExecutorConfig::default());

            // Test completion
            let context = create_test_context();
            let completions = completion_provider
                .get_completions("@f", 2, Some(&context))
                .await
                .unwrap();
            assert!(!completions.is_empty());

            // Parse a command
            let parse_result = registry
                .parse_command("@file src/main.rs", 17)
                .await
                .unwrap();

            match parse_result {
                ParseResult::Success(command) => {
                    // Validate the command
                    let errors = registry.validate_command(&command).await.unwrap();
                    assert!(errors.is_empty());

                    // Execute the command
                    let exec_result = executor.execute(command, context).await.unwrap();
                    assert!(exec_result.success);
                }
                _ => panic!("Expected successful parse"),
            }
        }

        #[tokio::test]
        async fn test_error_handling_flow() {
            let registry = create_test_registry();
            registry.register_builtin_providers().await.unwrap();

            let executor = CommandExecutor::new(registry.clone(), ExecutorConfig::default());

            // Test invalid command
            let mut invalid_command =
                ParsedCommand::new(CommandType::Context, "@invalid".to_string(), 8);
            invalid_command.set_name("invalid".to_string());

            let context = create_test_context();
            let result = executor.execute(invalid_command, context).await.unwrap();
            assert!(!result.success);
            assert!(result.error.is_some());
        }

        #[tokio::test]
        async fn test_performance_characteristics() {
            let registry = create_test_registry();
            registry.register_builtin_providers().await.unwrap();

            let completion_provider =
                UnifiedCompletionProvider::new(registry.clone(), CompletionConfig::default());

            // Test completion performance
            let start = std::time::Instant::now();
            let context = create_test_context();

            for _ in 0..100 {
                let _ = completion_provider
                    .get_completions("@", 1, Some(&context))
                    .await
                    .unwrap();
            }

            let elapsed = start.elapsed();
            assert!(
                elapsed < Duration::from_secs(1),
                "Completions should be fast"
            );

            // Test parsing performance
            let start = std::time::Instant::now();

            for i in 0..100 {
                let input = format!("@file test{}.rs", i);
                let _ = registry.parse_command(&input, input.len()).await.unwrap();
            }

            let elapsed = start.elapsed();
            assert!(elapsed < Duration::from_secs(1), "Parsing should be fast");
        }
    }
}
