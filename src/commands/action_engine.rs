//! Action Command Engine - Core orchestrator for all action commands
//!
//! Provides the main interface for executing /memory, MCP tools, and custom commands.

use crate::errors::{AutorunError, Result};
use crate::storage::memory_store::{MemoryStore, Memory, MemoryType, SearchFilters};
use crate::mcp::client::McpClient;
use crate::commands::types::{ActionCommandType, CommandResult, CommandContext};
use crate::tools::ExecutionContext;

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use uuid::Uuid;
use serde::{Deserialize, Serialize};

/// Main orchestrator for all action commands
pub struct ActionEngine {
    memory_store: Arc<RwLock<MemoryStore>>,
    mcp_client: Arc<RwLock<McpClient>>,
    execution_tracker: Arc<RwLock<HashMap<Uuid, ExecutionStatus>>>,
}

/// Execution status for tracking command progress
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionStatus {
    pub id: Uuid,
    pub command: String,
    pub status: ExecutionState,
    pub progress: u8,
    pub started_at: std::time::SystemTime,
    pub message: Option<String>,
}

/// Execution state enumeration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ExecutionState {
    Queued,
    Running,
    Completed,
    Failed,
    Cancelled,
}

impl ActionEngine {
    /// Create a new action engine
    pub fn new(
        memory_store: Arc<RwLock<MemoryStore>>,
        mcp_client: Arc<RwLock<McpClient>>,
    ) -> Self {
        Self {
            memory_store,
            mcp_client,
            execution_tracker: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Execute an action command
    pub async fn execute_action(
        &self,
        action_type: ActionCommandType,
        args: Vec<String>,
        context: &CommandContext,
        execution_context: &ExecutionContext,
    ) -> Result<CommandResult> {
        let execution_id = Uuid::new_v4();
        
        // Start execution tracking
        self.start_execution(execution_id, format!("{:?}", action_type)).await;

        let result = match action_type {
            ActionCommandType::Memory => {
                self.execute_memory_command(args, execution_context).await
            },
            ActionCommandType::McpTool(tool_name) => {
                self.execute_mcp_tool(tool_name, args, execution_context).await
            },
            ActionCommandType::Custom(command_name) => {
                self.execute_custom_command(command_name, args, execution_context).await
            },
        };

        // Complete execution tracking
        match &result {
            Ok(_) => self.complete_execution(execution_id, "Completed successfully").await,
            Err(e) => self.fail_execution(execution_id, &e.to_string()).await,
        }

        result
    }

    /// Execute memory commands
    async fn execute_memory_command(
        &self,
        args: Vec<String>,
        _execution_context: &ExecutionContext,
    ) -> Result<CommandResult> {
        if args.is_empty() {
            return Ok(CommandResult::Error("Memory command requires subcommand".to_string()));
        }

        let subcommand = &args[0];
        let mut store = self.memory_store.write().await;

        match subcommand.as_str() {
            "create" => {
                if args.len() < 3 {
                    return Ok(CommandResult::Error("Usage: /memory create <title> <content> [type] [tags...]".to_string()));
                }
                
                let title = args[1].clone();
                let content = args[2].clone();
                let memory_type = if args.len() > 3 {
                    match args[3].as_str() {
                        "code" => MemoryType::Code,
                        "project" => MemoryType::Project,
                        "learning" => MemoryType::Learning,
                        _ => MemoryType::Note,
                    }
                } else {
                    MemoryType::Note
                };
                
                let tags = if args.len() > 4 {
                    args[4..].to_vec()
                } else {
                    Vec::new()
                };

                let memory = store.create_memory(title, content, memory_type, tags, 50).await?;
                Ok(CommandResult::Success(Some(format!("Created memory with ID: {}", memory.id))))
            },
            "search" => {
                if args.len() < 2 {
                    return Ok(CommandResult::Error("Usage: /memory search <query>".to_string()));
                }
                
                let query = args[1..].join(" ");
                let filters = SearchFilters::default();
                let results = store.search_memories(&query, filters, 10).await?;
                
                let result_text = if results.is_empty() {
                    "No memories found".to_string()
                } else {
                    results.iter()
                        .map(|r| format!("• {} ({})", r.memory.title, r.memory.id))
                        .collect::<Vec<_>>()
                        .join("\n")
                };
                
                Ok(CommandResult::Success(Some(result_text)))
            },
            "list" => {
                let tag = args.get(1).map(|s| s.as_str()).unwrap_or("");
                let memories = if tag.is_empty() {
                    // Get recent memories - simplified implementation
                    store.search_memories("", SearchFilters::default(), 10).await?
                        .into_iter()
                        .map(|r| r.memory)
                        .collect()
                } else {
                    store.get_memories_by_tag(tag, 10).await?
                };
                
                let result_text = if memories.is_empty() {
                    "No memories found".to_string()
                } else {
                    memories.iter()
                        .map(|m| format!("• {} - {} ({})", m.title, m.content.chars().take(50).collect::<String>(), m.id))
                        .collect::<Vec<_>>()
                        .join("\n")
                };
                
                Ok(CommandResult::Success(Some(result_text)))
            },
            "delete" => {
                if args.len() < 2 {
                    return Ok(CommandResult::Error("Usage: /memory delete <id>".to_string()));
                }
                
                let id_str = &args[1];
                let id = Uuid::parse_str(id_str)
                    .map_err(|_| AutorunError::ArgumentError(format!("Invalid UUID: {}", id_str)))?;
                
                let deleted = store.archive_memory(id).await?;
                if deleted {
                    Ok(CommandResult::Success(Some("Memory archived".to_string())))
                } else {
                    Ok(CommandResult::Error("Memory not found".to_string()))
                }
            },
            _ => Ok(CommandResult::Error(format!("Unknown memory subcommand: {}", subcommand))),
        }
    }

    /// Execute MCP tool commands
    async fn execute_mcp_tool(
        &self,
        tool_name: String,
        args: Vec<String>,
        _execution_context: &ExecutionContext,
    ) -> Result<CommandResult> {
        // Placeholder for MCP tool execution
        // This will be implemented when MCP integration is ready
        Ok(CommandResult::Success(Some(format!("Would execute MCP tool: {} with args: {:?}", tool_name, args))))
    }

    /// Execute custom commands
    async fn execute_custom_command(
        &self,
        command_name: String,
        args: Vec<String>,
        _execution_context: &ExecutionContext,
    ) -> Result<CommandResult> {
        // Placeholder for custom command execution
        // This will be implemented when template system is ready
        Ok(CommandResult::Success(Some(format!("Would execute custom command: {} with args: {:?}", command_name, args))))
    }

    /// Start execution tracking
    async fn start_execution(&self, id: Uuid, command: String) {
        let status = ExecutionStatus {
            id,
            command,
            status: ExecutionState::Running,
            progress: 0,
            started_at: std::time::SystemTime::now(),
            message: None,
        };
        
        let mut tracker = self.execution_tracker.write().await;
        tracker.insert(id, status);
    }

    /// Complete execution tracking
    async fn complete_execution(&self, id: Uuid, message: &str) {
        let mut tracker = self.execution_tracker.write().await;
        if let Some(status) = tracker.get_mut(&id) {
            status.status = ExecutionState::Completed;
            status.progress = 100;
            status.message = Some(message.to_string());
        }
    }

    /// Fail execution tracking
    async fn fail_execution(&self, id: Uuid, error: &str) {
        let mut tracker = self.execution_tracker.write().await;
        if let Some(status) = tracker.get_mut(&id) {
            status.status = ExecutionState::Failed;
            status.message = Some(error.to_string());
        }
    }

    /// Get execution status
    pub async fn get_execution_status(&self, id: Uuid) -> Option<ExecutionStatus> {
        let tracker = self.execution_tracker.read().await;
        tracker.get(&id).cloned()
    }

    /// List all executions
    pub async fn list_executions(&self) -> Vec<ExecutionStatus> {
        let tracker = self.execution_tracker.read().await;
        tracker.values().cloned().collect()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::storage::StorageConfig;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_action_engine_creation() {
        let temp_dir = TempDir::new().unwrap();
        let mut config = StorageConfig::default();
        config.storage_dir = temp_dir.path().to_path_buf();
        
        let memory_store = Arc::new(RwLock::new(MemoryStore::new(config).await.unwrap()));
        let mcp_client = Arc::new(RwLock::new(McpClient::new().unwrap()));
        
        let _engine = ActionEngine::new(memory_store, mcp_client);
    }
}