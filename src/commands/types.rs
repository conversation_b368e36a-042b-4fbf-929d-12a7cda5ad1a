//! Unified command types for the three-tier command system
//!
//! This module defines the core types for:
//! - @ Commands: Context & Reference System
//! - / Commands: Action & Tool Execution
//! - : Commands: Mode & Configuration

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// The three types of commands supported by AutoRun
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum CommandType {
    /// @ Commands: Context & Reference System
    Context,
    /// / Commands: Action & Tool Execution  
    Action,
    /// : Commands: Mode & Configuration
    Configuration,
}

impl CommandType {
    /// Get the trigger character for this command type
    pub fn trigger_char(&self) -> char {
        match self {
            CommandType::Context => '@',
            CommandType::Action => '/',
            CommandType::Configuration => ':',
        }
    }

    /// Parse command type from trigger character
    pub fn from_trigger_char(ch: char) -> Option<Self> {
        match ch {
            '@' => Some(CommandType::Context),
            '/' => Some(CommandType::Action),
            ':' => Some(CommandType::Configuration),
            _ => None,
        }
    }
}

/// A parsed command with its type, name, and arguments
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Command {
    /// The type of command
    pub command_type: CommandType,
    /// The command name (without trigger character)
    pub name: String,
    /// Command arguments
    pub args: Vec<String>,
    /// Raw input text for commands that need it
    pub raw_input: String,
    /// Position in the original text where command starts
    pub start_position: usize,
    /// Position in the original text where command ends
    pub end_position: usize,
    /// Additional metadata
    pub metadata: HashMap<String, String>,
}

/// Context command subtypes for @ commands
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ContextCommandType {
    /// File reference (@file)
    File,
    /// Folder reference (@folder)
    Folder,
    /// Codebase analysis (@codebase)
    Codebase,
    /// MCP server reference (@mcp)
    Mcp,
    /// Symbol reference (@symbol)
    Symbol,
    /// Web search (@web)
    Web,
    /// Session reference (@session)
    Session,
    /// Workspace reference (@workspace)
    Workspace,
    /// Git reference (@git)
    Git,
    /// Memory reference (@memory)
    Memory,
}

impl ContextCommandType {
    /// Get the command name for this context type
    pub fn command_name(&self) -> &'static str {
        match self {
            ContextCommandType::File => "file",
            ContextCommandType::Folder => "folder",
            ContextCommandType::Codebase => "codebase",
            ContextCommandType::Mcp => "mcp",
            ContextCommandType::Symbol => "symbol",
            ContextCommandType::Web => "web",
            ContextCommandType::Session => "session",
            ContextCommandType::Workspace => "workspace",
            ContextCommandType::Git => "git",
            ContextCommandType::Memory => "memory",
        }
    }

    /// Parse context command type from string
    pub fn from_str(s: &str) -> Option<Self> {
        match s.to_lowercase().as_str() {
            "file" => Some(ContextCommandType::File),
            "folder" => Some(ContextCommandType::Folder),
            "codebase" => Some(ContextCommandType::Codebase),
            "mcp" => Some(ContextCommandType::Mcp),
            "symbol" => Some(ContextCommandType::Symbol),
            "web" => Some(ContextCommandType::Web),
            "session" => Some(ContextCommandType::Session),
            "workspace" => Some(ContextCommandType::Workspace),
            "git" => Some(ContextCommandType::Git),
            "memory" => Some(ContextCommandType::Memory),
            _ => None,
        }
    }

    /// Get all available context command types
    pub fn all() -> Vec<Self> {
        vec![
            ContextCommandType::File,
            ContextCommandType::Folder,
            ContextCommandType::Codebase,
            ContextCommandType::Mcp,
            ContextCommandType::Symbol,
            ContextCommandType::Web,
            ContextCommandType::Session,
            ContextCommandType::Workspace,
            ContextCommandType::Git,
            ContextCommandType::Memory,
        ]
    }
}

/// Action command subtypes for / commands
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ActionCommandType {
    /// Memory management (/memory)
    Memory,
    /// MCP tool execution (dynamic based on MCP servers)
    McpTool(String),
    /// Custom command execution
    Custom(String),
}

impl ActionCommandType {
    /// Get the command name for this action type
    pub fn command_name(&self) -> String {
        match self {
            ActionCommandType::Memory => "memory".to_string(),
            ActionCommandType::McpTool(name) => name.clone(),
            ActionCommandType::Custom(name) => name.clone(),
        }
    }

    /// Parse action command type from string and available MCP tools
    pub fn from_str(
        s: &str,
        available_mcp_tools: &[String],
        available_custom_commands: &[String],
    ) -> Option<Self> {
        match s.to_lowercase().as_str() {
            "memory" => Some(ActionCommandType::Memory),
            _ => {
                // Check if it's an MCP tool
                if available_mcp_tools.contains(&s.to_string()) {
                    Some(ActionCommandType::McpTool(s.to_string()))
                }
                // Check if it's a custom command
                else if available_custom_commands.contains(&s.to_string()) {
                    Some(ActionCommandType::Custom(s.to_string()))
                } else {
                    None
                }
            }
        }
    }
}

/// Configuration command subtypes for : commands
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ConfigCommandType {
    /// Mode switching (:mode)
    Mode,
    /// Settings modification (:set)
    Set,
    /// Theme switching (:theme)
    Theme,
    /// LLM provider switching (:provider)
    Provider,
    /// Workspace switching (:workspace)
    Workspace,
    /// Session management (:session)
    Session,
    /// Configuration management (:config)
    Config,
    /// Log level adjustment (:log)
    Log,
    /// Help system (:help)
    Help,
    /// Application exit (:exit)
    Exit,
    /// Clear chat history (:clear)
    Clear,
    /// Save context (:save)
    Save,
    /// Load context (:load)
    Load,
    /// Export data (:export)
    Export,
}

impl ConfigCommandType {
    /// Get the command name for this config type
    pub fn command_name(&self) -> &'static str {
        match self {
            ConfigCommandType::Mode => "mode",
            ConfigCommandType::Set => "set",
            ConfigCommandType::Theme => "theme",
            ConfigCommandType::Provider => "provider",
            ConfigCommandType::Workspace => "workspace",
            ConfigCommandType::Session => "session",
            ConfigCommandType::Config => "config",
            ConfigCommandType::Log => "log",
            ConfigCommandType::Help => "help",
            ConfigCommandType::Exit => "exit",
            ConfigCommandType::Clear => "clear",
            ConfigCommandType::Save => "save",
            ConfigCommandType::Load => "load",
            ConfigCommandType::Export => "export",
        }
    }

    /// Parse configuration command type from string
    pub fn from_str(s: &str) -> Option<Self> {
        match s.to_lowercase().as_str() {
            "mode" => Some(ConfigCommandType::Mode),
            "set" => Some(ConfigCommandType::Set),
            "theme" => Some(ConfigCommandType::Theme),
            "provider" => Some(ConfigCommandType::Provider),
            "workspace" => Some(ConfigCommandType::Workspace),
            "session" => Some(ConfigCommandType::Session),
            "config" => Some(ConfigCommandType::Config),
            "log" => Some(ConfigCommandType::Log),
            "help" => Some(ConfigCommandType::Help),
            "exit" => Some(ConfigCommandType::Exit),
            "clear" => Some(ConfigCommandType::Clear),
            "save" => Some(ConfigCommandType::Save),
            "load" => Some(ConfigCommandType::Load),
            "export" => Some(ConfigCommandType::Export),
            _ => None,
        }
    }

    /// Get all available configuration command types
    pub fn all() -> Vec<Self> {
        vec![
            ConfigCommandType::Mode,
            ConfigCommandType::Set,
            ConfigCommandType::Theme,
            ConfigCommandType::Provider,
            ConfigCommandType::Workspace,
            ConfigCommandType::Session,
            ConfigCommandType::Config,
            ConfigCommandType::Log,
            ConfigCommandType::Help,
            ConfigCommandType::Exit,
            ConfigCommandType::Clear,
            ConfigCommandType::Save,
            ConfigCommandType::Load,
            ConfigCommandType::Export,
        ]
    }
}

/// Command execution result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CommandResult {
    /// Command executed successfully with optional output
    Success(Option<String>),
    /// Command failed with error message
    Error(String),
    /// Command requires user interaction
    RequiresInput(String),
    /// Command was cancelled
    Cancelled,
    /// Command is executing asynchronously
    Executing(String),
    /// Command completed with data
    Data(serde_json::Value),
}

/// Command execution context
#[derive(Debug, Clone)]
pub struct CommandContext {
    /// Current working directory
    pub working_directory: std::path::PathBuf,
    /// Current session ID
    pub session_id: String,
    /// Available MCP tools
    pub available_mcp_tools: Vec<String>,
    /// Available custom commands
    pub available_custom_commands: Vec<String>,
    /// Additional context data
    pub data: HashMap<String, serde_json::Value>,
}

impl Command {
    /// Create a new command
    pub fn new(
        command_type: CommandType,
        name: String,
        args: Vec<String>,
        raw_input: String,
        start_position: usize,
        end_position: usize,
    ) -> Self {
        Self {
            command_type,
            name,
            args,
            raw_input,
            start_position,
            end_position,
            metadata: HashMap::new(),
        }
    }

    /// Add metadata to the command
    pub fn with_metadata(mut self, key: String, value: String) -> Self {
        self.metadata.insert(key, value);
        self
    }

    /// Get the full command text (with trigger character)
    pub fn full_command(&self) -> String {
        format!("{}{}", self.command_type.trigger_char(), self.name)
    }

    /// Check if this is a context command
    pub fn is_context_command(&self) -> bool {
        matches!(self.command_type, CommandType::Context)
    }

    /// Check if this is an action command
    pub fn is_action_command(&self) -> bool {
        matches!(self.command_type, CommandType::Action)
    }

    /// Check if this is a configuration command
    pub fn is_config_command(&self) -> bool {
        matches!(self.command_type, CommandType::Configuration)
    }

    /// Parse the command as a context command
    pub fn as_context_command(&self) -> Option<ContextCommandType> {
        if self.is_context_command() {
            ContextCommandType::from_str(&self.name)
        } else {
            None
        }
    }

    /// Parse the command as an action command
    pub fn as_action_command(&self, context: &CommandContext) -> Option<ActionCommandType> {
        if self.is_action_command() {
            ActionCommandType::from_str(
                &self.name,
                &context.available_mcp_tools,
                &context.available_custom_commands,
            )
        } else {
            None
        }
    }

    /// Parse the command as a configuration command
    pub fn as_config_command(&self) -> Option<ConfigCommandType> {
        if self.is_config_command() {
            ConfigCommandType::from_str(&self.name)
        } else {
            None
        }
    }
}

impl Default for CommandContext {
    fn default() -> Self {
        Self {
            working_directory: std::env::current_dir()
                .unwrap_or_else(|_| std::path::PathBuf::from(".")),
            session_id: uuid::Uuid::new_v4().to_string(),
            available_mcp_tools: Vec::new(),
            available_custom_commands: Vec::new(),
            data: HashMap::new(),
        }
    }
}
