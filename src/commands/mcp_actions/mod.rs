//! MCP Actions module for handling MCP tool execution and management
//!
//! This module provides the `McpToolExecutor` for dynamic tool execution across multiple MCP servers
//! with features like parameter validation, progress tracking, result caching, and cancellation support.
//!
//! # Example Usage
//!
//! ```rust,no_run
//! use std::sync::Arc;
//! use tokio::sync::RwLock;
//! use serde_json::json;
//! use crate::commands::mcp_actions::{McpToolExecutor, McpToolExecutorConfig};
//! use crate::mcp::client::McpClientManager;
//!
//! #[tokio::main]
//! async fn main() -> Result<(), Box<dyn std::error::Error>> {
//!     // Create MCP client manager
//!     let mcp_manager = Arc::new(RwLock::new(McpClientManager::new()));
//!     
//!     // Configure the executor
//!     let config = McpToolExecutorConfig {
//!         enable_caching: true,
//!         enable_progress_tracking: true,
//!         max_concurrent_executions: 5,
//!         ..Default::default()
//!     };
//!     
//!     // Create the executor
//!     let executor = McpToolExecutor::new(mcp_manager, config);
//!     
//!     // Execute a tool with parameters
//!     let parameters = json!({
//!         "query": "Hello world",
//!         "max_results": 10
//!     });
//!     
//!     let (execution_id, result) = executor
//!         .execute_tool("my-server", "search_tool", parameters, None)
//!         .await?;
//!     
//!     println!("Execution ID: {}", execution_id);
//!     println!("Result: {:?}", result);
//!     
//!     // Subscribe to progress updates
//!     let mut progress_receiver = executor.subscribe_to_progress();
//!     tokio::spawn(async move {
//!         while let Ok(progress) = progress_receiver.recv().await {
//!             println!("Progress: {}% - {}", progress.progress, 
//!                      progress.message.unwrap_or_default());
//!         }
//!     });
//!     
//!     Ok(())
//! }
//! ```

pub mod mcp_tool_executor;
pub mod mcp_prompt_handler;
pub mod mcp_resource_manager;

pub use mcp_tool_executor::{
    McpToolExecutor, McpToolExecutorConfig, McpExecutionProgress, McpExecutionStatus, ParameterConfig,
};
pub use mcp_prompt_handler::{
    McpPromptHandler, McpPromptHandlerConfig, McpPromptProgress, McpPromptStatus, ContextConfig,
};
pub use mcp_resource_manager::{
    McpResourceManager, ResourceManagerConfig, CachedResource, ResourceAccessControl,
    ServerResourceInfo, ResourceAccessStats, BatchResourceRequest, BatchResourceResult,
    ResourceOperationProgress, ResourceChangeEvent, ResourceChangeType, ResourceManagerStats,
    CacheStats,
};