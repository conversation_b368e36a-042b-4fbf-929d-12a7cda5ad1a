//! MCP Tool Executor for dynamic tool execution with parameter mapping and progress tracking

use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime};

use dashmap::DashMap;
use rmcp::model::{CallToolResult, JsonObject, Tool};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use tokio::sync::{broadcast, RwLock};
use tracing::{debug, info, warn};
use uuid::Uuid;

use crate::errors::{AutorunError, Result};
use crate::mcp::client::McpClientManager;

/// Execution status for tracking tool operations
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum McpExecutionStatus {
    /// Execution is queued but not started
    Queued,
    /// Execution is currently running
    Running,
    /// Execution completed successfully
    Completed,
    /// Execution failed with error
    Failed,
    /// Execution was cancelled
    Cancelled,
}

/// Progress information for a tool execution
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct McpExecutionProgress {
    /// Unique execution identifier
    pub execution_id: Uuid,
    /// Tool name being executed
    pub tool_name: String,
    /// MCP server ID
    pub server_id: String,
    /// Current execution status
    pub status: McpExecutionStatus,
    /// Progress percentage (0-100)
    pub progress: f32,
    /// Optional status message
    pub message: Option<String>,
    /// Execution start time
    pub started_at: SystemTime,
    /// Execution completion time
    pub completed_at: Option<SystemTime>,
    /// Execution error if any
    pub error: Option<String>,
}

impl McpExecutionProgress {
    fn new(execution_id: Uuid, tool_name: String, server_id: String) -> Self {
        Self {
            execution_id,
            tool_name,
            server_id,
            status: McpExecutionStatus::Queued,
            progress: 0.0,
            message: None,
            started_at: SystemTime::now(),
            completed_at: None,
            error: None,
        }
    }

    fn start(&mut self) {
        self.status = McpExecutionStatus::Running;
        self.started_at = SystemTime::now();
        self.progress = 0.0;
    }

    fn update_progress(&mut self, progress: f32, message: Option<String>) {
        self.progress = progress.clamp(0.0, 100.0);
        self.message = message;
    }

    fn complete(&mut self, result: Option<String>) {
        self.status = McpExecutionStatus::Completed;
        self.progress = 100.0;
        self.completed_at = Some(SystemTime::now());
        if let Some(msg) = result {
            self.message = Some(msg);
        }
    }

    fn fail(&mut self, error: String) {
        self.status = McpExecutionStatus::Failed;
        self.completed_at = Some(SystemTime::now());
        self.error = Some(error);
    }

    fn cancel(&mut self) {
        self.status = McpExecutionStatus::Cancelled;
        self.completed_at = Some(SystemTime::now());
        self.message = Some("Execution cancelled by user".to_string());
    }
}

/// Cached execution result
#[derive(Debug, Clone)]
struct CachedResult {
    result: CallToolResult,
    cached_at: SystemTime,
    ttl: Duration,
}

impl CachedResult {
    fn new(result: CallToolResult, ttl: Duration) -> Self {
        Self {
            result,
            cached_at: SystemTime::now(),
            ttl,
        }
    }

    fn is_expired(&self) -> bool {
        self.cached_at.elapsed().unwrap_or(Duration::MAX) > self.ttl
    }
}

/// Parameter validation and mapping configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParameterConfig {
    /// Enable automatic type coercion
    pub auto_coerce: bool,
    /// Strict validation mode
    pub strict_validation: bool,
    /// Maximum string length for parameters
    pub max_string_length: usize,
    /// Maximum array length for parameters
    pub max_array_length: usize,
    /// Custom type mappings
    pub type_mappings: HashMap<String, String>,
}

impl Default for ParameterConfig {
    fn default() -> Self {
        Self {
            auto_coerce: true,
            strict_validation: false,
            max_string_length: 10_000,
            max_array_length: 1_000,
            type_mappings: HashMap::new(),
        }
    }
}

/// Configuration for the MCP Tool Executor
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct McpToolExecutorConfig {
    /// Default execution timeout
    pub default_timeout: Duration,
    /// Maximum concurrent executions
    pub max_concurrent_executions: usize,
    /// Enable result caching
    pub enable_caching: bool,
    /// Default cache TTL
    pub cache_ttl: Duration,
    /// Maximum cache size
    pub max_cache_size: usize,
    /// Parameter validation configuration
    pub parameter_config: ParameterConfig,
    /// Enable progress tracking
    pub enable_progress_tracking: bool,
}

impl Default for McpToolExecutorConfig {
    fn default() -> Self {
        Self {
            default_timeout: Duration::from_secs(30),
            max_concurrent_executions: 10,
            enable_caching: true,
            cache_ttl: Duration::from_secs(300), // 5 minutes
            max_cache_size: 100,
            parameter_config: ParameterConfig::default(),
            enable_progress_tracking: true,
        }
    }
}

/// Dynamic MCP Tool Executor with parameter mapping and progress tracking
pub struct McpToolExecutor {
    /// Configuration
    config: McpToolExecutorConfig,
    /// MCP client manager for server communication
    mcp_client_manager: Arc<RwLock<McpClientManager>>,
    /// Active executions tracking
    active_executions: Arc<DashMap<Uuid, McpExecutionProgress>>,
    /// Result cache
    result_cache: Arc<DashMap<String, CachedResult>>,
    /// Progress broadcast channel
    progress_sender: broadcast::Sender<McpExecutionProgress>,
    /// Cancellation tokens
    cancellation_tokens: Arc<DashMap<Uuid, broadcast::Sender<()>>>,
}

impl McpToolExecutor {
    /// Create a new MCP Tool Executor
    pub fn new(
        mcp_client_manager: Arc<RwLock<McpClientManager>>,
        config: McpToolExecutorConfig,
    ) -> Self {
        let (progress_sender, _) = broadcast::channel(1000);

        Self {
            config,
            mcp_client_manager,
            active_executions: Arc::new(DashMap::new()),
            result_cache: Arc::new(DashMap::new()),
            progress_sender,
            cancellation_tokens: Arc::new(DashMap::new()),
        }
    }

    /// Execute a tool with dynamic parameter mapping
    pub async fn execute_tool(
        &self,
        server_id: &str,
        tool_name: &str,
        parameters: Value,
        execution_timeout: Option<Duration>,
    ) -> Result<(Uuid, CallToolResult)> {
        let execution_id = Uuid::new_v4();
        let timeout_duration = execution_timeout.unwrap_or(self.config.default_timeout);

        // Check cache first if enabled
        if self.config.enable_caching {
            let cache_key = self.generate_cache_key(server_id, tool_name, &parameters);
            if let Some(cached) = self.result_cache.get(&cache_key) {
                if !cached.is_expired() {
                    debug!(
                        execution_id = %execution_id,
                        server_id = server_id,
                        tool_name = tool_name,
                        "Returning cached result"
                    );
                    return Ok((execution_id, cached.result.clone()));
                } else {
                    // Remove expired entry
                    self.result_cache.remove(&cache_key);
                }
            }
        }

        // Initialize progress tracking
        let mut progress = McpExecutionProgress::new(
            execution_id,
            tool_name.to_string(),
            server_id.to_string(),
        );

        if self.config.enable_progress_tracking {
            self.active_executions.insert(execution_id, progress.clone());
        }

        // Create cancellation token
        let (cancel_sender, mut cancel_receiver) = broadcast::channel(1);
        self.cancellation_tokens.insert(execution_id, cancel_sender);

        // Start execution
        progress.start();
        self.update_progress(execution_id, &progress).await;

        let result = tokio::select! {
            // Main execution
            result = self.execute_tool_internal(server_id, tool_name, parameters.clone(), execution_id) => {
                result
            }
            // Timeout
            _ = tokio::time::sleep(timeout_duration) => {
                let error_msg = format!("Tool execution timed out after {:?}", timeout_duration);
                progress.fail(error_msg.clone());
                self.update_progress(execution_id, &progress).await;
                Err(AutorunError::McpError(error_msg))
            }
            // Cancellation
            _ = cancel_receiver.recv() => {
                progress.cancel();
                self.update_progress(execution_id, &progress).await;
                Err(AutorunError::McpError("Tool execution cancelled".to_string()))
            }
        };

        // Cleanup
        self.cancellation_tokens.remove(&execution_id);

        match result {
            Ok(call_result) => {
                progress.complete(Some("Tool executed successfully".to_string()));
                self.update_progress(execution_id, &progress).await;

                // Cache result if enabled
                if self.config.enable_caching {
                    let cache_key = self.generate_cache_key(server_id, tool_name, &parameters);
                    let cached_result = CachedResult::new(call_result.clone(), self.config.cache_ttl);
                    
                    // Ensure cache size limit
                    if self.result_cache.len() >= self.config.max_cache_size {
                        self.cleanup_cache();
                    }
                    
                    self.result_cache.insert(cache_key, cached_result);
                }

                Ok((execution_id, call_result))
            }
            Err(e) => {
                progress.fail(e.to_string());
                self.update_progress(execution_id, &progress).await;
                Err(e)
            }
        }
    }

    /// Internal tool execution with parameter validation and mapping
    async fn execute_tool_internal(
        &self,
        server_id: &str,
        tool_name: &str,
        parameters: Value,
        execution_id: Uuid,
    ) -> Result<CallToolResult> {
        // Update progress
        self.update_execution_progress(execution_id, 10.0, Some("Validating parameters".to_string())).await;

        // Get tool schema for validation
        let tool_schema = self.get_tool_schema(server_id, tool_name).await?;

        // Validate and map parameters
        let mapped_params = self.validate_and_map_parameters(&parameters, &tool_schema)?;

        self.update_execution_progress(execution_id, 30.0, Some("Connecting to MCP server".to_string())).await;

        // Get MCP client
        let client_manager = self.mcp_client_manager.read().await;
        let client = client_manager.get_client(server_id).ok_or_else(|| {
            AutorunError::McpError(format!("MCP server '{}' not found or not connected", server_id))
        })?;

        self.update_execution_progress(execution_id, 50.0, Some("Executing tool".to_string())).await;

        // Execute the tool
        let result = client.call_tool(tool_name, mapped_params).await?;

        self.update_execution_progress(execution_id, 90.0, Some("Processing result".to_string())).await;

        info!(
            execution_id = %execution_id,
            server_id = server_id,
            tool_name = tool_name,
            "Tool executed successfully"
        );

        Ok(result)
    }

    /// Get tool schema from MCP server
    async fn get_tool_schema(&self, server_id: &str, tool_name: &str) -> Result<Tool> {
        let client_manager = self.mcp_client_manager.read().await;
        let client = client_manager.get_client(server_id).ok_or_else(|| {
            AutorunError::McpError(format!("MCP server '{}' not found", server_id))
        })?;

        let tools = client.list_tools().await?;
        tools
            .into_iter()
            .find(|tool| tool.name == tool_name)
            .ok_or_else(|| {
                AutorunError::McpError(format!("Tool '{}' not found on server '{}'", tool_name, server_id))
            })
    }

    /// Validate and map parameters according to tool schema
    fn validate_and_map_parameters(
        &self,
        parameters: &Value,
        tool_schema: &Tool,
    ) -> Result<Option<JsonObject>> {
        if parameters.is_null() {
            return Ok(None);
        }

        let param_obj = match parameters {
            Value::Object(obj) => obj.clone(),
            _ => {
                if self.config.parameter_config.auto_coerce {
                    // Try to wrap single value in object if tool expects it
                    let mut obj = serde_json::Map::new();
                    obj.insert("value".to_string(), parameters.clone());
                    obj
                } else {
                    return Err(AutorunError::McpError(
                        "Parameters must be an object".to_string(),
                    ));
                }
            }
        };

        // Convert to JsonObject
        let mut json_object = JsonObject::new();
        for (key, value) in param_obj {
            // Apply type coercion if enabled
            let mapped_value = if self.config.parameter_config.auto_coerce {
                self.coerce_parameter_type(&value)?
            } else {
                value
            };

            // Validate parameter constraints
            self.validate_parameter_constraints(&key, &mapped_value)?;

            json_object.insert(key, mapped_value);
        }

        Ok(Some(json_object))
    }

    /// Coerce parameter types for better compatibility
    fn coerce_parameter_type(&self, value: &Value) -> Result<Value> {
        match value {
            Value::String(s) => {
                // Try parsing numbers
                if let Ok(num) = s.parse::<i64>() {
                    Ok(json!(num))
                } else if let Ok(num) = s.parse::<f64>() {
                    Ok(json!(num))
                } else if let Ok(bool_val) = s.parse::<bool>() {
                    Ok(json!(bool_val))
                } else {
                    Ok(value.clone())
                }
            }
            _ => Ok(value.clone()),
        }
    }

    /// Validate parameter constraints
    fn validate_parameter_constraints(&self, key: &str, value: &Value) -> Result<()> {
        match value {
            Value::String(s) => {
                if s.len() > self.config.parameter_config.max_string_length {
                    return Err(AutorunError::McpError(format!(
                        "Parameter '{}' string length exceeds maximum of {}",
                        key, self.config.parameter_config.max_string_length
                    )));
                }
            }
            Value::Array(arr) => {
                if arr.len() > self.config.parameter_config.max_array_length {
                    return Err(AutorunError::McpError(format!(
                        "Parameter '{}' array length exceeds maximum of {}",
                        key, self.config.parameter_config.max_array_length
                    )));
                }
            }
            _ => {}
        }

        Ok(())
    }

    /// Cancel a running execution
    pub async fn cancel_execution(&self, execution_id: Uuid) -> Result<()> {
        if let Some((_, cancel_sender)) = self.cancellation_tokens.remove(&execution_id) {
            let _ = cancel_sender.send(());
            info!(execution_id = %execution_id, "Tool execution cancelled");
            Ok(())
        } else {
            Err(AutorunError::McpError(format!(
                "Execution {} not found or already completed",
                execution_id
            )))
        }
    }

    /// Get execution progress
    pub fn get_execution_progress(&self, execution_id: Uuid) -> Option<McpExecutionProgress> {
        self.active_executions.get(&execution_id).map(|e| e.clone())
    }

    /// Get all active executions
    pub fn get_active_executions(&self) -> Vec<McpExecutionProgress> {
        self.active_executions
            .iter()
            .map(|entry| entry.value().clone())
            .collect()
    }

    /// Subscribe to progress updates
    pub fn subscribe_to_progress(&self) -> broadcast::Receiver<McpExecutionProgress> {
        self.progress_sender.subscribe()
    }

    /// Update execution progress
    async fn update_execution_progress(
        &self,
        execution_id: Uuid,
        progress: f32,
        message: Option<String>,
    ) {
        if let Some(mut entry) = self.active_executions.get_mut(&execution_id) {
            entry.update_progress(progress, message);
            let _ = self.progress_sender.send(entry.clone());
        }
    }

    /// Update progress and broadcast
    async fn update_progress(&self, execution_id: Uuid, progress: &McpExecutionProgress) {
        if self.config.enable_progress_tracking {
            self.active_executions.insert(execution_id, progress.clone());
            let _ = self.progress_sender.send(progress.clone());
        }
    }

    /// Generate cache key for result caching
    fn generate_cache_key(&self, server_id: &str, tool_name: &str, parameters: &Value) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        server_id.hash(&mut hasher);
        tool_name.hash(&mut hasher);
        parameters.to_string().hash(&mut hasher);
        format!("mcp_tool_{}_{}", server_id, hasher.finish())
    }

    /// Cleanup expired cache entries
    fn cleanup_cache(&self) {
        let expired_keys: Vec<String> = self
            .result_cache
            .iter()
            .filter_map(|entry| {
                if entry.value().is_expired() {
                    Some(entry.key().clone())
                } else {
                    None
                }
            })
            .collect();

        for key in expired_keys {
            self.result_cache.remove(&key);
        }

        // If still over limit, remove oldest entries
        if self.result_cache.len() >= self.config.max_cache_size {
            let mut entries: Vec<_> = self.result_cache.iter().collect();
            entries.sort_by(|a, b| a.value().cached_at.cmp(&b.value().cached_at));

            let to_remove = entries.len() - (self.config.max_cache_size / 2);
            for entry in entries.iter().take(to_remove) {
                self.result_cache.remove(entry.key());
            }
        }
    }

    /// Clear all cached results
    pub fn clear_cache(&self) {
        self.result_cache.clear();
    }

    /// Get cache statistics
    pub fn get_cache_stats(&self) -> HashMap<String, Value> {
        let mut stats = HashMap::new();
        stats.insert("total_entries".to_string(), json!(self.result_cache.len()));
        stats.insert("max_size".to_string(), json!(self.config.max_cache_size));
        stats.insert("ttl_seconds".to_string(), json!(self.config.cache_ttl.as_secs()));
        
        let expired_count = self.result_cache
            .iter()
            .filter(|entry| entry.value().is_expired())
            .count();
        stats.insert("expired_entries".to_string(), json!(expired_count));
        
        stats
    }

    /// List available tools from all connected MCP servers
    pub async fn list_available_tools(&self) -> Result<HashMap<String, Vec<Tool>>> {
        let client_manager = self.mcp_client_manager.read().await;
        let mut all_tools = HashMap::new();

        for client_id in client_manager.list_clients() {
            if let Some(client) = client_manager.get_client(&client_id) {
                match client.list_tools().await {
                    Ok(tools) => {
                        all_tools.insert(client_id.to_string(), tools);
                    }
                    Err(e) => {
                        warn!(server_id = client_id, error = %e, "Failed to list tools from MCP server");
                    }
                }
            }
        }

        Ok(all_tools)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;

    #[test]
    fn test_parameter_validation() {
        let config = McpToolExecutorConfig::default();
        let mcp_manager = Arc::new(RwLock::new(McpClientManager::new()));
        let executor = McpToolExecutor::new(mcp_manager, config);

        // Test string length validation
        let long_string = "x".repeat(11000);
        let result = executor.validate_parameter_constraints("test", &json!(long_string));
        assert!(result.is_err());

        // Test array length validation
        let long_array = vec![1; 1001];
        let result = executor.validate_parameter_constraints("test", &json!(long_array));
        assert!(result.is_err());

        // Test valid parameters
        let result = executor.validate_parameter_constraints("test", &json!("valid string"));
        assert!(result.is_ok());
    }

    #[test]
    fn test_type_coercion() {
        let config = McpToolExecutorConfig::default();
        let mcp_manager = Arc::new(RwLock::new(McpClientManager::new()));
        let executor = McpToolExecutor::new(mcp_manager, config);

        // Test number coercion
        let result = executor.coerce_parameter_type(&json!("123")).unwrap();
        assert_eq!(result, json!(123));

        let result = executor.coerce_parameter_type(&json!("123.45")).unwrap();
        assert_eq!(result, json!(123.45));

        // Test boolean coercion
        let result = executor.coerce_parameter_type(&json!("true")).unwrap();
        assert_eq!(result, json!(true));

        // Test string passthrough
        let result = executor.coerce_parameter_type(&json!("hello")).unwrap();
        assert_eq!(result, json!("hello"));
    }

    #[test]
    fn test_cache_key_generation() {
        let config = McpToolExecutorConfig::default();
        let mcp_manager = Arc::new(RwLock::new(McpClientManager::new()));
        let executor = McpToolExecutor::new(mcp_manager, config);

        let key1 = executor.generate_cache_key("server1", "tool1", &json!({"param": "value"}));
        let key2 = executor.generate_cache_key("server1", "tool1", &json!({"param": "value"}));
        let key3 = executor.generate_cache_key("server1", "tool1", &json!({"param": "different"}));

        assert_eq!(key1, key2);
        assert_ne!(key1, key3);
    }

    #[test]
    fn test_execution_progress() {
        let execution_id = Uuid::new_v4();
        let mut progress = McpExecutionProgress::new(
            execution_id,
            "test_tool".to_string(),
            "test_server".to_string(),
        );

        assert_eq!(progress.status, McpExecutionStatus::Queued);
        assert_eq!(progress.progress, 0.0);

        progress.start();
        assert_eq!(progress.status, McpExecutionStatus::Running);

        progress.update_progress(50.0, Some("Half done".to_string()));
        assert_eq!(progress.progress, 50.0);
        assert_eq!(progress.message, Some("Half done".to_string()));

        progress.complete(Some("Done".to_string()));
        assert_eq!(progress.status, McpExecutionStatus::Completed);
        assert_eq!(progress.progress, 100.0);
        assert!(progress.completed_at.is_some());
    }
}