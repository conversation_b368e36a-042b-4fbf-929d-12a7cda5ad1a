//! MCP Prompt Handler for executing MCP prompts with context injection
//! 
//! This module provides the `McpPromptHandler` which executes MCP prompts from
//! connected servers with automatic context injection from the current session,
//! memory, and workspace state.

use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime};

use dashmap::DashMap;
use rmcp::model::{GetPromptResult, JsonObject, Prompt};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use tokio::sync::{broadcast, RwLock};
use tracing::{debug, info, warn};
use uuid::Uuid;

use crate::errors::{AutorunError, Result};
use crate::mcp::client::McpClientManager;
use crate::prompts::templates::TemplateEngine;
use crate::tools::context::ExecutionContext;

/// Progress tracking for prompt execution
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct McpPromptProgress {
    /// Unique execution identifier
    pub execution_id: Uuid,
    /// Prompt name being executed
    pub prompt_name: String,
    /// MCP server ID
    pub server_id: String,
    /// Current execution status
    pub status: McpPromptStatus,
    /// Progress percentage (0-100)
    pub progress: f32,
    /// Optional status message
    pub message: Option<String>,
    /// Execution start time
    pub started_at: SystemTime,
    /// Execution completion time
    pub completed_at: Option<SystemTime>,
    /// Execution error if any
    pub error: Option<String>,
}

/// Status of prompt execution
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum McpPromptStatus {
    /// Execution queued
    Queued,
    /// Execution in progress
    Running,
    /// Execution completed successfully
    Completed,
    /// Execution failed
    Failed,
    /// Execution cancelled
    Cancelled,
}

impl McpPromptProgress {
    fn new(execution_id: Uuid, prompt_name: String, server_id: String) -> Self {
        Self {
            execution_id,
            prompt_name,
            server_id,
            status: McpPromptStatus::Queued,
            progress: 0.0,
            message: None,
            started_at: SystemTime::now(),
            completed_at: None,
            error: None,
        }
    }

    fn start(&mut self) {
        self.status = McpPromptStatus::Running;
        self.started_at = SystemTime::now();
        self.progress = 0.0;
    }

    fn update_progress(&mut self, progress: f32, message: Option<String>) {
        self.progress = progress.clamp(0.0, 100.0);
        self.message = message;
    }

    fn complete(&mut self, result: Option<String>) {
        self.status = McpPromptStatus::Completed;
        self.progress = 100.0;
        self.completed_at = Some(SystemTime::now());
        if let Some(msg) = result {
            self.message = Some(msg);
        }
    }

    fn fail(&mut self, error: String) {
        self.status = McpPromptStatus::Failed;
        self.completed_at = Some(SystemTime::now());
        self.error = Some(error);
    }

    fn cancel(&mut self) {
        self.status = McpPromptStatus::Cancelled;
        self.completed_at = Some(SystemTime::now());
        self.message = Some("Execution cancelled by user".to_string());
    }
}

/// Cached prompt result
#[derive(Debug, Clone)]
struct CachedPromptResult {
    result: GetPromptResult,
    cached_at: SystemTime,
    ttl: Duration,
}

impl CachedPromptResult {
    fn new(result: GetPromptResult, ttl: Duration) -> Self {
        Self {
            result,
            cached_at: SystemTime::now(),
            ttl,
        }
    }

    fn is_expired(&self) -> bool {
        self.cached_at.elapsed().unwrap_or(Duration::MAX) > self.ttl
    }
}

/// Context injection configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContextConfig {
    /// Include session history in context
    pub include_session: bool,
    /// Include memory store in context
    pub include_memory: bool,
    /// Include workspace state in context
    pub include_workspace: bool,
    /// Maximum session messages to include
    pub max_session_messages: usize,
    /// Maximum memory entries to include
    pub max_memory_entries: usize,
    /// Memory tags to filter by
    pub memory_tags: Vec<String>,
}

impl Default for ContextConfig {
    fn default() -> Self {
        Self {
            include_session: true,
            include_memory: true,
            include_workspace: true,
            max_session_messages: 10,
            max_memory_entries: 20,
            memory_tags: vec![],
        }
    }
}

/// Configuration for the MCP Prompt Handler
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct McpPromptHandlerConfig {
    /// Default execution timeout
    pub default_timeout: Duration,
    /// Maximum concurrent executions
    pub max_concurrent_executions: usize,
    /// Enable result caching
    pub enable_caching: bool,
    /// Default cache TTL
    pub cache_ttl: Duration,
    /// Maximum cache size
    pub max_cache_size: usize,
    /// Default context injection configuration
    pub context_config: ContextConfig,
    /// Enable progress tracking
    pub enable_progress_tracking: bool,
    /// Enable template processing
    pub enable_template_processing: bool,
}

impl Default for McpPromptHandlerConfig {
    fn default() -> Self {
        Self {
            default_timeout: Duration::from_secs(60),
            max_concurrent_executions: 5,
            enable_caching: true,
            cache_ttl: Duration::from_secs(600), // 10 minutes
            max_cache_size: 50,
            context_config: ContextConfig::default(),
            enable_progress_tracking: true,
            enable_template_processing: true,
        }
    }
}

/// MCP Prompt Handler with context injection and template processing
pub struct McpPromptHandler {
    /// Configuration
    config: McpPromptHandlerConfig,
    /// MCP client manager for server communication
    mcp_client_manager: Arc<RwLock<McpClientManager>>,
    /// Template engine for prompt processing
    template_engine: Arc<TemplateEngine>,
    /// Active executions tracking
    active_executions: Arc<DashMap<Uuid, McpPromptProgress>>,
    /// Result cache
    result_cache: Arc<DashMap<String, CachedPromptResult>>,
    /// Progress broadcast channel
    progress_sender: broadcast::Sender<McpPromptProgress>,
    /// Cancellation tokens
    cancellation_tokens: Arc<DashMap<Uuid, broadcast::Sender<()>>>,
}

impl McpPromptHandler {
    /// Create a new MCP Prompt Handler
    pub fn new(
        mcp_client_manager: Arc<RwLock<McpClientManager>>,
        template_engine: Arc<TemplateEngine>,
        config: McpPromptHandlerConfig,
    ) -> Self {
        let (progress_sender, _) = broadcast::channel(1000);

        Self {
            config,
            mcp_client_manager,
            template_engine,
            active_executions: Arc::new(DashMap::new()),
            result_cache: Arc::new(DashMap::new()),
            progress_sender,
            cancellation_tokens: Arc::new(DashMap::new()),
        }
    }

    /// Execute a prompt with context injection
    pub async fn execute_prompt(
        &self,
        server_id: &str,
        prompt_name: &str,
        arguments: Option<JsonObject>,
        execution_context: &ExecutionContext,
        context_config: Option<ContextConfig>,
        execution_timeout: Option<Duration>,
    ) -> Result<(Uuid, GetPromptResult)> {
        let execution_id = Uuid::new_v4();
        let timeout_duration = execution_timeout.unwrap_or(self.config.default_timeout);
        let context_config = context_config.unwrap_or_else(|| self.config.context_config.clone());

        // Check cache first if enabled
        if self.config.enable_caching {
            let cache_key = self.generate_cache_key(server_id, prompt_name, &arguments, &context_config);
            if let Some(cached) = self.result_cache.get(&cache_key) {
                if !cached.is_expired() {
                    debug!(
                        execution_id = %execution_id,
                        server_id = server_id,
                        prompt_name = prompt_name,
                        "Returning cached prompt result"
                    );
                    return Ok((execution_id, cached.result.clone()));
                } else {
                    // Remove expired entry
                    self.result_cache.remove(&cache_key);
                }
            }
        }

        // Initialize progress tracking
        let mut progress = McpPromptProgress::new(
            execution_id,
            prompt_name.to_string(),
            server_id.to_string(),
        );

        if self.config.enable_progress_tracking {
            self.active_executions.insert(execution_id, progress.clone());
        }

        // Create cancellation token
        let (cancel_sender, mut cancel_receiver) = broadcast::channel(1);
        self.cancellation_tokens.insert(execution_id, cancel_sender);

        // Start execution
        progress.start();
        self.update_progress(execution_id, &progress).await;

        let result = tokio::select! {
            // Main execution
            result = self.execute_prompt_internal(
                server_id,
                prompt_name,
                arguments.clone(),
                execution_context,
                &context_config,
                execution_id
            ) => {
                result
            }
            // Timeout
            _ = tokio::time::sleep(timeout_duration) => {
                let error_msg = format!("Prompt execution timed out after {:?}", timeout_duration);
                progress.fail(error_msg.clone());
                self.update_progress(execution_id, &progress).await;
                Err(AutorunError::McpError(error_msg))
            }
            // Cancellation
            _ = cancel_receiver.recv() => {
                progress.cancel();
                self.update_progress(execution_id, &progress).await;
                Err(AutorunError::McpError("Prompt execution cancelled".to_string()))
            }
        };

        // Cleanup
        self.cancellation_tokens.remove(&execution_id);

        match result {
            Ok(prompt_result) => {
                progress.complete(Some("Prompt executed successfully".to_string()));
                self.update_progress(execution_id, &progress).await;

                // Cache result if enabled
                if self.config.enable_caching {
                    let cache_key = self.generate_cache_key(server_id, prompt_name, &arguments, &context_config);
                    let cached_result = CachedPromptResult::new(prompt_result.clone(), self.config.cache_ttl);
                    
                    // Ensure cache size limit
                    if self.result_cache.len() >= self.config.max_cache_size {
                        self.cleanup_cache();
                    }
                    
                    self.result_cache.insert(cache_key, cached_result);
                }

                Ok((execution_id, prompt_result))
            }
            Err(e) => {
                progress.fail(e.to_string());
                self.update_progress(execution_id, &progress).await;
                Err(e)
            }
        }
    }

    /// Internal prompt execution with context injection
    async fn execute_prompt_internal(
        &self,
        server_id: &str,
        prompt_name: &str,
        arguments: Option<JsonObject>,
        execution_context: &ExecutionContext,
        context_config: &ContextConfig,
        execution_id: Uuid,
    ) -> Result<GetPromptResult> {
        // Update progress
        self.update_execution_progress(execution_id, 10.0, Some("Injecting context".to_string())).await;

        // Inject context into arguments
        let enhanced_arguments = self.inject_context(arguments, execution_context, context_config).await?;

        self.update_execution_progress(execution_id, 30.0, Some("Connecting to MCP server".to_string())).await;

        // Get MCP client
        let client_manager = self.mcp_client_manager.read().await;
        let client = client_manager.get_client(server_id).ok_or_else(|| {
            AutorunError::McpError(format!("MCP server '{}' not found or not connected", server_id))
        })?;

        self.update_execution_progress(execution_id, 50.0, Some("Executing prompt".to_string())).await;

        // Execute the prompt
        let result = client.get_prompt(prompt_name, enhanced_arguments).await?;

        self.update_execution_progress(execution_id, 80.0, Some("Processing result".to_string())).await;

        // Post-process result if template processing is enabled
        let processed_result = if self.config.enable_template_processing {
            self.process_prompt_result(result, execution_context).await?
        } else {
            result
        };

        self.update_execution_progress(execution_id, 90.0, Some("Finalizing result".to_string())).await;

        info!(
            execution_id = %execution_id,
            server_id = server_id,
            prompt_name = prompt_name,
            "Prompt executed successfully"
        );

        Ok(processed_result)
    }

    /// Inject context into prompt arguments
    async fn inject_context(
        &self,
        arguments: Option<JsonObject>,
        execution_context: &ExecutionContext,
        context_config: &ContextConfig,
    ) -> Result<Option<JsonObject>> {
        let mut enhanced_args = arguments.unwrap_or_else(JsonObject::new);

        // Inject session context
        if context_config.include_session {
            let session_context = self.build_session_context(execution_context, context_config).await?;
            enhanced_args.insert("session_context".to_string(), session_context);
        }

        // Inject memory context
        if context_config.include_memory {
            let memory_context = self.build_memory_context(execution_context, context_config).await?;
            enhanced_args.insert("memory_context".to_string(), memory_context);
        }

        // Inject workspace context
        if context_config.include_workspace {
            let workspace_context = self.build_workspace_context(execution_context).await?;
            enhanced_args.insert("workspace_context".to_string(), workspace_context);
        }

        // Add execution metadata
        enhanced_args.insert("execution_metadata".to_string(), json!({
            "session_id": execution_context.session_id,
            "working_directory": execution_context.working_directory,
            "timestamp": SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        }));

        Ok(Some(enhanced_args))
    }

    /// Build session context from execution context
    async fn build_session_context(
        &self,
        execution_context: &ExecutionContext,
        context_config: &ContextConfig,
    ) -> Result<Value> {
        // TODO: This would integrate with the session store to get recent messages
        // For now, return basic session info
        Ok(json!({
            "session_id": execution_context.session_id,
            "max_messages": context_config.max_session_messages,
            "recent_messages": [] // Placeholder for actual session messages
        }))
    }

    /// Build memory context from execution context
    async fn build_memory_context(
        &self,
        execution_context: &ExecutionContext,
        context_config: &ContextConfig,
    ) -> Result<Value> {
        // TODO: This would integrate with the memory store to get relevant memories
        // For now, return basic memory info
        Ok(json!({
            "max_entries": context_config.max_memory_entries,
            "filter_tags": context_config.memory_tags,
            "memories": [] // Placeholder for actual memory entries
        }))
    }

    /// Build workspace context from execution context
    async fn build_workspace_context(&self, execution_context: &ExecutionContext) -> Result<Value> {
        Ok(json!({
            "working_directory": execution_context.working_directory,
            "permissions": {
                "read": execution_context.permissions.read,
                "write": execution_context.permissions.write,
                "execute": execution_context.permissions.execute,
                "network": execution_context.permissions.network,
                "system": execution_context.permissions.system,
            },
            "file_states": execution_context.file_states
        }))
    }

    /// Process prompt result through template engine if needed
    async fn process_prompt_result(
        &self,
        result: GetPromptResult,
        _execution_context: &ExecutionContext,
    ) -> Result<GetPromptResult> {
        // Apply any template processing to the result content
        // This could include variable substitution, formatting, etc.
        // For now, return the result as-is
        
        // TODO: Implement template processing for prompt results
        // This could include:
        // - Variable substitution in messages
        // - Content formatting
        // - Dynamic content injection
        
        Ok(result)
    }

    /// List available prompts from all connected MCP servers
    pub async fn list_available_prompts(&self) -> Result<HashMap<String, Vec<Prompt>>> {
        let client_manager = self.mcp_client_manager.read().await;
        let mut all_prompts = HashMap::new();

        for client_id in client_manager.list_clients() {
            if let Some(client) = client_manager.get_client(&client_id) {
                match client.list_prompts().await {
                    Ok(prompts) => {
                        all_prompts.insert(client_id.to_string(), prompts);
                    }
                    Err(e) => {
                        warn!(server_id = client_id, error = %e, "Failed to list prompts from MCP server");
                    }
                }
            }
        }

        Ok(all_prompts)
    }

    /// Cancel a running execution
    pub async fn cancel_execution(&self, execution_id: Uuid) -> Result<()> {
        if let Some((_, cancel_sender)) = self.cancellation_tokens.remove(&execution_id) {
            let _ = cancel_sender.send(());
            info!(execution_id = %execution_id, "Prompt execution cancelled");
            Ok(())
        } else {
            Err(AutorunError::McpError(format!(
                "Execution {} not found or already completed",
                execution_id
            )))
        }
    }

    /// Get execution progress
    pub fn get_execution_progress(&self, execution_id: Uuid) -> Option<McpPromptProgress> {
        self.active_executions.get(&execution_id).map(|e| e.clone())
    }

    /// Get all active executions
    pub fn get_active_executions(&self) -> Vec<McpPromptProgress> {
        self.active_executions
            .iter()
            .map(|entry| entry.value().clone())
            .collect()
    }

    /// Subscribe to progress updates
    pub fn subscribe_to_progress(&self) -> broadcast::Receiver<McpPromptProgress> {
        self.progress_sender.subscribe()
    }

    /// Update execution progress
    async fn update_execution_progress(
        &self,
        execution_id: Uuid,
        progress: f32,
        message: Option<String>,
    ) {
        if let Some(mut entry) = self.active_executions.get_mut(&execution_id) {
            entry.update_progress(progress, message);
            let _ = self.progress_sender.send(entry.clone());
        }
    }

    /// Update progress and broadcast
    async fn update_progress(&self, execution_id: Uuid, progress: &McpPromptProgress) {
        if self.config.enable_progress_tracking {
            self.active_executions.insert(execution_id, progress.clone());
            let _ = self.progress_sender.send(progress.clone());
        }
    }

    /// Generate cache key for result caching
    fn generate_cache_key(
        &self,
        server_id: &str,
        prompt_name: &str,
        arguments: &Option<JsonObject>,
        context_config: &ContextConfig,
    ) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        server_id.hash(&mut hasher);
        prompt_name.hash(&mut hasher);
        if let Some(args) = arguments {
            // Hash arguments, excluding dynamic context that changes frequently
            for (key, value) in args {
                if !key.ends_with("_context") && key != "execution_metadata" {
                    key.hash(&mut hasher);
                    value.to_string().hash(&mut hasher);
                }
            }
        }
        serde_json::to_string(context_config).unwrap_or_default().hash(&mut hasher);
        format!("mcp_prompt_{}_{}", server_id, hasher.finish())
    }

    /// Cleanup expired cache entries
    fn cleanup_cache(&self) {
        let expired_keys: Vec<String> = self
            .result_cache
            .iter()
            .filter_map(|entry| {
                if entry.value().is_expired() {
                    Some(entry.key().clone())
                } else {
                    None
                }
            })
            .collect();

        for key in expired_keys {
            self.result_cache.remove(&key);
        }

        // If still over limit, remove oldest entries
        if self.result_cache.len() >= self.config.max_cache_size {
            let mut entries: Vec<_> = self.result_cache.iter().collect();
            entries.sort_by(|a, b| a.value().cached_at.cmp(&b.value().cached_at));

            let to_remove = entries.len() - (self.config.max_cache_size / 2);
            for entry in entries.iter().take(to_remove) {
                self.result_cache.remove(entry.key());
            }
        }
    }

    /// Clear all cached results
    pub fn clear_cache(&self) {
        self.result_cache.clear();
    }

    /// Get cache statistics
    pub fn get_cache_stats(&self) -> HashMap<String, Value> {
        let mut stats = HashMap::new();
        stats.insert("total_entries".to_string(), json!(self.result_cache.len()));
        stats.insert("max_size".to_string(), json!(self.config.max_cache_size));
        stats.insert("ttl_seconds".to_string(), json!(self.config.cache_ttl.as_secs()));
        
        let expired_count = self.result_cache
            .iter()
            .filter(|entry| entry.value().is_expired())
            .count();
        stats.insert("expired_entries".to_string(), json!(expired_count));
        
        stats
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tools::context::{Permission, ExecutionContext};
    use crate::config::Config;
    use std::path::PathBuf;

    fn create_test_execution_context() -> ExecutionContext {
        ExecutionContext::with_config(
            PathBuf::from("/tmp"),
            Config::default(),
            "test-session".to_string(),
        )
    }

    #[test]
    fn test_context_config_default() {
        let config = ContextConfig::default();
        assert!(config.include_session);
        assert!(config.include_memory);
        assert!(config.include_workspace);
        assert_eq!(config.max_session_messages, 10);
        assert_eq!(config.max_memory_entries, 20);
    }

    #[test]
    fn test_prompt_progress_lifecycle() {
        let execution_id = Uuid::new_v4();
        let mut progress = McpPromptProgress::new(
            execution_id,
            "test-prompt".to_string(),
            "test-server".to_string(),
        );

        // Test initial state
        assert!(matches!(progress.status, McpPromptStatus::Queued));
        assert_eq!(progress.progress, 0.0);

        // Test start
        progress.start();
        assert!(matches!(progress.status, McpPromptStatus::Running));

        // Test progress update
        progress.update_progress(50.0, Some("Processing".to_string()));
        assert_eq!(progress.progress, 50.0);
        assert_eq!(progress.message, Some("Processing".to_string()));

        // Test completion
        progress.complete(Some("Done".to_string()));
        assert!(matches!(progress.status, McpPromptStatus::Completed));
        assert_eq!(progress.progress, 100.0);
        assert!(progress.completed_at.is_some());
    }

    #[tokio::test]
    async fn test_context_injection() {
        let mcp_manager = Arc::new(RwLock::new(McpClientManager::new()));
        let template_engine = Arc::new(TemplateEngine::new().expect("Failed to create template engine"));
        let config = McpPromptHandlerConfig::default();
        let handler = McpPromptHandler::new(mcp_manager, template_engine, config);

        let execution_context = create_test_execution_context();
        let context_config = ContextConfig::default();

        let result = handler.inject_context(
            None,
            &execution_context,
            &context_config,
        ).await;

        assert!(result.is_ok());
        let enhanced_args = result.unwrap().unwrap();
        
        // Check that context was injected
        assert!(enhanced_args.contains_key("session_context"));
        assert!(enhanced_args.contains_key("memory_context"));
        assert!(enhanced_args.contains_key("workspace_context"));
        assert!(enhanced_args.contains_key("execution_metadata"));
    }

    #[test]
    fn test_cache_key_generation() {
        let mcp_manager = Arc::new(RwLock::new(McpClientManager::new()));
        let template_engine = Arc::new(TemplateEngine::new().expect("Failed to create template engine"));
        let config = McpPromptHandlerConfig::default();
        let handler = McpPromptHandler::new(mcp_manager, template_engine, config);

        let args = Some({
            let mut obj = JsonObject::new();
            obj.insert("test_param".to_string(), json!("test_value"));
            obj
        });
        let context_config = ContextConfig::default();

        let key1 = handler.generate_cache_key("server1", "prompt1", &args, &context_config);
        let key2 = handler.generate_cache_key("server1", "prompt1", &args, &context_config);
        let key3 = handler.generate_cache_key("server2", "prompt1", &args, &context_config);

        // Same inputs should generate same key
        assert_eq!(key1, key2);
        // Different server should generate different key
        assert_ne!(key1, key3);
    }
}