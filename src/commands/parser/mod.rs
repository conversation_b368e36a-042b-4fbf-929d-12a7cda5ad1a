//! Command parsing infrastructure for the three-tier command system
//!
//! This module provides the core parsing capabilities for:
//! - @ commands (context and reference)
//! - / commands (action and tool execution)  
//! - : commands (configuration and mode)

use crate::errors::{AutorunError, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

pub mod action;
pub mod config;
pub mod context;

/// The three types of commands supported by the system
#[derive(Debug, <PERSON>lone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum CommandType {
    /// Context and reference commands (@)
    Context,
    /// Action and tool execution commands (/)
    Action,
    /// Configuration and mode commands (:)
    Config,
}

/// Parsed command with its components
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParsedCommand {
    /// Command type (@, /, :)
    pub command_type: CommandType,
    /// Raw input text
    pub raw: String,
    /// Command name (first token after prefix)
    pub name: String,
    /// Command arguments
    pub args: Vec<String>,
    /// Parsed options/flags
    pub options: HashMap<String, String>,
    /// Original cursor position when command was triggered
    pub cursor_position: usize,
    /// Whether the command is complete (has all required arguments)
    pub is_complete: bool,
}

/// Context for command parsing
#[derive(Debug, Clone)]
pub struct ParsingContext {
    /// Full input text
    pub input: String,
    /// Current cursor position
    pub cursor_position: usize,
    /// Available context data
    pub context_data: HashMap<String, String>,
}

/// Result of command parsing
#[derive(Debug, Clone)]
pub enum ParseResult {
    /// Successfully parsed command
    Success(ParsedCommand),
    /// Partial command that needs more input
    Partial(ParsedCommand),
    /// Invalid command syntax
    Invalid(String),
    /// No command detected
    None,
}

/// Unified command parser trait
#[async_trait::async_trait]
pub trait UnifiedCommandParser: Send + Sync {
    /// Parse a command from the given context
    async fn parse(&self, context: &ParsingContext) -> Result<ParseResult>;

    /// Get the command type this parser handles
    fn command_type(&self) -> CommandType;

    /// Get trigger characters for this parser
    fn trigger_chars(&self) -> Vec<char>;

    /// Validate a parsed command
    async fn validate(&self, command: &ParsedCommand) -> Result<Vec<String>>;

    /// Get completion suggestions for partial commands
    async fn get_completions(&self, command: &ParsedCommand) -> Result<Vec<CommandCompletion>>;
}

/// Command completion suggestion
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommandCompletion {
    /// Display text
    pub text: String,
    /// Text to insert when selected
    pub insert_text: String,
    /// Optional description
    pub description: Option<String>,
    /// Completion type
    pub completion_type: CompletionType,
    /// Priority for sorting (higher = more important)
    pub priority: u8,
    /// Additional metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Types of command completions
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum CompletionType {
    /// Command name
    Command,
    /// Argument value
    Argument,
    /// Option/flag name
    Option,
    /// File path
    File,
    /// Directory path
    Directory,
    /// Variable reference
    Variable,
    /// Custom completion type
    Custom(String),
}

/// Main command parser that coordinates all command types
pub struct CommandParser {
    /// Context command parser (@)
    context_parser: Box<dyn UnifiedCommandParser>,
    /// Action command parser (/)
    action_parser: Box<dyn UnifiedCommandParser>,
    /// Config command parser (:)
    config_parser: Box<dyn UnifiedCommandParser>,
}

impl CommandParser {
    /// Create a new command parser with default implementations
    pub fn new() -> Self {
        Self {
            context_parser: Box::new(context::ContextCommandParser::new()),
            action_parser: Box::new(action::ActionCommandParser::new()),
            config_parser: Box::new(config::ConfigCommandParser::new()),
        }
    }

    /// Create a new command parser with context engine integration
    pub fn new_with_context_engine(
        context_engine: std::sync::Arc<crate::ui::completion::context_engine::ContextEngine>,
    ) -> Self {
        Self {
            context_parser: Box::new(context::ContextCommandParser::new_with_context_engine(
                context_engine,
            )),
            action_parser: Box::new(action::ActionCommandParser::new()),
            config_parser: Box::new(config::ConfigCommandParser::new()),
        }
    }

    /// Parse any command type from the given context
    pub async fn parse_any(&self, context: &ParsingContext) -> Result<ParseResult> {
        // Determine command type from the input
        let command_type = self.detect_command_type(&context.input)?;

        match command_type {
            Some(CommandType::Context) => self.context_parser.parse(context).await,
            Some(CommandType::Action) => self.action_parser.parse(context).await,
            Some(CommandType::Config) => self.config_parser.parse(context).await,
            None => Ok(ParseResult::None),
        }
    }

    /// Get completions for any command type
    pub async fn get_completions(
        &self,
        context: &ParsingContext,
    ) -> Result<Vec<CommandCompletion>> {
        let parse_result = self.parse_any(context).await?;

        match parse_result {
            ParseResult::Success(command) | ParseResult::Partial(command) => {
                match command.command_type {
                    CommandType::Context => self.context_parser.get_completions(&command).await,
                    CommandType::Action => self.action_parser.get_completions(&command).await,
                    CommandType::Config => self.config_parser.get_completions(&command).await,
                }
            }
            _ => Ok(vec![]),
        }
    }

    /// Detect command type from input
    fn detect_command_type(&self, input: &str) -> Result<Option<CommandType>> {
        if input.trim().is_empty() {
            return Ok(None);
        }

        // Find the last trigger character before or at cursor
        let chars: Vec<char> = input.chars().collect();

        for &ch in chars.iter().rev() {
            match ch {
                '@' => return Ok(Some(CommandType::Context)),
                '/' => return Ok(Some(CommandType::Action)),
                ':' => return Ok(Some(CommandType::Config)),
                ' ' | '\t' | '\n' => continue, // Skip whitespace
                _ => break,                    // Stop at non-whitespace, non-trigger character
            }
        }

        Ok(None)
    }

    /// Validate a command
    pub async fn validate(&self, command: &ParsedCommand) -> Result<Vec<String>> {
        match command.command_type {
            CommandType::Context => self.context_parser.validate(command).await,
            CommandType::Action => self.action_parser.validate(command).await,
            CommandType::Config => self.config_parser.validate(command).await,
        }
    }
}

impl Default for CommandParser {
    fn default() -> Self {
        Self::new()
    }
}

impl ParsedCommand {
    /// Create a new parsed command
    pub fn new(command_type: CommandType, raw: String, cursor_position: usize) -> Self {
        Self {
            command_type,
            raw: raw.clone(),
            name: String::new(),
            args: Vec::new(),
            options: HashMap::new(),
            cursor_position,
            is_complete: false,
        }
    }

    /// Get argument by index
    pub fn get_arg(&self, index: usize) -> Option<&str> {
        self.args.get(index).map(|s| s.as_str())
    }

    /// Get option by name
    pub fn get_option(&self, name: &str) -> Option<&str> {
        self.options.get(name).map(|s| s.as_str())
    }

    /// Check if command has a specific option
    pub fn has_option(&self, name: &str) -> bool {
        self.options.contains_key(name)
    }

    /// Add an argument
    pub fn add_arg(&mut self, arg: String) {
        self.args.push(arg);
    }

    /// Add an option
    pub fn add_option(&mut self, name: String, value: String) {
        self.options.insert(name, value);
    }

    /// Set command name
    pub fn set_name(&mut self, name: String) {
        self.name = name;
    }

    /// Mark command as complete
    pub fn mark_complete(&mut self) {
        self.is_complete = true;
    }
}

impl CommandCompletion {
    /// Create a new command completion
    pub fn new(text: String, completion_type: CompletionType) -> Self {
        Self {
            insert_text: text.clone(),
            text,
            description: None,
            completion_type,
            priority: 0,
            metadata: HashMap::new(),
        }
    }

    /// Set description
    pub fn with_description(mut self, description: String) -> Self {
        self.description = Some(description);
        self
    }

    /// Set insert text (different from display text)
    pub fn with_insert_text(mut self, insert_text: String) -> Self {
        self.insert_text = insert_text;
        self
    }

    /// Set priority
    pub fn with_priority(mut self, priority: u8) -> Self {
        self.priority = priority;
        self
    }

    /// Add metadata
    pub fn with_metadata(mut self, key: String, value: serde_json::Value) -> Self {
        self.metadata.insert(key, value);
        self
    }
}

impl ParsingContext {
    /// Create a new parsing context
    pub fn new(input: String, cursor_position: usize) -> Self {
        Self {
            input,
            cursor_position,
            context_data: HashMap::new(),
        }
    }

    /// Add context data
    pub fn with_context_data(mut self, key: String, value: String) -> Self {
        self.context_data.insert(key, value);
        self
    }

    /// Get text before cursor
    pub fn text_before_cursor(&self) -> &str {
        &self.input[..self.cursor_position.min(self.input.len())]
    }

    /// Get text after cursor
    pub fn text_after_cursor(&self) -> &str {
        &self.input[self.cursor_position.min(self.input.len())..]
    }

    /// Find the position of the last trigger character before cursor
    pub fn find_last_trigger(&self, triggers: &[char]) -> Option<(char, usize)> {
        let text = self.text_before_cursor();

        for (i, ch) in text.char_indices().rev() {
            if triggers.contains(&ch) {
                return Some((ch, i));
            }
        }

        None
    }
}

/// Utility functions for command parsing
pub mod utils {
    use super::*;

    /// Parse arguments from a command string
    pub fn parse_args(input: &str) -> Vec<String> {
        // Simple argument parsing - can be enhanced for quoted strings, etc.
        input.split_whitespace().map(|s| s.to_string()).collect()
    }

    /// Parse options from arguments (--key=value or --key value)
    pub fn parse_options(args: &[String]) -> (Vec<String>, HashMap<String, String>) {
        let mut remaining_args = Vec::new();
        let mut options = HashMap::new();

        let mut i = 0;
        while i < args.len() {
            let arg = &args[i];

            if arg.starts_with("--") {
                let key = &arg[2..];

                if let Some(eq_pos) = key.find('=') {
                    // --key=value format
                    let (option_key, value) = key.split_at(eq_pos);
                    options.insert(option_key.to_string(), value[1..].to_string());
                } else if i + 1 < args.len() && !args[i + 1].starts_with("--") {
                    // --key value format
                    options.insert(key.to_string(), args[i + 1].clone());
                    i += 1; // Skip the value
                } else {
                    // --key (boolean flag)
                    options.insert(key.to_string(), "true".to_string());
                }
            } else {
                remaining_args.push(arg.clone());
            }

            i += 1;
        }

        (remaining_args, options)
    }

    /// Extract the command name from the first argument
    pub fn extract_command_name(args: &[String]) -> Option<String> {
        args.first().cloned()
    }

    /// Calculate fuzzy match score between two strings
    pub fn fuzzy_score(text: &str, query: &str) -> u32 {
        if query.is_empty() {
            return 100;
        }

        let text = text.to_lowercase();
        let query = query.to_lowercase();

        // Exact match gets highest score
        if text == query {
            return 1000;
        }

        // Prefix match gets high score
        if text.starts_with(&query) {
            return 500;
        }

        // Substring match gets medium score
        if let Some(pos) = text.find(&query) {
            return 200 - pos as u32; // Earlier matches score higher
        }

        // Character sequence match gets low score
        let mut score = 0;
        let mut text_chars = text.chars().peekable();
        let mut query_chars = query.chars().peekable();

        while let (Some(&text_char), Some(&query_char)) = (text_chars.peek(), query_chars.peek()) {
            if text_char == query_char {
                score += 1;
                query_chars.next();
            }
            text_chars.next();
        }

        // If we matched all query characters, return the score
        if query_chars.peek().is_none() {
            score
        } else {
            0
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_detect_command_type() {
        let parser = CommandParser::new();

        assert_eq!(
            parser.detect_command_type("@file").unwrap(),
            Some(CommandType::Context)
        );
        assert_eq!(
            parser.detect_command_type("/memory").unwrap(),
            Some(CommandType::Action)
        );
        assert_eq!(
            parser.detect_command_type(":mode vim").unwrap(),
            Some(CommandType::Config)
        );
        assert_eq!(parser.detect_command_type("hello world").unwrap(), None);
        assert_eq!(parser.detect_command_type("").unwrap(), None);
    }

    #[test]
    fn test_parse_args() {
        let args = utils::parse_args("cmd arg1 arg2 arg3");
        assert_eq!(args, vec!["cmd", "arg1", "arg2", "arg3"]);

        let args = utils::parse_args("  spaced   args  ");
        assert_eq!(args, vec!["spaced", "args"]);
    }

    #[test]
    fn test_parse_options() {
        let args = vec![
            "cmd".to_string(),
            "--key1=value1".to_string(),
            "--key2".to_string(),
            "value2".to_string(),
            "--flag".to_string(),
            "remaining".to_string(),
        ];

        let (remaining, options) = utils::parse_options(&args);

        assert_eq!(remaining, vec!["cmd", "remaining"]);
        assert_eq!(options.get("key1"), Some(&"value1".to_string()));
        assert_eq!(options.get("key2"), Some(&"value2".to_string()));
        assert_eq!(options.get("flag"), Some(&"true".to_string()));
    }

    #[test]
    fn test_fuzzy_score() {
        assert_eq!(utils::fuzzy_score("test", "test"), 1000);
        assert_eq!(utils::fuzzy_score("testing", "test"), 500);
        assert!(utils::fuzzy_score("best test", "test") > 0);
        assert!(utils::fuzzy_score("best test", "test") < 500);
    }
}
