// Action command parser (/) - handles tool execution, operations, and workflows

use super::{
    utils, CommandCompletion, CommandType, CompletionType, ParseResult, Parsed<PERSON>ommand,
    ParsingContext, UnifiedCommandParser,
};
use crate::errors::Result;
use async_trait::async_trait;
use std::collections::HashMap;

/// Parser for action commands (/)
pub struct ActionCommandParser {
    /// Available action commands
    commands: HashMap<String, ActionCommandInfo>,
}

/// Information about an action command
#[derive(Debug, <PERSON><PERSON>)]
struct ActionCommandInfo {
    /// Command name
    name: String,
    /// Command description
    description: String,
    /// Command category
    category: ActionCategory,
    /// Required arguments
    required_args: Vec<String>,
    /// Optional arguments
    optional_args: Vec<String>,
    /// Supported flags
    flags: Vec<String>,
    /// Whether this command supports auto-completion
    supports_completion: bool,
    /// Priority for completion ordering
    priority: u8,
}

/// Categories of action commands
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>)]
enum ActionCategory {
    /// File operations
    File,
    /// Search operations
    Search,
    /// Code analysis
    Analysis,
    /// Tool execution
    Tool,
    /// Memory operations
    Memory,
    /// Session management
    Session,
    /// Workflow automation
    Workflow,
    /// System operations
    System,
}

impl ActionCommandParser {
    /// Create a new action command parser
    pub fn new() -> Self {
        let mut commands = HashMap::new();

        // Define all action commands
        let action_commands = vec![
            // File operations
            ActionCommandInfo {
                name: "edit".to_string(),
                description: "Edit a file".to_string(),
                category: ActionCategory::File,
                required_args: vec!["file".to_string()],
                optional_args: vec!["line".to_string(), "column".to_string()],
                flags: vec!["create".to_string(), "backup".to_string()],
                supports_completion: true,
                priority: 10,
            },
            ActionCommandInfo {
                name: "create".to_string(),
                description: "Create a new file or directory".to_string(),
                category: ActionCategory::File,
                required_args: vec!["path".to_string()],
                optional_args: vec!["template".to_string()],
                flags: vec!["directory".to_string(), "force".to_string()],
                supports_completion: true,
                priority: 9,
            },
            ActionCommandInfo {
                name: "delete".to_string(),
                description: "Delete a file or directory".to_string(),
                category: ActionCategory::File,
                required_args: vec!["path".to_string()],
                optional_args: vec![],
                flags: vec!["recursive".to_string(), "force".to_string()],
                supports_completion: true,
                priority: 8,
            },
            ActionCommandInfo {
                name: "copy".to_string(),
                description: "Copy files or directories".to_string(),
                category: ActionCategory::File,
                required_args: vec!["source".to_string(), "destination".to_string()],
                optional_args: vec![],
                flags: vec!["recursive".to_string(), "preserve".to_string()],
                supports_completion: true,
                priority: 7,
            },
            ActionCommandInfo {
                name: "move".to_string(),
                description: "Move or rename files".to_string(),
                category: ActionCategory::File,
                required_args: vec!["source".to_string(), "destination".to_string()],
                optional_args: vec![],
                flags: vec!["force".to_string()],
                supports_completion: true,
                priority: 6,
            },
            // Search operations
            ActionCommandInfo {
                name: "search".to_string(),
                description: "Search for text in files".to_string(),
                category: ActionCategory::Search,
                required_args: vec!["pattern".to_string()],
                optional_args: vec!["path".to_string()],
                flags: vec![
                    "regex".to_string(),
                    "case_sensitive".to_string(),
                    "whole_word".to_string(),
                ],
                supports_completion: true,
                priority: 10,
            },
            ActionCommandInfo {
                name: "find".to_string(),
                description: "Find files by name or pattern".to_string(),
                category: ActionCategory::Search,
                required_args: vec!["pattern".to_string()],
                optional_args: vec!["path".to_string()],
                flags: vec!["regex".to_string(), "type".to_string()],
                supports_completion: true,
                priority: 9,
            },
            ActionCommandInfo {
                name: "replace".to_string(),
                description: "Replace text in files".to_string(),
                category: ActionCategory::Search,
                required_args: vec!["pattern".to_string(), "replacement".to_string()],
                optional_args: vec!["path".to_string()],
                flags: vec![
                    "regex".to_string(),
                    "all".to_string(),
                    "dry_run".to_string(),
                ],
                supports_completion: true,
                priority: 8,
            },
            // Code analysis
            ActionCommandInfo {
                name: "analyze".to_string(),
                description: "Analyze code structure and patterns".to_string(),
                category: ActionCategory::Analysis,
                required_args: vec![],
                optional_args: vec!["path".to_string(), "type".to_string()],
                flags: vec!["deep".to_string(), "metrics".to_string()],
                supports_completion: true,
                priority: 7,
            },
            ActionCommandInfo {
                name: "symbols".to_string(),
                description: "Find and list code symbols".to_string(),
                category: ActionCategory::Analysis,
                required_args: vec![],
                optional_args: vec!["pattern".to_string(), "type".to_string()],
                flags: vec!["definitions".to_string(), "references".to_string()],
                supports_completion: true,
                priority: 6,
            },
            ActionCommandInfo {
                name: "dependencies".to_string(),
                description: "Analyze project dependencies".to_string(),
                category: ActionCategory::Analysis,
                required_args: vec![],
                optional_args: vec!["format".to_string()],
                flags: vec![
                    "tree".to_string(),
                    "unused".to_string(),
                    "outdated".to_string(),
                ],
                supports_completion: false,
                priority: 5,
            },
            // Tool execution
            ActionCommandInfo {
                name: "run".to_string(),
                description: "Run a command or tool".to_string(),
                category: ActionCategory::Tool,
                required_args: vec!["command".to_string()],
                optional_args: vec!["args".to_string()],
                flags: vec!["async".to_string(), "capture".to_string()],
                supports_completion: true,
                priority: 9,
            },
            ActionCommandInfo {
                name: "test".to_string(),
                description: "Run tests".to_string(),
                category: ActionCategory::Tool,
                required_args: vec![],
                optional_args: vec!["pattern".to_string(), "file".to_string()],
                flags: vec![
                    "verbose".to_string(),
                    "watch".to_string(),
                    "coverage".to_string(),
                ],
                supports_completion: true,
                priority: 8,
            },
            ActionCommandInfo {
                name: "build".to_string(),
                description: "Build the project".to_string(),
                category: ActionCategory::Tool,
                required_args: vec![],
                optional_args: vec!["target".to_string()],
                flags: vec![
                    "release".to_string(),
                    "watch".to_string(),
                    "clean".to_string(),
                ],
                supports_completion: false,
                priority: 7,
            },
            ActionCommandInfo {
                name: "format".to_string(),
                description: "Format code".to_string(),
                category: ActionCategory::Tool,
                required_args: vec![],
                optional_args: vec!["path".to_string()],
                flags: vec!["check".to_string(), "diff".to_string()],
                supports_completion: true,
                priority: 6,
            },
            ActionCommandInfo {
                name: "lint".to_string(),
                description: "Run linter".to_string(),
                category: ActionCategory::Tool,
                required_args: vec![],
                optional_args: vec!["path".to_string()],
                flags: vec!["fix".to_string(), "strict".to_string()],
                supports_completion: true,
                priority: 5,
            },
            // Memory operations
            ActionCommandInfo {
                name: "remember".to_string(),
                description: "Store information in memory".to_string(),
                category: ActionCategory::Memory,
                required_args: vec!["content".to_string()],
                optional_args: vec!["tag".to_string(), "priority".to_string()],
                flags: vec!["persistent".to_string()],
                supports_completion: false,
                priority: 4,
            },
            ActionCommandInfo {
                name: "recall".to_string(),
                description: "Retrieve stored memories".to_string(),
                category: ActionCategory::Memory,
                required_args: vec![],
                optional_args: vec!["query".to_string(), "tag".to_string()],
                flags: vec!["recent".to_string(), "all".to_string()],
                supports_completion: true,
                priority: 3,
            },
            ActionCommandInfo {
                name: "forget".to_string(),
                description: "Remove memories".to_string(),
                category: ActionCategory::Memory,
                required_args: vec!["id".to_string()],
                optional_args: vec![],
                flags: vec!["confirm".to_string()],
                supports_completion: true,
                priority: 2,
            },
            // Session management
            ActionCommandInfo {
                name: "save".to_string(),
                description: "Save current session".to_string(),
                category: ActionCategory::Session,
                required_args: vec![],
                optional_args: vec!["name".to_string()],
                flags: vec!["auto".to_string()],
                supports_completion: false,
                priority: 4,
            },
            ActionCommandInfo {
                name: "load".to_string(),
                description: "Load a saved session".to_string(),
                category: ActionCategory::Session,
                required_args: vec!["name".to_string()],
                optional_args: vec![],
                flags: vec!["merge".to_string()],
                supports_completion: true,
                priority: 3,
            },
            ActionCommandInfo {
                name: "reset".to_string(),
                description: "Reset current session".to_string(),
                category: ActionCategory::Session,
                required_args: vec![],
                optional_args: vec![],
                flags: vec!["confirm".to_string()],
                supports_completion: false,
                priority: 2,
            },
            // Workflow automation
            ActionCommandInfo {
                name: "workflow".to_string(),
                description: "Execute a workflow".to_string(),
                category: ActionCategory::Workflow,
                required_args: vec!["name".to_string()],
                optional_args: vec!["params".to_string()],
                flags: vec!["dry_run".to_string(), "verbose".to_string()],
                supports_completion: true,
                priority: 6,
            },
            ActionCommandInfo {
                name: "macro".to_string(),
                description: "Execute a macro".to_string(),
                category: ActionCategory::Workflow,
                required_args: vec!["name".to_string()],
                optional_args: vec!["args".to_string()],
                flags: vec!["record".to_string(), "edit".to_string()],
                supports_completion: true,
                priority: 5,
            },
            // System operations
            ActionCommandInfo {
                name: "doctor".to_string(),
                description: "Run system diagnostics".to_string(),
                category: ActionCategory::System,
                required_args: vec![],
                optional_args: vec!["component".to_string()],
                flags: vec!["verbose".to_string(), "fix".to_string()],
                supports_completion: false,
                priority: 3,
            },
            ActionCommandInfo {
                name: "update".to_string(),
                description: "Update system components".to_string(),
                category: ActionCategory::System,
                required_args: vec![],
                optional_args: vec!["component".to_string()],
                flags: vec!["force".to_string(), "dry_run".to_string()],
                supports_completion: true,
                priority: 2,
            },
            ActionCommandInfo {
                name: "config".to_string(),
                description: "Manage configuration".to_string(),
                category: ActionCategory::System,
                required_args: vec!["action".to_string()],
                optional_args: vec!["key".to_string(), "value".to_string()],
                flags: vec!["global".to_string(), "local".to_string()],
                supports_completion: true,
                priority: 4,
            },
        ];

        for cmd in action_commands {
            commands.insert(cmd.name.clone(), cmd);
        }

        Self { commands }
    }

    /// Parse the action command
    fn parse_action_command(&self, raw: &str, cursor_position: usize) -> Result<ParsedCommand> {
        let mut command = ParsedCommand::new(CommandType::Action, raw.to_string(), cursor_position);

        // Remove / prefix and parse
        let content = if raw.starts_with('/') { &raw[1..] } else { raw };

        let args = utils::parse_args(content);
        let (remaining_args, options) = utils::parse_options(&args);

        if let Some(name) = utils::extract_command_name(&remaining_args) {
            command.set_name(name);

            // Add remaining arguments
            for arg in remaining_args.iter().skip(1) {
                command.add_arg(arg.clone());
            }

            // Add options
            for (key, value) in options {
                command.add_option(key, value);
            }

            // Check if command is complete
            if let Some(cmd_info) = self.commands.get(&command.name) {
                if command.args.len() >= cmd_info.required_args.len() {
                    command.mark_complete();
                }
            }
        }

        Ok(command)
    }
}

#[async_trait]
impl UnifiedCommandParser for ActionCommandParser {
    async fn parse(&self, context: &ParsingContext) -> Result<ParseResult> {
        // Find / trigger in the input
        if let Some((_, trigger_pos)) = context.find_last_trigger(&['/']) {
            let command_text = &context.input[trigger_pos..];
            let relative_cursor = context.cursor_position.saturating_sub(trigger_pos);

            match self.parse_action_command(command_text, relative_cursor) {
                Ok(command) => {
                    if command.name.is_empty() {
                        Ok(ParseResult::Partial(command))
                    } else if command.is_complete {
                        Ok(ParseResult::Success(command))
                    } else {
                        Ok(ParseResult::Partial(command))
                    }
                }
                Err(e) => Ok(ParseResult::Invalid(e.to_string())),
            }
        } else {
            Ok(ParseResult::None)
        }
    }

    fn command_type(&self) -> CommandType {
        CommandType::Action
    }

    fn trigger_chars(&self) -> Vec<char> {
        vec!['/']
    }

    async fn validate(&self, command: &ParsedCommand) -> Result<Vec<String>> {
        let mut errors = Vec::new();

        if command.command_type != CommandType::Action {
            errors.push("Invalid command type for action parser".to_string());
            return Ok(errors);
        }

        if let Some(cmd_info) = self.commands.get(&command.name) {
            // Check required arguments
            if command.args.len() < cmd_info.required_args.len() {
                errors.push(format!(
                    "Command '{}' requires {} arguments, got {}",
                    command.name,
                    cmd_info.required_args.len(),
                    command.args.len()
                ));
            }

            // Validate specific command requirements
            match command.name.as_str() {
                "edit" | "create" | "delete" => {
                    if let Some(path) = command.get_arg(0) {
                        if path.is_empty() {
                            errors.push("File path cannot be empty".to_string());
                        }
                    }
                }
                "search" | "find" | "replace" => {
                    if let Some(pattern) = command.get_arg(0) {
                        if pattern.is_empty() {
                            errors.push("Search pattern cannot be empty".to_string());
                        }
                    }
                }
                "copy" | "move" => {
                    if command.args.len() >= 2 {
                        if command.args[0].is_empty() || command.args[1].is_empty() {
                            errors.push("Source and destination paths cannot be empty".to_string());
                        }
                    }
                }
                "run" => {
                    if let Some(cmd) = command.get_arg(0) {
                        if cmd.is_empty() {
                            errors.push("Command cannot be empty".to_string());
                        }
                    }
                }
                _ => {}
            }
        } else {
            errors.push(format!("Unknown action command: {}", command.name));
        }

        Ok(errors)
    }

    async fn get_completions(&self, command: &ParsedCommand) -> Result<Vec<CommandCompletion>> {
        let mut completions = Vec::new();

        // If no command name yet, suggest all commands
        if command.name.is_empty() {
            for cmd_info in self.commands.values() {
                let completion =
                    CommandCompletion::new(cmd_info.name.clone(), CompletionType::Command)
                        .with_description(cmd_info.description.clone())
                        .with_priority(cmd_info.priority);

                completions.push(completion);
            }
        } else {
            // Filter commands by partial name match
            let query = &command.name;
            for cmd_info in self.commands.values() {
                if cmd_info.name.starts_with(query) || utils::fuzzy_score(&cmd_info.name, query) > 0
                {
                    let completion =
                        CommandCompletion::new(cmd_info.name.clone(), CompletionType::Command)
                            .with_description(cmd_info.description.clone())
                            .with_priority(cmd_info.priority);

                    completions.push(completion);
                }
            }

            // Add argument completions for known commands
            if let Some(cmd_info) = self.commands.get(&command.name) {
                self.add_argument_completions(&mut completions, cmd_info, command)
                    .await?;
            }
        }

        // Sort by priority and fuzzy score
        completions.sort_by(|a, b| {
            b.priority.cmp(&a.priority).then_with(|| {
                let score_a = utils::fuzzy_score(&a.text, &command.name);
                let score_b = utils::fuzzy_score(&b.text, &command.name);
                score_b.cmp(&score_a)
            })
        });

        Ok(completions)
    }
}

impl ActionCommandParser {
    /// Add argument-specific completions
    async fn add_argument_completions(
        &self,
        completions: &mut Vec<CommandCompletion>,
        cmd_info: &ActionCommandInfo,
        command: &ParsedCommand,
    ) -> Result<()> {
        let arg_index = command.args.len();

        match cmd_info.name.as_str() {
            "edit" | "create" | "delete" => {
                if arg_index == 0 {
                    // File path completions
                    self.add_file_completions(completions).await?;
                }
            }
            "copy" | "move" => {
                if arg_index == 0 || arg_index == 1 {
                    // Source and destination file completions
                    self.add_file_completions(completions).await?;
                }
            }
            "search" | "find" => {
                if arg_index == 1 {
                    // Path argument for search
                    self.add_directory_completions(completions).await?;
                }
            }
            "test" => {
                if arg_index == 0 {
                    // Test pattern completions
                    completions.push(
                        CommandCompletion::new(
                            "unit".to_string(),
                            CompletionType::Custom("test_type".to_string()),
                        )
                        .with_description("Unit tests".to_string())
                        .with_priority(5),
                    );
                    completions.push(
                        CommandCompletion::new(
                            "integration".to_string(),
                            CompletionType::Custom("test_type".to_string()),
                        )
                        .with_description("Integration tests".to_string())
                        .with_priority(5),
                    );
                }
            }
            "build" => {
                if arg_index == 0 {
                    // Build target completions
                    completions.push(
                        CommandCompletion::new(
                            "debug".to_string(),
                            CompletionType::Custom("build_target".to_string()),
                        )
                        .with_description("Debug build".to_string())
                        .with_priority(5),
                    );
                    completions.push(
                        CommandCompletion::new(
                            "release".to_string(),
                            CompletionType::Custom("build_target".to_string()),
                        )
                        .with_description("Release build".to_string())
                        .with_priority(5),
                    );
                }
            }
            "config" => {
                if arg_index == 0 {
                    // Config action completions
                    let actions = vec![
                        ("get", "Get configuration value"),
                        ("set", "Set configuration value"),
                        ("list", "List all configuration"),
                        ("reset", "Reset configuration"),
                    ];

                    for (action, desc) in actions {
                        completions.push(
                            CommandCompletion::new(
                                action.to_string(),
                                CompletionType::Custom("config_action".to_string()),
                            )
                            .with_description(desc.to_string())
                            .with_priority(5),
                        );
                    }
                }
            }
            _ => {}
        }

        Ok(())
    }

    /// Add file path completions
    async fn add_file_completions(&self, completions: &mut Vec<CommandCompletion>) -> Result<()> {
        // Common file patterns - in a real implementation, this would scan the filesystem
        let files = vec![
            "src/main.rs",
            "src/lib.rs",
            "Cargo.toml",
            "README.md",
            ".gitignore",
        ];

        for file in files {
            completions.push(
                CommandCompletion::new(file.to_string(), CompletionType::File)
                    .with_description(format!("File: {}", file))
                    .with_priority(3),
            );
        }

        Ok(())
    }

    /// Add directory completions
    async fn add_directory_completions(
        &self,
        completions: &mut Vec<CommandCompletion>,
    ) -> Result<()> {
        // Common directories - in a real implementation, this would scan the filesystem
        let dirs = vec!["src/", "tests/", "docs/", "target/"];

        for dir in dirs {
            completions.push(
                CommandCompletion::new(dir.to_string(), CompletionType::Directory)
                    .with_description(format!("Directory: {}", dir))
                    .with_priority(3),
            );
        }

        Ok(())
    }
}

impl Default for ActionCommandParser {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_parse_action_command() {
        let parser = ActionCommandParser::new();
        let context = ParsingContext::new("/edit src/main.rs".to_string(), 16);

        let result = parser.parse(&context).await.unwrap();
        if let ParseResult::Success(command) = result {
            assert_eq!(command.command_type, CommandType::Action);
            assert_eq!(command.name, "edit");
            assert_eq!(command.args.len(), 1);
            assert_eq!(command.args[0], "src/main.rs");
        } else {
            panic!("Expected successful parse");
        }
    }

    #[tokio::test]
    async fn test_partial_action_command() {
        let parser = ActionCommandParser::new();
        let context = ParsingContext::new("/edi".to_string(), 4);

        let result = parser.parse(&context).await.unwrap();
        if let ParseResult::Partial(command) = result {
            assert_eq!(command.command_type, CommandType::Action);
            assert_eq!(command.name, "edi");
            assert!(!command.is_complete);
        } else {
            panic!("Expected partial parse");
        }
    }

    #[tokio::test]
    async fn test_action_completions() {
        let parser = ActionCommandParser::new();
        let mut command = ParsedCommand::new(CommandType::Action, "/ed".to_string(), 3);
        command.set_name("ed".to_string());

        let completions = parser.get_completions(&command).await.unwrap();
        assert!(!completions.is_empty());

        // Should include "edit" as a completion
        let edit_completion = completions.iter().find(|c| c.text == "edit");
        assert!(edit_completion.is_some());
    }

    #[tokio::test]
    async fn test_validate_action_command() {
        let parser = ActionCommandParser::new();
        let mut command = ParsedCommand::new(CommandType::Action, "/edit".to_string(), 5);
        command.set_name("edit".to_string());

        // Missing required argument
        let errors = parser.validate(&command).await.unwrap();
        assert!(!errors.is_empty());

        // With required argument
        command.add_arg("src/main.rs".to_string());
        command.mark_complete();
        let errors = parser.validate(&command).await.unwrap();
        assert!(errors.is_empty());
    }
}
