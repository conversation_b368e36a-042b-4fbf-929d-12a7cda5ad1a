// Config command parser (:) - handles configuration, settings, and mode changes

use super::{
    utils, CommandCompletion, CommandType, CompletionType, ParseResult, ParsedCommand,
    ParsingContext, UnifiedCommandParser,
};
use crate::errors::Result;
use async_trait::async_trait;
use std::collections::HashMap;

/// Parser for config commands (:)
pub struct ConfigCommandParser {
    /// Available config commands
    commands: HashMap<String, ConfigCommandInfo>,
}

/// Information about a config command
#[derive(Debug, Clone)]
struct ConfigCommandInfo {
    /// Command name
    name: String,
    /// Command description
    description: String,
    /// Command category
    category: ConfigCategory,
    /// Required arguments
    required_args: Vec<String>,
    /// Optional arguments
    optional_args: Vec<String>,
    /// Supported flags
    flags: Vec<String>,
    /// Whether this command supports auto-completion
    supports_completion: bool,
    /// Priority for completion ordering
    priority: u8,
}

/// Categories of config commands
#[derive(Debug, <PERSON><PERSON>, <PERSON>ialEq)]
enum ConfigCategory {
    /// UI mode and display settings
    Mode,
    /// User preferences
    Preferences,
    /// Theme and appearance
    Theme,
    /// Keybindings and shortcuts
    Keybindings,
    /// Plugin and extension settings
    Plugins,
    /// Performance and behavior
    Performance,
    /// Security and privacy
    Security,
    /// Integration settings
    Integration,
}

impl ConfigCommandParser {
    /// Create a new config command parser
    pub fn new() -> Self {
        let mut commands = HashMap::new();

        // Define all config commands
        let config_commands = vec![
            // Mode commands
            ConfigCommandInfo {
                name: "mode".to_string(),
                description: "Change UI mode".to_string(),
                category: ConfigCategory::Mode,
                required_args: vec!["mode".to_string()],
                optional_args: vec![],
                flags: vec!["temporary".to_string()],
                supports_completion: true,
                priority: 10,
            },
            ConfigCommandInfo {
                name: "view".to_string(),
                description: "Change view layout".to_string(),
                category: ConfigCategory::Mode,
                required_args: vec!["layout".to_string()],
                optional_args: vec![],
                flags: vec!["split".to_string(), "tab".to_string()],
                supports_completion: true,
                priority: 9,
            },
            ConfigCommandInfo {
                name: "panel".to_string(),
                description: "Show/hide panels".to_string(),
                category: ConfigCategory::Mode,
                required_args: vec!["panel".to_string(), "state".to_string()],
                optional_args: vec![],
                flags: vec!["toggle".to_string()],
                supports_completion: true,
                priority: 8,
            },
            // Preference commands
            ConfigCommandInfo {
                name: "set".to_string(),
                description: "Set configuration value".to_string(),
                category: ConfigCategory::Preferences,
                required_args: vec!["key".to_string(), "value".to_string()],
                optional_args: vec![],
                flags: vec![
                    "global".to_string(),
                    "local".to_string(),
                    "temporary".to_string(),
                ],
                supports_completion: true,
                priority: 10,
            },
            ConfigCommandInfo {
                name: "get".to_string(),
                description: "Get configuration value".to_string(),
                category: ConfigCategory::Preferences,
                required_args: vec!["key".to_string()],
                optional_args: vec![],
                flags: vec!["default".to_string()],
                supports_completion: true,
                priority: 9,
            },
            ConfigCommandInfo {
                name: "unset".to_string(),
                description: "Remove configuration value".to_string(),
                category: ConfigCategory::Preferences,
                required_args: vec!["key".to_string()],
                optional_args: vec![],
                flags: vec!["confirm".to_string()],
                supports_completion: true,
                priority: 8,
            },
            ConfigCommandInfo {
                name: "list".to_string(),
                description: "List configuration values".to_string(),
                category: ConfigCategory::Preferences,
                required_args: vec![],
                optional_args: vec!["filter".to_string()],
                flags: vec!["all".to_string(), "modified".to_string()],
                supports_completion: true,
                priority: 7,
            },
            ConfigCommandInfo {
                name: "reset".to_string(),
                description: "Reset configuration to defaults".to_string(),
                category: ConfigCategory::Preferences,
                required_args: vec![],
                optional_args: vec!["scope".to_string()],
                flags: vec!["confirm".to_string(), "backup".to_string()],
                supports_completion: true,
                priority: 6,
            },
            // Theme commands
            ConfigCommandInfo {
                name: "theme".to_string(),
                description: "Change color theme".to_string(),
                category: ConfigCategory::Theme,
                required_args: vec!["name".to_string()],
                optional_args: vec![],
                flags: vec!["preview".to_string(), "permanent".to_string()],
                supports_completion: true,
                priority: 8,
            },
            ConfigCommandInfo {
                name: "color".to_string(),
                description: "Set color scheme".to_string(),
                category: ConfigCategory::Theme,
                required_args: vec!["scheme".to_string()],
                optional_args: vec![],
                flags: vec!["dark".to_string(), "light".to_string(), "auto".to_string()],
                supports_completion: true,
                priority: 7,
            },
            ConfigCommandInfo {
                name: "font".to_string(),
                description: "Change font settings".to_string(),
                category: ConfigCategory::Theme,
                required_args: vec!["property".to_string(), "value".to_string()],
                optional_args: vec![],
                flags: vec!["preview".to_string()],
                supports_completion: true,
                priority: 6,
            },
            // Keybinding commands
            ConfigCommandInfo {
                name: "bind".to_string(),
                description: "Set key binding".to_string(),
                category: ConfigCategory::Keybindings,
                required_args: vec!["key".to_string(), "command".to_string()],
                optional_args: vec!["mode".to_string()],
                flags: vec!["global".to_string(), "override".to_string()],
                supports_completion: true,
                priority: 7,
            },
            ConfigCommandInfo {
                name: "unbind".to_string(),
                description: "Remove key binding".to_string(),
                category: ConfigCategory::Keybindings,
                required_args: vec!["key".to_string()],
                optional_args: vec!["mode".to_string()],
                flags: vec!["confirm".to_string()],
                supports_completion: true,
                priority: 6,
            },
            ConfigCommandInfo {
                name: "keymap".to_string(),
                description: "Load keymap preset".to_string(),
                category: ConfigCategory::Keybindings,
                required_args: vec!["preset".to_string()],
                optional_args: vec![],
                flags: vec!["merge".to_string(), "override".to_string()],
                supports_completion: true,
                priority: 5,
            },
            // Plugin commands
            ConfigCommandInfo {
                name: "plugin".to_string(),
                description: "Manage plugins".to_string(),
                category: ConfigCategory::Plugins,
                required_args: vec!["action".to_string()],
                optional_args: vec!["name".to_string()],
                flags: vec!["force".to_string(), "reload".to_string()],
                supports_completion: true,
                priority: 6,
            },
            ConfigCommandInfo {
                name: "extension".to_string(),
                description: "Manage extensions".to_string(),
                category: ConfigCategory::Plugins,
                required_args: vec!["action".to_string(), "name".to_string()],
                optional_args: vec![],
                flags: vec!["dev".to_string(), "beta".to_string()],
                supports_completion: true,
                priority: 5,
            },
            // Performance commands
            ConfigCommandInfo {
                name: "cache".to_string(),
                description: "Manage cache settings".to_string(),
                category: ConfigCategory::Performance,
                required_args: vec!["action".to_string()],
                optional_args: vec!["type".to_string()],
                flags: vec!["force".to_string(), "verbose".to_string()],
                supports_completion: true,
                priority: 4,
            },
            ConfigCommandInfo {
                name: "performance".to_string(),
                description: "Performance settings".to_string(),
                category: ConfigCategory::Performance,
                required_args: vec!["setting".to_string(), "value".to_string()],
                optional_args: vec![],
                flags: vec!["benchmark".to_string()],
                supports_completion: true,
                priority: 3,
            },
            // Security commands
            ConfigCommandInfo {
                name: "security".to_string(),
                description: "Security settings".to_string(),
                category: ConfigCategory::Security,
                required_args: vec!["setting".to_string()],
                optional_args: vec!["value".to_string()],
                flags: vec!["strict".to_string(), "audit".to_string()],
                supports_completion: true,
                priority: 5,
            },
            ConfigCommandInfo {
                name: "permissions".to_string(),
                description: "Manage permissions".to_string(),
                category: ConfigCategory::Security,
                required_args: vec!["scope".to_string(), "level".to_string()],
                optional_args: vec![],
                flags: vec!["grant".to_string(), "revoke".to_string()],
                supports_completion: true,
                priority: 4,
            },
            // Integration commands
            ConfigCommandInfo {
                name: "llm".to_string(),
                description: "LLM provider settings".to_string(),
                category: ConfigCategory::Integration,
                required_args: vec!["provider".to_string()],
                optional_args: vec!["model".to_string(), "key".to_string()],
                flags: vec!["test".to_string(), "default".to_string()],
                supports_completion: true,
                priority: 7,
            },
            ConfigCommandInfo {
                name: "mcp".to_string(),
                description: "MCP server settings".to_string(),
                category: ConfigCategory::Integration,
                required_args: vec!["action".to_string()],
                optional_args: vec!["server".to_string()],
                flags: vec!["auto".to_string(), "debug".to_string()],
                supports_completion: true,
                priority: 6,
            },
            ConfigCommandInfo {
                name: "git".to_string(),
                description: "Git integration settings".to_string(),
                category: ConfigCategory::Integration,
                required_args: vec!["setting".to_string()],
                optional_args: vec!["value".to_string()],
                flags: vec!["global".to_string(), "repo".to_string()],
                supports_completion: true,
                priority: 5,
            },
        ];

        for cmd in config_commands {
            commands.insert(cmd.name.clone(), cmd);
        }

        Self { commands }
    }

    /// Parse the config command
    fn parse_config_command(&self, raw: &str, cursor_position: usize) -> Result<ParsedCommand> {
        let mut command = ParsedCommand::new(CommandType::Config, raw.to_string(), cursor_position);

        // Remove : prefix and parse
        let content = if raw.starts_with(':') { &raw[1..] } else { raw };

        let args = utils::parse_args(content);
        let (remaining_args, options) = utils::parse_options(&args);

        if let Some(name) = utils::extract_command_name(&remaining_args) {
            command.set_name(name);

            // Add remaining arguments
            for arg in remaining_args.iter().skip(1) {
                command.add_arg(arg.clone());
            }

            // Add options
            for (key, value) in options {
                command.add_option(key, value);
            }

            // Check if command is complete
            if let Some(cmd_info) = self.commands.get(&command.name) {
                if command.args.len() >= cmd_info.required_args.len() {
                    command.mark_complete();
                }
            }
        }

        Ok(command)
    }
}

#[async_trait]
impl UnifiedCommandParser for ConfigCommandParser {
    async fn parse(&self, context: &ParsingContext) -> Result<ParseResult> {
        // Find : trigger in the input
        if let Some((_, trigger_pos)) = context.find_last_trigger(&[':']) {
            let command_text = &context.input[trigger_pos..];
            let relative_cursor = context.cursor_position.saturating_sub(trigger_pos);

            match self.parse_config_command(command_text, relative_cursor) {
                Ok(command) => {
                    if command.name.is_empty() {
                        Ok(ParseResult::Partial(command))
                    } else if command.is_complete {
                        Ok(ParseResult::Success(command))
                    } else {
                        Ok(ParseResult::Partial(command))
                    }
                }
                Err(e) => Ok(ParseResult::Invalid(e.to_string())),
            }
        } else {
            Ok(ParseResult::None)
        }
    }

    fn command_type(&self) -> CommandType {
        CommandType::Config
    }

    fn trigger_chars(&self) -> Vec<char> {
        vec![':']
    }

    async fn validate(&self, command: &ParsedCommand) -> Result<Vec<String>> {
        let mut errors = Vec::new();

        if command.command_type != CommandType::Config {
            errors.push("Invalid command type for config parser".to_string());
            return Ok(errors);
        }

        if let Some(cmd_info) = self.commands.get(&command.name) {
            // Check required arguments
            if command.args.len() < cmd_info.required_args.len() {
                errors.push(format!(
                    "Command '{}' requires {} arguments, got {}",
                    command.name,
                    cmd_info.required_args.len(),
                    command.args.len()
                ));
            }

            // Validate specific command requirements
            match command.name.as_str() {
                "set" | "get" | "unset" => {
                    if let Some(key) = command.get_arg(0) {
                        if key.is_empty() {
                            errors.push("Configuration key cannot be empty".to_string());
                        }
                    }
                }
                "mode" => {
                    if let Some(mode) = command.get_arg(0) {
                        let valid_modes = vec!["normal", "insert", "visual", "command"];
                        if !valid_modes.contains(&mode) {
                            errors.push(format!(
                                "Invalid mode: {}. Valid modes: {}",
                                mode,
                                valid_modes.join(", ")
                            ));
                        }
                    }
                }
                "theme" => {
                    if let Some(theme) = command.get_arg(0) {
                        if theme.is_empty() {
                            errors.push("Theme name cannot be empty".to_string());
                        }
                    }
                }
                "bind" | "unbind" => {
                    if let Some(key) = command.get_arg(0) {
                        if key.is_empty() {
                            errors.push("Key binding cannot be empty".to_string());
                        }
                    }
                }
                _ => {}
            }
        } else {
            errors.push(format!("Unknown config command: {}", command.name));
        }

        Ok(errors)
    }

    async fn get_completions(&self, command: &ParsedCommand) -> Result<Vec<CommandCompletion>> {
        let mut completions = Vec::new();

        // If no command name yet, suggest all commands
        if command.name.is_empty() {
            for cmd_info in self.commands.values() {
                let completion =
                    CommandCompletion::new(cmd_info.name.clone(), CompletionType::Command)
                        .with_description(cmd_info.description.clone())
                        .with_priority(cmd_info.priority);

                completions.push(completion);
            }
        } else {
            // Filter commands by partial name match
            let query = &command.name;
            for cmd_info in self.commands.values() {
                if cmd_info.name.starts_with(query) || utils::fuzzy_score(&cmd_info.name, query) > 0
                {
                    let completion =
                        CommandCompletion::new(cmd_info.name.clone(), CompletionType::Command)
                            .with_description(cmd_info.description.clone())
                            .with_priority(cmd_info.priority);

                    completions.push(completion);
                }
            }

            // Add argument completions for known commands
            if let Some(cmd_info) = self.commands.get(&command.name) {
                self.add_argument_completions(&mut completions, cmd_info, command)
                    .await?;
            }
        }

        // Sort by priority and fuzzy score
        completions.sort_by(|a, b| {
            b.priority.cmp(&a.priority).then_with(|| {
                let score_a = utils::fuzzy_score(&a.text, &command.name);
                let score_b = utils::fuzzy_score(&b.text, &command.name);
                score_b.cmp(&score_a)
            })
        });

        Ok(completions)
    }
}

impl ConfigCommandParser {
    /// Add argument-specific completions
    async fn add_argument_completions(
        &self,
        completions: &mut Vec<CommandCompletion>,
        cmd_info: &ConfigCommandInfo,
        command: &ParsedCommand,
    ) -> Result<()> {
        let arg_index = command.args.len();

        match cmd_info.name.as_str() {
            "mode" => {
                if arg_index == 0 {
                    let modes = vec![
                        ("normal", "Normal mode"),
                        ("insert", "Insert mode"),
                        ("visual", "Visual mode"),
                        ("command", "Command mode"),
                    ];

                    for (mode, desc) in modes {
                        completions.push(
                            CommandCompletion::new(
                                mode.to_string(),
                                CompletionType::Custom("mode".to_string()),
                            )
                            .with_description(desc.to_string())
                            .with_priority(8),
                        );
                    }
                }
            }
            "view" => {
                if arg_index == 0 {
                    let layouts = vec![
                        ("split", "Split view"),
                        ("tab", "Tabbed view"),
                        ("single", "Single pane"),
                        ("dual", "Dual pane"),
                    ];

                    for (layout, desc) in layouts {
                        completions.push(
                            CommandCompletion::new(
                                layout.to_string(),
                                CompletionType::Custom("layout".to_string()),
                            )
                            .with_description(desc.to_string())
                            .with_priority(7),
                        );
                    }
                }
            }
            "theme" => {
                if arg_index == 0 {
                    let themes = vec![
                        ("dark", "Dark theme"),
                        ("light", "Light theme"),
                        ("monokai", "Monokai theme"),
                        ("solarized", "Solarized theme"),
                        ("dracula", "Dracula theme"),
                    ];

                    for (theme, desc) in themes {
                        completions.push(
                            CommandCompletion::new(
                                theme.to_string(),
                                CompletionType::Custom("theme".to_string()),
                            )
                            .with_description(desc.to_string())
                            .with_priority(6),
                        );
                    }
                }
            }
            "set" | "get" | "unset" => {
                if arg_index == 0 {
                    let config_keys = vec![
                        ("editor.font_size", "Font size for editor"),
                        ("editor.tab_size", "Tab size in characters"),
                        ("editor.word_wrap", "Enable word wrap"),
                        ("ui.theme", "UI color theme"),
                        ("ui.animations", "Enable UI animations"),
                        ("completion.auto_trigger", "Auto-trigger completions"),
                        ("performance.cache_size", "Cache size in MB"),
                        ("security.allow_external", "Allow external commands"),
                    ];

                    for (key, desc) in config_keys {
                        completions.push(
                            CommandCompletion::new(
                                key.to_string(),
                                CompletionType::Custom("config_key".to_string()),
                            )
                            .with_description(desc.to_string())
                            .with_priority(5),
                        );
                    }
                }
            }
            "llm" => {
                if arg_index == 0 {
                    let providers = vec![
                        ("anthropic", "Anthropic Claude"),
                        ("openrouter", "OpenRouter API"),
                        ("openai", "OpenAI GPT"),
                        ("local", "Local LLM"),
                    ];

                    for (provider, desc) in providers {
                        completions.push(
                            CommandCompletion::new(
                                provider.to_string(),
                                CompletionType::Custom("llm_provider".to_string()),
                            )
                            .with_description(desc.to_string())
                            .with_priority(6),
                        );
                    }
                }
            }
            "plugin" | "extension" => {
                if arg_index == 0 {
                    let actions = vec![
                        ("enable", "Enable plugin/extension"),
                        ("disable", "Disable plugin/extension"),
                        ("install", "Install plugin/extension"),
                        ("uninstall", "Uninstall plugin/extension"),
                        ("update", "Update plugin/extension"),
                        ("list", "List plugins/extensions"),
                    ];

                    for (action, desc) in actions {
                        completions.push(
                            CommandCompletion::new(
                                action.to_string(),
                                CompletionType::Custom("plugin_action".to_string()),
                            )
                            .with_description(desc.to_string())
                            .with_priority(5),
                        );
                    }
                }
            }
            _ => {}
        }

        Ok(())
    }
}

impl Default for ConfigCommandParser {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_parse_config_command() {
        let parser = ConfigCommandParser::new();
        let context = ParsingContext::new(":set editor.font_size 14".to_string(), 22);

        let result = parser.parse(&context).await.unwrap();
        if let ParseResult::Success(command) = result {
            assert_eq!(command.command_type, CommandType::Config);
            assert_eq!(command.name, "set");
            assert_eq!(command.args.len(), 2);
            assert_eq!(command.args[0], "editor.font_size");
            assert_eq!(command.args[1], "14");
        } else {
            panic!("Expected successful parse");
        }
    }

    #[tokio::test]
    async fn test_partial_config_command() {
        let parser = ConfigCommandParser::new();
        let context = ParsingContext::new(":se".to_string(), 3);

        let result = parser.parse(&context).await.unwrap();
        if let ParseResult::Partial(command) = result {
            assert_eq!(command.command_type, CommandType::Config);
            assert_eq!(command.name, "se");
            assert!(!command.is_complete);
        } else {
            panic!("Expected partial parse");
        }
    }

    #[tokio::test]
    async fn test_config_completions() {
        let parser = ConfigCommandParser::new();
        let mut command = ParsedCommand::new(CommandType::Config, ":mo".to_string(), 3);
        command.set_name("mo".to_string());

        let completions = parser.get_completions(&command).await.unwrap();
        assert!(!completions.is_empty());

        // Should include "mode" as a completion
        let mode_completion = completions.iter().find(|c| c.text == "mode");
        assert!(mode_completion.is_some());
    }

    #[tokio::test]
    async fn test_validate_config_command() {
        let parser = ConfigCommandParser::new();
        let mut command = ParsedCommand::new(CommandType::Config, ":set".to_string(), 4);
        command.set_name("set".to_string());

        // Missing required arguments
        let errors = parser.validate(&command).await.unwrap();
        assert!(!errors.is_empty());

        // With required arguments
        command.add_arg("key".to_string());
        command.add_arg("value".to_string());
        command.mark_complete();
        let errors = parser.validate(&command).await.unwrap();
        assert!(errors.is_empty());
    }
}
