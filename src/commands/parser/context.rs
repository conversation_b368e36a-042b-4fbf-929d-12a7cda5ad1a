//! Context command parser for @ commands
//!
//! Handles parsing of context and reference commands like:
//! - @file, @folder, @codebase
//! - @mcp, @symbol, @web
//! - @session, @workspace, @git, @memory

use super::{
    utils, CommandCompletion, CommandType, CompletionType, ParseResult, Parsed<PERSON>ommand,
    ParsingContext, UnifiedCommandParser,
};
use crate::errors::Result;
use crate::tools::ExecutionContext;
use crate::ui::completion::context_engine::{ContextEngine, ContextEngineConfig};
use std::collections::HashMap;
use std::sync::Arc;

/// Parser for context commands (@)
pub struct ContextCommandParser {
    /// Available context commands
    commands: HashMap<String, ContextCommandInfo>,
    /// Context engine for completion and resolution
    context_engine: Option<Arc<ContextEngine>>,
}

/// Information about a context command
#[derive(Debug, <PERSON>lone)]
struct ContextCommandInfo {
    /// Command name
    name: String,
    /// Command description
    description: String,
    /// Required arguments
    required_args: Vec<String>,
    /// Optional arguments
    optional_args: Vec<String>,
    /// Whether this command supports auto-completion
    supports_completion: bool,
    /// Priority for completion ordering
    priority: u8,
}

impl ContextCommandParser {
    /// Create a new context command parser
    pub fn new() -> Self {
        let mut commands = HashMap::new();

        // Define all context commands
        let context_commands = vec![
            ContextCommandInfo {
                name: "file".to_string(),
                description: "Reference a specific file".to_string(),
                required_args: vec!["path".to_string()],
                optional_args: vec!["line".to_string(), "column".to_string()],
                supports_completion: true,
                priority: 10,
            },
            ContextCommandInfo {
                name: "folder".to_string(),
                description: "Reference a directory".to_string(),
                required_args: vec!["path".to_string()],
                optional_args: vec![],
                supports_completion: true,
                priority: 9,
            },
            ContextCommandInfo {
                name: "codebase".to_string(),
                description: "Reference the entire codebase".to_string(),
                required_args: vec![],
                optional_args: vec!["pattern".to_string()],
                supports_completion: false,
                priority: 8,
            },
            ContextCommandInfo {
                name: "mcp".to_string(),
                description: "Reference MCP server or resource".to_string(),
                required_args: vec!["server".to_string()],
                optional_args: vec!["resource".to_string()],
                supports_completion: true,
                priority: 7,
            },
            ContextCommandInfo {
                name: "symbol".to_string(),
                description: "Reference a code symbol".to_string(),
                required_args: vec!["name".to_string()],
                optional_args: vec!["file".to_string()],
                supports_completion: true,
                priority: 6,
            },
            ContextCommandInfo {
                name: "web".to_string(),
                description: "Reference web resource".to_string(),
                required_args: vec!["query".to_string()],
                optional_args: vec!["engine".to_string()],
                supports_completion: false,
                priority: 5,
            },
            ContextCommandInfo {
                name: "session".to_string(),
                description: "Reference a conversation session".to_string(),
                required_args: vec!["id".to_string()],
                optional_args: vec![],
                supports_completion: true,
                priority: 4,
            },
            ContextCommandInfo {
                name: "workspace".to_string(),
                description: "Reference workspace configuration".to_string(),
                required_args: vec!["name".to_string()],
                optional_args: vec![],
                supports_completion: true,
                priority: 3,
            },
            ContextCommandInfo {
                name: "git".to_string(),
                description: "Reference git object (branch, commit, etc.)".to_string(),
                required_args: vec!["ref".to_string()],
                optional_args: vec!["type".to_string()],
                supports_completion: true,
                priority: 2,
            },
            ContextCommandInfo {
                name: "memory".to_string(),
                description: "Reference stored memory".to_string(),
                required_args: vec!["id".to_string()],
                optional_args: vec!["tag".to_string()],
                supports_completion: true,
                priority: 1,
            },
        ];

        for cmd in context_commands {
            commands.insert(cmd.name.clone(), cmd);
        }

        Self {
            commands,
            context_engine: None,
        }
    }

    /// Create a new context command parser with context engine
    pub fn new_with_context_engine(context_engine: Arc<ContextEngine>) -> Self {
        let mut parser = Self::new();
        parser.context_engine = Some(context_engine);
        parser
    }

    /// Set the context engine for completion and resolution
    pub fn set_context_engine(&mut self, context_engine: Arc<ContextEngine>) {
        self.context_engine = Some(context_engine);
    }

    /// Parse the context command
    fn parse_context_command(&self, raw: &str, cursor_position: usize) -> Result<ParsedCommand> {
        let mut command =
            ParsedCommand::new(CommandType::Context, raw.to_string(), cursor_position);

        // Remove @ prefix and parse
        let content = if raw.starts_with('@') { &raw[1..] } else { raw };

        let args = utils::parse_args(content);
        let (remaining_args, options) = utils::parse_options(&args);

        if let Some(name) = utils::extract_command_name(&remaining_args) {
            command.set_name(name);

            // Add remaining arguments
            for arg in remaining_args.iter().skip(1) {
                command.add_arg(arg.clone());
            }

            // Add options
            for (key, value) in options {
                command.add_option(key, value);
            }

            // Check if command is complete
            if let Some(cmd_info) = self.commands.get(&command.name) {
                if command.args.len() >= cmd_info.required_args.len() {
                    command.mark_complete();
                }
            }
        }

        Ok(command)
    }
}

#[async_trait::async_trait]
impl UnifiedCommandParser for ContextCommandParser {
    async fn parse(&self, context: &ParsingContext) -> Result<ParseResult> {
        // Find @ trigger in the input
        if let Some((_, trigger_pos)) = context.find_last_trigger(&['@']) {
            let command_text = &context.input[trigger_pos..];
            let relative_cursor = context.cursor_position.saturating_sub(trigger_pos);

            match self.parse_context_command(command_text, relative_cursor) {
                Ok(command) => {
                    if command.name.is_empty() {
                        Ok(ParseResult::Partial(command))
                    } else if command.is_complete {
                        Ok(ParseResult::Success(command))
                    } else {
                        Ok(ParseResult::Partial(command))
                    }
                }
                Err(e) => Ok(ParseResult::Invalid(e.to_string())),
            }
        } else {
            Ok(ParseResult::None)
        }
    }

    fn command_type(&self) -> CommandType {
        CommandType::Context
    }

    fn trigger_chars(&self) -> Vec<char> {
        vec!['@']
    }

    async fn validate(&self, command: &ParsedCommand) -> Result<Vec<String>> {
        let mut errors = Vec::new();

        if command.command_type != CommandType::Context {
            errors.push("Invalid command type for context parser".to_string());
            return Ok(errors);
        }

        if let Some(cmd_info) = self.commands.get(&command.name) {
            // Check required arguments
            if command.args.len() < cmd_info.required_args.len() {
                errors.push(format!(
                    "Command '{}' requires {} arguments, got {}",
                    command.name,
                    cmd_info.required_args.len(),
                    command.args.len()
                ));
            }

            // Validate specific command requirements
            match command.name.as_str() {
                "file" => {
                    if let Some(path) = command.get_arg(0) {
                        if path.is_empty() {
                            errors.push("File path cannot be empty".to_string());
                        }
                    }
                }
                "folder" => {
                    if let Some(path) = command.get_arg(0) {
                        if path.is_empty() {
                            errors.push("Folder path cannot be empty".to_string());
                        }
                    }
                }
                "session" => {
                    if let Some(id) = command.get_arg(0) {
                        if id.is_empty() {
                            errors.push("Session ID cannot be empty".to_string());
                        }
                    }
                }
                "memory" => {
                    if let Some(id) = command.get_arg(0) {
                        if id.is_empty() {
                            errors.push("Memory ID cannot be empty".to_string());
                        }
                    }
                }
                _ => {}
            }
        } else {
            errors.push(format!("Unknown context command: {}", command.name));
        }

        Ok(errors)
    }

    async fn get_completions(&self, command: &ParsedCommand) -> Result<Vec<CommandCompletion>> {
        let mut completions = Vec::new();

        // If no command name yet, suggest all commands
        if command.name.is_empty() {
            for cmd_info in self.commands.values() {
                let completion =
                    CommandCompletion::new(cmd_info.name.clone(), CompletionType::Command)
                        .with_description(cmd_info.description.clone())
                        .with_priority(cmd_info.priority);

                completions.push(completion);
            }
        } else {
            // Filter commands by partial name match
            let query = &command.name;
            for cmd_info in self.commands.values() {
                if cmd_info.name.starts_with(query) || utils::fuzzy_score(&cmd_info.name, query) > 0
                {
                    let completion =
                        CommandCompletion::new(cmd_info.name.clone(), CompletionType::Command)
                            .with_description(cmd_info.description.clone())
                            .with_priority(cmd_info.priority);

                    completions.push(completion);
                }
            }

            // Add argument completions for known commands
            if let Some(cmd_info) = self.commands.get(&command.name) {
                self.add_argument_completions(&mut completions, cmd_info, command)
                    .await?;
            }
        }

        // Sort by priority and fuzzy score
        completions.sort_by(|a, b| {
            b.priority.cmp(&a.priority).then_with(|| {
                let score_a = utils::fuzzy_score(&a.text, &command.name);
                let score_b = utils::fuzzy_score(&b.text, &command.name);
                score_b.cmp(&score_a)
            })
        });

        Ok(completions)
    }
}

impl ContextCommandParser {
    /// Add argument-specific completions
    async fn add_argument_completions(
        &self,
        completions: &mut Vec<CommandCompletion>,
        cmd_info: &ContextCommandInfo,
        command: &ParsedCommand,
    ) -> Result<()> {
        let arg_index = command.args.len();

        // If we have a context engine, try to use it for enhanced completions
        if let Some(context_engine) = &self.context_engine {
            match self
                .get_context_engine_completions(context_engine, cmd_info, command, arg_index)
                .await
            {
                Ok(mut engine_completions) => {
                    // Convert context completions to command completions
                    for completion in engine_completions.drain(..) {
                        completions.push(
                            CommandCompletion::new(completion.name, CompletionType::Argument)
                                .with_description(completion.description)
                                .with_priority(5),
                        );
                    }
                    return Ok(());
                }
                Err(_) => {
                    // Fall back to placeholders if context engine fails
                }
            }
        }

        // Fallback to placeholder completions
        match cmd_info.name.as_str() {
            "file" => {
                if arg_index == 0 {
                    completions.push(
                        CommandCompletion::new("src/".to_string(), CompletionType::Directory)
                            .with_description("Source directory".to_string())
                            .with_priority(5),
                    );
                }
            }
            "folder" => {
                if arg_index == 0 {
                    completions.push(
                        CommandCompletion::new("src/".to_string(), CompletionType::Directory)
                            .with_description("Source directory".to_string())
                            .with_priority(5),
                    );
                }
            }
            "mcp" => {
                if arg_index == 0 {
                    completions.push(
                        CommandCompletion::new(
                            "playwright".to_string(),
                            CompletionType::Custom("mcp_server".to_string()),
                        )
                        .with_description("Playwright MCP server".to_string())
                        .with_priority(5),
                    );
                }
            }
            "session" => {
                if arg_index == 0 {
                    completions.push(
                        CommandCompletion::new(
                            "recent".to_string(),
                            CompletionType::Custom("session_id".to_string()),
                        )
                        .with_description("Most recent session".to_string())
                        .with_priority(5),
                    );
                }
            }
            "workspace" => {
                if arg_index == 0 {
                    completions.push(
                        CommandCompletion::new(
                            "default".to_string(),
                            CompletionType::Custom("workspace".to_string()),
                        )
                        .with_description("Default workspace".to_string())
                        .with_priority(5),
                    );
                }
            }
            "git" => {
                if arg_index == 0 {
                    let git_refs = vec![
                        ("main", "Main branch"),
                        ("HEAD", "Current commit"),
                        ("origin/main", "Remote main branch"),
                    ];

                    for (git_ref, desc) in git_refs {
                        completions.push(
                            CommandCompletion::new(
                                git_ref.to_string(),
                                CompletionType::Custom("git_ref".to_string()),
                            )
                            .with_description(desc.to_string())
                            .with_priority(5),
                        );
                    }
                }
            }
            "memory" => {
                if arg_index == 0 {
                    completions.push(
                        CommandCompletion::new(
                            "latest".to_string(),
                            CompletionType::Custom("memory_id".to_string()),
                        )
                        .with_description("Latest memory entry".to_string())
                        .with_priority(5),
                    );
                }
            }
            _ => {}
        }

        Ok(())
    }

    /// Get completions from context engine
    async fn get_context_engine_completions(
        &self,
        context_engine: &ContextEngine,
        cmd_info: &ContextCommandInfo,
        command: &ParsedCommand,
        arg_index: usize,
    ) -> Result<Vec<crate::ui::completion::mention_completion::MentionSuggestion>> {
        use crate::tools::{ExecutionContext, PermissionLevel};

        // Create a basic execution context for completion requests
        let execution_context = ExecutionContext {
            working_directory: std::env::current_dir().unwrap_or_default(),
            permissions: PermissionLevel::ReadOnly, // Safe for completions
            session_data: HashMap::new(),
            environment: HashMap::new(),
            tool_registry: None,
        };

        if arg_index == 0 {
            // Get the partial reference from the command args or empty string
            let partial_reference = command
                .args
                .get(arg_index)
                .map(|s| s.as_str())
                .unwrap_or("");

            // Build the partial @ command for the context engine
            let partial_input = format!("@{} {}", cmd_info.name, partial_reference);

            context_engine
                .get_completions(&partial_input, &execution_context, Some(10))
                .await
        } else {
            // For now, only handle the first argument
            Ok(Vec::new())
        }
    }
}

impl Default for ContextCommandParser {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_parse_context_command() {
        let parser = ContextCommandParser::new();
        let context = ParsingContext::new("@file src/main.rs".to_string(), 16);

        let result = parser.parse(&context).await.unwrap();
        if let ParseResult::Success(command) = result {
            assert_eq!(command.command_type, CommandType::Context);
            assert_eq!(command.name, "file");
            assert_eq!(command.args.len(), 1);
            assert_eq!(command.args[0], "src/main.rs");
        } else {
            panic!("Expected successful parse");
        }
    }

    #[tokio::test]
    async fn test_partial_context_command() {
        let parser = ContextCommandParser::new();
        let context = ParsingContext::new("@fil".to_string(), 4);

        let result = parser.parse(&context).await.unwrap();
        if let ParseResult::Partial(command) = result {
            assert_eq!(command.command_type, CommandType::Context);
            assert_eq!(command.name, "fil");
            assert!(!command.is_complete);
        } else {
            panic!("Expected partial parse");
        }
    }

    #[tokio::test]
    async fn test_context_completions() {
        let parser = ContextCommandParser::new();
        let mut command = ParsedCommand::new(CommandType::Context, "@fi".to_string(), 3);
        command.set_name("fi".to_string());

        let completions = parser.get_completions(&command).await.unwrap();
        assert!(!completions.is_empty());

        // Should include "file" as a completion
        let file_completion = completions.iter().find(|c| c.text == "file");
        assert!(file_completion.is_some());
    }

    #[tokio::test]
    async fn test_validate_context_command() {
        let parser = ContextCommandParser::new();
        let mut command = ParsedCommand::new(CommandType::Context, "@file".to_string(), 5);
        command.set_name("file".to_string());

        // Missing required argument
        let errors = parser.validate(&command).await.unwrap();
        assert!(!errors.is_empty());

        // With required argument
        command.add_arg("src/main.rs".to_string());
        command.mark_complete();
        let errors = parser.validate(&command).await.unwrap();
        assert!(errors.is_empty());
    }
}
