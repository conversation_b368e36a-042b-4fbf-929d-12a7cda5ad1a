//! Command Loader for Phase 3 Action Commands
//!
//! This module provides discovery and loading of custom commands from filesystem,
//! with hot reloading capabilities using file system watching.

use crate::errors::{AutorunError, Result};
use crate::templates::{Template, TemplateEngine, TemplateMetadata};
use notify::{Event, EventKind, RecommendedWatcher, RecursiveMode, Watcher};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use tokio::sync::{mpsc, RwLock};
use tokio::task::Join<PERSON>andle;
use tracing::{debug, error, info, warn};
use walkdir::WalkDir;

/// Configuration for the CommandLoader
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommandLoaderConfig {
    /// Directories to watch for command files
    pub watch_directories: Vec<PathBuf>,
    /// File extensions to monitor (e.g., .md, .yml, .yaml)
    pub monitored_extensions: Vec<String>,
    /// Enable hot reloading with file watching
    pub hot_reload_enabled: bool,
    /// Debounce duration for file system events (ms)
    pub debounce_ms: u64,
    /// Maximum number of commands to load
    pub max_commands: usize,
    /// Enable recursive directory scanning
    pub recursive_scan: bool,
    /// Validation level for loaded commands
    pub validation_level: ValidationLevel,
}

impl Default for CommandLoaderConfig {
    fn default() -> Self {
        Self {
            watch_directories: vec![
                PathBuf::from(".autorun/commands"),
                PathBuf::from(".claude/commands"),
            ],
            monitored_extensions: vec![
                "md".to_string(),
                "yml".to_string(),
                "yaml".to_string(),
            ],
            hot_reload_enabled: true,
            debounce_ms: 500,
            max_commands: 1000,
            recursive_scan: true,
            validation_level: ValidationLevel::Strict,
        }
    }
}

/// Validation level for command loading
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum ValidationLevel {
    /// Minimal validation - only check file format
    Minimal,
    /// Standard validation - check metadata and basic structure
    Standard,
    /// Strict validation - full validation of all components
    Strict,
}

/// Command file metadata and content
#[derive(Debug, Clone)]
pub struct CommandFile {
    /// File path
    pub path: PathBuf,
    /// Command metadata from YAML frontmatter
    pub metadata: CommandMetadata,
    /// Template content
    pub template: Template,
    /// File hash for change detection
    pub hash: String,
    /// Last modified time
    pub modified: SystemTime,
    /// Whether the command passed validation
    pub is_valid: bool,
    /// Validation errors if any
    pub validation_errors: Vec<String>,
}

/// Command metadata extracted from YAML frontmatter
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommandMetadata {
    /// Command name/identifier
    pub name: String,
    /// Command description
    pub description: Option<String>,
    /// Command type (action, context, config)
    pub command_type: String,
    /// Command prefix (@, /, :)
    pub prefix: String,
    /// Command parameters
    pub parameters: Vec<CommandParameter>,
    /// Command aliases
    pub aliases: Vec<String>,
    /// Required permissions
    pub permissions: Vec<String>,
    /// Command category/tags
    pub tags: Vec<String>,
    /// Command version
    pub version: Option<String>,
    /// Command author
    pub author: Option<String>,
    /// Whether command requires confirmation
    pub requires_confirmation: bool,
    /// Command priority for ordering
    pub priority: i32,
    /// Whether command is enabled
    pub enabled: bool,
}

/// Command parameter definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommandParameter {
    /// Parameter name
    pub name: String,
    /// Parameter description
    pub description: Option<String>,
    /// Parameter type
    pub param_type: String,
    /// Whether parameter is required
    pub required: bool,
    /// Default value
    pub default: Option<String>,
    /// Validation pattern (regex)
    pub validation: Option<String>,
    /// Possible values for enum-like parameters
    pub options: Vec<String>,
}

impl Default for CommandMetadata {
    fn default() -> Self {
        Self {
            name: String::new(),
            description: None,
            command_type: "action".to_string(),
            prefix: "@".to_string(),
            parameters: Vec::new(),
            aliases: Vec::new(),
            permissions: Vec::new(),
            tags: Vec::new(),
            version: None,
            author: None,
            requires_confirmation: false,
            priority: 0,
            enabled: true,
        }
    }
}

/// File system event for command updates
#[derive(Debug, Clone)]
pub enum CommandEvent {
    /// Command file was created
    Created(PathBuf),
    /// Command file was modified
    Modified(PathBuf),
    /// Command file was deleted
    Deleted(PathBuf),
    /// Directory was created
    DirectoryCreated(PathBuf),
    /// Directory was deleted
    DirectoryDeleted(PathBuf),
    /// Validation error occurred
    ValidationError(PathBuf, String),
}

/// Main CommandLoader for discovering and managing custom commands
pub struct CommandLoader {
    /// Configuration
    config: CommandLoaderConfig,
    /// Template engine for processing commands
    template_engine: Arc<TemplateEngine>,
    /// Loaded commands registry
    commands: Arc<RwLock<HashMap<String, CommandFile>>>,
    /// File watcher handle
    watcher_handle: Option<JoinHandle<()>>,
    /// Event sender for notifying about changes
    event_sender: Option<mpsc::UnboundedSender<CommandEvent>>,
    /// Loading statistics
    stats: Arc<RwLock<LoaderStats>>,
}

/// Command loader statistics
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct LoaderStats {
    /// Total commands loaded
    pub commands_loaded: u64,
    /// Commands with validation errors
    pub validation_errors: u64,
    /// File system events processed
    pub events_processed: u64,
    /// Last scan duration
    pub last_scan_duration: Duration,
    /// Total directories scanned
    pub directories_scanned: u64,
    /// Files processed
    pub files_processed: u64,
    /// Cache hits
    pub cache_hits: u64,
}

impl CommandLoader {
    /// Create a new CommandLoader
    pub fn new(config: CommandLoaderConfig, template_engine: Arc<TemplateEngine>) -> Self {
        Self {
            config,
            template_engine,
            commands: Arc::new(RwLock::new(HashMap::new())),
            watcher_handle: None,
            event_sender: None,
            stats: Arc::new(RwLock::new(LoaderStats::default())),
        }
    }

    /// Initialize the command loader and start watching
    pub async fn initialize(&mut self) -> Result<()> {
        info!("Initializing CommandLoader with {} directories", self.config.watch_directories.len());
        
        // Initial scan of all directories
        self.scan_all_directories().await?;
        
        // Start file watching if enabled
        if self.config.hot_reload_enabled {
            self.start_file_watching().await?;
        }

        info!("CommandLoader initialized successfully");
        Ok(())
    }

    /// Scan all configured directories for command files
    pub async fn scan_all_directories(&mut self) -> Result<()> {
        let start_time = std::time::Instant::now();
        let mut stats = self.stats.write().await;
        
        stats.directories_scanned = 0;
        stats.files_processed = 0;
        
        for directory in &self.config.watch_directories {
            if directory.exists() {
                info!("Scanning directory: {}", directory.display());
                self.scan_directory(directory).await?;
                stats.directories_scanned += 1;
            } else {
                warn!("Directory does not exist: {}", directory.display());
            }
        }
        
        stats.last_scan_duration = start_time.elapsed();
        info!("Directory scan completed in {:?}", stats.last_scan_duration);
        Ok(())
    }

    /// Scan a single directory for command files
    async fn scan_directory(&mut self, directory: &Path) -> Result<()> {
        let walker = if self.config.recursive_scan {
            WalkDir::new(directory)
        } else {
            WalkDir::new(directory).max_depth(1)
        };

        for entry in walker {
            let entry = entry.map_err(|e| AutorunError::Io(e.into()))?;
            let path = entry.path();
            
            if self.is_command_file(path) {
                match self.load_command_file(path).await {
                    Ok(command_file) => {
                        self.register_command(command_file).await?;
                    }
                    Err(e) => {
                        error!("Failed to load command file {}: {}", path.display(), e);
                        self.stats.write().await.validation_errors += 1;
                    }
                }
            }
        }

        Ok(())
    }

    /// Check if a path represents a command file
    fn is_command_file(&self, path: &Path) -> bool {
        if !path.is_file() {
            return false;
        }

        if let Some(extension) = path.extension().and_then(|ext| ext.to_str()) {
            self.config.monitored_extensions.contains(&extension.to_string())
        } else {
            false
        }
    }

    /// Load a command file and extract metadata
    async fn load_command_file(&self, path: &Path) -> Result<CommandFile> {
        debug!("Loading command file: {}", path.display());
        
        let content = tokio::fs::read_to_string(path).await
            .map_err(|e| AutorunError::Io(e))?;
        
        let metadata = self.extract_metadata(&content).await?;
        let hash = self.calculate_file_hash(&content);
        let modified = path.metadata()
            .map_err(|e| AutorunError::Io(e))?
            .modified()
            .map_err(|e| AutorunError::Io(e))?;

        // Create template from content
        let template_metadata = self.create_template_metadata(&metadata);
        let template = self.create_template_from_content(&content, &template_metadata, path)?;
        
        let mut command_file = CommandFile {
            path: path.to_path_buf(),
            metadata,
            template,
            hash,
            modified,
            is_valid: false,
            validation_errors: Vec::new(),
        };

        // Validate the command
        self.validate_command(&mut command_file).await?;
        
        self.stats.write().await.files_processed += 1;
        Ok(command_file)
    }

    /// Extract metadata from file content (YAML frontmatter)
    async fn extract_metadata(&self, content: &str) -> Result<CommandMetadata> {
        // Look for YAML frontmatter between --- markers
        let parts: Vec<&str> = content.splitn(3, "---").collect();
        
        if parts.len() < 3 {
            return Err(AutorunError::ValidationError(
                "No YAML frontmatter found in command file".to_string()
            ));
        }

        let yaml_content = parts[1].trim();
        let metadata: CommandMetadata = serde_yaml::from_str(yaml_content)
            .map_err(|e| AutorunError::ValidationError(format!("Invalid YAML frontmatter: {}", e)))?;

        Ok(metadata)
    }

    /// Create template metadata from command metadata
    fn create_template_metadata(&self, cmd_metadata: &CommandMetadata) -> TemplateMetadata {
        let mut variables = HashMap::new();
        for param in &cmd_metadata.parameters {
            variables.insert(
                param.name.clone(),
                param.description.clone().unwrap_or_else(|| format!("Parameter: {}", param.name))
            );
        }

        TemplateMetadata {
            name: cmd_metadata.name.clone(),
            description: cmd_metadata.description.clone(),
            variables,
            version: cmd_metadata.version.clone(),
            author: cmd_metadata.author.clone(),
            tags: cmd_metadata.tags.clone(),
            requires_confirmation: cmd_metadata.requires_confirmation,
            includes: Vec::new(),
        }
    }

    /// Create template from file content
    fn create_template_from_content(
        &self,
        content: &str,
        template_metadata: &TemplateMetadata,
        path: &Path,
    ) -> Result<Template> {
        let hash = self.calculate_file_hash(content);
        let modified = path.metadata()
            .map_err(|e| AutorunError::Io(e))?
            .modified()
            .map_err(|e| AutorunError::Io(e))?;

        Ok(Template {
            metadata: template_metadata.clone(),
            content: content.to_string(),
            source_path: Some(path.to_path_buf()),
            hash,
            modified,
        })
    }

    /// Validate a command file
    async fn validate_command(&self, command_file: &mut CommandFile) -> Result<()> {
        let mut errors = Vec::new();
        
        match self.config.validation_level {
            ValidationLevel::Minimal => {
                self.validate_minimal(command_file, &mut errors);
            }
            ValidationLevel::Standard => {
                self.validate_minimal(command_file, &mut errors);
                self.validate_standard(command_file, &mut errors);
            }
            ValidationLevel::Strict => {
                self.validate_minimal(command_file, &mut errors);
                self.validate_standard(command_file, &mut errors);
                self.validate_strict(command_file, &mut errors).await;
            }
        }

        command_file.validation_errors = errors;
        command_file.is_valid = command_file.validation_errors.is_empty();
        
        if !command_file.is_valid {
            warn!("Command file validation failed: {}", command_file.path.display());
            for error in &command_file.validation_errors {
                warn!("  - {}", error);
            }
        }

        Ok(())
    }

    /// Minimal validation - check basic structure
    fn validate_minimal(&self, command_file: &CommandFile, errors: &mut Vec<String>) {
        if command_file.metadata.name.is_empty() {
            errors.push("Command name is required".to_string());
        }
        
        if !["@", "/", ":"].contains(&command_file.metadata.prefix.as_str()) {
            errors.push("Command prefix must be @, /, or :".to_string());
        }
        
        if !["action", "context", "config"].contains(&command_file.metadata.command_type.as_str()) {
            errors.push("Command type must be action, context, or config".to_string());
        }
    }

    /// Standard validation - check metadata consistency
    fn validate_standard(&self, command_file: &CommandFile, errors: &mut Vec<String>) {
        // Validate parameters
        for param in &command_file.metadata.parameters {
            if param.name.is_empty() {
                errors.push("Parameter name cannot be empty".to_string());
            }
            
            if param.required && param.default.is_some() {
                errors.push(format!("Parameter '{}' cannot be both required and have a default value", param.name));
            }
        }
        
        // Check for duplicate parameter names
        let mut param_names = std::collections::HashSet::new();
        for param in &command_file.metadata.parameters {
            if !param_names.insert(&param.name) {
                errors.push(format!("Duplicate parameter name: {}", param.name));
            }
        }
        
        // Validate aliases
        for alias in &command_file.metadata.aliases {
            if alias.is_empty() {
                errors.push("Command alias cannot be empty".to_string());
            }
        }
    }

    /// Strict validation - comprehensive checks including template validation
    async fn validate_strict(&self, command_file: &CommandFile, errors: &mut Vec<String>) {
        // Validate template content with template engine
        match self.template_engine.validate_template(&command_file.template).await {
            Ok(_) => {},
            Err(e) => {
                errors.push(format!("Template validation failed: {}", e));
            }
        }
        
        // Check for required template variables
        for param in &command_file.metadata.parameters {
            if param.required {
                let var_name = format!("{{{{{}}}}}", param.name);
                if !command_file.template.content.contains(&var_name) {
                    errors.push(format!("Required parameter '{}' not found in template", param.name));
                }
            }
        }
    }

    /// Register a loaded command
    async fn register_command(&mut self, command_file: CommandFile) -> Result<()> {
        if !command_file.is_valid {
            warn!("Skipping invalid command: {}", command_file.metadata.name);
            return Ok(());
        }

        let name = command_file.metadata.name.clone();
        debug!("Registering command: {}", name);
        
        self.commands.write().await.insert(name.clone(), command_file);
        self.stats.write().await.commands_loaded += 1;
        
        info!("Command registered successfully: {}", name);
        Ok(())
    }

    /// Start file system watching for hot reloading
    async fn start_file_watching(&mut self) -> Result<()> {
        let (tx, mut rx) = mpsc::unbounded_channel();
        let (event_tx, event_rx) = mpsc::unbounded_channel();
        
        self.event_sender = Some(event_tx.clone());
        
        let directories = self.config.watch_directories.clone();
        let debounce_duration = Duration::from_millis(self.config.debounce_ms);
        
        // Start the file watcher in a separate task
        let watcher_handle = tokio::spawn(async move {
            let mut watcher: RecommendedWatcher = Watcher::new(
                move |res: notify::Result<Event>| {
                    if let Ok(event) = res {
                        let _ = tx.send(event);
                    }
                },
                notify::Config::default(),
            ).expect("Failed to create file watcher");

            // Watch all configured directories
            for directory in &directories {
                if directory.exists() {
                    if let Err(e) = watcher.watch(directory, RecursiveMode::Recursive) {
                        error!("Failed to watch directory {}: {}", directory.display(), e);
                    } else {
                        info!("Watching directory: {}", directory.display());
                    }
                }
            }

            // Process file system events with debouncing
            let mut debounce_map: HashMap<PathBuf, tokio::time::Instant> = HashMap::new();
            
            while let Some(event) = rx.recv().await {
                match event.kind {
                    EventKind::Create(_) => {
                        for path in event.paths {
                            let now = tokio::time::Instant::now();
                            if let Some(&last_event) = debounce_map.get(&path) {
                                if now.duration_since(last_event) < debounce_duration {
                                    continue;
                                }
                            }
                            debounce_map.insert(path.clone(), now);
                            
                            let _ = event_tx.send(CommandEvent::Created(path));
                        }
                    }
                    EventKind::Modify(_) => {
                        for path in event.paths {
                            let now = tokio::time::Instant::now();
                            if let Some(&last_event) = debounce_map.get(&path) {
                                if now.duration_since(last_event) < debounce_duration {
                                    continue;
                                }
                            }
                            debounce_map.insert(path.clone(), now);
                            
                            let _ = event_tx.send(CommandEvent::Modified(path));
                        }
                    }
                    EventKind::Remove(_) => {
                        for path in event.paths {
                            let _ = event_tx.send(CommandEvent::Deleted(path));
                        }
                    }
                    _ => {}
                }
            }
        });

        self.watcher_handle = Some(watcher_handle);
        
        // Start event processing
        let commands = Arc::clone(&self.commands);
        let stats = Arc::clone(&self.stats);
        let template_engine = Arc::clone(&self.template_engine);
        let config = self.config.clone();
        
        tokio::spawn(async move {
            Self::process_file_events(event_rx, commands, stats, template_engine, config).await;
        });

        info!("File watching started successfully");
        Ok(())
    }

    /// Process file system events
    async fn process_file_events(
        mut event_rx: mpsc::UnboundedReceiver<CommandEvent>,
        commands: Arc<RwLock<HashMap<String, CommandFile>>>,
        stats: Arc<RwLock<LoaderStats>>,
        template_engine: Arc<TemplateEngine>,
        config: CommandLoaderConfig,
    ) {
        while let Some(event) = event_rx.recv().await {
            stats.write().await.events_processed += 1;
            
            match event {
                CommandEvent::Created(path) | CommandEvent::Modified(path) => {
                    if Self::is_command_file_static(&path, &config.monitored_extensions) {
                        info!("Reloading command file: {}", path.display());
                        
                        // Create a temporary loader for processing
                        let loader = CommandLoader::new(config.clone(), template_engine.clone());
                        
                        match loader.load_command_file(&path).await {
                            Ok(command_file) => {
                                let name = command_file.metadata.name.clone();
                                commands.write().await.insert(name.clone(), command_file);
                                info!("Command reloaded successfully: {}", name);
                            }
                            Err(e) => {
                                error!("Failed to reload command file {}: {}", path.display(), e);
                            }
                        }
                    }
                }
                CommandEvent::Deleted(path) => {
                    // Remove the command from registry
                    let mut commands_guard = commands.write().await;
                    commands_guard.retain(|_, cmd| cmd.path != path);
                    info!("Command removed due to file deletion: {}", path.display());
                }
                CommandEvent::DirectoryCreated(path) => {
                    info!("New directory created: {}", path.display());
                    // Could trigger a rescan of the new directory
                }
                CommandEvent::DirectoryDeleted(path) => {
                    info!("Directory deleted: {}", path.display());
                    // Remove all commands from this directory
                    let mut commands_guard = commands.write().await;
                    commands_guard.retain(|_, cmd| !cmd.path.starts_with(&path));
                }
                CommandEvent::ValidationError(path, error) => {
                    error!("Validation error for {}: {}", path.display(), error);
                }
            }
        }
    }

    /// Static helper to check if a file is a command file
    fn is_command_file_static(path: &Path, monitored_extensions: &[String]) -> bool {
        if !path.is_file() {
            return false;
        }

        if let Some(extension) = path.extension().and_then(|ext| ext.to_str()) {
            monitored_extensions.contains(&extension.to_string())
        } else {
            false
        }
    }

    /// Calculate hash of file content for change detection
    fn calculate_file_hash(&self, content: &str) -> String {
        use sha2::{Digest, Sha256};
        let mut hasher = Sha256::new();
        hasher.update(content.as_bytes());
        format!("{:x}", hasher.finalize())
    }

    /// Get all loaded commands
    pub async fn get_commands(&self) -> HashMap<String, CommandFile> {
        self.commands.read().await.clone()
    }

    /// Get a specific command by name
    pub async fn get_command(&self, name: &str) -> Option<CommandFile> {
        self.commands.read().await.get(name).cloned()
    }

    /// Get commands by type
    pub async fn get_commands_by_type(&self, command_type: &str) -> Vec<CommandFile> {
        self.commands
            .read()
            .await
            .values()
            .filter(|cmd| cmd.metadata.command_type == command_type)
            .cloned()
            .collect()
    }

    /// Get commands by prefix
    pub async fn get_commands_by_prefix(&self, prefix: &str) -> Vec<CommandFile> {
        self.commands
            .read()
            .await
            .values()
            .filter(|cmd| cmd.metadata.prefix == prefix)
            .cloned()
            .collect()
    }

    /// Get loader statistics
    pub async fn get_stats(&self) -> LoaderStats {
        self.stats.read().await.clone()
    }

    /// Reload all commands
    pub async fn reload_all(&mut self) -> Result<()> {
        info!("Reloading all commands");
        self.commands.write().await.clear();
        self.scan_all_directories().await?;
        info!("All commands reloaded successfully");
        Ok(())
    }

    /// Stop file watching and cleanup
    pub async fn shutdown(&mut self) -> Result<()> {
        info!("Shutting down CommandLoader");
        
        if let Some(handle) = self.watcher_handle.take() {
            handle.abort();
        }
        
        self.event_sender = None;
        info!("CommandLoader shutdown complete");
        Ok(())
    }
}

impl Drop for CommandLoader {
    fn drop(&mut self) {
        if let Some(handle) = self.watcher_handle.take() {
            handle.abort();
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use tokio::fs;

    #[tokio::test]
    async fn test_command_loader_initialization() {
        let temp_dir = TempDir::new().unwrap();
        let mut config = CommandLoaderConfig::default();
        config.watch_directories = vec![temp_dir.path().to_path_buf()];
        config.hot_reload_enabled = false; // Disable for test
        
        let template_engine = Arc::new(TemplateEngine::new()?);
        let mut loader = CommandLoader::new(config, template_engine);
        
        assert!(loader.initialize().await.is_ok());
    }

    #[tokio::test]
    async fn test_load_valid_command_file() {
        let temp_dir = TempDir::new().unwrap();
        let command_file_path = temp_dir.path().join("test_command.md");
        
        let command_content = r#"---
name: test_command
description: A test command
command_type: action
prefix: "@"
parameters:
  - name: input
    description: Input parameter
    param_type: string
    required: true
aliases: []
permissions: []
tags: [test]
version: "1.0"
author: Test Author
requires_confirmation: false
priority: 0
enabled: true
---

# Test Command

This is a test command with parameter: {{input}}
"#;
        
        fs::write(&command_file_path, command_content).await.unwrap();
        
        let template_engine = Arc::new(TemplateEngine::new()?);
        let config = CommandLoaderConfig::default();
        let loader = CommandLoader::new(config, template_engine);
        
        let result = loader.load_command_file(&command_file_path).await;
        assert!(result.is_ok());
        
        let command_file = result.unwrap();
        assert_eq!(command_file.metadata.name, "test_command");
        assert_eq!(command_file.metadata.command_type, "action");
        assert_eq!(command_file.metadata.prefix, "@");
        assert!(command_file.is_valid);
    }

    #[tokio::test]
    async fn test_invalid_command_file() {
        let temp_dir = TempDir::new().unwrap();
        let command_file_path = temp_dir.path().join("invalid_command.md");
        
        let invalid_content = r#"---
name: ""
command_type: invalid_type
prefix: "!"
---

# Invalid Command
"#;
        
        fs::write(&command_file_path, invalid_content).await.unwrap();
        
        let template_engine = Arc::new(TemplateEngine::new()?);
        let config = CommandLoaderConfig::default();
        let loader = CommandLoader::new(config, template_engine);
        
        let result = loader.load_command_file(&command_file_path).await;
        assert!(result.is_ok());
        
        let command_file = result.unwrap();
        assert!(!command_file.is_valid);
        assert!(!command_file.validation_errors.is_empty());
    }
}