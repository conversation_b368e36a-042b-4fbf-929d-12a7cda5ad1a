//! Custom Commands Module
//!
//! This module provides infrastructure for loading and managing custom commands
//! from filesystem with hot reloading capabilities.

pub mod command_loader;

pub use command_loader::{
    CommandEvent, CommandFile, CommandLoader, CommandLoaderConfig, CommandMetadata,
    CommandParameter, LoaderStats, ValidationLevel,
};

use crate::errors::Result;
use crate::templates::TemplateEngine;
use std::sync::Arc;

/// Initialize the custom command system
pub async fn initialize_custom_commands(
    config: CommandLoaderConfig,
    template_engine: Arc<TemplateEngine>,
) -> Result<CommandLoader> {
    let mut loader = CommandLoader::new(config, template_engine);
    loader.initialize().await?;
    Ok(loader)
}