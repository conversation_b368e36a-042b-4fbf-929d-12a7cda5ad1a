//! Core template processing engine with Tera integration

use super::{
    ProcessedTemplate, Template, TemplateCache, TemplateError, TemplateMetadata, TemplateStats,
    TemplateValidator, VariableResolver,
};
use crate::errors::{AutorunError, Result};

use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant, SystemTime};
use tera::{Context, Tera, Value};
use tokio::fs;
use tokio::sync::RwLock as AsyncRwLock;
use tracing::{debug, info, warn};

/// Template processing options
#[derive(Debug, Clone)]
pub struct TemplateProcessingOptions {
    /// Whether to use cache
    pub use_cache: bool,
    /// Whether to validate template before processing
    pub validate: bool,
    /// Whether to prompt for missing variables
    pub interactive: bool,
    /// Whether to include system variables
    pub include_system_vars: bool,
    /// Whether to include environment variables
    pub include_env_vars: bool,
    /// Maximum processing time before timeout
    pub timeout: Option<Duration>,
    /// Whether to allow template includes
    pub allow_includes: bool,
}

impl Default for TemplateProcessingOptions {
    fn default() -> Self {
        Self {
            use_cache: true,
            validate: true,
            interactive: true,
            include_system_vars: true,
            include_env_vars: true,
            timeout: Some(Duration::from_secs(30)),
            allow_includes: true,
        }
    }
}

/// Template context with resolved variables
#[derive(Debug, Clone)]
pub struct TemplateContext {
    /// User-provided variables
    pub variables: HashMap<String, String>,
    /// System variables (username, date, etc.)
    pub system_vars: HashMap<String, String>,
    /// Environment variables
    pub env_vars: HashMap<String, String>,
    /// Template metadata variables
    pub metadata_vars: HashMap<String, String>,
}

impl TemplateContext {
    pub fn new() -> Self {
        Self {
            variables: HashMap::new(),
            system_vars: HashMap::new(),
            env_vars: HashMap::new(),
            metadata_vars: HashMap::new(),
        }
    }

    /// Get all variables combined
    pub fn all_variables(&self) -> HashMap<String, String> {
        let mut all = HashMap::new();
        all.extend(self.env_vars.clone());
        all.extend(self.system_vars.clone());
        all.extend(self.metadata_vars.clone());
        all.extend(self.variables.clone()); // User vars take precedence
        all
    }

    /// Convert to Tera Context
    pub fn to_tera_context(&self) -> Context {
        let mut context = Context::new();
        let all_vars = self.all_variables();

        for (key, value) in all_vars {
            context.insert(&key, &value);
        }

        context
    }
}

/// Main template engine
pub struct TemplateEngine {
    /// Tera template engine instance
    tera: Tera,
    /// Template cache for performance
    cache: Arc<AsyncRwLock<TemplateCache>>,
    /// Variable resolver
    variable_resolver: VariableResolver,
    /// Template validator
    validator: TemplateValidator,
    /// Processing statistics
    stats: Arc<RwLock<TemplateStats>>,
    /// Template directories to search
    template_dirs: Vec<PathBuf>,
}

impl TemplateEngine {
    /// Create new template engine
    pub fn new() -> Result<Self> {
        let mut tera = Tera::new("templates/**/*").map_err(|e| {
            AutorunError::ValidationError(format!("Failed to initialize Tera: {}", e))
        })?;

        // Add custom filters and functions
        Self::register_custom_filters(&mut tera)?;
        Self::register_custom_functions(&mut tera)?;

        Ok(Self {
            tera,
            cache: Arc::new(AsyncRwLock::new(TemplateCache::new())),
            variable_resolver: VariableResolver::new(),
            validator: TemplateValidator::new(),
            stats: Arc::new(RwLock::new(TemplateStats::default())),
            template_dirs: vec![PathBuf::from("templates")],
        })
    }

    /// Create template engine with custom configuration
    pub fn with_config(template_dirs: Vec<PathBuf>) -> Result<Self> {
        let mut engine = Self::new()?;
        engine.template_dirs = template_dirs;

        // Reinitialize Tera with new directories
        let mut patterns = Vec::new();
        for dir in &engine.template_dirs {
            patterns.push(format!("{}/**/*", dir.display()));
        }

        if !patterns.is_empty() {
            engine.tera = Tera::new(&patterns.join(";")).map_err(|e| {
                AutorunError::ValidationError(format!("Failed to initialize Tera with dirs: {}", e))
            })?;
            Self::register_custom_filters(&mut engine.tera)?;
            Self::register_custom_functions(&mut engine.tera)?;
        }

        Ok(engine)
    }

    /// Load template from file
    pub async fn load_template<P: AsRef<Path>>(&self, path: P) -> Result<Template> {
        let path = path.as_ref();
        debug!("Loading template from: {}", path.display());

        let content = fs::read_to_string(path)
            .await
            .map_err(|e| AutorunError::Io(e))?;

        let metadata = fs::metadata(path).await.map_err(|e| AutorunError::Io(e))?;

        let modified = metadata.modified().map_err(|e| AutorunError::Io(e))?;

        self.parse_template_content(content, Some(path.to_path_buf()), modified)
            .await
    }

    /// Parse template content with frontmatter
    pub async fn parse_template_content(
        &self,
        content: String,
        source_path: Option<PathBuf>,
        modified: SystemTime,
    ) -> Result<Template> {
        let (metadata, template_content) = self.parse_frontmatter(&content)?;
        let hash = self.calculate_hash(&content);

        Ok(Template {
            metadata,
            content: template_content,
            source_path,
            hash,
            modified,
        })
    }

    /// Process template with context
    pub async fn process_template(
        &mut self,
        template: &Template,
        mut context: TemplateContext,
        options: TemplateProcessingOptions,
    ) -> Result<ProcessedTemplate> {
        let start_time = Instant::now();

        // Validate template if requested
        if options.validate {
            let validation_result = self.validator.validate_template(template).await;
            if let Err(e) = validation_result {
                return Err(AutorunError::ValidationError(format!(
                    "Template validation failed: {}",
                    e
                )));
            }
        }

        // Check cache if enabled
        if options.use_cache {
            let mut cache = self.cache.write().await;
            if let Some(cached) = cache.get(&template.hash) {
                self.update_stats(|stats| stats.cache_hits += 1);
                debug!("Template cache hit for hash: {}", template.hash);
                return Ok(cached.clone());
            }
        }

        // Increment cache miss
        self.update_stats(|stats| stats.cache_misses += 1);

        // Resolve variables
        context = self
            .resolve_template_variables(template, context, &options)
            .await?;

        // Compile and render template
        let rendered_content = self.render_template(template, &context).await?;

        // Create result
        let processing_time = start_time.elapsed();
        let result = ProcessedTemplate {
            content: rendered_content,
            variables_used: context.all_variables(),
            metadata: template.metadata.clone(),
            processing_time,
        };

        // Cache result if enabled
        if options.use_cache {
            let mut cache = self.cache.write().await;
            cache.insert(template.hash.clone(), result.clone()).await;
        }

        // Update statistics
        self.update_stats(|stats| {
            stats.templates_processed += 1;
            stats.total_processing_time += processing_time;
            if stats.templates_processed > 0 {
                stats.average_processing_time =
                    stats.total_processing_time / stats.templates_processed as u32;
            }
        });

        info!("Template processed successfully in {:?}", processing_time);
        Ok(result)
    }

    /// Process template from file path
    pub async fn process_template_file<P: AsRef<Path>>(
        &mut self,
        path: P,
        context: TemplateContext,
        options: TemplateProcessingOptions,
    ) -> Result<ProcessedTemplate> {
        let template = self.load_template(path).await?;
        self.process_template(&template, context, options).await
    }

    /// Process template from string content
    pub async fn process_template_string(
        &mut self,
        content: String,
        context: TemplateContext,
        options: TemplateProcessingOptions,
    ) -> Result<ProcessedTemplate> {
        let template = self
            .parse_template_content(content, None, SystemTime::now())
            .await?;
        self.process_template(&template, context, options).await
    }

    /// Resolve variables for template
    async fn resolve_template_variables(
        &mut self,
        template: &Template,
        mut context: TemplateContext,
        options: &TemplateProcessingOptions,
    ) -> Result<TemplateContext> {
        // Add system variables if requested
        if options.include_system_vars {
            context.system_vars = self.variable_resolver.get_system_variables().await;
        }

        // Add environment variables if requested
        if options.include_env_vars {
            context.env_vars = self.variable_resolver.get_environment_variables();
        }

        // Extract variables from template content
        let required_vars = self.extract_template_variables(&template.content);

        // Resolve missing variables
        for var_name in required_vars {
            if !context.all_variables().contains_key(&var_name) {
                if let Some(description) = template.metadata.variables.get(&var_name) {
                    if options.interactive {
                        let value = self
                            .variable_resolver
                            .prompt_for_variable(&var_name, Some(description))
                            .await?;
                        context.variables.insert(var_name, value);
                    } else {
                        warn!("Missing required variable: {}", var_name);
                        return Err(AutorunError::ValidationError(format!(
                            "Missing required variable: {}",
                            var_name
                        )));
                    }
                } else if options.interactive {
                    let value = self
                        .variable_resolver
                        .prompt_for_variable(&var_name, None)
                        .await?;
                    context.variables.insert(var_name, value);
                }
            }
        }

        self.update_stats(|stats| stats.variables_resolved += context.all_variables().len() as u64);
        Ok(context)
    }

    /// Render template with Tera
    async fn render_template(
        &self,
        template: &Template,
        context: &TemplateContext,
    ) -> Result<String> {
        let tera_context = context.to_tera_context();

        // Create a unique template name based on hash
        let template_name = format!("template_{}", template.hash);

        // Add template to Tera if not already present
        if self
            .tera
            .get_template_names()
            .find(|name| *name == template_name)
            .is_none()
        {
            let mut tera = self.tera.clone();
            tera.add_raw_template(&template_name, &template.content)
                .map_err(|e| TemplateError::CompilationError(e.to_string()))?;
        }

        // Render template
        let rendered = self
            .tera
            .render(&template_name, &tera_context)
            .map_err(|e| TemplateError::RenderingError(e.to_string()))?;

        Ok(rendered)
    }

    /// Parse YAML frontmatter from template content
    fn parse_frontmatter(&self, content: &str) -> Result<(TemplateMetadata, String)> {
        if !content.starts_with("---\n") {
            return Ok((TemplateMetadata::default(), content.to_string()));
        }

        let lines: Vec<&str> = content.lines().collect();
        let mut frontmatter_end = None;

        for (i, line) in lines.iter().enumerate().skip(1) {
            if *line == "---" {
                frontmatter_end = Some(i);
                break;
            }
        }

        let Some(end_idx) = frontmatter_end else {
            return Ok((TemplateMetadata::default(), content.to_string()));
        };

        let frontmatter: Vec<&str> = lines[1..end_idx].to_vec();
        let frontmatter_yaml = frontmatter.join("\n");

        let metadata: TemplateMetadata = serde_yaml::from_str(&frontmatter_yaml)
            .map_err(|e| TemplateError::FrontmatterError(e.to_string()))?;

        let template_content = if end_idx + 1 < lines.len() {
            lines[end_idx + 1..].join("\n")
        } else {
            String::new()
        };

        Ok((metadata, template_content))
    }

    /// Extract variable names from template content
    fn extract_template_variables(&self, content: &str) -> Vec<String> {
        let mut variables = Vec::new();

        // Extract Tera-style variables: {{ variable_name }}
        let re = regex::Regex::new(r"\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\}\}").unwrap();
        for cap in re.captures_iter(content) {
            if let Some(var_name) = cap.get(1) {
                let name = var_name.as_str().to_string();
                if !variables.contains(&name) {
                    variables.push(name);
                }
            }
        }

        // Extract simple variable syntax: $VAR or ${VAR}
        let re_simple = regex::Regex::new(r"\$\{?([a-zA-Z_][a-zA-Z0-9_]*)\}?").unwrap();
        for cap in re_simple.captures_iter(content) {
            if let Some(var_name) = cap.get(1) {
                let name = var_name.as_str().to_string();
                if !variables.contains(&name) {
                    variables.push(name);
                }
            }
        }

        variables
    }

    /// Calculate content hash for caching
    fn calculate_hash(&self, content: &str) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        content.hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }

    /// Register custom Tera filters
    fn register_custom_filters(tera: &mut Tera) -> Result<()> {
        // Add uppercase filter
        tera.register_filter(
            "upper",
            |value: &Value, _: &HashMap<String, Value>| match value {
                Value::String(s) => Ok(Value::String(s.to_uppercase())),
                _ => Err(tera::Error::msg(
                    "upper filter can only be applied to strings",
                )),
            },
        );

        // Add lowercase filter
        tera.register_filter(
            "lower",
            |value: &Value, _: &HashMap<String, Value>| match value {
                Value::String(s) => Ok(Value::String(s.to_lowercase())),
                _ => Err(tera::Error::msg(
                    "lower filter can only be applied to strings",
                )),
            },
        );

        // Add snake_case filter
        tera.register_filter(
            "snake_case",
            |value: &Value, _: &HashMap<String, Value>| match value {
                Value::String(s) => {
                    let snake = s
                        .chars()
                        .map(|c| {
                            if c.is_uppercase() {
                                format!("_{}", c.to_lowercase())
                            } else {
                                c.to_string()
                            }
                        })
                        .collect::<String>()
                        .trim_start_matches('_')
                        .to_string();
                    Ok(Value::String(snake))
                }
                _ => Err(tera::Error::msg(
                    "snake_case filter can only be applied to strings",
                )),
            },
        );

        Ok(())
    }

    /// Register custom Tera functions
    fn register_custom_functions(tera: &mut Tera) -> Result<()> {
        // Add now() function for current timestamp
        tera.register_function("now", |_: &HashMap<String, Value>| {
            let now = chrono::Utc::now();
            Ok(Value::String(
                now.format("%Y-%m-%d %H:%M:%S UTC").to_string(),
            ))
        });

        // Add date() function for formatted dates
        tera.register_function("date", |args: &HashMap<String, Value>| {
            let format = args
                .get("format")
                .and_then(|v| v.as_str())
                .unwrap_or("%Y-%m-%d");

            let now = chrono::Utc::now();
            Ok(Value::String(now.format(format).to_string()))
        });

        Ok(())
    }

    /// Update statistics with thread safety
    fn update_stats<F>(&self, updater: F)
    where
        F: FnOnce(&mut TemplateStats),
    {
        if let Ok(mut stats) = self.stats.write() {
            updater(&mut *stats);
        }
    }

    /// Get processing statistics
    pub fn get_stats(&self) -> TemplateStats {
        self.stats
            .read()
            .unwrap_or_else(|_| TemplateStats::default().into())
            .clone()
    }

    /// Clear template cache
    pub async fn clear_cache(&self) {
        let mut cache = self.cache.write().await;
        cache.clear();
    }

    /// Get cache statistics
    pub async fn get_cache_stats(&self) -> (usize, usize) {
        let cache = self.cache.read().await;
        (cache.len(), cache.capacity())
    }
}

impl Default for TemplateEngine {
    fn default() -> Self {
        Self::new().expect("Failed to create default TemplateEngine")
    }
}
