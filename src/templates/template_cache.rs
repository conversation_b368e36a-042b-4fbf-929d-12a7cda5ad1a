//! Template caching system for improved performance

use super::ProcessedTemplate;
use crate::errors::{AutorunError, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{Duration, Instant};
use tracing::{debug, info, warn};

/// Cache configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheConfig {
    /// Maximum number of cached templates
    pub max_size: usize,
    /// Time-to-live for cache entries
    pub ttl: Duration,
    /// Whether to enable cache compression
    pub compression: bool,
    /// Maximum memory usage in bytes
    pub max_memory: usize,
    /// Cache cleanup interval
    pub cleanup_interval: Duration,
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            max_size: 1000,
            ttl: Duration::from_secs(3600), // 1 hour
            compression: false,
            max_memory: 100 * 1024 * 1024,              // 100MB
            cleanup_interval: Duration::from_secs(300), // 5 minutes
        }
    }
}

/// Cache entry with metadata
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct CacheEntry {
    /// Cached template result
    pub result: ProcessedTemplate,
    /// When the entry was created
    pub created_at: Instant,
    /// When the entry was last accessed
    pub last_accessed: Instant,
    /// How many times this entry has been accessed
    pub access_count: u64,
    /// Estimated memory usage of this entry
    pub memory_size: usize,
    /// Entry hash for validation
    pub hash: String,
}

impl CacheEntry {
    /// Create new cache entry
    pub fn new(result: ProcessedTemplate, hash: String) -> Self {
        let now = Instant::now();
        let memory_size = Self::estimate_memory_size(&result);

        Self {
            result,
            created_at: now,
            last_accessed: now,
            access_count: 0,
            memory_size,
            hash,
        }
    }

    /// Check if entry is expired
    pub fn is_expired(&self, ttl: Duration) -> bool {
        self.created_at.elapsed() > ttl
    }

    /// Mark entry as accessed
    pub fn mark_accessed(&mut self) {
        self.last_accessed = Instant::now();
        self.access_count += 1;
    }

    /// Get entry age
    pub fn age(&self) -> Duration {
        self.created_at.elapsed()
    }

    /// Estimate memory usage of a processed template
    fn estimate_memory_size(template: &ProcessedTemplate) -> usize {
        let mut size = 0;

        // Content size
        size += template.content.len();

        // Variables size
        for (key, value) in &template.variables_used {
            size += key.len() + value.len();
        }

        // Metadata size
        size += template.metadata.name.len();
        if let Some(ref desc) = template.metadata.description {
            size += desc.len();
        }

        // Approximate overhead
        size += 1024; // Fixed overhead for struct fields

        size
    }
}

/// Template cache with TTL and LRU eviction
pub struct TemplateCache {
    /// Cache entries indexed by template hash
    entries: HashMap<String, CacheEntry>,
    /// Cache configuration
    config: CacheConfig,
    /// Access order for LRU eviction
    access_order: Vec<String>,
    /// Total memory usage
    total_memory: usize,
    /// Cache statistics
    stats: CacheStats,
    /// Last cleanup time
    last_cleanup: Instant,
}

/// Cache statistics
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct CacheStats {
    /// Total cache hits
    pub hits: u64,
    /// Total cache misses
    pub misses: u64,
    /// Total entries added
    pub entries_added: u64,
    /// Total entries evicted
    pub entries_evicted: u64,
    /// Total entries expired
    pub entries_expired: u64,
    /// Total memory saved (estimated)
    pub memory_saved: u64,
    /// Average entry lifetime
    pub average_lifetime: Duration,
}

impl CacheStats {
    /// Calculate hit rate
    pub fn hit_rate(&self) -> f64 {
        if self.hits + self.misses == 0 {
            0.0
        } else {
            self.hits as f64 / (self.hits + self.misses) as f64
        }
    }

    /// Calculate memory efficiency
    pub fn memory_efficiency(&self) -> f64 {
        if self.entries_added == 0 {
            0.0
        } else {
            self.memory_saved as f64 / self.entries_added as f64
        }
    }
}

impl TemplateCache {
    /// Create new template cache with default configuration
    pub fn new() -> Self {
        Self::with_config(CacheConfig::default())
    }

    /// Create new template cache with custom configuration
    pub fn with_config(config: CacheConfig) -> Self {
        Self {
            entries: HashMap::new(),
            config,
            access_order: Vec::new(),
            total_memory: 0,
            stats: CacheStats::default(),
            last_cleanup: Instant::now(),
        }
    }

    /// Get cached template result
    pub fn get(&mut self, hash: &str) -> Option<ProcessedTemplate> {
        // Check if cleanup is needed
        if self.last_cleanup.elapsed() > self.config.cleanup_interval {
            self.cleanup_expired();
        }

        if let Some(entry) = self.entries.get_mut(hash) {
            // Check if entry is expired
            if entry.is_expired(self.config.ttl) {
                debug!("Cache entry expired for hash: {}", hash);
                self.remove_entry(hash);
                self.stats.entries_expired += 1;
                self.stats.misses += 1;
                return None;
            }

            // Mark as accessed and update LRU order
            entry.mark_accessed();
            self.update_access_order(hash);

            self.stats.hits += 1;
            self.stats.memory_saved += entry.memory_size as u64;

            debug!(
                "Cache hit for hash: {} (accessed {} times)",
                hash, entry.access_count
            );
            Some(entry.result.clone())
        } else {
            self.stats.misses += 1;
            debug!("Cache miss for hash: {}", hash);
            None
        }
    }

    /// Insert template result into cache
    pub async fn insert(&mut self, hash: String, result: ProcessedTemplate) {
        let entry = CacheEntry::new(result, hash.clone());
        let entry_size = entry.memory_size;

        // Check if we need to make space
        self.ensure_capacity(entry_size).await;

        // Insert entry
        if let Some(old_entry) = self.entries.insert(hash.clone(), entry) {
            // Replace existing entry, adjust memory
            self.total_memory = self.total_memory.saturating_sub(old_entry.memory_size);
            self.remove_from_access_order(&hash);
        } else {
            self.stats.entries_added += 1;
        }

        self.total_memory += entry_size;
        self.access_order.push(hash.clone());

        info!(
            "Cached template with hash: {} (size: {} bytes)",
            hash, entry_size
        );
        debug!(
            "Cache size: {}/{}, Memory: {}/{} bytes",
            self.entries.len(),
            self.config.max_size,
            self.total_memory,
            self.config.max_memory
        );
    }

    /// Ensure cache has capacity for new entry
    async fn ensure_capacity(&mut self, required_size: usize) {
        // Check size limit
        while self.entries.len() >= self.config.max_size {
            if !self.evict_lru() {
                break; // Safety: prevent infinite loop
            }
        }

        // Check memory limit
        while self.total_memory + required_size > self.config.max_memory {
            if !self.evict_lru() {
                break; // Safety: prevent infinite loop
            }
        }
    }

    /// Evict least recently used entry
    fn evict_lru(&mut self) -> bool {
        if let Some(hash) = self.access_order.first().cloned() {
            debug!("Evicting LRU entry: {}", hash);
            self.remove_entry(&hash);
            self.stats.entries_evicted += 1;
            true
        } else {
            false
        }
    }

    /// Remove entry from cache
    fn remove_entry(&mut self, hash: &str) {
        if let Some(entry) = self.entries.remove(hash) {
            self.total_memory = self.total_memory.saturating_sub(entry.memory_size);
            self.remove_from_access_order(hash);

            // Update average lifetime stats
            let lifetime = entry.age();
            let total_entries = self.stats.entries_added;
            if total_entries > 0 {
                let current_avg = self.stats.average_lifetime;
                self.stats.average_lifetime = Duration::from_nanos(
                    (current_avg.as_nanos() as u64 * (total_entries - 1)
                        + lifetime.as_nanos() as u64)
                        / total_entries,
                );
            }
        }
    }

    /// Update access order for LRU
    fn update_access_order(&mut self, hash: &str) {
        self.remove_from_access_order(hash);
        self.access_order.push(hash.to_string());
    }

    /// Remove hash from access order
    fn remove_from_access_order(&mut self, hash: &str) {
        self.access_order.retain(|h| h != hash);
    }

    /// Clean up expired entries
    pub fn cleanup_expired(&mut self) {
        let expired_hashes: Vec<String> = self
            .entries
            .iter()
            .filter(|(_, entry)| entry.is_expired(self.config.ttl))
            .map(|(hash, _)| hash.clone())
            .collect();

        for hash in expired_hashes {
            debug!("Removing expired cache entry: {}", hash);
            self.remove_entry(&hash);
            self.stats.entries_expired += 1;
        }

        self.last_cleanup = Instant::now();

        if self.stats.entries_expired > 0 {
            info!(
                "Cleaned up {} expired cache entries",
                self.stats.entries_expired
            );
        }
    }

    /// Clear all cache entries
    pub fn clear(&mut self) {
        let count = self.entries.len();
        self.entries.clear();
        self.access_order.clear();
        self.total_memory = 0;

        info!("Cleared {} cache entries", count);
    }

    /// Get cache statistics
    pub fn stats(&self) -> CacheStats {
        self.stats.clone()
    }

    /// Get current cache size
    pub fn len(&self) -> usize {
        self.entries.len()
    }

    /// Get cache capacity
    pub fn capacity(&self) -> usize {
        self.config.max_size
    }

    /// Check if cache is empty
    pub fn is_empty(&self) -> bool {
        self.entries.is_empty()
    }

    /// Get current memory usage
    pub fn memory_usage(&self) -> usize {
        self.total_memory
    }

    /// Get memory capacity
    pub fn memory_capacity(&self) -> usize {
        self.config.max_memory
    }

    /// Get cache configuration
    pub fn config(&self) -> &CacheConfig {
        &self.config
    }

    /// Update cache configuration
    pub fn update_config(&mut self, config: CacheConfig) {
        self.config = config;

        // Trigger cleanup if new limits are lower
        if self.entries.len() > self.config.max_size || self.total_memory > self.config.max_memory {
            tokio::spawn({
                let mut cache = self.clone();
                async move {
                    cache.ensure_capacity(0).await;
                }
            });
        }
    }

    /// Get cache entries sorted by access count
    pub fn get_popular_entries(&self, limit: usize) -> Vec<(String, u64)> {
        let mut entries: Vec<(String, u64)> = self
            .entries
            .iter()
            .map(|(hash, entry)| (hash.clone(), entry.access_count))
            .collect();

        entries.sort_by(|a, b| b.1.cmp(&a.1));
        entries.truncate(limit);
        entries
    }

    /// Get detailed cache information
    pub fn get_cache_info(&self) -> HashMap<String, serde_json::Value> {
        let mut info = HashMap::new();

        info.insert(
            "size".to_string(),
            serde_json::Value::from(self.entries.len()),
        );
        info.insert(
            "capacity".to_string(),
            serde_json::Value::from(self.config.max_size),
        );
        info.insert(
            "memory_usage".to_string(),
            serde_json::Value::from(self.total_memory),
        );
        info.insert(
            "memory_capacity".to_string(),
            serde_json::Value::from(self.config.max_memory),
        );
        info.insert(
            "hit_rate".to_string(),
            serde_json::Value::from(self.stats.hit_rate()),
        );
        info.insert(
            "total_hits".to_string(),
            serde_json::Value::from(self.stats.hits),
        );
        info.insert(
            "total_misses".to_string(),
            serde_json::Value::from(self.stats.misses),
        );
        info.insert(
            "entries_expired".to_string(),
            serde_json::Value::from(self.stats.entries_expired),
        );
        info.insert(
            "entries_evicted".to_string(),
            serde_json::Value::from(self.stats.entries_evicted),
        );

        info
    }

    /// Export cache statistics as JSON
    pub fn export_stats(&self) -> Result<String> {
        serde_json::to_string_pretty(&self.stats).map_err(|e| AutorunError::Json(e))
    }

    /// Validate cache integrity
    pub fn validate(&self) -> Result<()> {
        // Check that all entries in access_order exist in entries map
        for hash in &self.access_order {
            if !self.entries.contains_key(hash) {
                return Err(AutorunError::ValidationError(format!(
                    "Access order contains non-existent entry: {}",
                    hash
                )));
            }
        }

        // Check that memory calculation is correct
        let calculated_memory: usize = self.entries.values().map(|entry| entry.memory_size).sum();

        if calculated_memory != self.total_memory {
            warn!(
                "Memory calculation mismatch: calculated={}, stored={}",
                calculated_memory, self.total_memory
            );
        }

        Ok(())
    }
}

impl Clone for TemplateCache {
    fn clone(&self) -> Self {
        Self {
            entries: self.entries.clone(),
            config: self.config.clone(),
            access_order: self.access_order.clone(),
            total_memory: self.total_memory,
            stats: self.stats.clone(),
            last_cleanup: self.last_cleanup,
        }
    }
}

impl Default for TemplateCache {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::templates::{ProcessedTemplate, TemplateMetadata};
    use std::time::Duration;

    fn create_test_template() -> ProcessedTemplate {
        ProcessedTemplate {
            content: "Test template content".to_string(),
            variables_used: std::collections::HashMap::new(),
            metadata: TemplateMetadata::default(),
            processing_time: Duration::from_millis(10),
        }
    }

    #[tokio::test]
    async fn test_cache_basic_operations() {
        let mut cache = TemplateCache::new();
        let template = create_test_template();
        let hash = "test_hash".to_string();

        // Test miss
        assert!(cache.get(&hash).is_none());
        assert_eq!(cache.stats().misses, 1);

        // Test insert and hit
        cache.insert(hash.clone(), template.clone()).await;
        assert!(cache.get(&hash).is_some());
        assert_eq!(cache.stats().hits, 1);
        assert_eq!(cache.len(), 1);
    }

    #[tokio::test]
    async fn test_cache_expiration() {
        let config = CacheConfig {
            ttl: Duration::from_millis(50),
            ..Default::default()
        };
        let mut cache = TemplateCache::with_config(config);
        let template = create_test_template();
        let hash = "test_hash".to_string();

        // Insert and verify it exists
        cache.insert(hash.clone(), template).await;
        assert!(cache.get(&hash).is_some());

        // Wait for expiration
        tokio::time::sleep(Duration::from_millis(100)).await;

        // Should be expired now
        assert!(cache.get(&hash).is_none());
        assert_eq!(cache.stats().entries_expired, 1);
    }

    #[tokio::test]
    async fn test_cache_lru_eviction() {
        let config = CacheConfig {
            max_size: 2,
            ..Default::default()
        };
        let mut cache = TemplateCache::with_config(config);
        let template = create_test_template();

        // Fill cache to capacity
        cache.insert("hash1".to_string(), template.clone()).await;
        cache.insert("hash2".to_string(), template.clone()).await;
        assert_eq!(cache.len(), 2);

        // Access first entry to make it more recently used
        cache.get("hash1");

        // Insert third entry, should evict hash2 (LRU)
        cache.insert("hash3".to_string(), template).await;
        assert_eq!(cache.len(), 2);
        assert!(cache.get("hash1").is_some());
        assert!(cache.get("hash2").is_none());
        assert!(cache.get("hash3").is_some());
    }

    #[test]
    fn test_cache_stats() {
        let mut cache = TemplateCache::new();
        let stats = cache.stats();

        assert_eq!(stats.hit_rate(), 0.0);
        assert_eq!(stats.hits, 0);
        assert_eq!(stats.misses, 0);
    }

    #[test]
    fn test_cache_validation() {
        let cache = TemplateCache::new();
        assert!(cache.validate().is_ok());
    }
}
