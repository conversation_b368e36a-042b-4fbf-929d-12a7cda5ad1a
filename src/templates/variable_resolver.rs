//! Variable resolution system for templates

use crate::errors::{AutorunError, Result};
use std::collections::HashMap;
use std::env;
use std::io::{self, Write};

use tracing::{debug, info, warn};

/// Variable source types
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum VariableSource {
    /// User-provided variable
    User,
    /// System-generated variable
    System,
    /// Environment variable
    Environment,
    /// Computed variable
    Computed,
    /// Default value
    Default,
}

/// Variable prompt configuration
#[derive(Debug, Clone)]
pub struct VariablePrompt {
    /// Variable name
    pub name: String,
    /// Human-readable prompt
    pub prompt: String,
    /// Optional description
    pub description: Option<String>,
    /// Default value
    pub default: Option<String>,
    /// Whether input should be hidden (for passwords)
    pub hidden: bool,
    /// Validation pattern (regex)
    pub validation: Option<String>,
    /// Whether variable is required
    pub required: bool,
}

impl VariablePrompt {
    pub fn new(name: String, prompt: String) -> Self {
        Self {
            name,
            prompt,
            description: None,
            default: None,
            hidden: false,
            validation: None,
            required: true,
        }
    }

    pub fn with_description(mut self, description: String) -> Self {
        self.description = Some(description);
        self
    }

    pub fn with_default(mut self, default: String) -> Self {
        self.default = Some(default);
        self.required = false;
        self
    }

    pub fn hidden(mut self) -> Self {
        self.hidden = true;
        self
    }

    pub fn with_validation(mut self, pattern: String) -> Self {
        self.validation = Some(pattern);
        self
    }

    pub fn optional(mut self) -> Self {
        self.required = false;
        self
    }
}

/// Variable resolver for template processing
pub struct VariableResolver {
    /// System variables cache
    system_variables: Option<HashMap<String, String>>,
    /// Environment variables cache
    env_variables: Option<HashMap<String, String>>,
    /// Custom variable providers
    custom_providers: HashMap<String, Box<dyn Fn() -> Result<String> + Send + Sync>>,
}

impl VariableResolver {
    /// Create new variable resolver
    pub fn new() -> Self {
        Self {
            system_variables: None,
            env_variables: None,
            custom_providers: HashMap::new(),
        }
    }

    /// Get system variables (cached)
    pub async fn get_system_variables(&mut self) -> HashMap<String, String> {
        if let Some(ref vars) = self.system_variables {
            return vars.clone();
        }

        let mut vars = HashMap::new();

        // Username
        if let Ok(username) = env::var("USER")
            .or_else(|_| env::var("USERNAME"))
            .or_else(|_| env::var("LOGNAME"))
        {
            vars.insert("username".to_string(), username);
            vars.insert("user".to_string(), vars["username"].clone());
        }

        // Home directory
        if let Ok(home) = env::var("HOME").or_else(|_| env::var("USERPROFILE")) {
            vars.insert("home".to_string(), home);
            vars.insert("home_dir".to_string(), vars["home"].clone());
        }

        // Current working directory
        if let Ok(cwd) = env::current_dir() {
            vars.insert("cwd".to_string(), cwd.display().to_string());
            vars.insert("pwd".to_string(), vars["cwd"].clone());
        }

        // Date and time
        let now = chrono::Local::now();
        vars.insert("date".to_string(), now.format("%Y-%m-%d").to_string());
        vars.insert("time".to_string(), now.format("%H:%M:%S").to_string());
        vars.insert(
            "datetime".to_string(),
            now.format("%Y-%m-%d %H:%M:%S").to_string(),
        );
        vars.insert("timestamp".to_string(), now.timestamp().to_string());
        vars.insert("year".to_string(), now.format("%Y").to_string());
        vars.insert("month".to_string(), now.format("%m").to_string());
        vars.insert("day".to_string(), now.format("%d").to_string());
        vars.insert("hour".to_string(), now.format("%H").to_string());
        vars.insert("minute".to_string(), now.format("%M").to_string());
        vars.insert("second".to_string(), now.format("%S").to_string());

        // ISO date formats
        vars.insert("iso_date".to_string(), now.format("%Y-%m-%d").to_string());
        vars.insert("iso_datetime".to_string(), now.to_rfc3339());

        // Platform information
        vars.insert("os".to_string(), env::consts::OS.to_string());
        vars.insert("arch".to_string(), env::consts::ARCH.to_string());
        vars.insert("family".to_string(), env::consts::FAMILY.to_string());

        // Process information
        vars.insert("pid".to_string(), std::process::id().to_string());

        // Generate UUID
        vars.insert("uuid".to_string(), uuid::Uuid::new_v4().to_string());
        vars.insert(
            "uuid_short".to_string(),
            uuid::Uuid::new_v4().simple().to_string(),
        );

        // Random values
        use rand::Rng;
        let mut rng = rand::thread_rng();
        vars.insert(
            "random_number".to_string(),
            rng.gen_range(0..1000000).to_string(),
        );
        vars.insert(
            "random_hex".to_string(),
            format!("{:08x}", rng.gen::<u32>()),
        );

        debug!("Generated {} system variables", vars.len());
        self.system_variables = Some(vars.clone());
        vars
    }

    /// Get environment variables (cached)
    pub fn get_environment_variables(&mut self) -> HashMap<String, String> {
        if let Some(ref vars) = self.env_variables {
            return vars.clone();
        }

        let vars: HashMap<String, String> = env::vars()
            .filter(|(key, _)| {
                // Filter out sensitive variables
                !key.to_uppercase().contains("PASSWORD")
                    && !key.to_uppercase().contains("SECRET")
                    && !key.to_uppercase().contains("TOKEN")
                    && !key.to_uppercase().contains("KEY")
                    && !key.to_uppercase().contains("AUTH")
            })
            .collect();

        debug!("Loaded {} environment variables", vars.len());
        self.env_variables = Some(vars.clone());
        vars
    }

    /// Prompt user for variable value
    pub async fn prompt_for_variable(
        &self,
        name: &str,
        description: Option<&str>,
    ) -> Result<String> {
        let prompt = if let Some(desc) = description {
            VariablePrompt::new(
                name.to_string(),
                format!("Enter value for '{}' ({})", name, desc),
            )
            .with_description(desc.to_string())
        } else {
            VariablePrompt::new(name.to_string(), format!("Enter value for '{}'", name))
        };

        self.prompt_with_config(prompt).await
    }

    /// Prompt user with full configuration
    pub async fn prompt_with_config(&self, config: VariablePrompt) -> Result<String> {
        // Display prompt
        print!("{}", config.prompt);
        if let Some(ref default) = config.default {
            print!(" [default: {}]", default);
        }
        print!(": ");
        io::stdout().flush().map_err(|e| AutorunError::Io(e))?;

        // Read input
        let mut input = String::new();
        if config.hidden {
            // For hidden input (passwords), we'd need a more sophisticated approach
            // For now, just use regular input with a warning
            warn!("Hidden input not fully implemented, using regular input");
            io::stdin()
                .read_line(&mut input)
                .map_err(|e| AutorunError::Io(e))?;
        } else {
            io::stdin()
                .read_line(&mut input)
                .map_err(|e| AutorunError::Io(e))?;
        }

        let input = input.trim().to_string();

        // Handle empty input
        if input.is_empty() {
            if let Some(default) = config.default {
                return Ok(default);
            } else if !config.required {
                return Ok(String::new());
            } else {
                return Err(AutorunError::ValidationError(format!(
                    "Variable '{}' is required",
                    config.name
                )));
            }
        }

        // Validate input
        if let Some(ref pattern) = config.validation {
            let regex = regex::Regex::new(pattern).map_err(|e| {
                AutorunError::ValidationError(format!("Invalid validation pattern: {}", e))
            })?;

            if !regex.is_match(&input) {
                return Err(AutorunError::ValidationError(format!(
                    "Input for '{}' does not match required pattern",
                    config.name
                )));
            }
        }

        info!("Variable '{}' set successfully", config.name);
        Ok(input)
    }

    /// Resolve variable from multiple sources
    pub async fn resolve_variable(
        &mut self,
        name: &str,
        user_vars: &HashMap<String, String>,
        allow_prompt: bool,
    ) -> Result<(String, VariableSource)> {
        // Check user variables first
        if let Some(value) = user_vars.get(name) {
            return Ok((value.clone(), VariableSource::User));
        }

        // Check system variables
        let system_vars = self.get_system_variables().await;
        if let Some(value) = system_vars.get(name) {
            return Ok((value.clone(), VariableSource::System));
        }

        // Check environment variables
        let env_vars = self.get_environment_variables();
        if let Some(value) = env_vars.get(name) {
            return Ok((value.clone(), VariableSource::Environment));
        }

        // Check custom providers
        if let Some(provider) = self.custom_providers.get(name) {
            match provider() {
                Ok(value) => return Ok((value, VariableSource::Computed)),
                Err(e) => warn!("Custom provider for '{}' failed: {}", name, e),
            }
        }

        // Prompt user if allowed
        if allow_prompt {
            let value = self.prompt_for_variable(name, None).await?;
            return Ok((value, VariableSource::User));
        }

        Err(AutorunError::ValidationError(format!(
            "Variable '{}' not found and prompting disabled",
            name
        )))
    }

    /// Add custom variable provider
    pub fn add_custom_provider<F>(&mut self, name: String, provider: F)
    where
        F: Fn() -> Result<String> + Send + Sync + 'static,
    {
        self.custom_providers.insert(name, Box::new(provider));
    }

    /// Resolve multiple variables at once
    pub async fn resolve_variables(
        &mut self,
        names: &[String],
        user_vars: &HashMap<String, String>,
        allow_prompt: bool,
    ) -> Result<HashMap<String, (String, VariableSource)>> {
        let mut resolved = HashMap::new();

        for name in names {
            match self.resolve_variable(name, user_vars, allow_prompt).await {
                Ok((value, source)) => {
                    resolved.insert(name.clone(), (value, source));
                }
                Err(e) => {
                    warn!("Failed to resolve variable '{}': {}", name, e);
                    if allow_prompt {
                        return Err(e);
                    }
                }
            }
        }

        Ok(resolved)
    }

    /// Clear cached variables
    pub fn clear_cache(&mut self) {
        self.system_variables = None;
        self.env_variables = None;
        debug!("Variable cache cleared");
    }

    /// Get variable statistics
    pub fn get_stats(&self) -> (usize, usize, usize) {
        let system_count = self.system_variables.as_ref().map_or(0, |v| v.len());
        let env_count = self.env_variables.as_ref().map_or(0, |v| v.len());
        let custom_count = self.custom_providers.len();

        (system_count, env_count, custom_count)
    }

    /// Validate variable name
    pub fn validate_variable_name(name: &str) -> Result<()> {
        if name.is_empty() {
            return Err(AutorunError::ValidationError(
                "Variable name cannot be empty".to_string(),
            ));
        }

        if !name.chars().all(|c| c.is_alphanumeric() || c == '_') {
            return Err(AutorunError::ValidationError(
                "Variable name can only contain alphanumeric characters and underscores"
                    .to_string(),
            ));
        }

        if name.chars().next().unwrap().is_numeric() {
            return Err(AutorunError::ValidationError(
                "Variable name cannot start with a number".to_string(),
            ));
        }

        Ok(())
    }

    /// Get variable suggestions based on partial name
    pub async fn get_variable_suggestions(&mut self, partial: &str) -> Vec<String> {
        let mut suggestions = Vec::new();

        // Get all available variables
        let system_vars = self.get_system_variables().await;
        let env_vars = self.get_environment_variables();

        // Add matching system variables
        for name in system_vars.keys() {
            if name.starts_with(partial) {
                suggestions.push(name.clone());
            }
        }

        // Add matching environment variables
        for name in env_vars.keys() {
            if name.to_lowercase().starts_with(&partial.to_lowercase()) {
                suggestions.push(name.clone());
            }
        }

        // Add matching custom providers
        for name in self.custom_providers.keys() {
            if name.starts_with(partial) {
                suggestions.push(name.clone());
            }
        }

        suggestions.sort();
        suggestions.dedup();
        suggestions
    }
}

impl Default for VariableResolver {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_system_variables() {
        let mut resolver = VariableResolver::new();
        let vars = resolver.get_system_variables().await;

        assert!(!vars.is_empty());
        assert!(vars.contains_key("date"));
        assert!(vars.contains_key("time"));
        assert!(vars.contains_key("os"));
    }

    #[tokio::test]
    async fn test_environment_variables() {
        let mut resolver = VariableResolver::new();
        let vars = resolver.get_environment_variables();

        // Should have at least some environment variables
        // but exact count depends on system
        assert!(!vars.is_empty());
    }

    #[test]
    fn test_variable_name_validation() {
        assert!(VariableResolver::validate_variable_name("valid_name").is_ok());
        assert!(VariableResolver::validate_variable_name("ValidName123").is_ok());
        assert!(VariableResolver::validate_variable_name("_private").is_ok());

        assert!(VariableResolver::validate_variable_name("").is_err());
        assert!(VariableResolver::validate_variable_name("123invalid").is_err());
        assert!(VariableResolver::validate_variable_name("invalid-name").is_err());
        assert!(VariableResolver::validate_variable_name("invalid.name").is_err());
    }

    #[tokio::test]
    async fn test_variable_suggestions() {
        let mut resolver = VariableResolver::new();
        let suggestions = resolver.get_variable_suggestions("da").await;

        // Should include "date" from system variables
        assert!(suggestions.contains(&"date".to_string()));
    }
}
