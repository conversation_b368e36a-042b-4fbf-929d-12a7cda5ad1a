//! Template validation system for syntax and semantic checking

use super::{Template, TemplateMetadata};
use crate::errors::{AutorunError, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashSet;
use tera::Tera;
use tracing::{debug, error, warn};

/// Validation severity levels
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ValidationSeverity {
    /// Critical error that prevents template processing
    Error,
    /// Warning about potential issues
    Warning,
    /// Informational notice
    Info,
}

/// Validation error details
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationError {
    /// Error message
    pub message: String,
    /// Severity level
    pub severity: ValidationSeverity,
    /// Line number where error occurred (if applicable)
    pub line: Option<usize>,
    /// Column number where error occurred (if applicable)
    pub column: Option<usize>,
    /// Error category
    pub category: String,
    /// Suggested fix (if available)
    pub suggestion: Option<String>,
}

impl ValidationError {
    pub fn new(message: String, severity: ValidationSeverity, category: String) -> Self {
        Self {
            message,
            severity,
            line: None,
            column: None,
            category,
            suggestion: None,
        }
    }

    pub fn error(message: String, category: String) -> Self {
        Self::new(message, ValidationSeverity::Error, category)
    }

    pub fn warning(message: String, category: String) -> Self {
        Self::new(message, ValidationSeverity::Warning, category)
    }

    pub fn info(message: String, category: String) -> Self {
        Self::new(message, ValidationSeverity::Info, category)
    }

    pub fn with_location(mut self, line: usize, column: usize) -> Self {
        self.line = Some(line);
        self.column = Some(column);
        self
    }

    pub fn with_suggestion(mut self, suggestion: String) -> Self {
        self.suggestion = Some(suggestion);
        self
    }

    pub fn is_error(&self) -> bool {
        self.severity == ValidationSeverity::Error
    }

    pub fn is_warning(&self) -> bool {
        self.severity == ValidationSeverity::Warning
    }
}

/// Validation result containing all errors and warnings
#[derive(Debug, Clone)]
pub struct ValidationResult {
    /// All validation errors found
    pub errors: Vec<ValidationError>,
    /// Whether validation passed (no errors)
    pub is_valid: bool,
    /// Template variables found during validation
    pub variables_found: HashSet<String>,
    /// Template includes found
    pub includes_found: Vec<String>,
    /// Validation statistics
    pub stats: ValidationStats,
}

impl ValidationResult {
    pub fn new() -> Self {
        Self {
            errors: Vec::new(),
            is_valid: true,
            variables_found: HashSet::new(),
            includes_found: Vec::new(),
            stats: ValidationStats::default(),
        }
    }

    pub fn add_error(&mut self, error: ValidationError) {
        if error.is_error() {
            self.is_valid = false;
        }
        self.errors.push(error);
    }

    pub fn has_errors(&self) -> bool {
        self.errors.iter().any(|e| e.is_error())
    }

    pub fn has_warnings(&self) -> bool {
        self.errors.iter().any(|e| e.is_warning())
    }

    pub fn error_count(&self) -> usize {
        self.errors.iter().filter(|e| e.is_error()).count()
    }

    pub fn warning_count(&self) -> usize {
        self.errors.iter().filter(|e| e.is_warning()).count()
    }

    pub fn get_errors(&self) -> Vec<&ValidationError> {
        self.errors.iter().filter(|e| e.is_error()).collect()
    }

    pub fn get_warnings(&self) -> Vec<&ValidationError> {
        self.errors.iter().filter(|e| e.is_warning()).collect()
    }
}

/// Validation statistics
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct ValidationStats {
    /// Number of templates validated
    pub templates_validated: u64,
    /// Number of syntax errors found
    pub syntax_errors: u64,
    /// Number of semantic errors found
    pub semantic_errors: u64,
    /// Number of warnings found
    pub warnings: u64,
    /// Number of variables validated
    pub variables_validated: u64,
    /// Number of includes validated
    pub includes_validated: u64,
}

/// Template validator configuration
#[derive(Debug, Clone)]
pub struct ValidatorConfig {
    /// Whether to validate template syntax
    pub validate_syntax: bool,
    /// Whether to validate variable references
    pub validate_variables: bool,
    /// Whether to validate template includes
    pub validate_includes: bool,
    /// Whether to validate metadata
    pub validate_metadata: bool,
    /// Maximum template size in bytes
    pub max_template_size: usize,
    /// Maximum nesting depth for includes
    pub max_include_depth: usize,
    /// Whether to allow undefined variables
    pub allow_undefined_variables: bool,
    /// Custom validation rules
    pub custom_rules: Vec<Box<dyn Fn(&Template) -> Vec<ValidationError> + Send + Sync>>,
}

impl Default for ValidatorConfig {
    fn default() -> Self {
        Self {
            validate_syntax: true,
            validate_variables: true,
            validate_includes: true,
            validate_metadata: true,
            max_template_size: 1024 * 1024, // 1MB
            max_include_depth: 5,
            allow_undefined_variables: false,
            custom_rules: Vec::new(),
        }
    }
}

/// Template validator
pub struct TemplateValidator {
    /// Validator configuration
    config: ValidatorConfig,
    /// Tera instance for syntax validation
    tera: Tera,
    /// Validation statistics
    stats: ValidationStats,
}

impl TemplateValidator {
    /// Create new template validator
    pub fn new() -> Self {
        Self::with_config(ValidatorConfig::default())
    }

    /// Create validator with custom configuration
    pub fn with_config(config: ValidatorConfig) -> Self {
        let tera = Tera::new("").expect("Failed to create Tera instance for validation");

        Self {
            config,
            tera,
            stats: ValidationStats::default(),
        }
    }

    /// Validate a template
    pub async fn validate_template(&mut self, template: &Template) -> Result<ValidationResult> {
        let mut result = ValidationResult::new();
        self.stats.templates_validated += 1;

        debug!("Validating template: {}", template.metadata.name);

        // Validate template size
        if template.content.len() > self.config.max_template_size {
            result.add_error(ValidationError::error(
                format!(
                    "Template size ({} bytes) exceeds maximum allowed size ({} bytes)",
                    template.content.len(),
                    self.config.max_template_size
                ),
                "size".to_string(),
            ));
        }

        // Validate metadata if enabled
        if self.config.validate_metadata {
            self.validate_metadata(&template.metadata, &mut result);
        }

        // Validate syntax if enabled
        if self.config.validate_syntax {
            self.validate_syntax(&template.content, &mut result).await;
        }

        // Validate variables if enabled
        if self.config.validate_variables {
            self.validate_variables(template, &mut result);
        }

        // Validate includes if enabled
        if self.config.validate_includes {
            self.validate_includes(template, &mut result).await;
        }

        // Apply custom validation rules
        for rule in &self.config.custom_rules {
            let custom_errors = rule(template);
            for error in custom_errors {
                result.add_error(error);
            }
        }

        // Update statistics
        self.stats.syntax_errors += result
            .errors
            .iter()
            .filter(|e| e.is_error() && e.category == "syntax")
            .count() as u64;

        self.stats.semantic_errors += result
            .errors
            .iter()
            .filter(|e| e.is_error() && e.category != "syntax")
            .count() as u64;

        self.stats.warnings += result.warning_count() as u64;

        if result.has_errors() {
            error!(
                "Template validation failed with {} errors",
                result.error_count()
            );
        } else if result.has_warnings() {
            warn!(
                "Template validation completed with {} warnings",
                result.warning_count()
            );
        } else {
            debug!("Template validation passed");
        }

        Ok(result)
    }

    /// Validate template metadata
    fn validate_metadata(&self, metadata: &TemplateMetadata, result: &mut ValidationResult) {
        // Check required fields
        if metadata.name.is_empty() {
            result.add_error(
                ValidationError::error(
                    "Template name is required".to_string(),
                    "metadata".to_string(),
                )
                .with_suggestion("Add a 'name' field to the template frontmatter".to_string()),
            );
        }

        // Validate variable definitions
        for (var_name, description) in &metadata.variables {
            if var_name.is_empty() {
                result.add_error(ValidationError::error(
                    "Variable name cannot be empty".to_string(),
                    "metadata".to_string(),
                ));
            }

            if !Self::is_valid_variable_name(var_name) {
                result.add_error(ValidationError::error(
                    format!("Invalid variable name: '{}'", var_name),
                    "metadata".to_string(),
                ).with_suggestion("Variable names must start with a letter and contain only letters, numbers, and underscores".to_string()));
            }

            if description.is_empty() {
                result.add_error(
                    ValidationError::warning(
                        format!("Variable '{}' has no description", var_name),
                        "metadata".to_string(),
                    )
                    .with_suggestion("Add a description for better user experience".to_string()),
                );
            }

            self.stats.variables_validated += 1;
        }

        // Validate includes
        for include in &metadata.includes {
            if include.is_empty() {
                result.add_error(ValidationError::error(
                    "Include path cannot be empty".to_string(),
                    "metadata".to_string(),
                ));
            }
            self.stats.includes_validated += 1;
        }

        // Validate tags
        for tag in &metadata.tags {
            if tag.is_empty() {
                result.add_error(ValidationError::warning(
                    "Empty tag found in metadata".to_string(),
                    "metadata".to_string(),
                ));
            }
        }
    }

    /// Validate template syntax using Tera
    async fn validate_syntax(&mut self, content: &str, result: &mut ValidationResult) {
        // Create a temporary template name for validation
        let template_name = format!("validation_{}", uuid::Uuid::new_v4().simple());

        // Try to compile the template
        match self.tera.add_raw_template(&template_name, content) {
            Ok(_) => {
                debug!("Template syntax validation passed");

                // Extract variables from template
                result.variables_found = self.extract_template_variables(content);

                // Note: In newer Tera versions, temporary templates are automatically managed
                // No explicit cleanup needed for validation templates
            }
            Err(e) => {
                let error_msg = format!("Template syntax error: {}", e);
                result.add_error(
                    ValidationError::error(error_msg, "syntax".to_string())
                        .with_suggestion("Check Tera template syntax documentation".to_string()),
                );
            }
        }

        // Additional syntax checks
        self.validate_template_structure(content, result);
    }

    /// Validate template structure and common patterns
    fn validate_template_structure(&self, content: &str, result: &mut ValidationResult) {
        let lines: Vec<&str> = content.lines().collect();

        for (line_num, line) in lines.iter().enumerate() {
            let line_number = line_num + 1;

            // Check for unmatched braces
            let open_braces = line.matches("{{").count();
            let close_braces = line.matches("}}").count();
            if open_braces != close_braces {
                result.add_error(
                    ValidationError::warning(
                        "Unmatched template braces on this line".to_string(),
                        "syntax".to_string(),
                    )
                    .with_location(line_number, 0),
                );
            }

            // Check for common syntax errors
            if line.contains("{%") && !line.contains("%}") {
                result.add_error(
                    ValidationError::error(
                        "Unclosed template tag".to_string(),
                        "syntax".to_string(),
                    )
                    .with_location(line_number, 0),
                );
            }

            if line.contains("{{") && !line.contains("}}") {
                result.add_error(
                    ValidationError::error(
                        "Unclosed variable reference".to_string(),
                        "syntax".to_string(),
                    )
                    .with_location(line_number, 0),
                );
            }

            // Check for deprecated syntax
            if line.contains("$VAR") || line.contains("${VAR}") {
                result.add_error(
                    ValidationError::warning(
                        "Consider using Tera syntax {{ VAR }} instead of shell-style variables"
                            .to_string(),
                        "style".to_string(),
                    )
                    .with_location(line_number, 0)
                    .with_suggestion("Replace $VAR or ${VAR} with {{ VAR }}".to_string()),
                );
            }
        }
    }

    /// Validate variable references in template
    fn validate_variables(&self, template: &Template, result: &mut ValidationResult) {
        let template_vars = self.extract_template_variables(&template.content);
        let defined_vars: HashSet<String> = template.metadata.variables.keys().cloned().collect();

        for var in &template_vars {
            result.variables_found.insert(var.clone());

            if !defined_vars.contains(var) && !self.config.allow_undefined_variables {
                result.add_error(
                    ValidationError::warning(
                        format!("Variable '{}' is used but not defined in metadata", var),
                        "variables".to_string(),
                    )
                    .with_suggestion(format!(
                        "Add '{}' to the variables section in frontmatter",
                        var
                    )),
                );
            }
        }

        // Check for unused variable definitions
        for defined_var in &defined_vars {
            if !template_vars.contains(defined_var) {
                result.add_error(ValidationError::info(
                    format!(
                        "Variable '{}' is defined but not used in template",
                        defined_var
                    ),
                    "variables".to_string(),
                ));
            }
        }
    }

    /// Validate template includes
    async fn validate_includes(&self, template: &Template, result: &mut ValidationResult) {
        for include_path in &template.metadata.includes {
            result.includes_found.push(include_path.clone());

            // Check include path format
            if include_path.contains("..") {
                result.add_error(
                    ValidationError::error(
                        format!(
                            "Include path '{}' contains relative path traversal",
                            include_path
                        ),
                        "security".to_string(),
                    )
                    .with_suggestion(
                        "Use absolute paths or paths relative to template directory".to_string(),
                    ),
                );
            }

            // Check for circular includes (basic check)
            if *include_path
                == template
                    .source_path
                    .as_ref()
                    .map(|p| p.to_string_lossy().to_string())
                    .unwrap_or_default()
            {
                result.add_error(ValidationError::error(
                    "Template cannot include itself".to_string(),
                    "includes".to_string(),
                ));
            }
        }
    }

    /// Extract variable names from template content
    fn extract_template_variables(&self, content: &str) -> HashSet<String> {
        let mut variables = HashSet::new();

        // Extract Tera-style variables: {{ variable_name }}
        let re = regex::Regex::new(r"\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\}\}").unwrap();
        for cap in re.captures_iter(content) {
            if let Some(var_name) = cap.get(1) {
                variables.insert(var_name.as_str().to_string());
            }
        }

        // Extract variables from filters: {{ var | filter }}
        let re_filter = regex::Regex::new(r"\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\|").unwrap();
        for cap in re_filter.captures_iter(content) {
            if let Some(var_name) = cap.get(1) {
                variables.insert(var_name.as_str().to_string());
            }
        }

        variables
    }

    /// Check if variable name is valid
    fn is_valid_variable_name(name: &str) -> bool {
        if name.is_empty() {
            return false;
        }

        // Must start with letter or underscore
        let first_char = name.chars().next().unwrap();
        if !first_char.is_alphabetic() && first_char != '_' {
            return false;
        }

        // Rest must be alphanumeric or underscore
        name.chars().all(|c| c.is_alphanumeric() || c == '_')
    }

    /// Add custom validation rule
    pub fn add_custom_rule<F>(&mut self, rule: F)
    where
        F: Fn(&Template) -> Vec<ValidationError> + Send + Sync + 'static,
    {
        self.config.custom_rules.push(Box::new(rule));
    }

    /// Get validation statistics
    pub fn get_stats(&self) -> &ValidationStats {
        &self.stats
    }

    /// Reset validation statistics
    pub fn reset_stats(&mut self) {
        self.stats = ValidationStats::default();
    }

    /// Update validator configuration
    pub fn update_config(&mut self, config: ValidatorConfig) {
        self.config = config;
    }

    /// Get current configuration
    pub fn get_config(&self) -> &ValidatorConfig {
        &self.config
    }

    /// Validate multiple templates concurrently
    pub async fn validate_templates(
        &mut self,
        templates: Vec<&Template>,
    ) -> Result<Vec<ValidationResult>> {
        let mut results = Vec::new();

        for template in templates {
            let result = self.validate_template(template).await?;
            results.push(result);
        }

        Ok(results)
    }

    /// Export validation report as JSON
    pub fn export_validation_report(&self, results: &[ValidationResult]) -> Result<String> {
        let mut report = serde_json::Map::new();

        // Summary statistics
        let total_errors: usize = results.iter().map(|r| r.error_count()).sum();
        let total_warnings: usize = results.iter().map(|r| r.warning_count()).sum();
        let total_valid = results.iter().filter(|r| r.is_valid).count();

        report.insert(
            "summary".to_string(),
            serde_json::json!({
                "total_templates": results.len(),
                "valid_templates": total_valid,
                "total_errors": total_errors,
                "total_warnings": total_warnings,
                "validation_stats": self.stats
            }),
        );

        // Detailed results
        let detailed_results: Vec<serde_json::Value> = results
            .iter()
            .enumerate()
            .map(|(i, result)| {
                serde_json::json!({
                    "template_index": i,
                    "is_valid": result.is_valid,
                    "error_count": result.error_count(),
                    "warning_count": result.warning_count(),
                    "variables_found": result.variables_found,
                    "includes_found": result.includes_found,
                    "errors": result.errors
                })
            })
            .collect();

        report.insert(
            "results".to_string(),
            serde_json::Value::Array(detailed_results),
        );

        serde_json::to_string_pretty(&report).map_err(|e| AutorunError::Json(e))
    }
}

impl Default for TemplateValidator {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::templates::{Template, TemplateMetadata};
    use std::time::SystemTime;

    fn create_test_template(content: &str) -> Template {
        Template {
            metadata: TemplateMetadata {
                name: "test".to_string(),
                variables: [("name".to_string(), "User name".to_string())]
                    .iter()
                    .cloned()
                    .collect(),
                ..Default::default()
            },
            content: content.to_string(),
            source_path: None,
            hash: "test_hash".to_string(),
            modified: SystemTime::now(),
        }
    }

    #[tokio::test]
    async fn test_valid_template() {
        let mut validator = TemplateValidator::new();
        let template = create_test_template("Hello {{ name }}!");

        let result = validator.validate_template(&template).await.unwrap();
        assert!(result.is_valid);
        assert!(result.variables_found.contains("name"));
    }

    #[tokio::test]
    async fn test_syntax_error() {
        let mut validator = TemplateValidator::new();
        let template = create_test_template("Hello {{ name }!");

        let result = validator.validate_template(&template).await.unwrap();
        assert!(!result.is_valid);
        assert!(result.has_errors());
    }

    #[tokio::test]
    async fn test_undefined_variable_warning() {
        let mut validator = TemplateValidator::new();
        let template = create_test_template("Hello {{ undefined_var }}!");

        let result = validator.validate_template(&template).await.unwrap();
        assert!(result.has_warnings());
    }

    #[test]
    fn test_variable_name_validation() {
        assert!(TemplateValidator::is_valid_variable_name("valid_name"));
        assert!(TemplateValidator::is_valid_variable_name("_private"));
        assert!(TemplateValidator::is_valid_variable_name("name123"));

        assert!(!TemplateValidator::is_valid_variable_name(""));
        assert!(!TemplateValidator::is_valid_variable_name("123invalid"));
        assert!(!TemplateValidator::is_valid_variable_name("invalid-name"));
    }

    #[tokio::test]
    async fn test_custom_validation_rule() {
        let mut validator = TemplateValidator::new();

        // Add custom rule that requires templates to contain "Hello"
        validator.add_custom_rule(|template| {
            if !template.content.contains("Hello") {
                vec![ValidationError::error(
                    "Template must contain 'Hello'".to_string(),
                    "custom".to_string(),
                )]
            } else {
                vec![]
            }
        });

        let template = create_test_template("Goodbye {{ name }}!");
        let result = validator.validate_template(&template).await.unwrap();

        assert!(!result.is_valid);
        assert!(result.errors.iter().any(|e| e.category == "custom"));
    }
}
