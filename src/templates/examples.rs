//! Template engine usage examples and integration tests

use super::{Template<PERSON>ontext, TemplateEngine, TemplateProcessingOptions};
use crate::errors::Result;
use std::collections::HashMap;

/// Example demonstrating basic template processing
pub async fn basic_template_example() -> Result<()> {
    println!("=== Basic Template Example ===");

    // Create template engine
    let mut engine = TemplateEngine::new()?;

    // Create simple template content
    let template_content = r#"
---
name: "Hello Template"
description: "A simple hello world template"
variables:
  name: "User's name"
  greeting: "Greeting message"
---

{{ greeting }}, {{ name }}!

Today is {{ date }} and you are using {{ os }} on {{ arch }}.
Your username is {{ username }} and you're in {{ cwd }}.

Random UUID: {{ uuid }}
"#
    .to_string();

    // Create template context with user variables
    let mut context = TemplateContext::new();
    context
        .variables
        .insert("name".to_string(), "World".to_string());
    context
        .variables
        .insert("greeting".to_string(), "Hello".to_string());

    // Process template with default options
    let options = TemplateProcessingOptions {
        interactive: false, // Don't prompt for missing variables
        ..Default::default()
    };

    let result = engine
        .process_template_string(template_content, context, options)
        .await?;

    println!("Processed template:");
    println!("{}", result.content);
    println!(
        "Variables used: {:?}",
        result.variables_used.keys().collect::<Vec<_>>()
    );
    println!("Processing time: {:?}", result.processing_time);

    Ok(())
}

/// Example demonstrating template validation
pub async fn validation_example() -> Result<()> {
    println!("\n=== Template Validation Example ===");

    let mut engine = TemplateEngine::new()?;

    // Valid template
    let valid_template = r#"
---
name: "Valid Template"
variables:
  message: "A message"
---

Hello {{ message }}!
"#;

    // Invalid template (syntax error)
    let invalid_template = r#"
---
name: "Invalid Template"
variables:
  message: "A message"
---

Hello {{ message }!  <!-- Missing closing brace -->
"#;

    let context = TemplateContext::new();
    let options = TemplateProcessingOptions {
        validate: true,
        interactive: false,
        ..Default::default()
    };

    // Test valid template
    println!("Testing valid template...");
    match engine
        .process_template_string(valid_template.to_string(), context.clone(), options.clone())
        .await
    {
        Ok(_) => println!("✓ Valid template processed successfully"),
        Err(e) => println!("✗ Unexpected error: {}", e),
    }

    // Test invalid template
    println!("Testing invalid template...");
    match engine
        .process_template_string(invalid_template.to_string(), context, options)
        .await
    {
        Ok(_) => println!("✗ Invalid template should have failed"),
        Err(e) => println!("✓ Invalid template correctly failed: {}", e),
    }

    Ok(())
}

/// Example demonstrating cache functionality
pub async fn cache_example() -> Result<()> {
    println!("\n=== Cache Example ===");

    let mut engine = TemplateEngine::new()?;

    let template_content = r#"
---
name: "Cache Test"
variables:
  counter: "A counter value"
---

Cache test #{{ counter }}
Generated at {{ time }}
"#
    .to_string();

    let options = TemplateProcessingOptions {
        use_cache: true,
        interactive: false,
        ..Default::default()
    };

    // Process template multiple times
    for i in 1..=3 {
        let mut context = TemplateContext::new();
        context
            .variables
            .insert("counter".to_string(), i.to_string());

        let start = std::time::Instant::now();
        let _result = engine
            .process_template_string(template_content.clone(), context, options.clone())
            .await?;
        let duration = start.elapsed();

        println!("Processing #{}: {:?}", i, duration);
    }

    // Show cache statistics
    let stats = engine.get_stats();
    println!("Cache hits: {}", stats.cache_hits);
    println!("Cache misses: {}", stats.cache_misses);
    println!(
        "Hit rate: {:.2}%",
        (stats.cache_hits as f64 / (stats.cache_hits + stats.cache_misses) as f64) * 100.0
    );

    Ok(())
}

/// Example demonstrating variable resolution
pub async fn variable_resolution_example() -> Result<()> {
    println!("\n=== Variable Resolution Example ===");

    let mut engine = TemplateEngine::new()?;

    let template_content = r#"
---
name: "Variable Resolution Demo"
variables:
  app_name: "Application name"
  version: "Version number"
---

# {{ app_name }} v{{ version }}

## System Information
- Operating System: {{ os }}
- Architecture: {{ arch }}
- User: {{ username }}
- Home Directory: {{ home }}
- Current Directory: {{ cwd }}

## Time Information
- Date: {{ date }}
- Time: {{ time }}
- ISO DateTime: {{ iso_datetime }}
- Timestamp: {{ timestamp }}

## Unique Identifiers
- UUID: {{ uuid }}
- Short UUID: {{ uuid_short }}
- Random Number: {{ random_number }}
- Random Hex: {{ random_hex }}
"#
    .to_string();

    let mut context = TemplateContext::new();
    context.variables.insert(
        "app_name".to_string(),
        "AutoRun Template Engine".to_string(),
    );
    context
        .variables
        .insert("version".to_string(), "1.0.0".to_string());

    let options = TemplateProcessingOptions {
        include_system_vars: true,
        include_env_vars: false, // Don't include env vars for privacy
        interactive: false,
        ..Default::default()
    };

    let result = engine
        .process_template_string(template_content, context, options)
        .await?;

    println!("Generated content:");
    println!("{}", result.content);

    println!("\nAll variables used:");
    for (key, value) in result.variables_used {
        // Only show non-sensitive variables
        if !key.to_lowercase().contains("password") && !key.to_lowercase().contains("secret") {
            println!("  {}: {}", key, value);
        }
    }

    Ok(())
}

/// Run all template examples
pub async fn run_all_examples() -> Result<()> {
    println!("AutoRun Template Engine Examples\n");

    basic_template_example().await?;
    validation_example().await?;
    cache_example().await?;
    variable_resolution_example().await?;

    println!("\n=== All examples completed successfully! ===");
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_basic_template_processing() {
        let result = basic_template_example().await;
        assert!(result.is_ok(), "Basic template example should succeed");
    }

    #[tokio::test]
    async fn test_validation() {
        let result = validation_example().await;
        assert!(result.is_ok(), "Validation example should succeed");
    }

    #[tokio::test]
    async fn test_cache_functionality() {
        let result = cache_example().await;
        assert!(result.is_ok(), "Cache example should succeed");
    }

    #[tokio::test]
    async fn test_variable_resolution() {
        let result = variable_resolution_example().await;
        assert!(result.is_ok(), "Variable resolution example should succeed");
    }
}
