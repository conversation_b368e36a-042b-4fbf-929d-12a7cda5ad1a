//! Simple demonstration script to test template engine functionality
//! 
//! This can be run independently to verify the template system works

use super::{TemplateEngine, TemplateContext, TemplateProcessingOptions};

pub async fn run_template_demo() {
    println!("🔧 AutoRun Template Engine Demo");
    println!("================================\n");

    // Test 1: Basic template processing
    match test_basic_template().await {
        Ok(_) => println!("✅ Basic template processing: PASSED"),
        Err(e) => println!("❌ Basic template processing: FAILED - {}", e),
    }

    // Test 2: Variable resolution
    match test_variable_resolution().await {
        Ok(_) => println!("✅ Variable resolution: PASSED"),
        Err(e) => println!("❌ Variable resolution: FAILED - {}", e),
    }

    // Test 3: Cache functionality
    match test_cache_functionality().await {
        Ok(_) => println!("✅ Cache functionality: PASSED"),
        Err(e) => println!("❌ Cache functionality: FAILED - {}", e),
    }

    // Test 4: Template validation
    match test_validation().await {
        Ok(_) => println!("✅ Template validation: PASSED"),
        Err(e) => println!("❌ Template validation: FAILED - {}", e),
    }

    println!("\n🎉 Template Engine Demo Complete!");
}

async fn test_basic_template() -> Result<(), Box<dyn std::error::Error>> {
    let mut engine = TemplateEngine::new()?;
    
    let template_content = r#"
---
name: "Basic Test"
variables:
  greeting: "Greeting message"
  name: "Person's name"
---
{{ greeting }}, {{ name }}! Welcome to AutoRun Template Engine.
"#.to_string();

    let mut context = TemplateContext::new();
    context.variables.insert("greeting".to_string(), "Hello".to_string());
    context.variables.insert("name".to_string(), "Developer".to_string());

    let options = TemplateProcessingOptions {
        interactive: false,
        include_system_vars: false,
        ..Default::default()
    };

    let result = engine.process_template_string(template_content, context, options).await?;
    
    // Verify result contains expected content
    if result.content.contains("Hello, Developer!") {
        Ok(())
    } else {
        Err("Template output doesn't match expected content".into())
    }
}

async fn test_variable_resolution() -> Result<(), Box<dyn std::error::Error>> {
    let mut engine = TemplateEngine::new()?;
    
    let template_content = r#"
---
name: "Variable Test"
variables:
  app: "Application name"
---
App: {{ app }}
User: {{ username }}
Date: {{ date }}
OS: {{ os }}
"#.to_string();

    let mut context = TemplateContext::new();
    context.variables.insert("app".to_string(), "AutoRun".to_string());

    let options = TemplateProcessingOptions {
        interactive: false,
        include_system_vars: true,
        include_env_vars: false,
        ..Default::default()
    };

    let result = engine.process_template_string(template_content, context, options).await?;
    
    // Verify system variables were resolved
    if result.content.contains("AutoRun") && 
       result.variables_used.contains_key("username") &&
       result.variables_used.contains_key("date") &&
       result.variables_used.contains_key("os") {
        Ok(())
    } else {
        Err("System variables not properly resolved".into())
    }
}

async fn test_cache_functionality() -> Result<(), Box<dyn std::error::Error>> {
    let mut engine = TemplateEngine::new()?;
    
    let template_content = r#"
---
name: "Cache Test"
variables:
  value: "Test value"
---
Cached content: {{ value }}
"#.to_string();

    let mut context = TemplateContext::new();
    context.variables.insert("value".to_string(), "test123".to_string());

    let options = TemplateProcessingOptions {
        use_cache: true,
        interactive: false,
        include_system_vars: false,
        ..Default::default()
    };

    // Process template twice
    let _result1 = engine.process_template_string(template_content.clone(), context.clone(), options.clone()).await?;
    let _result2 = engine.process_template_string(template_content, context, options).await?;

    let stats = engine.get_stats();
    
    // Verify cache was used (should have 1 hit)
    if stats.cache_hits > 0 {
        Ok(())
    } else {
        Err("Cache not working properly".into())
    }
}

async fn test_validation() -> Result<(), Box<dyn std::error::Error>> {
    let mut engine = TemplateEngine::new()?;
    
    // Valid template
    let valid_template = r#"
---
name: "Valid"
variables:
  msg: "Message"
---
{{ msg }}
"#.to_string();

    // Invalid template (syntax error)
    let invalid_template = r#"
---
name: "Invalid"
variables:
  msg: "Message"
---
{{ msg }
"#.to_string();

    let context = TemplateContext::new();
    let options = TemplateProcessingOptions {
        validate: true,
        interactive: false,
        include_system_vars: false,
        ..Default::default()
    };

    // Valid template should work
    let valid_result = engine.process_template_string(valid_template, context.clone(), options.clone()).await;
    if valid_result.is_err() {
        return Err("Valid template failed validation".into());
    }

    // Invalid template should fail
    let invalid_result = engine.process_template_string(invalid_template, context, options).await;
    if invalid_result.is_ok() {
        return Err("Invalid template passed validation".into());
    }

    Ok(())
}

#[tokio::main]
async fn main() {
    run_template_demo().await;
}