//! Template Engine for variable substitution and custom commands
//!
//! Provides template processing capabilities for the action command system.

use crate::errors::{<PERSON>runError, Result};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

/// Simple template engine for variable substitution
pub struct TemplateEngine {
    variables: Arc<RwLock<HashMap<String, String>>>,
}

/// Template processing context
pub struct TemplateContext {
    pub variables: HashMap<String, String>,
}

impl TemplateEngine {
    /// Create a new template engine
    pub fn new() -> Result<Self> {
        Ok(Self {
            variables: Arc::new(RwLock::new(HashMap::new())),
        })
    }

    /// Process a template string with variable substitution
    pub async fn process_template(
        &self,
        template: &str,
        context: &TemplateContext,
    ) -> Result<String> {
        let mut result = template.to_string();
        
        // Simple variable substitution for $VAR and ${VAR} patterns
        for (key, value) in &context.variables {
            let patterns = [
                format!("${}", key),
                format!("${{{}}}", key),
            ];
            
            for pattern in &patterns {
                result = result.replace(pattern, value);
            }
        }
        
        // Add environment variables
        if result.contains("$ENV") {
            for (key, value) in std::env::vars() {
                let patterns = [
                    format!("$ENV_{}", key),
                    format!("${{ENV_{}}}", key),
                ];
                
                for pattern in &patterns {
                    result = result.replace(pattern, &value);
                }
            }
        }
        
        Ok(result)
    }

    /// Set a global variable
    pub async fn set_variable(&self, key: String, value: String) {
        let mut vars = self.variables.write().await;
        vars.insert(key, value);
    }

    /// Get a variable
    pub async fn get_variable(&self, key: &str) -> Option<String> {
        let vars = self.variables.read().await;
        vars.get(key).cloned()
    }
}

impl TemplateContext {
    /// Create a new template context
    pub fn new() -> Self {
        Self {
            variables: HashMap::new(),
        }
    }

    /// Add a variable to the context
    pub fn with_variable(mut self, key: String, value: String) -> Self {
        self.variables.insert(key, value);
        self
    }
}

impl Default for TemplateContext {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_simple_variable_substitution() {
        let engine = TemplateEngine::new().unwrap();
        let context = TemplateContext::new()
            .with_variable("name".to_string(), "World".to_string());

        let result = engine.process_template("Hello $name!", &context).await.unwrap();
        assert_eq!(result, "Hello World!");
    }

    #[tokio::test]
    async fn test_braced_variable_substitution() {
        let engine = TemplateEngine::new().unwrap();
        let context = TemplateContext::new()
            .with_variable("user".to_string(), "Alice".to_string());

        let result = engine.process_template("Hello ${user}!", &context).await.unwrap();
        assert_eq!(result, "Hello Alice!");
    }
}