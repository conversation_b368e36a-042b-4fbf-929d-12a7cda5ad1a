//! Runtime configuration management system
//! 
//! This module provides hot-reloadable configuration with validation,
//! atomic updates, and rollback capabilities.

pub mod runtime_config;
pub mod setting_validator;
pub mod config_persistence;
pub mod config_migration;

// Re-export main types
pub use runtime_config::{RuntimeConfig, ConfigState, ConfigMetadata, ValidationStatus};
pub use setting_validator::{SettingValidator, SettingValidationResult};
pub use config_persistence::{ConfigPersistence, ConfigFormat};
pub use config_migration::{ConfigMigration, MigrationPlan};

use crate::errors::Result;
use crate::config::Config;

/// Initialize the runtime configuration system
pub async fn initialize_runtime_config(base_config: Config) -> Result<RuntimeConfig> {
    let runtime_config = RuntimeConfig::new(base_config);
    
    // TODO: Load any existing configuration overrides
    // TODO: Apply any pending migrations
    // TODO: Validate the final configuration
    
    Ok(runtime_config)
}

/// Configuration change event for notifying components
#[derive(Debug, Clone)]
pub enum ConfigChangeEvent {
    /// Setting value changed
    SettingChanged {
        key: String,
        old_value: Option<String>,
        new_value: String,
    },
    
    /// Configuration imported
    ConfigImported {
        source: String,
        changes: u32,
    },
    
    /// Configuration reset
    ConfigReset,
    
    /// Configuration validation failed
    ValidationFailed {
        errors: Vec<String>,
    },
}

/// Configuration backup policy
#[derive(Debug, Clone)]
pub struct BackupPolicy {
    /// Maximum number of backups to keep
    pub max_backups: usize,
    
    /// Automatic backup triggers
    pub auto_backup_triggers: Vec<BackupTrigger>,
    
    /// Backup compression enabled
    pub compress_backups: bool,
}

/// Backup trigger conditions
#[derive(Debug, Clone)]
pub enum BackupTrigger {
    /// Backup before any change
    BeforeChange,
    
    /// Backup before import
    BeforeImport,
    
    /// Backup on schedule
    Scheduled(chrono::Duration),
    
    /// Backup on significant changes
    SignificantChange,
}

impl Default for BackupPolicy {
    fn default() -> Self {
        Self {
            max_backups: 10,
            auto_backup_triggers: vec![
                BackupTrigger::BeforeChange,
                BackupTrigger::BeforeImport,
            ],
            compress_backups: true,
        }
    }
}