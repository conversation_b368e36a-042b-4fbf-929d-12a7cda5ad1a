use crate::llm::{ModelCapability, ToolSupport};
use once_cell::sync::Lazy;
use std::collections::HashMap;

#[derive(Debug, Clone)]
pub struct ModelInfo {
    pub provider: String,
    pub canonical_name: String,
    pub capabilities: Vec<ModelCapability>,
    pub tool_support: ToolSupport,
}

#[derive(Debug, Clone)]
pub struct ProviderConfig {
    pub name: String,
    pub base_url: String,
    pub env_key: String,
    pub default_model: String,
    pub supports_streaming: bool,
    pub supports_tools: bool,
}

pub struct ConfigRegistry {
    model_mappings: HashMap<String, ModelInfo>,
    provider_configs: HashMap<String, ProviderConfig>,
}

impl ConfigRegistry {
    pub fn new() -> Self {
        let mut registry = Self {
            model_mappings: HashMap::new(),
            provider_configs: HashMap::new(),
        };
        registry.initialize_providers();
        registry.initialize_models();
        registry
    }

    fn initialize_providers(&mut self) {
        self.provider_configs.insert(
            "anthropic".to_string(),
            ProviderConfig {
                name: "anthropic".to_string(),
                base_url: "https://api.anthropic.com".to_string(),
                env_key: "ANTHROPIC_API_KEY".to_string(),
                default_model: "claude-3-5-sonnet-20241022".to_string(),
                supports_streaming: true,
                supports_tools: true,
            },
        );

        self.provider_configs.insert(
            "openrouter".to_string(),
            ProviderConfig {
                name: "openrouter".to_string(),
                base_url: "https://openrouter.ai/api/v1".to_string(),
                env_key: "OPENROUTER_API_KEY".to_string(),
                default_model: "openai/gpt-4o".to_string(),
                supports_streaming: true,
                supports_tools: true,
            },
        );

        self.provider_configs.insert(
            "openai".to_string(),
            ProviderConfig {
                name: "openai".to_string(),
                base_url: "https://api.openai.com/v1".to_string(),
                env_key: "OPENAI_API_KEY".to_string(),
                default_model: "gpt-4o".to_string(),
                supports_streaming: true,
                supports_tools: true,
            },
        );

        self.provider_configs.insert(
            "google".to_string(),
            ProviderConfig {
                name: "google".to_string(),
                base_url: "https://generativelanguage.googleapis.com".to_string(),
                env_key: "GOOGLE_API_KEY".to_string(),
                default_model: "gemini-pro".to_string(),
                supports_streaming: true,
                supports_tools: true,
            },
        );

        self.provider_configs.insert(
            "ollama".to_string(),
            ProviderConfig {
                name: "ollama".to_string(),
                base_url: "http://localhost:11434".to_string(),
                env_key: "OLLAMA_HOST".to_string(),
                default_model: "llama3".to_string(),
                supports_streaming: true,
                supports_tools: false,
            },
        );
    }

    fn initialize_models(&mut self) {
        // Anthropic models
        self.add_model(
            "claude-3-5-sonnet-20241022",
            ModelInfo {
                provider: "anthropic".to_string(),
                canonical_name: "claude-3-5-sonnet-20241022".to_string(),
                capabilities: vec![ModelCapability::Standard],
                tool_support: ToolSupport::Full,
            },
        );

        self.add_model(
            "claude-3-5-sonnet",
            ModelInfo {
                provider: "anthropic".to_string(),
                canonical_name: "claude-3-5-sonnet-20241022".to_string(),
                capabilities: vec![ModelCapability::Standard],
                tool_support: ToolSupport::Full,
            },
        );

        self.add_model(
            "claude-3-opus",
            ModelInfo {
                provider: "anthropic".to_string(),
                canonical_name: "claude-3-opus-20240229".to_string(),
                capabilities: vec![ModelCapability::Standard],
                tool_support: ToolSupport::Full,
            },
        );

        self.add_model(
            "claude-3-haiku",
            ModelInfo {
                provider: "anthropic".to_string(),
                canonical_name: "claude-3-haiku-20240307".to_string(),
                capabilities: vec![ModelCapability::Standard],
                tool_support: ToolSupport::Full,
            },
        );

        // OpenRouter models - comprehensive list with capabilities
        let openrouter_models = vec![
            // Google Gemini models
            (
                "google/gemini-2.0-flash-lite-001",
                vec![ModelCapability::Standard],
            ),
            (
                "google/gemini-2.0-flash-001",
                vec![ModelCapability::Standard],
            ),
            (
                "google/gemini-2.5-flash-preview",
                vec![ModelCapability::Standard, ModelCapability::Beta],
            ),
            (
                "google/gemini-2.5-flash-preview:thinking",
                vec![
                    ModelCapability::Standard,
                    ModelCapability::Beta,
                    ModelCapability::Thinking,
                ],
            ),
            (
                "google/gemini-2.5-flash-preview-05-20",
                vec![ModelCapability::Standard, ModelCapability::Beta],
            ),
            (
                "google/gemini-2.5-flash-preview-05-20:thinking",
                vec![
                    ModelCapability::Standard,
                    ModelCapability::Beta,
                    ModelCapability::Thinking,
                ],
            ),
            (
                "google/gemini-2.5-pro-preview",
                vec![ModelCapability::Standard, ModelCapability::Beta],
            ),
            ("google/gemini-pro-1.5", vec![ModelCapability::Standard]),
            // Anthropic Claude models via OpenRouter
            (
                "anthropic/claude-3.5-haiku",
                vec![ModelCapability::Standard],
            ),
            ("anthropic/claude-sonnet-4", vec![ModelCapability::Standard]),
            (
                "anthropic/claude-3.7-sonnet",
                vec![ModelCapability::Standard],
            ),
            (
                "anthropic/claude-3.5-sonnet",
                vec![ModelCapability::Standard],
            ),
            ("anthropic/claude-opus-4", vec![ModelCapability::Standard]),
            (
                "anthropic/claude-3.7-sonnet:thinking",
                vec![ModelCapability::Standard, ModelCapability::Thinking],
            ),
            (
                "anthropic/claude-3.7-sonnet:beta",
                vec![ModelCapability::Standard, ModelCapability::Beta],
            ),
            (
                "anthropic/claude-3.5-sonnet:beta",
                vec![ModelCapability::Standard, ModelCapability::Beta],
            ),
            // OpenAI models
            ("openai/gpt-4o", vec![ModelCapability::Standard]),
            (
                "openai/gpt-4o-mini",
                vec![ModelCapability::Standard, ModelCapability::Nano],
            ),
            (
                "openai/gpt-4o-mini-2024-07-18",
                vec![ModelCapability::Standard, ModelCapability::Nano],
            ),
            ("openai/gpt-4.1", vec![ModelCapability::Standard]),
            (
                "openai/gpt-4.1-mini",
                vec![ModelCapability::Standard, ModelCapability::Nano],
            ),
            (
                "openai/gpt-4.1-nano",
                vec![ModelCapability::Standard, ModelCapability::Nano],
            ),
            (
                "openai/codex-mini",
                vec![ModelCapability::Standard, ModelCapability::Nano],
            ),
            ("openai/o3", vec![ModelCapability::Standard]),
            (
                "openai/o3-mini",
                vec![ModelCapability::Standard, ModelCapability::Nano],
            ),
            (
                "openai/o3-mini-high",
                vec![
                    ModelCapability::Standard,
                    ModelCapability::Nano,
                    ModelCapability::High,
                ],
            ),
            (
                "openai/o4-mini",
                vec![ModelCapability::Standard, ModelCapability::Nano],
            ),
            (
                "openai/o4-mini-high",
                vec![
                    ModelCapability::Standard,
                    ModelCapability::Nano,
                    ModelCapability::High,
                ],
            ),
            // DeepSeek models
            (
                "deepseek/deepseek-chat-v3-0324:free",
                vec![ModelCapability::Standard, ModelCapability::Free],
            ),
            (
                "deepseek/deepseek-chat-v3-0324",
                vec![ModelCapability::Standard],
            ),
            ("deepseek/deepseek-chat", vec![ModelCapability::Standard]),
            (
                "deepseek/deepseek-r1-0528-qwen3-8b:free",
                vec![ModelCapability::Standard, ModelCapability::Free],
            ),
            ("deepseek/deepseek-r1", vec![ModelCapability::Standard]),
            // Meta LLaMA models
            (
                "meta-llama/llama-4-maverick:free",
                vec![ModelCapability::Standard, ModelCapability::Free],
            ),
            (
                "meta-llama/llama-4-maverick",
                vec![ModelCapability::Standard],
            ),
            (
                "meta-llama/llama-4-scout:free",
                vec![ModelCapability::Standard, ModelCapability::Free],
            ),
            ("meta-llama/llama-4-scout", vec![ModelCapability::Standard]),
            // Mistral models
            (
                "mistralai/mistral-medium-3",
                vec![ModelCapability::Standard],
            ),
            (
                "mistralai/devstral-small:free",
                vec![ModelCapability::Standard, ModelCapability::Free],
            ),
            ("mistralai/devstral-small", vec![ModelCapability::Standard]),
        ];

        for (model_name, capabilities) in openrouter_models {
            self.add_model(
                model_name,
                ModelInfo {
                    provider: "openrouter".to_string(),
                    canonical_name: model_name.to_string(),
                    capabilities,
                    tool_support: ToolSupport::Full,
                },
            );
        }

        // GPT models - backward compatibility mappings
        self.add_model(
            "gpt-4o",
            ModelInfo {
                provider: "openrouter".to_string(),
                canonical_name: "openai/gpt-4o".to_string(),
                capabilities: vec![ModelCapability::Standard],
                tool_support: ToolSupport::Full,
            },
        );

        self.add_model(
            "gpt-3.5-turbo",
            ModelInfo {
                provider: "openrouter".to_string(),
                canonical_name: "openai/gpt-3.5-turbo".to_string(),
                capabilities: vec![ModelCapability::Standard],
                tool_support: ToolSupport::Full,
            },
        );

        self.add_model(
            "gpt-4",
            ModelInfo {
                provider: "openrouter".to_string(),
                canonical_name: "openai/gpt-4".to_string(),
                capabilities: vec![ModelCapability::Standard],
                tool_support: ToolSupport::Full,
            },
        );
    }

    fn add_model(&mut self, alias: &str, info: ModelInfo) {
        self.model_mappings.insert(alias.to_string(), info);
    }

    pub fn get_model_info(&self, model: &str) -> Option<&ModelInfo> {
        self.model_mappings.get(model)
    }

    pub fn get_provider_config(&self, provider: &str) -> Option<&ProviderConfig> {
        self.provider_configs.get(provider)
    }

    pub fn parse_model_string(&self, model: &str) -> (String, String) {
        // Check if it's a known model alias
        if let Some(info) = self.get_model_info(model) {
            return (info.provider.clone(), info.canonical_name.clone());
        }

        // Handle explicit provider prefix
        if let Some((prefix, suffix)) = model.split_once('/') {
            match prefix {
                "openrouter" => return ("openrouter".to_string(), suffix.to_string()),
                "anthropic" => return ("anthropic".to_string(), suffix.to_string()),
                // OpenRouter models in format "provider/model" (e.g., "openai/gpt-4o")
                _ => return ("openrouter".to_string(), model.to_string()),
            }
        }

        // Pattern-based fallback for unknown models
        match model {
            m if m.starts_with("claude") => ("anthropic".to_string(), model.to_string()),
            m if m.starts_with("gpt") => ("openrouter".to_string(), format!("openai/{}", model)),
            _ => ("anthropic".to_string(), model.to_string()),
        }
    }

    pub fn detect_provider_and_key(&self) -> (String, Option<String>) {
        // Priority: explicit env var > auto-detection > default
        if let Ok(provider) = std::env::var("AUTORUN_LLM_PROVIDER") {
            if let Some(config) = self.get_provider_config(&provider) {
                let api_key = std::env::var(&config.env_key).ok();
                return (provider, api_key);
            }
        }

        // Auto-detect based on available API keys
        for (provider_name, config) in &self.provider_configs {
            if let Ok(key) = std::env::var(&config.env_key) {
                if !key.is_empty() {
                    return (provider_name.clone(), Some(key));
                }
            }
        }

        // Fallback to anthropic
        ("anthropic".to_string(), None)
    }

    pub fn validate_config(&self, provider: &str, model: &str) -> Result<(), String> {
        // Check if provider is valid
        if !self.provider_configs.contains_key(provider) {
            return Err(format!("Unknown provider: {}", provider));
        }

        // Optionally validate model compatibility
        if let Some(model_info) = self.get_model_info(model) {
            if model_info.provider != provider && provider != "openrouter" {
                return Err(format!(
                    "Model '{}' is not compatible with provider '{}'. Expected provider: '{}'",
                    model, provider, model_info.provider
                ));
            }
        }

        Ok(())
    }

    pub fn get_default_model(&self, provider: &str) -> Option<String> {
        self.get_provider_config(provider)
            .map(|config| config.default_model.clone())
    }

    pub fn get_base_url(&self, provider: &str) -> Option<String> {
        self.get_provider_config(provider)
            .map(|config| config.base_url.clone())
    }

    pub fn list_providers(&self) -> Vec<&str> {
        self.provider_configs.keys().map(|s| s.as_str()).collect()
    }

    pub fn list_models_for_provider(&self, provider: &str) -> Vec<&str> {
        self.model_mappings
            .iter()
            .filter(|(_, info)| info.provider == provider)
            .map(|(alias, _)| alias.as_str())
            .collect()
    }
}

// Global singleton instance
pub static REGISTRY: Lazy<ConfigRegistry> = Lazy::new(ConfigRegistry::new);

// Convenience functions for global access
pub fn parse_model_string(model: &str) -> (String, String) {
    REGISTRY.parse_model_string(model)
}

pub fn detect_provider_and_key() -> (String, Option<String>) {
    REGISTRY.detect_provider_and_key()
}

pub fn get_model_info(model: &str) -> Option<&'static ModelInfo> {
    REGISTRY.get_model_info(model)
}

pub fn get_provider_config(provider: &str) -> Option<&'static ProviderConfig> {
    REGISTRY.get_provider_config(provider)
}

pub fn get_default_model(provider: &str) -> Option<String> {
    REGISTRY.get_default_model(provider)
}

pub fn get_base_url(provider: &str) -> Option<String> {
    REGISTRY.get_base_url(provider)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_model_string() {
        let registry = ConfigRegistry::new();

        // Test known model aliases
        assert_eq!(
            registry.parse_model_string("claude-3-5-sonnet"),
            (
                "anthropic".to_string(),
                "claude-3-5-sonnet-20241022".to_string()
            )
        );

        // Test explicit provider prefix
        assert_eq!(
            registry.parse_model_string("openrouter/anthropic/claude-3-sonnet"),
            (
                "openrouter".to_string(),
                "anthropic/claude-3-sonnet".to_string()
            )
        );

        // Test OpenRouter format
        assert_eq!(
            registry.parse_model_string("openai/gpt-4o"),
            ("openrouter".to_string(), "openai/gpt-4o".to_string())
        );

        // Test backward compatibility
        assert_eq!(
            registry.parse_model_string("gpt-4o"),
            ("openrouter".to_string(), "openai/gpt-4o".to_string())
        );
    }

    #[test]
    fn test_validate_config() {
        let registry = ConfigRegistry::new();

        // Valid configurations
        assert!(registry
            .validate_config("anthropic", "claude-3-5-sonnet")
            .is_ok());
        assert!(registry
            .validate_config("openrouter", "openai/gpt-4o")
            .is_ok());

        // Invalid provider
        assert!(registry.validate_config("invalid", "some-model").is_err());
    }

    #[test]
    fn test_get_default_model() {
        let registry = ConfigRegistry::new();

        assert_eq!(
            registry.get_default_model("anthropic"),
            Some("claude-3-5-sonnet-20241022".to_string())
        );
        assert_eq!(
            registry.get_default_model("openrouter"),
            Some("openai/gpt-4o".to_string())
        );
    }

    #[test]
    fn test_model_capabilities() {
        let registry = ConfigRegistry::new();

        // Test thinking model
        let thinking_model = registry
            .get_model_info("google/gemini-2.5-flash-preview:thinking")
            .unwrap();
        assert!(thinking_model
            .capabilities
            .contains(&ModelCapability::Thinking));
        assert!(thinking_model.capabilities.contains(&ModelCapability::Beta));

        // Test free model
        let free_model = registry
            .get_model_info("deepseek/deepseek-chat-v3-0324:free")
            .unwrap();
        assert!(free_model.capabilities.contains(&ModelCapability::Free));
    }
}
