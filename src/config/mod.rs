// Config module - handles application configuration

pub mod registry;

use crate::errors::Result;
use crate::llm::ProviderFactory;
use directories::ProjectDirs;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use tokio::fs;

/// Provider-specific configuration settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ProviderSpecificConfig {
    /// Anthropic-specific configuration
    Anthropic(AnthropicProviderConfig),
    /// OpenRouter-specific configuration  
    OpenRouter(OpenRouterProviderConfig),
    /// OpenAI-specific configuration
    OpenAI(OpenAIProviderConfig),
    /// Custom provider configuration
    Custom(CustomProviderConfig),
}

/// Anthropic provider configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnthropicProviderConfig {
    /// Custom headers for requests
    #[serde(default)]
    pub custom_headers: Option<std::collections::HashMap<String, String>>,
    /// Request timeout in seconds
    #[serde(default)]
    pub timeout_seconds: Option<u64>,
    /// Enable streaming responses
    #[serde(default)]
    pub enable_streaming: Option<bool>,
}

/// OpenRouter provider configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OpenRouterProviderConfig {
    /// Site URL for credits attribution
    #[serde(default)]
    pub site_url: Option<String>,
    /// Application name for credits attribution
    #[serde(default)]
    pub app_name: Option<String>,
    /// Custom headers for requests
    #[serde(default)]
    pub custom_headers: Option<std::collections::HashMap<String, String>>,
    /// Request timeout in seconds
    #[serde(default)]
    pub timeout_seconds: Option<u64>,
    /// Enable streaming responses
    #[serde(default)]
    pub enable_streaming: Option<bool>,
}

/// OpenAI provider configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OpenAIProviderConfig {
    /// Organization ID
    #[serde(default)]
    pub organization: Option<String>,
    /// Custom headers for requests
    #[serde(default)]
    pub custom_headers: Option<std::collections::HashMap<String, String>>,
    /// Request timeout in seconds
    #[serde(default)]
    pub timeout_seconds: Option<u64>,
    /// Enable streaming responses
    #[serde(default)]
    pub enable_streaming: Option<bool>,
}

/// Custom provider configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CustomProviderConfig {
    /// Custom headers for requests
    #[serde(default)]
    pub custom_headers: Option<std::collections::HashMap<String, String>>,
    /// Request timeout in seconds
    #[serde(default)]
    pub timeout_seconds: Option<u64>,
    /// Enable streaming responses
    #[serde(default)]
    pub enable_streaming: Option<bool>,
    /// Authentication method
    #[serde(default)]
    pub auth_method: Option<String>,
    /// Additional provider-specific settings
    #[serde(default)]
    pub additional_settings: Option<std::collections::HashMap<String, serde_json::Value>>,
}

/// Rate limiting configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitConfig {
    /// Maximum requests per minute
    #[serde(default)]
    pub requests_per_minute: Option<u32>,
    /// Maximum requests per hour
    #[serde(default)]
    pub requests_per_hour: Option<u32>,
    /// Maximum requests per day
    #[serde(default)]
    pub requests_per_day: Option<u32>,
    /// Backoff strategy for rate limiting
    #[serde(default)]
    pub backoff_strategy: Option<BackoffStrategy>,
    /// Enable rate limiting
    #[serde(default = "default_true")]
    pub enabled: bool,
}

/// Backoff strategy for rate limiting
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BackoffStrategy {
    /// Linear backoff (constant delay)
    Linear { delay_seconds: u64 },
    /// Exponential backoff (delay doubles each time)
    Exponential { initial_delay_seconds: u64, max_delay_seconds: u64 },
    /// Fixed delay between retries
    Fixed { delay_seconds: u64 },
}

/// Health check configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheckConfig {
    /// Enable health checks
    #[serde(default = "default_true")]
    pub enabled: bool,
    /// Health check interval in seconds
    #[serde(default)]
    pub interval_seconds: Option<u64>,
    /// Health check timeout in seconds
    #[serde(default)]
    pub timeout_seconds: Option<u64>,
    /// Endpoint to use for health checks
    #[serde(default)]
    pub endpoint: Option<String>,
    /// Number of consecutive failures before marking unhealthy
    #[serde(default)]
    pub failure_threshold: Option<u32>,
}

/// API key source configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ApiKeySource {
    /// Load from environment variable
    Environment { var_name: String },
    /// Load from file
    File { path: String },
    /// Load from configuration
    Config { key: String },
    /// Load from system keychain/credential store
    KeychainService { service_name: String },
}

/// Helper function for serde default = true
fn default_true() -> bool {
    true
}

// Default implementations for new configuration structures

impl Default for ProviderSpecificConfig {
    fn default() -> Self {
        ProviderSpecificConfig::Anthropic(AnthropicProviderConfig::default())
    }
}

impl Default for AnthropicProviderConfig {
    fn default() -> Self {
        Self {
            custom_headers: None,
            timeout_seconds: Some(30),
            enable_streaming: Some(true),
        }
    }
}

impl Default for OpenRouterProviderConfig {
    fn default() -> Self {
        Self {
            site_url: None,
            app_name: Some("AutoRun".to_string()),
            custom_headers: None,
            timeout_seconds: Some(30),
            enable_streaming: Some(true),
        }
    }
}

impl Default for OpenAIProviderConfig {
    fn default() -> Self {
        Self {
            organization: None,
            custom_headers: None,
            timeout_seconds: Some(30),
            enable_streaming: Some(true),
        }
    }
}

impl Default for CustomProviderConfig {
    fn default() -> Self {
        Self {
            custom_headers: None,
            timeout_seconds: Some(30),
            enable_streaming: Some(true),
            auth_method: Some("Bearer".to_string()),
            additional_settings: None,
        }
    }
}

impl Default for RateLimitConfig {
    fn default() -> Self {
        Self {
            requests_per_minute: Some(60),
            requests_per_hour: Some(1000),
            requests_per_day: None,
            backoff_strategy: Some(BackoffStrategy::Exponential {
                initial_delay_seconds: 1,
                max_delay_seconds: 60,
            }),
            enabled: true,
        }
    }
}

impl Default for BackoffStrategy {
    fn default() -> Self {
        BackoffStrategy::Exponential {
            initial_delay_seconds: 1,
            max_delay_seconds: 60,
        }
    }
}

impl Default for HealthCheckConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            interval_seconds: Some(300), // 5 minutes
            timeout_seconds: Some(10),
            endpoint: None, // Use provider's default health check endpoint
            failure_threshold: Some(3),
        }
    }
}

impl Default for ApiKeySource {
    fn default() -> Self {
        ApiKeySource::Environment {
            var_name: "API_KEY".to_string(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    pub llm: LLMConfig,
    pub ui: UIConfig,
    pub tools: ToolsConfig,
    pub context: Option<String>, // AUTORUN.md content
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMConfig {
    pub provider: String,
    pub api_key: Option<String>,
    pub base_url: Option<String>,
    pub model: String,
    pub temperature: Option<f32>,
    pub max_tokens: Option<u32>,
    
    // Phase 1: Enhanced configuration fields (all optional for backward compatibility)
    /// Provider-specific configuration settings
    #[serde(default)]
    pub provider_config: Option<ProviderSpecificConfig>,
    
    /// Rate limiting configuration
    #[serde(default)]
    pub rate_limits: Option<RateLimitConfig>,
    
    /// Health check configuration  
    #[serde(default)]
    pub health_check: Option<HealthCheckConfig>,
    
    /// Fallback providers to use if primary provider fails
    #[serde(default)]
    pub fallback_providers: Option<Vec<String>>,
    
    /// API key source priorities
    #[serde(default)]
    pub api_key_sources: Option<Vec<ApiKeySource>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UIConfig {
    pub theme: String,
    pub keybindings: HashMap<String, String>,
    pub show_line_numbers: bool,
    pub auto_save_sessions: bool,
    pub vim_mode: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolsConfig {
    pub enabled_tools: Vec<String>,
    pub mcp_servers: Vec<crate::mcp::McpServer>,
    pub default_permissions: PermissionLevel,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PermissionLevel {
    ReadOnly,
    WriteAllowed,
    ExecuteAllowed,
    FullAccess,
}

#[derive(Debug)]
pub struct ConfigManager {
    global_config_path: PathBuf,
    project_config_path: Option<PathBuf>,
    local_config_path: Option<PathBuf>,
    autorun_md_path: Option<PathBuf>,
}

impl ConfigManager {
    pub fn new(project_dir: Option<&Path>) -> Result<Self> {
        let project_dirs =
            ProjectDirs::from("com", "theaileverage", "autorun").ok_or_else(|| {
                crate::errors::AutorunError::ConfigError(
                    "Unable to determine config directory".to_string(),
                )
            })?;

        let global_config_path = project_dirs.config_dir().join("config.toml");

        let (project_config_path, local_config_path, autorun_md_path) =
            if let Some(dir) = project_dir {
                let project_config = dir.join(".autorun").join("config.toml");
                let local_config = dir.join("autorun.toml");
                let autorun_md = dir.join("AUTORUN.md");
                (Some(project_config), Some(local_config), Some(autorun_md))
            } else {
                (None, None, None)
            };

        Ok(Self {
            global_config_path,
            project_config_path,
            local_config_path,
            autorun_md_path,
        })
    }

    pub async fn load_config(&self) -> Result<Config> {
        let mut config = Config::default();

        // Load global config
        if self.global_config_path.exists() {
            if let Ok(global_config) = self.load_config_file(&self.global_config_path).await {
                config = self.merge_configs(config, global_config);
            }
        }

        // Load project config (.autorun/config.toml)
        if let Some(ref project_path) = self.project_config_path {
            if project_path.exists() {
                if let Ok(project_config) = self.load_config_file(project_path).await {
                    config = self.merge_configs(config, project_config);
                }
            }
        }

        // Load local config (autorun.toml)
        if let Some(ref local_path) = self.local_config_path {
            if local_path.exists() {
                if let Ok(local_config) = self.load_config_file(local_path).await {
                    config = self.merge_configs(config, local_config);
                }
            }
        }

        // Load AUTORUN.md context
        if let Some(ref autorun_md) = self.autorun_md_path {
            if autorun_md.exists() {
                if let Ok(context) = fs::read_to_string(autorun_md).await {
                    config.context = Some(context);
                }
            }
        }

        Ok(config)
    }

    async fn load_config_file(&self, path: &Path) -> Result<Config> {
        let content = fs::read_to_string(path).await.map_err(|e| {
            crate::errors::AutorunError::IoError(format!(
                "Failed to read config file {}: {}",
                path.display(),
                e
            ))
        })?;

        let config: Config = toml::from_str(&content).map_err(|e| {
            crate::errors::AutorunError::ConfigError(format!(
                "Failed to parse config file {}: {}",
                path.display(),
                e
            ))
        })?;

        Ok(config)
    }

    fn merge_configs(&self, base: Config, override_config: Config) -> Config {
        Config {
            llm: LLMConfig {
                provider: if override_config.llm.provider != base.llm.provider {
                    override_config.llm.provider
                } else {
                    base.llm.provider
                },
                api_key: override_config.llm.api_key.or(base.llm.api_key),
                base_url: override_config.llm.base_url.or(base.llm.base_url),
                model: if override_config.llm.model != base.llm.model {
                    override_config.llm.model
                } else {
                    base.llm.model
                },
                temperature: override_config.llm.temperature.or(base.llm.temperature),
                max_tokens: override_config.llm.max_tokens.or(base.llm.max_tokens),
                
                // Phase 1: Enhanced configuration fields
                provider_config: override_config.llm.provider_config.or(base.llm.provider_config),
                rate_limits: override_config.llm.rate_limits.or(base.llm.rate_limits),
                health_check: override_config.llm.health_check.or(base.llm.health_check),
                fallback_providers: override_config.llm.fallback_providers.or(base.llm.fallback_providers),
                api_key_sources: override_config.llm.api_key_sources.or(base.llm.api_key_sources),
            },
            ui: UIConfig {
                theme: if override_config.ui.theme != base.ui.theme {
                    override_config.ui.theme
                } else {
                    base.ui.theme
                },
                keybindings: {
                    let mut merged = base.ui.keybindings;
                    merged.extend(override_config.ui.keybindings);
                    merged
                },
                show_line_numbers: override_config.ui.show_line_numbers,
                auto_save_sessions: override_config.ui.auto_save_sessions,
                vim_mode: override_config.ui.vim_mode,
            },
            tools: ToolsConfig {
                enabled_tools: if !override_config.tools.enabled_tools.is_empty() {
                    override_config.tools.enabled_tools
                } else {
                    base.tools.enabled_tools
                },
                mcp_servers: if !override_config.tools.mcp_servers.is_empty() {
                    override_config.tools.mcp_servers
                } else {
                    base.tools.mcp_servers
                },
                default_permissions: override_config.tools.default_permissions,
            },
            context: override_config.context.or(base.context),
        }
    }

    pub async fn save_global_config(&self, config: &Config) -> Result<()> {
        if let Some(parent) = self.global_config_path.parent() {
            fs::create_dir_all(parent).await.map_err(|e| {
                crate::errors::AutorunError::IoError(format!(
                    "Failed to create config directory: {}",
                    e
                ))
            })?;
        }

        let config_toml = toml::to_string_pretty(config).map_err(|e| {
            crate::errors::AutorunError::SerializationError(format!(
                "Failed to serialize config: {}",
                e
            ))
        })?;

        fs::write(&self.global_config_path, config_toml)
            .await
            .map_err(|e| {
                crate::errors::AutorunError::IoError(format!("Failed to write config file: {}", e))
            })?;

        Ok(())
    }
}

impl Config {
    pub async fn load(project_dir: Option<&Path>) -> Result<Self> {
        let manager = ConfigManager::new(project_dir)?;
        manager.load_config().await
    }

    pub async fn load_with_override(
        project_dir: Option<&Path>,
        config_path: Option<&Path>,
    ) -> Result<Self> {
        let manager = ConfigManager::new(project_dir)?;
        let mut config = manager.load_config().await?;

        // If a specific config path is provided, load it and merge
        if let Some(path) = config_path {
            if path.exists() {
                let override_config = manager.load_config_file(path).await?;
                config = manager.merge_configs(config, override_config);
            } else {
                return Err(crate::errors::AutorunError::ConfigError(format!(
                    "Config file not found: {}",
                    path.display()
                )));
            }
        }

        Ok(config)
    }

    pub async fn save_global(&self) -> Result<()> {
        let manager = ConfigManager::new(None)?;
        manager.save_global_config(self).await
    }
}

/// Detect LLM provider and API key based on environment variables
/// Priority: explicit AUTORUN_LLM_PROVIDER > auto-detection > default
fn detect_provider_and_key() -> (String, Option<String>) {
    registry::detect_provider_and_key()
}

impl Default for Config {
    fn default() -> Self {
        let mut default_keybindings = HashMap::new();
        default_keybindings.insert("quit".to_string(), "q".to_string());
        default_keybindings.insert("help".to_string(), "?".to_string());
        default_keybindings.insert("clear".to_string(), "ctrl+l".to_string());
        default_keybindings.insert("submit".to_string(), "enter".to_string());

        // Use smart detection for provider and API key
        let (llm_provider, api_key) = detect_provider_and_key();

        // Set model based on provider - use registry defaults
        let model = match llm_provider.as_str() {
            "openrouter" => std::env::var("OPENROUTER_MODEL").unwrap_or_else(|_| {
                registry::get_default_model("openrouter")
                    .unwrap_or_else(|| "openai/gpt-4o".to_string())
            }),
            provider => registry::get_default_model(provider)
                .unwrap_or_else(|| "claude-3-5-sonnet-20241022".to_string()),
        };

        Self {
            llm: LLMConfig {
                provider: llm_provider,
                api_key,
                base_url: None, // Will be set by provider factory based on provider
                model,
                temperature: Some(0.7),
                max_tokens: Some(4096),
                
                // Phase 1: Enhanced configuration fields (optional for backward compatibility)
                provider_config: None,
                rate_limits: None,
                health_check: None,
                fallback_providers: None,
                api_key_sources: None,
            },
            ui: UIConfig {
                theme: "default".to_string(),
                keybindings: default_keybindings,
                show_line_numbers: true,
                auto_save_sessions: true,
                vim_mode: false,
            },
            tools: ToolsConfig {
                enabled_tools: vec![
                    "file_read".to_string(),
                    "file_write".to_string(),
                    "shell_execute".to_string(),
                ],
                mcp_servers: vec![],
                default_permissions: PermissionLevel::WriteAllowed,
            },
            context: None,
        }
    }
}

/// Parse a model string to determine provider and model name
/// Supports formats:
/// - "openrouter/model-name" -> ("openrouter", "model-name")  
/// - "anthropic/model-name" -> ("anthropic", "model-name")
/// - "claude-3.5-sonnet" -> ("anthropic", "claude-3.5-sonnet") [backward compatibility]
/// - "openai/gpt-4o" -> ("openrouter", "openai/gpt-4o") [OpenRouter format]
pub fn parse_model_string(model: &str) -> (String, String) {
    registry::parse_model_string(model)
}

impl LLMConfig {
    /// Validate that the provider is supported using the provider factory
    pub fn validate_provider(&self) -> Result<()> {
        let factory = ProviderFactory::new();
        factory.validate_provider(&self.provider)
    }

    /// Get the default base URL for a provider
    pub fn default_base_url(&self) -> String {
        registry::get_base_url(&self.provider)
            .unwrap_or_else(|| "https://api.anthropic.com".to_string())
    }

    /// Get the effective base URL (configured or default)
    pub fn effective_base_url(&self) -> String {
        self.base_url
            .clone()
            .unwrap_or_else(|| self.default_base_url())
    }

    /// Check if API key is configured for this provider
    pub fn has_api_key(&self) -> bool {
        self.api_key.is_some() && !self.api_key.as_ref().unwrap().is_empty()
    }
}

impl Default for PermissionLevel {
    fn default() -> Self {
        PermissionLevel::WriteAllowed
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_llm_config_validate_provider() {
        let config = LLMConfig {
            provider: "openrouter".to_string(),
            api_key: Some("test-key".to_string()),
            base_url: None,
            model: "openai/gpt-4o".to_string(),
            temperature: Some(0.7),
            max_tokens: Some(4096),
            provider_config: None,
            rate_limits: None,
            health_check: None,
            fallback_providers: None,
            api_key_sources: None,
        };

        assert!(config.validate_provider().is_ok());

        let invalid_config = LLMConfig {
            provider: "invalid-provider".to_string(),
            api_key: Some("test-key".to_string()),
            base_url: None,
            model: "some-model".to_string(),
            temperature: Some(0.7),
            max_tokens: Some(4096),
            provider_config: None,
            rate_limits: None,
            health_check: None,
            fallback_providers: None,
            api_key_sources: None,
        };

        assert!(invalid_config.validate_provider().is_err());
    }

    #[test]
    fn test_llm_config_default_base_url() {
        let anthropic_config = LLMConfig {
            provider: "anthropic".to_string(),
            api_key: None,
            base_url: None,
            model: "claude-3-5-sonnet".to_string(),
            temperature: None,
            max_tokens: None,
            provider_config: None,
            rate_limits: None,
            health_check: None,
            fallback_providers: None,
            api_key_sources: None,
        };

        assert_eq!(
            anthropic_config.default_base_url(),
            "https://api.anthropic.com"
        );

        let openrouter_config = LLMConfig {
            provider: "openrouter".to_string(),
            api_key: None,
            base_url: None,
            model: "openai/gpt-4o".to_string(),
            temperature: None,
            max_tokens: None,
            provider_config: None,
            rate_limits: None,
            health_check: None,
            fallback_providers: None,
            api_key_sources: None,
        };

        assert_eq!(
            openrouter_config.default_base_url(),
            "https://openrouter.ai/api/v1"
        );
    }

    #[test]
    fn test_llm_config_effective_base_url() {
        let config_with_custom_url = LLMConfig {
            provider: "openrouter".to_string(),
            api_key: None,
            base_url: Some("https://custom.example.com".to_string()),
            model: "openai/gpt-4o".to_string(),
            temperature: None,
            max_tokens: None,
            provider_config: None,
            rate_limits: None,
            health_check: None,
            fallback_providers: None,
            api_key_sources: None,
        };

        assert_eq!(
            config_with_custom_url.effective_base_url(),
            "https://custom.example.com"
        );

        let config_without_url = LLMConfig {
            provider: "openrouter".to_string(),
            api_key: None,
            base_url: None,
            model: "openai/gpt-4o".to_string(),
            temperature: None,
            max_tokens: None,
            provider_config: None,
            rate_limits: None,
            health_check: None,
            fallback_providers: None,
            api_key_sources: None,
        };

        assert_eq!(
            config_without_url.effective_base_url(),
            "https://openrouter.ai/api/v1"
        );
    }

    #[test]
    fn test_llm_config_has_api_key() {
        let config_with_key = LLMConfig {
            provider: "openrouter".to_string(),
            api_key: Some("test-key".to_string()),
            base_url: None,
            model: "openai/gpt-4o".to_string(),
            temperature: None,
            max_tokens: None,
            provider_config: None,
            rate_limits: None,
            health_check: None,
            fallback_providers: None,
            api_key_sources: None,
        };

        assert!(config_with_key.has_api_key());

        let config_without_key = LLMConfig {
            provider: "openrouter".to_string(),
            api_key: None,
            base_url: None,
            model: "openai/gpt-4o".to_string(),
            temperature: None,
            max_tokens: None,
            provider_config: None,
            rate_limits: None,
            health_check: None,
            fallback_providers: None,
            api_key_sources: None,
        };

        assert!(!config_without_key.has_api_key());

        let config_with_empty_key = LLMConfig {
            provider: "openrouter".to_string(),
            api_key: Some("".to_string()),
            base_url: None,
            model: "openai/gpt-4o".to_string(),
            temperature: None,
            max_tokens: None,
            provider_config: None,
            rate_limits: None,
            health_check: None,
            fallback_providers: None,
            api_key_sources: None,
        };

        assert!(!config_with_empty_key.has_api_key());
    }

    #[test]
    fn test_config_environment_variables() {
        // Test OpenRouter configuration via environment variables
        std::env::set_var("AUTORUN_LLM_PROVIDER", "openrouter");
        std::env::set_var("OPENROUTER_API_KEY", "test-openrouter-key");
        std::env::set_var("OPENROUTER_MODEL", "anthropic/claude-3-sonnet");

        let config = Config::default();

        assert_eq!(config.llm.provider, "openrouter");
        assert_eq!(config.llm.api_key, Some("test-openrouter-key".to_string()));
        assert_eq!(config.llm.model, "anthropic/claude-3-sonnet");
        assert_eq!(
            config.llm.effective_base_url(),
            "https://openrouter.ai/api/v1"
        );

        // Clean up environment variables
        std::env::remove_var("AUTORUN_LLM_PROVIDER");
        std::env::remove_var("OPENROUTER_API_KEY");
        std::env::remove_var("OPENROUTER_MODEL");

        // Test fallback when no OPENROUTER_API_KEY is present
        std::env::remove_var("OPENROUTER_API_KEY");
        let default_config = Config::default();
        assert_eq!(default_config.llm.provider, "anthropic");
        assert_eq!(
            default_config.llm.effective_base_url(),
            "https://api.anthropic.com"
        );
    }

    #[test]
    fn test_smart_provider_detection() {
        // Test with OPENROUTER_API_KEY only
        std::env::set_var("OPENROUTER_API_KEY", "test-openrouter-key");
        std::env::remove_var("ANTHROPIC_API_KEY");
        std::env::remove_var("AUTORUN_LLM_PROVIDER");

        let config = Config::default();
        assert_eq!(config.llm.provider, "openrouter");
        assert_eq!(config.llm.api_key, Some("test-openrouter-key".to_string()));

        // Test with ANTHROPIC_API_KEY only
        std::env::remove_var("OPENROUTER_API_KEY");
        std::env::set_var("ANTHROPIC_API_KEY", "test-anthropic-key");

        let config = Config::default();
        assert_eq!(config.llm.provider, "anthropic");
        assert_eq!(config.llm.api_key, Some("test-anthropic-key".to_string()));

        // Test with both keys present (OpenRouter takes precedence)
        std::env::set_var("OPENROUTER_API_KEY", "test-openrouter-key");
        std::env::set_var("ANTHROPIC_API_KEY", "test-anthropic-key");

        let config = Config::default();
        assert_eq!(config.llm.provider, "openrouter");
        assert_eq!(config.llm.api_key, Some("test-openrouter-key".to_string()));

        // Test with explicit AUTORUN_LLM_PROVIDER override
        std::env::set_var("AUTORUN_LLM_PROVIDER", "anthropic");
        std::env::set_var("OPENROUTER_API_KEY", "should-be-ignored");

        let config = Config::default();
        assert_eq!(config.llm.provider, "anthropic");
        assert_eq!(config.llm.api_key, Some("test-anthropic-key".to_string()));

        // Test with no keys present (fallback to default)
        std::env::remove_var("OPENROUTER_API_KEY");
        std::env::remove_var("ANTHROPIC_API_KEY");
        std::env::remove_var("AUTORUN_LLM_PROVIDER");

        let config = Config::default();
        assert_eq!(config.llm.provider, "anthropic");
        assert_eq!(config.llm.api_key, None);

        // Cleanup all environment variables
        std::env::remove_var("OPENROUTER_API_KEY");
        std::env::remove_var("ANTHROPIC_API_KEY");
        std::env::remove_var("AUTORUN_LLM_PROVIDER");
    }

    #[test]
    fn test_parse_model_string() {
        // Test OpenRouter explicit format
        assert_eq!(
            parse_model_string("openrouter/anthropic/claude-3-sonnet"),
            (
                "openrouter".to_string(),
                "anthropic/claude-3-sonnet".to_string()
            )
        );

        // Test Anthropic explicit format
        assert_eq!(
            parse_model_string("anthropic/claude-3.5-sonnet"),
            ("anthropic".to_string(), "claude-3.5-sonnet".to_string())
        );

        // Test OpenRouter provider/model format (should default to openrouter)
        assert_eq!(
            parse_model_string("openai/gpt-4o"),
            ("openrouter".to_string(), "openai/gpt-4o".to_string())
        );
        assert_eq!(
            parse_model_string("google/gemini-pro"),
            ("openrouter".to_string(), "google/gemini-pro".to_string())
        );

        // Test backward compatibility - Claude models
        assert_eq!(
            parse_model_string("claude-3.5-sonnet"),
            ("anthropic".to_string(), "claude-3.5-sonnet".to_string())
        );
        assert_eq!(
            parse_model_string("claude-3-opus"),
            ("anthropic".to_string(), "claude-3-opus".to_string())
        );

        // Test backward compatibility - GPT models (should go to OpenRouter with openai/ prefix)
        assert_eq!(
            parse_model_string("gpt-4o"),
            ("openrouter".to_string(), "openai/gpt-4o".to_string())
        );
        assert_eq!(
            parse_model_string("gpt-3.5-turbo"),
            ("openrouter".to_string(), "openai/gpt-3.5-turbo".to_string())
        );

        // Test unknown models (should default to anthropic)
        assert_eq!(
            parse_model_string("some-unknown-model"),
            ("anthropic".to_string(), "some-unknown-model".to_string())
        );
    }
}
