use anyhow::Result;
use chrono::DateTime;
use colored::*;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::collections::HashMap;
use std::fs::File;
use std::io::{<PERSON><PERSON>R<PERSON>, BufReader, Seek, SeekFrom};
use std::path::{Path, PathBuf};
use std::time::Duration;
use tokio::time::sleep;

#[derive(Debug, Deserialize, Serialize)]
struct LogEntry {
    timestamp: String,
    level: String,
    fields: LogFields,
    target: String,
    filename: String,
    line_number: u32,
    #[serde(rename = "threadId")]
    thread_id: Option<String>,
}

#[derive(Debug, Deserialize, Serialize)]
struct LogFields {
    message: String,
}

struct LogFile {
    path: PathBuf,
    reader: BufReader<File>,
    position: u64,
}

impl LogFile {
    fn new(path: PathBuf) -> Result<Self> {
        let file = File::open(&path)?;
        let mut reader = BufReader::new(file);

        // Start reading from the end of the file to only show new entries
        let position = reader.seek(SeekFrom::End(0))?;

        Ok(LogFile {
            path,
            reader,
            position,
        })
    }

    fn read_new_lines(&mut self) -> Result<Vec<String>> {
        // Check if file has grown
        let current_pos = self.reader.seek(SeekFrom::Current(0))?;
        let file_size = self.reader.seek(SeekFrom::End(0))?;

        if file_size <= current_pos {
            return Ok(Vec::new());
        }

        // Go back to where we were
        self.reader.seek(SeekFrom::Start(self.position))?;

        let mut lines = Vec::new();
        let mut line = String::new();

        while self.reader.read_line(&mut line)? > 0 {
            if !line.trim().is_empty() {
                lines.push(line.trim().to_string());
            }
            line.clear();
        }

        // Update position
        self.position = self.reader.seek(SeekFrom::Current(0))?;

        Ok(lines)
    }
}

struct LogViewer {
    log_files: HashMap<PathBuf, LogFile>,
    logs_dir: PathBuf,
    show_debug: bool,
    show_http_details: bool,
}

impl LogViewer {
    fn new(logs_dir: PathBuf, show_debug: bool, show_http_details: bool) -> Self {
        LogViewer {
            log_files: HashMap::new(),
            logs_dir,
            show_debug,
            show_http_details,
        }
    }

    fn discover_log_files(&mut self) -> Result<()> {
        if !self.logs_dir.exists() {
            println!(
                "{}",
                "Logs directory does not exist yet. Waiting for logs...".yellow()
            );
            return Ok(());
        }

        for entry in std::fs::read_dir(&self.logs_dir)? {
            let entry = entry?;
            let path = entry.path();

            if path.is_file() {
                if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
                    if file_name.starts_with("autorun-") && file_name.ends_with(".log") {
                        if !self.log_files.contains_key(&path) {
                            match LogFile::new(path.clone()) {
                                Ok(log_file) => {
                                    println!(
                                        "{} {}",
                                        "📁 Monitoring log file:".green().bold(),
                                        path.display().to_string().cyan()
                                    );
                                    self.log_files.insert(path, log_file);
                                }
                                Err(e) => {
                                    eprintln!(
                                        "{} {}: {}",
                                        "❌ Failed to open log file".red(),
                                        path.display(),
                                        e
                                    );
                                }
                            }
                        }
                    }
                }
            }
        }

        Ok(())
    }

    fn parse_log_entry(line: &str) -> Option<LogEntry> {
        serde_json::from_str::<LogEntry>(line).ok()
    }

    fn format_timestamp(&self, timestamp_str: &str) -> String {
        if let Ok(dt) = DateTime::parse_from_rfc3339(timestamp_str) {
            dt.format("%H:%M:%S%.3f").to_string().dimmed().to_string()
        } else {
            timestamp_str.dimmed().to_string()
        }
    }

    fn format_level(&self, level: &str) -> String {
        match level {
            "ERROR" => level.red().bold().to_string(),
            "WARN" => level.yellow().bold().to_string(),
            "INFO" => level.green().to_string(),
            "DEBUG" => level.blue().to_string(),
            "TRACE" => level.magenta().to_string(),
            _ => level.white().to_string(),
        }
    }

    fn format_target(&self, target: &str, _filename: &str, line_number: u32) -> String {
        let short_target = if target.starts_with("autorun") {
            target
                .strip_prefix("autorun")
                .unwrap_or(target)
                .trim_start_matches("::")
        } else {
            target
        };

        format!(
            "{}:{}",
            short_target.cyan(),
            line_number.to_string().dimmed()
        )
    }

    fn should_show_entry(&self, entry: &LogEntry) -> bool {
        // Filter debug logs if not requested
        if entry.level == "DEBUG" && !self.show_debug {
            return false;
        }

        // Filter HTTP details if not requested
        if !self.show_http_details {
            if entry.target.contains("reqwest")
                || entry.target.contains("hyper")
                || entry.target.contains("h2")
            {
                return false;
            }
        }

        true
    }

    fn format_message(&self, entry: &LogEntry) -> String {
        let message = &entry.fields.message;

        // Special formatting for specific message types
        if message.starts_with("Sending request to") {
            format!("🚀 {}", message.bright_blue())
        } else if message.starts_with("Received response from") {
            format!("📥 {}", message.bright_green())
        } else if message.contains("LLM response received") {
            format!("🤖 {}", message.bright_green())
        } else if message.starts_with("Making LLM API call") {
            format!("⚡ {}", message.bright_blue())
        } else if message.starts_with("Starting agent loop") {
            format!("🔄 {}", message.bright_magenta())
        } else if message.contains("User prompt:") {
            format!("💬 {}", message.bright_cyan())
        } else if message.contains("Request JSON:") {
            // Pretty format JSON if possible
            if let Some(json_start) = message.find('{') {
                let json_part = &message[json_start..];
                if let Ok(parsed) = serde_json::from_str::<Value>(json_part) {
                    if let Ok(pretty) = serde_json::to_string_pretty(&parsed) {
                        return format!(
                            "📋 Request JSON:\n{}",
                            pretty
                                .lines()
                                .map(|l| format!("    {}", l.bright_white()))
                                .collect::<Vec<_>>()
                                .join("\n")
                        );
                    }
                }
            }
            format!("📋 {}", message.white())
        } else if message.contains("content:") && entry.level == "DEBUG" {
            // Format content with proper indentation
            if message.len() > 100 {
                format!(
                    "📝 Content:\n    {}",
                    message
                        .lines()
                        .map(|l| format!("    {}", l.bright_white()))
                        .collect::<Vec<_>>()
                        .join("\n")
                )
            } else {
                format!("📝 {}", message.bright_white())
            }
        } else if entry.level == "ERROR" {
            format!("❌ {}", message.bright_red())
        } else if message.contains("usage:") || message.contains("tokens") {
            format!("📊 {}", message.bright_yellow())
        } else {
            message.white().to_string()
        }
    }

    fn display_log_entry(&self, entry: &LogEntry, _file_path: &Path) -> Result<()> {
        if !self.should_show_entry(entry) {
            return Ok(());
        }

        let timestamp = self.format_timestamp(&entry.timestamp);
        let level = self.format_level(&entry.level);
        let target = self.format_target(&entry.target, &entry.filename, entry.line_number);
        let message = self.format_message(entry);

        println!("{} {} {} {}", timestamp, level, target, message);

        // Add extra spacing for multi-line content
        if message.contains('\n') {
            println!();
        }

        Ok(())
    }

    fn read_new_entries(&mut self) -> Result<()> {
        let mut files_to_remove = Vec::new();
        let mut all_entries = Vec::new();

        // Collect all new entries first
        for (path, log_file) in &mut self.log_files {
            match log_file.read_new_lines() {
                Ok(lines) => {
                    for line in lines {
                        if let Some(entry) = Self::parse_log_entry(&line) {
                            all_entries.push((entry, path.clone()));
                        } else if !line.trim().is_empty() {
                            // Show unparseable lines as raw text
                            println!("{} {}", "📄".dimmed(), line.dimmed());
                        }
                    }
                }
                Err(e) => {
                    eprintln!(
                        "{} {}: {}",
                        "❌ Error reading log file".red(),
                        path.display(),
                        e
                    );
                    files_to_remove.push(path.clone());
                }
            }
        }

        // Display all entries
        for (entry, path) in all_entries {
            self.display_log_entry(&entry, &path)?;
        }

        // Remove files that couldn't be read
        for path in files_to_remove {
            self.log_files.remove(&path);
        }

        Ok(())
    }

    async fn start_monitoring(&mut self) -> Result<()> {
        println!(
            "{}",
            "🔍 AutoRun Log Viewer Starting...".bright_green().bold()
        );
        println!(
            "{}",
            "Monitoring logs directory for new AutoRun log files".cyan()
        );

        if self.show_debug {
            println!("{}", "📋 Debug mode: Showing DEBUG level logs".yellow());
        }

        if self.show_http_details {
            println!(
                "{}",
                "🌐 HTTP mode: Showing HTTP connection details".yellow()
            );
        }

        println!("{}", "─".repeat(80).dimmed());

        loop {
            // Discover new log files
            if let Err(e) = self.discover_log_files() {
                eprintln!("{} {}", "❌ Error discovering log files:".red(), e);
            }

            // Read new entries from existing files
            if let Err(e) = self.read_new_entries() {
                eprintln!("{} {}", "❌ Error reading log entries:".red(), e);
            }

            // Sleep briefly to avoid busy waiting
            sleep(Duration::from_millis(250)).await;
        }
    }
}

fn clean_log_files(logs_dir: &Path) -> Result<()> {
    if !logs_dir.exists() {
        println!(
            "{}",
            "Logs directory does not exist, nothing to clean.".yellow()
        );
        return Ok(());
    }

    let mut cleaned_count = 0;

    for entry in std::fs::read_dir(logs_dir)? {
        let entry = entry?;
        let path = entry.path();

        if path.is_file() {
            if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
                if file_name.ends_with(".log") {
                    match std::fs::remove_file(&path) {
                        Ok(()) => {
                            println!(
                                "{} {}",
                                "🗑️  Removed:".red(),
                                path.display().to_string().dimmed()
                            );
                            cleaned_count += 1;
                        }
                        Err(e) => {
                            eprintln!("{} {}: {}", "❌ Failed to remove".red(), path.display(), e);
                        }
                    }
                }
            }
        }
    }

    if cleaned_count > 0 {
        println!(
            "{} {} log files",
            "✅ Cleaned".green().bold(),
            cleaned_count
        );
    } else {
        println!("{}", "No log files found to clean.".yellow());
    }

    println!("{}", "─".repeat(80).dimmed());

    Ok(())
}

#[tokio::main]
async fn main() -> Result<()> {
    let args: Vec<String> = std::env::args().collect();

    let mut show_debug = false;
    let mut show_http_details = false;
    let mut clean_logs = false;
    let mut logs_dir = PathBuf::from("logs");

    // Parse command line arguments
    for arg in &args[1..] {
        match arg.as_str() {
            "--debug" | "-d" => show_debug = true,
            "--http" | "-h" => show_http_details = true,
            "--clean" | "-c" => clean_logs = true,
            "--help" => {
                println!("AutoRun Log Viewer");
                println!("Usage: log_viewer [OPTIONS]");
                println!();
                println!("OPTIONS:");
                println!("  --debug, -d     Show DEBUG level logs");
                println!("  --http, -h      Show HTTP connection details");
                println!("  --clean, -c     Clean existing *.log files before starting");
                println!("  --help          Show this help message");
                println!();
                println!("The viewer monitors the 'logs/' directory for autorun-*.log files");
                println!(
                    "and displays new log entries in real-time with human-readable formatting."
                );
                return Ok(());
            }
            _ => {
                if Path::new(arg).is_dir() {
                    logs_dir = PathBuf::from(arg);
                }
            }
        }
    }

    // Clean existing log files if requested
    if clean_logs {
        clean_log_files(&logs_dir)?;
    }

    let mut viewer = LogViewer::new(logs_dir, show_debug, show_http_details);
    viewer.start_monitoring().await
}
