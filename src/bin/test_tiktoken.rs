//! Test binary for TiktokenProvider functionality

use autorun::prompts::tokenization::{
    <PERSON><PERSON><PERSON><PERSON>Provider, TokenizerConfig, TokenizerFactory, TokenizerProvider,
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("Testing TiktokenProvider...");

    // Test creating a provider
    let provider = TiktokenProvider::new("gpt-4")?;
    println!("✓ Created TiktokenProvider for gpt-4");

    // Test token counting
    let config = TokenizerConfig::default();
    let text = "Hello, world! This is a test.";
    let token_count = provider.count_tokens(text, &config).await?;
    println!("✓ Token count for '{}': {}", text, token_count);

    // Test encoding/decoding
    let tokens = provider.encode(text, &config).await?;
    let decoded = provider.decode(&tokens, &config).await?;
    println!("✓ Encode/decode roundtrip: {}", text == decoded);

    // Test model info
    let info = provider.get_model_info("gpt-4").await?;
    println!(
        "✓ Model info - Max context: {}, Max output: {}",
        info.max_context_tokens, info.max_output_tokens
    );

    // Test factory
    let factory = TokenizerFactory::new();
    let factory_provider = factory.create_provider("gpt-4")?;
    let factory_count = factory_provider.count_tokens(text, &config).await?;
    println!("✓ Factory provider token count: {}", factory_count);

    // Test different models
    let models = ["gpt-4o", "gpt-3.5-turbo", "text-davinci-003"];
    for model in models {
        match TiktokenProvider::new(model) {
            Ok(p) => {
                let count = p.count_tokens("Test", &config).await?;
                println!("✓ {} - token count: {}", model, count);
            }
            Err(e) => {
                println!("✗ {} - failed: {}", model, e);
            }
        }
    }

    println!("All tests completed!");
    Ok(())
}
