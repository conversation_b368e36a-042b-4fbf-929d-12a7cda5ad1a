//! Prompt design architecture for AutoRun
//!
//! This module provides a comprehensive prompt management system inspired by Claude Code CLI,
//! featuring structured prompt templates, dynamic variable substitution, conversation history
//! management, and flexible prompt composition patterns.

pub mod context;
pub mod core;
pub mod error;
pub mod integration;
pub mod manager;
pub mod templates;
pub mod tokenization;

// Re-export commonly used types
pub use context::{ContextBuilder, PromptContext, VariableProvider};
pub use core::{Prompt, PromptMessage, PromptMetadata, PromptRole, PromptType};
pub use error::{PromptError, PromptResult};
pub use integration::{PromptIntegration, PromptStats};
pub use manager::{PromptManager, PromptRegistry};
pub use templates::{TemplateEngine, TemplateError};
pub use tokenization::{
    ModelTokenInfo, PaddingStrategy, TokenUsage, TokenizationError, TokenizationResult,
    TokenizerConfig, TokenizerFactory, TokenizerProvider, TruncationStrategy,
};

/// Version of the prompt system
pub const PROMPT_SYSTEM_VERSION: &str = "1.0.0";

/// Default prompt directories relative to project root
pub const DEFAULT_PROMPT_DIRS: &[&str] = &[
    "prompts/system",
    "prompts/user",
    "prompts/tools",
    "prompts/examples",
];

/// Constants for prompt template placeholders
pub mod constants {
    /// Environment context placeholder
    pub const ENV_CONTEXT: &str = "env_context";

    /// Tool descriptions placeholder
    pub const TOOL_DESCRIPTIONS: &str = "tool_descriptions";

    /// Conversation history placeholder
    pub const CONVERSATION_HISTORY: &str = "conversation_history";

    /// User input placeholder
    pub const USER_INPUT: &str = "user_input";

    /// Project context placeholder
    pub const PROJECT_CONTEXT: &str = "project_context";

    /// Code context placeholder
    pub const CODE_CONTEXT: &str = "code_context";

    /// Session context placeholder
    pub const SESSION_CONTEXT: &str = "session_context";

    /// Model capabilities placeholder
    pub const MODEL_CAPABILITIES: &str = "model_capabilities";
}
