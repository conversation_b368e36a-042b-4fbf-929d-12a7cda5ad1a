//! Context and variable management for prompt rendering

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

use crate::prompts::core::{PromptMessage, PromptRole};
use crate::prompts::error::{PromptError, PromptResult};

/// Context for prompt execution including variables and conversation history
#[derive(<PERSON>bu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct PromptContext {
    /// Variables for template substitution
    pub variables: HashMap<String, serde_json::Value>,
    /// Conversation history
    pub history: Vec<PromptMessage>,
    /// Environment information
    pub environment: EnvironmentContext,
    /// Session information
    pub session: SessionContext,
    /// Tool context
    pub tools: ToolContext,
    /// Project context
    pub project: ProjectContext,
    /// Model-specific context
    pub model: ModelContext,
    /// Custom context extensions
    pub custom: HashMap<String, serde_json::Value>,
}

impl Default for PromptContext {
    fn default() -> Self {
        Self::new()
    }
}

impl PromptContext {
    /// Create a new empty context
    pub fn new() -> Self {
        Self {
            variables: HashMap::new(),
            history: Vec::new(),
            environment: EnvironmentContext::default(),
            session: SessionContext::default(),
            tools: ToolContext::default(),
            project: ProjectContext::default(),
            model: ModelContext::default(),
            custom: HashMap::new(),
        }
    }

    /// Add a variable
    pub fn add_variable<K, V>(mut self, key: K, value: V) -> Self
    where
        K: Into<String>,
        V: Into<serde_json::Value>,
    {
        self.variables.insert(key.into(), value.into());
        self
    }

    /// Set multiple variables
    pub fn with_variables(mut self, variables: HashMap<String, serde_json::Value>) -> Self {
        self.variables.extend(variables);
        self
    }

    /// Add a message to the conversation history
    pub fn add_message(mut self, message: PromptMessage) -> Self {
        self.history.push(message);
        self
    }

    /// Set the conversation history
    pub fn with_history(mut self, history: Vec<PromptMessage>) -> Self {
        self.history = history;
        self
    }

    /// Set environment context
    pub fn with_environment(mut self, environment: EnvironmentContext) -> Self {
        self.environment = environment;
        self
    }

    /// Set session context
    pub fn with_session(mut self, session: SessionContext) -> Self {
        self.session = session;
        self
    }

    /// Set tool context
    pub fn with_tools(mut self, tools: ToolContext) -> Self {
        self.tools = tools;
        self
    }

    /// Set project context
    pub fn with_project(mut self, project: ProjectContext) -> Self {
        self.project = project;
        self
    }

    /// Set model context
    pub fn with_model(mut self, model: ModelContext) -> Self {
        self.model = model;
        self
    }

    /// Add custom context
    pub fn add_custom<K, V>(mut self, key: K, value: V) -> Self
    where
        K: Into<String>,
        V: Into<serde_json::Value>,
    {
        self.custom.insert(key.into(), value.into());
        self
    }

    /// Get a variable value
    pub fn get_variable(&self, key: &str) -> Option<&serde_json::Value> {
        self.variables.get(key)
    }

    /// Get a custom value
    pub fn get_custom(&self, key: &str) -> Option<&serde_json::Value> {
        self.custom.get(key)
    }

    /// Check if a variable exists
    pub fn has_variable(&self, key: &str) -> bool {
        self.variables.contains_key(key)
    }

    /// Get the conversation history for a specific role
    pub fn get_messages_by_role(&self, role: &PromptRole) -> Vec<&PromptMessage> {
        self.history
            .iter()
            .filter(|msg| &msg.role == role)
            .collect()
    }

    /// Get the last N messages
    pub fn get_recent_messages(&self, count: usize) -> &[PromptMessage] {
        let start = if self.history.len() > count {
            self.history.len() - count
        } else {
            0
        };
        &self.history[start..]
    }

    /// Convert to template variables for rendering
    pub fn to_template_variables(&self) -> PromptResult<HashMap<String, serde_json::Value>> {
        let mut vars = self.variables.clone();

        // Add structured context
        vars.insert("env".to_string(), serde_json::to_value(&self.environment)?);
        vars.insert("session".to_string(), serde_json::to_value(&self.session)?);
        vars.insert("tools".to_string(), serde_json::to_value(&self.tools)?);
        vars.insert("project".to_string(), serde_json::to_value(&self.project)?);
        vars.insert("model".to_string(), serde_json::to_value(&self.model)?);

        // Add conversation history
        vars.insert("history".to_string(), serde_json::to_value(&self.history)?);
        vars.insert(
            "history_count".to_string(),
            serde_json::Value::Number(self.history.len().into()),
        );

        // Add recent messages in different formats
        let user_messages: Vec<_> = self.get_messages_by_role(&PromptRole::User);
        let assistant_messages: Vec<_> = self.get_messages_by_role(&PromptRole::Assistant);
        let system_messages: Vec<_> = self.get_messages_by_role(&PromptRole::System);

        vars.insert(
            "user_messages".to_string(),
            serde_json::to_value(user_messages)?,
        );
        vars.insert(
            "assistant_messages".to_string(),
            serde_json::to_value(assistant_messages)?,
        );
        vars.insert(
            "system_messages".to_string(),
            serde_json::to_value(system_messages)?,
        );

        // Add custom context
        for (key, value) in &self.custom {
            vars.insert(format!("custom_{}", key), value.clone());
        }

        Ok(vars)
    }

    /// Validate the context for required variables
    pub fn validate(&self, required_variables: &[String]) -> PromptResult<()> {
        for var in required_variables {
            if !self.variables.contains_key(var) {
                return Err(PromptError::missing_variable(var));
            }
        }
        Ok(())
    }

    /// Merge with another context (other takes precedence)
    pub fn merge(mut self, other: PromptContext) -> Self {
        self.variables.extend(other.variables);
        self.history.extend(other.history);
        self.custom.extend(other.custom);
        // Note: For structured contexts, we take the other's values
        self.environment = other.environment;
        self.session = other.session;
        self.tools = other.tools;
        self.project = other.project;
        self.model = other.model;
        self
    }
}

/// Environment context information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnvironmentContext {
    /// Current working directory
    pub working_directory: String,
    /// Operating system
    pub os: String,
    /// Platform (e.g., "linux", "macos", "windows")
    pub platform: String,
    /// Whether the directory is a git repository
    pub is_git_repo: bool,
    /// Current git branch (if applicable)
    pub git_branch: Option<String>,
    /// Current date and time
    pub current_date: DateTime<Utc>,
    /// Environment variables (filtered for safety)
    pub environment_variables: HashMap<String, String>,
}

impl Default for EnvironmentContext {
    fn default() -> Self {
        Self {
            working_directory: std::env::current_dir()
                .unwrap_or_default()
                .to_string_lossy()
                .to_string(),
            os: std::env::consts::OS.to_string(),
            platform: std::env::consts::ARCH.to_string(),
            is_git_repo: false,
            git_branch: None,
            current_date: Utc::now(),
            environment_variables: HashMap::new(),
        }
    }
}

/// Session context information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionContext {
    /// Session ID
    pub session_id: String,
    /// User ID or name
    pub user: Option<String>,
    /// Session start time
    pub started_at: DateTime<Utc>,
    /// Last activity time
    pub last_activity: DateTime<Utc>,
    /// Session metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

impl Default for SessionContext {
    fn default() -> Self {
        let now = Utc::now();
        Self {
            session_id: uuid::Uuid::new_v4().to_string(),
            user: None,
            started_at: now,
            last_activity: now,
            metadata: HashMap::new(),
        }
    }
}

/// Tool context information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolContext {
    /// Available tools and their descriptions
    pub available_tools: Vec<ToolDescription>,
    /// Tool usage statistics
    pub usage_stats: HashMap<String, ToolUsageStats>,
    /// Tool permissions
    pub permissions: HashMap<String, bool>,
}

impl Default for ToolContext {
    fn default() -> Self {
        Self {
            available_tools: Vec::new(),
            usage_stats: HashMap::new(),
            permissions: HashMap::new(),
        }
    }
}

/// Description of an available tool
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolDescription {
    /// Tool name
    pub name: String,
    /// Tool description
    pub description: String,
    /// Input schema
    pub input_schema: serde_json::Value,
    /// Whether the tool is enabled
    pub enabled: bool,
    /// Tool category
    pub category: Option<String>,
}

/// Tool usage statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolUsageStats {
    /// Number of times used
    pub usage_count: usize,
    /// Number of successful executions
    pub success_count: usize,
    /// Average execution time in milliseconds
    pub avg_execution_time_ms: f64,
    /// Last used timestamp
    pub last_used: Option<DateTime<Utc>>,
}

/// Project context information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectContext {
    /// Project name
    pub name: Option<String>,
    /// Project description
    pub description: Option<String>,
    /// Programming language(s)
    pub languages: Vec<String>,
    /// Project type (e.g., "rust_binary", "node_library")
    pub project_type: Option<String>,
    /// Build system (e.g., "cargo", "npm", "make")
    pub build_system: Option<String>,
    /// Key directories
    pub directories: HashMap<String, String>,
    /// Configuration files
    pub config_files: Vec<String>,
    /// Dependencies
    pub dependencies: Vec<String>,
    /// Recent commits or changes
    pub recent_changes: Vec<String>,
}

impl Default for ProjectContext {
    fn default() -> Self {
        Self {
            name: None,
            description: None,
            languages: Vec::new(),
            project_type: None,
            build_system: None,
            directories: HashMap::new(),
            config_files: Vec::new(),
            dependencies: Vec::new(),
            recent_changes: Vec::new(),
        }
    }
}

/// Model-specific context
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelContext {
    /// Model name/identifier
    pub model_name: String,
    /// Model provider
    pub provider: String,
    /// Model capabilities
    pub capabilities: ModelCapabilities,
    /// Model limits
    pub limits: ModelLimits,
    /// Model configuration
    pub config: HashMap<String, serde_json::Value>,
}

impl Default for ModelContext {
    fn default() -> Self {
        Self {
            model_name: "unknown".to_string(),
            provider: "unknown".to_string(),
            capabilities: ModelCapabilities::default(),
            limits: ModelLimits::default(),
            config: HashMap::new(),
        }
    }
}

/// Model capabilities
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelCapabilities {
    /// Supports function calling
    pub function_calling: bool,
    /// Supports streaming
    pub streaming: bool,
    /// Supports vision/images
    pub vision: bool,
    /// Supports tool use
    pub tool_use: bool,
    /// Supports system messages
    pub system_messages: bool,
    /// Maximum context length
    pub max_context_length: Option<usize>,
}

impl Default for ModelCapabilities {
    fn default() -> Self {
        Self {
            function_calling: false,
            streaming: false,
            vision: false,
            tool_use: false,
            system_messages: true,
            max_context_length: None,
        }
    }
}

/// Model limits and constraints
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelLimits {
    /// Maximum input tokens
    pub max_input_tokens: Option<usize>,
    /// Maximum output tokens
    pub max_output_tokens: Option<usize>,
    /// Rate limit per minute
    pub rate_limit_per_minute: Option<usize>,
    /// Maximum message history length
    pub max_history_length: Option<usize>,
}

impl Default for ModelLimits {
    fn default() -> Self {
        Self {
            max_input_tokens: None,
            max_output_tokens: None,
            rate_limit_per_minute: None,
            max_history_length: None,
        }
    }
}

/// Builder for creating prompt contexts
#[derive(Debug, Default)]
pub struct ContextBuilder {
    context: PromptContext,
}

impl ContextBuilder {
    /// Create a new context builder
    pub fn new() -> Self {
        Self::default()
    }

    /// Add a variable
    pub fn variable<K, V>(mut self, key: K, value: V) -> Self
    where
        K: Into<String>,
        V: Into<serde_json::Value>,
    {
        self.context.variables.insert(key.into(), value.into());
        self
    }

    /// Add a message
    pub fn message(mut self, message: PromptMessage) -> Self {
        self.context.history.push(message);
        self
    }

    /// Add a user message
    pub fn user_message<S: Into<String>>(mut self, content: S) -> Self {
        self.context.history.push(PromptMessage::user(content));
        self
    }

    /// Add an assistant message
    pub fn assistant_message<S: Into<String>>(mut self, content: S) -> Self {
        self.context.history.push(PromptMessage::assistant(content));
        self
    }

    /// Add a system message
    pub fn system_message<S: Into<String>>(mut self, content: S) -> Self {
        self.context.history.push(PromptMessage::system(content));
        self
    }

    /// Set environment context
    pub fn environment(mut self, environment: EnvironmentContext) -> Self {
        self.context.environment = environment;
        self
    }

    /// Set session context
    pub fn session(mut self, session: SessionContext) -> Self {
        self.context.session = session;
        self
    }

    /// Set tool context
    pub fn tools(mut self, tools: ToolContext) -> Self {
        self.context.tools = tools;
        self
    }

    /// Set project context
    pub fn project(mut self, project: ProjectContext) -> Self {
        self.context.project = project;
        self
    }

    /// Set model context
    pub fn model(mut self, model: ModelContext) -> Self {
        self.context.model = model;
        self
    }

    /// Add custom data
    pub fn custom<K, V>(mut self, key: K, value: V) -> Self
    where
        K: Into<String>,
        V: Into<serde_json::Value>,
    {
        self.context.custom.insert(key.into(), value.into());
        self
    }

    /// Build the context
    pub fn build(self) -> PromptContext {
        self.context
    }
}

/// Trait for types that can provide variables to prompt contexts
pub trait VariableProvider {
    /// Get variables from this provider
    fn get_variables(&self) -> PromptResult<HashMap<String, serde_json::Value>>;

    /// Get the provider name for debugging
    fn provider_name(&self) -> &str;
}

/// Implementation for static variable providers
impl VariableProvider for HashMap<String, serde_json::Value> {
    fn get_variables(&self) -> PromptResult<HashMap<String, serde_json::Value>> {
        Ok(self.clone())
    }

    fn provider_name(&self) -> &str {
        "HashMap"
    }
}
