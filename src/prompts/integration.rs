//! Integration layer for connecting prompt system with AutoRun architecture

use chrono::Utc;
use std::collections::HashMap;

use crate::prompts::context::{
    EnvironmentContext, ModelCapabilities, ModelContext, ModelLimits, ProjectContext,
    SessionContext, ToolContext,
};
use crate::prompts::{
    <PERSON><PERSON><PERSON><PERSON><PERSON>, PromptContext, PromptError, PromptManager, PromptMessage, PromptResult,
};
// Note: Removing ToolRegistry import as it's not properly defined yet
// use crate::tools::{ToolRegistry, Tool};
// use crate::session::Session;
// use crate::config::Config;
use crate::errors::AutorunError;

/// Integration service for prompt system with AutoRun components
#[derive(Debug)]
pub struct PromptIntegration {
    /// Prompt manager instance
    pub prompt_manager: PromptManager,
    /// Tool registry for context building (placeholder until ToolRegistry is properly defined)
    tool_registry: Option<String>,
    /// Current session for context (placeholder until Session is properly defined)
    current_session: Option<String>,
    /// Configuration for context building (placeholder until Config is properly defined)
    config: Option<String>,
}

impl PromptIntegration {
    /// Create a new prompt integration service
    pub fn new() -> PromptResult<Self> {
        let mut prompt_manager = PromptManager::new()?;

        // Add default prompt directories
        let project_root = std::env::current_dir().unwrap_or_default();
        for dir in crate::prompts::DEFAULT_PROMPT_DIRS {
            let prompt_dir = project_root.join(dir);
            if prompt_dir.exists() {
                if let Err(e) = prompt_manager.add_base_directory(&prompt_dir) {
                    tracing::warn!("Failed to add prompt directory {:?}: {}", prompt_dir, e);
                }
            }
        }

        // Load all prompts
        if let Err(e) = prompt_manager.load_all_prompts() {
            tracing::warn!("Failed to load prompts: {}", e);
        }

        Ok(Self {
            prompt_manager,
            tool_registry: None,
            current_session: None,
            config: None,
        })
    }

    /// Set the tool registry for context building (placeholder)
    pub fn with_tool_registry(mut self, registry_name: String) -> Self {
        self.tool_registry = Some(registry_name);
        self
    }

    /// Set the current session for context building (placeholder)
    pub fn with_session(mut self, session_id: String) -> Self {
        self.current_session = Some(session_id);
        self
    }

    /// Set the configuration for context building (placeholder)
    pub fn with_config(mut self, config_name: String) -> Self {
        self.config = Some(config_name);
        self
    }

    /// Build a comprehensive prompt context from AutoRun state
    pub fn build_context(&self) -> PromptResult<PromptContext> {
        let mut builder = ContextBuilder::new();

        // Add environment context
        builder = builder.environment(self.build_environment_context()?);

        // Add session context if available
        if let Some(ref session_id) = self.current_session {
            builder = builder.session(self.build_session_context(session_id)?);
        }

        // Add tool context if registry is available
        if let Some(ref registry_name) = self.tool_registry {
            builder = builder.tools(self.build_tool_context(registry_name)?);
        }

        // Add project context
        builder = builder.project(self.build_project_context()?);

        // Add model context
        builder = builder.model(self.build_model_context()?);

        // Add common variables
        builder = self.add_common_variables(builder)?;

        Ok(builder.build())
    }

    /// Build environment context from system information
    fn build_environment_context(&self) -> PromptResult<EnvironmentContext> {
        let working_dir = std::env::current_dir()
            .unwrap_or_default()
            .to_string_lossy()
            .to_string();

        // Check if directory is a git repository
        let is_git_repo = std::path::Path::new(".git").exists();

        // Get git branch if available
        let git_branch = if is_git_repo {
            std::process::Command::new("git")
                .args(["branch", "--show-current"])
                .output()
                .ok()
                .and_then(|output| {
                    if output.status.success() {
                        String::from_utf8(output.stdout)
                            .ok()
                            .map(|s| s.trim().to_string())
                            .filter(|s| !s.is_empty())
                    } else {
                        None
                    }
                })
        } else {
            None
        };

        // Filter environment variables for safety (only include safe ones)
        let safe_env_vars = [
            "PATH",
            "HOME",
            "USER",
            "SHELL",
            "TERM",
            "LANG",
            "LC_ALL",
            "CARGO_HOME",
            "RUSTUP_HOME",
            "RUST_LOG",
            "RUST_BACKTRACE",
        ];

        let mut env_vars = HashMap::new();
        for var in &safe_env_vars {
            if let Ok(value) = std::env::var(var) {
                env_vars.insert(var.to_string(), value);
            }
        }

        Ok(EnvironmentContext {
            working_directory: working_dir,
            os: std::env::consts::OS.to_string(),
            platform: std::env::consts::ARCH.to_string(),
            is_git_repo,
            git_branch,
            current_date: Utc::now(),
            environment_variables: env_vars,
        })
    }

    /// Build session context from session ID (placeholder)
    fn build_session_context(&self, session_id: &str) -> PromptResult<SessionContext> {
        let now = Utc::now();
        Ok(SessionContext {
            session_id: session_id.to_string(),
            user: None, // TODO: Add user information when available
            started_at: now,
            last_activity: now,
            metadata: HashMap::new(), // TODO: Add session metadata
        })
    }

    /// Build tool context from registry name (placeholder)
    fn build_tool_context(&self, _registry_name: &str) -> PromptResult<ToolContext> {
        // TODO: Implement when ToolRegistry trait is properly defined
        // For now, return empty context
        Ok(ToolContext::default())
    }

    /// Build project context from current directory analysis
    fn build_project_context(&self) -> PromptResult<ProjectContext> {
        let mut context = ProjectContext::default();

        // Detect project type by looking for key files
        let current_dir = std::env::current_dir().unwrap_or_default();

        if current_dir.join("Cargo.toml").exists() {
            context.project_type = Some("rust_binary".to_string());
            context.build_system = Some("cargo".to_string());
            context.languages.push("rust".to_string());
            context.config_files.push("Cargo.toml".to_string());

            // Try to read project name from Cargo.toml
            if let Ok(cargo_content) = std::fs::read_to_string(current_dir.join("Cargo.toml")) {
                // Simple parsing - in practice you'd use a TOML parser
                for line in cargo_content.lines() {
                    if line.starts_with("name") && line.contains('=') {
                        if let Some(name) = line.split('=').nth(1) {
                            context.name = Some(name.trim().trim_matches('"').to_string());
                            break;
                        }
                    }
                }
            }
        }

        if current_dir.join("package.json").exists() {
            context.project_type = Some("node_project".to_string());
            context.build_system = Some("npm".to_string());
            context.languages.push("javascript".to_string());
            context.config_files.push("package.json".to_string());
        }

        // Add common directories
        let common_dirs = ["src", "tests", "examples", "docs", "target", "build"];
        for dir in &common_dirs {
            let dir_path = current_dir.join(dir);
            if dir_path.exists() && dir_path.is_dir() {
                context
                    .directories
                    .insert(dir.to_string(), dir_path.to_string_lossy().to_string());
            }
        }

        // Get recent git commits if available
        if context.directories.contains_key("src") {
            context.recent_changes = self.get_recent_git_changes().unwrap_or_default();
        }

        Ok(context)
    }

    /// Build model context based on configuration
    fn build_model_context(&self) -> PromptResult<ModelContext> {
        let mut context = ModelContext::default();

        // TODO: Get model information from LLM client when available
        context.model_name = "claude-3-sonnet".to_string();
        context.provider = "anthropic".to_string();

        context.capabilities = ModelCapabilities {
            function_calling: true,
            streaming: true,
            vision: false,
            tool_use: true,
            system_messages: true,
            max_context_length: Some(200_000),
        };

        context.limits = ModelLimits {
            max_input_tokens: Some(200_000),
            max_output_tokens: Some(4_000),
            rate_limit_per_minute: Some(60),
            max_history_length: Some(100),
        };

        Ok(context)
    }

    /// Add common variables to context builder
    fn add_common_variables(&self, mut builder: ContextBuilder) -> PromptResult<ContextBuilder> {
        // Add current timestamp
        builder = builder.variable("current_timestamp", Utc::now().to_rfc3339());

        // Add autorun version
        builder = builder.variable("autorun_version", crate::VERSION);

        // Add working directory
        let working_dir = std::env::current_dir()
            .unwrap_or_default()
            .to_string_lossy()
            .to_string();
        builder = builder.variable("working_directory", working_dir);

        // Add environment variables
        builder = builder.variable("is_debug", cfg!(debug_assertions));

        Ok(builder)
    }

    /// Get recent git changes
    fn get_recent_git_changes(&self) -> Option<Vec<String>> {
        std::process::Command::new("git")
            .args(["log", "--oneline", "-n", "10"])
            .output()
            .ok()
            .and_then(|output| {
                if output.status.success() {
                    String::from_utf8(output.stdout)
                        .ok()
                        .map(|s| s.lines().map(|line| line.to_string()).collect())
                } else {
                    None
                }
            })
    }

    /// Create a system prompt context for the base AutoRun agent
    pub fn create_system_context(&self) -> PromptResult<PromptContext> {
        let base_context = self.build_context()?;

        // Add system-specific variables
        let mut context = base_context
            .add_variable("agent_name", "AutoRun")
            .add_variable("agent_version", crate::VERSION)
            .add_variable(
                "capabilities",
                serde_json::json!({
                    "file_operations": true,
                    "code_analysis": true,
                    "task_management": true,
                    "tui_interface": true,
                    "async_execution": true
                }),
            );

        // Add tool descriptions if available
        if let Some(ref _registry) = self.tool_registry {
            // TODO: Build tool descriptions from registry
            context =
                context.add_variable("tool_descriptions", "Tool registry integration pending");
        }

        // Add environment context as formatted string
        let env_context = format!(
            "Working directory: {}\nOS: {} ({})\nGit repository: {}\nCurrent date: {}",
            context.environment.working_directory,
            context.environment.os,
            context.environment.platform,
            if context.environment.is_git_repo {
                "Yes"
            } else {
                "No"
            },
            context
                .environment
                .current_date
                .format("%Y-%m-%d %H:%M:%S UTC")
        );
        context = context.add_variable("env_context", env_context);

        // Add project context as formatted string
        let project_context = format!(
            "Project: {}\nType: {}\nLanguages: {}\nBuild system: {}",
            context.project.name.as_deref().unwrap_or("Unknown"),
            context.project.project_type.as_deref().unwrap_or("Unknown"),
            context.project.languages.join(", "),
            context.project.build_system.as_deref().unwrap_or("Unknown")
        );
        context = context.add_variable("project_context", project_context);

        // Add model capabilities
        let model_caps = serde_json::to_string_pretty(&context.model.capabilities)
            .unwrap_or_else(|_| "Model capabilities not available".to_string());
        context = context.add_variable("model_capabilities", model_caps);

        Ok(context)
    }

    /// Render the base system prompt
    pub fn render_system_prompt(&self) -> PromptResult<Vec<PromptMessage>> {
        let context = self.create_system_context()?;
        self.prompt_manager.render_prompt("autorun_base", &context)
    }

    /// Render a tool-specific prompt
    pub fn render_tool_prompt(
        &self,
        tool_name: &str,
        additional_vars: HashMap<String, serde_json::Value>,
    ) -> PromptResult<Vec<PromptMessage>> {
        let mut context = self.build_context()?;

        // Add tool-specific variables
        for (key, value) in additional_vars {
            context = context.add_variable(key, value);
        }

        self.prompt_manager.render_prompt(tool_name, &context)
    }

    /// Get available prompts by type
    pub fn get_prompts_by_type(&self, prompt_type: &str) -> PromptResult<Vec<String>> {
        let prompts = self.prompt_manager.list_prompts()?;
        Ok(prompts
            .into_iter()
            .filter(|p| p.prompt_type.to_string() == prompt_type)
            .map(|p| p.name)
            .collect())
    }

    /// Check if prompt system is properly initialized
    pub fn is_initialized(&self) -> bool {
        !self
            .prompt_manager
            .list_prompts()
            .unwrap_or_default()
            .is_empty()
    }

    /// Get prompt system statistics
    pub fn get_stats(&self) -> PromptResult<PromptStats> {
        let prompts = self.prompt_manager.list_prompts()?;
        let cache_stats = self.prompt_manager.cache_stats()?;

        Ok(PromptStats {
            total_prompts: prompts.len(),
            system_prompts: prompts
                .iter()
                .filter(|p| p.prompt_type.to_string().starts_with("system"))
                .count(),
            user_prompts: prompts
                .iter()
                .filter(|p| !p.prompt_type.to_string().starts_with("system"))
                .count(),
            cache_hit_ratio: cache_stats.cache_hit_ratio,
            has_tool_registry: self.tool_registry.is_some(),
            has_session: self.current_session.is_some(),
        })
    }
}

impl Default for PromptIntegration {
    fn default() -> Self {
        Self::new().expect("Failed to create default PromptIntegration")
    }
}

/// Statistics about the prompt system
#[derive(Debug, Clone)]
pub struct PromptStats {
    pub total_prompts: usize,
    pub system_prompts: usize,
    pub user_prompts: usize,
    pub cache_hit_ratio: f64,
    pub has_tool_registry: bool,
    pub has_session: bool,
}

/// Convert AutoRun errors to prompt errors
impl From<AutorunError> for PromptError {
    fn from(err: AutorunError) -> Self {
        PromptError::invalid_structure(err.to_string())
    }
}
