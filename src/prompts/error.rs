//! Error types for the prompt system

use thiserror::Error;

/// Result type for prompt operations
pub type PromptResult<T> = std::result::Result<T, PromptError>;

/// Comprehensive error types for the prompt design system
#[derive(Error, Debug)]
pub enum PromptError {
    #[error("Template rendering error: {0}")]
    Template(#[from] tera::Error),

    #[error("I/O error: {0}")]
    Io(#[from] std::io::Error),

    #[error("JSON serialization/deserialization error: {0}")]
    Json(#[from] serde_json::Error),

    #[error("TOML parsing error: {0}")]
    Toml(#[from] toml::de::Error),

    #[error("Prompt not found: {name}")]
    PromptNotFound { name: String },

    #[error("Template not found: {name}")]
    TemplateNotFound { name: String },

    #[error("Invalid prompt structure: {reason}")]
    InvalidStructure { reason: String },

    #[error("Missing required variable: {variable}")]
    MissingVariable { variable: String },

    #[error("Invalid prompt type: {prompt_type}")]
    InvalidPromptType { prompt_type: String },

    #[error("Context validation error: {reason}")]
    ContextValidation { reason: String },

    #[error("Circular dependency detected in prompt includes: {path}")]
    CircularDependency { path: String },

    #[error("Maximum include depth exceeded: {depth}")]
    MaxIncludeDepthExceeded { depth: usize },

    #[error("Variable type mismatch: expected {expected}, got {actual}")]
    VariableTypeMismatch { expected: String, actual: String },

    #[error("Prompt compilation error: {reason}")]
    CompilationError { reason: String },

    #[error("Registry error: {reason}")]
    Registry { reason: String },

    #[error("Permission denied: {resource}")]
    PermissionDenied { resource: String },

    #[error("Resource limit exceeded: {limit_type}")]
    ResourceLimitExceeded { limit_type: String },

    #[error("Configuration error: {reason}")]
    Configuration { reason: String },

    #[error("Tokenization error: {0}")]
    Tokenization(#[from] crate::prompts::tokenization::TokenizationError),
}

impl PromptError {
    /// Create a new PromptNotFound error
    pub fn prompt_not_found(name: impl Into<String>) -> Self {
        Self::PromptNotFound { name: name.into() }
    }

    /// Create a new TemplateNotFound error
    pub fn template_not_found(name: impl Into<String>) -> Self {
        Self::TemplateNotFound { name: name.into() }
    }

    /// Create a new InvalidStructure error
    pub fn invalid_structure(reason: impl Into<String>) -> Self {
        Self::InvalidStructure {
            reason: reason.into(),
        }
    }

    /// Create a new MissingVariable error
    pub fn missing_variable(variable: impl Into<String>) -> Self {
        Self::MissingVariable {
            variable: variable.into(),
        }
    }

    /// Create a new ContextValidation error
    pub fn context_validation(reason: impl Into<String>) -> Self {
        Self::ContextValidation {
            reason: reason.into(),
        }
    }

    /// Create a new CompilationError
    pub fn compilation_error(reason: impl Into<String>) -> Self {
        Self::CompilationError {
            reason: reason.into(),
        }
    }

    /// Create a new Registry error
    pub fn registry(reason: impl Into<String>) -> Self {
        Self::Registry {
            reason: reason.into(),
        }
    }

    /// Check if this error is related to missing resources
    pub fn is_not_found(&self) -> bool {
        matches!(
            self,
            Self::PromptNotFound { .. } | Self::TemplateNotFound { .. }
        )
    }

    /// Check if this error is a validation error
    pub fn is_validation_error(&self) -> bool {
        matches!(
            self,
            Self::InvalidStructure { .. }
                | Self::ContextValidation { .. }
                | Self::MissingVariable { .. }
                | Self::VariableTypeMismatch { .. }
        )
    }

    /// Check if this error is related to template processing
    pub fn is_template_error(&self) -> bool {
        matches!(
            self,
            Self::Template(_)
                | Self::TemplateNotFound { .. }
                | Self::CircularDependency { .. }
                | Self::MaxIncludeDepthExceeded { .. }
        )
    }

    /// Check if this error is related to tokenization
    pub fn is_tokenization_error(&self) -> bool {
        matches!(self, Self::Tokenization(_))
    }
}
