//! Core prompt types and structures

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use uuid::Uuid;

use crate::prompts::error::{PromptError, PromptResult};

/// Role in a conversation, compatible with LLM APIs
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
#[serde(rename_all = "lowercase")]
pub enum PromptRole {
    /// System instructions and context
    System,
    /// User input and queries
    User,
    /// Assistant responses
    Assistant,
    /// Tool execution results
    Tool,
    /// Internal system messages
    Internal,
}

impl std::fmt::Display for PromptRole {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::System => write!(f, "system"),
            Self::User => write!(f, "user"),
            Self::Assistant => write!(f, "assistant"),
            Self::Tool => write!(f, "tool"),
            Self::Internal => write!(f, "internal"),
        }
    }
}

/// Type of prompt for categorization and handling
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum PromptType {
    /// Base system prompt defining agent behavior
    SystemBase,
    /// System prompts for specific tools
    SystemTool,
    /// User interaction prompts
    UserInteraction,
    /// Code generation prompts
    CodeGeneration,
    /// Analysis and review prompts
    Analysis,
    /// Error handling and debugging prompts
    ErrorHandling,
    /// Documentation generation prompts
    Documentation,
    /// Task planning and management prompts
    TaskManagement,
    /// Examples and few-shot demonstrations
    Examples,
    /// Workflow automation prompts
    Workflow,
    /// Custom prompt types
    Custom(String),
}

impl std::fmt::Display for PromptType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::SystemBase => write!(f, "system_base"),
            Self::SystemTool => write!(f, "system_tool"),
            Self::UserInteraction => write!(f, "user_interaction"),
            Self::CodeGeneration => write!(f, "code_generation"),
            Self::Analysis => write!(f, "analysis"),
            Self::ErrorHandling => write!(f, "error_handling"),
            Self::Documentation => write!(f, "documentation"),
            Self::TaskManagement => write!(f, "task_management"),
            Self::Examples => write!(f, "examples"),
            Self::Workflow => write!(f, "workflow"),
            Self::Custom(name) => write!(f, "custom_{}", name),
        }
    }
}

/// Individual message in a conversation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PromptMessage {
    /// Unique identifier for this message
    pub id: Uuid,
    /// Role of the message sender
    pub role: PromptRole,
    /// Content of the message (can contain template variables)
    pub content: String,
    /// Optional metadata for the message
    #[serde(default)]
    pub metadata: HashMap<String, serde_json::Value>,
    /// Timestamp when the message was created
    pub timestamp: DateTime<Utc>,
    /// Optional tool call information
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tool_call: Option<ToolCall>,
    /// Optional tool result information
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tool_result: Option<ToolResult>,
}

impl PromptMessage {
    /// Create a new message
    pub fn new(role: PromptRole, content: impl Into<String>) -> Self {
        Self {
            id: Uuid::new_v4(),
            role,
            content: content.into(),
            metadata: HashMap::new(),
            timestamp: Utc::now(),
            tool_call: None,
            tool_result: None,
        }
    }

    /// Create a system message
    pub fn system(content: impl Into<String>) -> Self {
        Self::new(PromptRole::System, content)
    }

    /// Create a user message
    pub fn user(content: impl Into<String>) -> Self {
        Self::new(PromptRole::User, content)
    }

    /// Create an assistant message
    pub fn assistant(content: impl Into<String>) -> Self {
        Self::new(PromptRole::Assistant, content)
    }

    /// Create a tool message
    pub fn tool(content: impl Into<String>) -> Self {
        Self::new(PromptRole::Tool, content)
    }

    /// Add metadata to the message
    pub fn with_metadata(
        mut self,
        key: impl Into<String>,
        value: impl Into<serde_json::Value>,
    ) -> Self {
        self.metadata.insert(key.into(), value.into());
        self
    }

    /// Add tool call information
    pub fn with_tool_call(mut self, tool_call: ToolCall) -> Self {
        self.tool_call = Some(tool_call);
        self
    }

    /// Add tool result information
    pub fn with_tool_result(mut self, tool_result: ToolResult) -> Self {
        self.tool_result = Some(tool_result);
        self
    }

    /// Check if this message contains template variables
    pub fn has_template_variables(&self) -> bool {
        self.content.contains("{{") && self.content.contains("}}")
    }

    /// Get the length of the content
    pub fn content_length(&self) -> usize {
        self.content.len()
    }
}

/// Tool call information for function calling
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolCall {
    /// Unique identifier for the tool call
    pub id: String,
    /// Name of the tool being called
    pub name: String,
    /// Arguments passed to the tool
    pub arguments: serde_json::Value,
}

/// Tool execution result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolResult {
    /// ID of the tool call this result corresponds to
    pub tool_call_id: String,
    /// Result content
    pub content: String,
    /// Whether the tool execution was successful
    pub success: bool,
    /// Optional error information
    #[serde(skip_serializing_if = "Option::is_none")]
    pub error: Option<String>,
}

/// Metadata associated with a prompt
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PromptMetadata {
    /// Unique identifier for the prompt
    pub id: Uuid,
    /// Human-readable name
    pub name: String,
    /// Version of the prompt
    pub version: String,
    /// Description of the prompt's purpose
    pub description: String,
    /// Type/category of the prompt
    pub prompt_type: PromptType,
    /// Tags for categorization and search
    #[serde(default)]
    pub tags: Vec<String>,
    /// Author of the prompt
    #[serde(skip_serializing_if = "Option::is_none")]
    pub author: Option<String>,
    /// Creation timestamp
    pub created_at: DateTime<Utc>,
    /// Last modification timestamp
    pub updated_at: DateTime<Utc>,
    /// File path where the prompt is stored
    #[serde(skip_serializing_if = "Option::is_none")]
    pub file_path: Option<PathBuf>,
    /// Required variables for template rendering
    #[serde(default)]
    pub required_variables: Vec<String>,
    /// Optional variables with defaults
    #[serde(default)]
    pub optional_variables: HashMap<String, serde_json::Value>,
    /// Supported LLM models
    #[serde(default)]
    pub supported_models: Vec<String>,
    /// Performance characteristics
    #[serde(skip_serializing_if = "Option::is_none")]
    pub performance: Option<PerformanceMetrics>,
    /// Additional arbitrary metadata
    #[serde(default)]
    pub custom: HashMap<String, serde_json::Value>,
}

impl PromptMetadata {
    /// Create new metadata
    pub fn new(
        name: impl Into<String>,
        version: impl Into<String>,
        description: impl Into<String>,
        prompt_type: PromptType,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4(),
            name: name.into(),
            version: version.into(),
            description: description.into(),
            prompt_type,
            tags: Vec::new(),
            author: None,
            created_at: now,
            updated_at: now,
            file_path: None,
            required_variables: Vec::new(),
            optional_variables: HashMap::new(),
            supported_models: Vec::new(),
            performance: None,
            custom: HashMap::new(),
        }
    }

    /// Add a tag
    pub fn with_tag(mut self, tag: impl Into<String>) -> Self {
        self.tags.push(tag.into());
        self
    }

    /// Add required variable
    pub fn with_required_variable(mut self, variable: impl Into<String>) -> Self {
        self.required_variables.push(variable.into());
        self
    }

    /// Add optional variable with default
    pub fn with_optional_variable(
        mut self,
        variable: impl Into<String>,
        default: impl Into<serde_json::Value>,
    ) -> Self {
        self.optional_variables
            .insert(variable.into(), default.into());
        self
    }

    /// Add supported model
    pub fn with_supported_model(mut self, model: impl Into<String>) -> Self {
        self.supported_models.push(model.into());
        self
    }

    /// Update the modification timestamp
    pub fn touch(&mut self) {
        self.updated_at = Utc::now();
    }
}

/// Performance metrics for prompt optimization
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    /// Average token count for rendered prompts
    pub avg_token_count: Option<usize>,
    /// Average rendering time in milliseconds
    pub avg_render_time_ms: Option<f64>,
    /// Success rate for prompt execution
    pub success_rate: Option<f64>,
    /// Quality score (0.0 to 1.0)
    pub quality_score: Option<f64>,
}

/// Complete prompt definition including template and metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Prompt {
    /// Metadata about the prompt
    pub metadata: PromptMetadata,
    /// Template content (may contain includes and variables)
    pub template: String,
    /// System message template (optional)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub system_template: Option<String>,
    /// Few-shot examples
    #[serde(default)]
    pub examples: Vec<PromptMessage>,
    /// Include directives for modular composition
    #[serde(default)]
    pub includes: Vec<String>,
    /// Validation rules for input variables
    #[serde(default)]
    pub validation_rules: HashMap<String, ValidationRule>,
    /// Compiled template cache (not serialized)
    #[serde(skip)]
    pub compiled: Option<CompiledPrompt>,
}

impl Prompt {
    /// Create a new prompt
    pub fn new(
        name: impl Into<String>,
        version: impl Into<String>,
        description: impl Into<String>,
        prompt_type: PromptType,
        template: impl Into<String>,
    ) -> Self {
        Self {
            metadata: PromptMetadata::new(name, version, description, prompt_type),
            template: template.into(),
            system_template: None,
            examples: Vec::new(),
            includes: Vec::new(),
            validation_rules: HashMap::new(),
            compiled: None,
        }
    }

    /// Add a system template
    pub fn with_system_template(mut self, template: impl Into<String>) -> Self {
        self.system_template = Some(template.into());
        self
    }

    /// Add an example message
    pub fn with_example(mut self, message: PromptMessage) -> Self {
        self.examples.push(message);
        self
    }

    /// Add an include directive
    pub fn with_include(mut self, include_path: impl Into<String>) -> Self {
        self.includes.push(include_path.into());
        self
    }

    /// Add a validation rule
    pub fn with_validation_rule(
        mut self,
        variable: impl Into<String>,
        rule: ValidationRule,
    ) -> Self {
        self.validation_rules.insert(variable.into(), rule);
        self
    }

    /// Check if this prompt is compiled
    pub fn is_compiled(&self) -> bool {
        self.compiled.is_some()
    }

    /// Get the estimated token count (if compiled)
    pub fn estimated_token_count(&self) -> Option<usize> {
        self.compiled.as_ref().map(|c| c.estimated_token_count)
    }

    /// Get the best available token count (if compiled)
    pub fn best_token_count(&self) -> Option<usize> {
        self.compiled.as_ref().map(|c| c.get_best_token_count())
    }

    /// Get token count for a specific model (if compiled)
    pub fn token_count_for_model(&self, model: &str) -> Option<usize> {
        self.compiled
            .as_ref()
            .map(|c| c.get_token_count_for_target_model(model))
    }

    /// Check if accurate tokenization is available
    pub fn has_accurate_tokenization(&self) -> bool {
        self.compiled
            .as_ref()
            .map_or(false, |c| c.has_accurate_tokenization())
    }

    /// Get tokenization info (if compiled)
    pub fn tokenization_info(&self) -> Option<TokenizationInfo> {
        self.compiled.as_ref().map(|c| c.get_tokenization_info())
    }

    /// Validate required variables
    pub fn validate_variables(
        &self,
        variables: &HashMap<String, serde_json::Value>,
    ) -> PromptResult<()> {
        // Check required variables
        for required_var in &self.metadata.required_variables {
            if !variables.contains_key(required_var) {
                return Err(PromptError::missing_variable(required_var));
            }
        }

        // Validate against rules
        for (var_name, rule) in &self.validation_rules {
            if let Some(value) = variables.get(var_name) {
                rule.validate(value)?;
            }
        }

        Ok(())
    }
}

/// Compiled prompt with optimized template processing
#[derive(Debug, Clone)]
pub struct CompiledPrompt {
    /// Template engine instance
    pub template_id: String,
    /// Legacy estimated token count (for backward compatibility)
    pub estimated_token_count: usize,
    /// Accurate token counts per provider/model
    pub accurate_token_counts: HashMap<String, usize>,
    /// Template compilation timestamp
    pub compiled_at: DateTime<Utc>,
    /// Required variable names extracted from template
    pub extracted_variables: Vec<String>,
    /// Tokenizer provider used for accurate counting
    pub tokenizer_provider: Option<String>,
    /// Model used for tokenization
    pub tokenizer_model: Option<String>,
}

impl CompiledPrompt {
    /// Create a new compiled prompt with basic information
    pub fn new(
        template_id: String,
        estimated_token_count: usize,
        extracted_variables: Vec<String>,
    ) -> Self {
        Self {
            template_id,
            estimated_token_count,
            accurate_token_counts: HashMap::new(),
            compiled_at: Utc::now(),
            extracted_variables,
            tokenizer_provider: None,
            tokenizer_model: None,
        }
    }

    /// Create a new compiled prompt with accurate tokenization
    pub fn with_accurate_tokenization(
        template_id: String,
        estimated_token_count: usize,
        extracted_variables: Vec<String>,
        provider: String,
        model: String,
        accurate_count: usize,
    ) -> Self {
        let mut prompt = Self::new(template_id, estimated_token_count, extracted_variables);
        prompt.tokenizer_provider = Some(provider.clone());
        prompt.tokenizer_model = Some(model.clone());
        prompt
            .accurate_token_counts
            .insert(format!("{}:{}", provider, model), accurate_count);
        prompt
    }

    /// Get token count for a specific provider
    pub fn get_token_count_for_provider(&self, provider: &str) -> Option<usize> {
        self.accurate_token_counts.get(provider).copied()
    }

    /// Get token count for a specific provider and model combination
    pub fn get_token_count_for_model(&self, provider: &str, model: &str) -> Option<usize> {
        let key = format!("{}:{}", provider, model);
        self.accurate_token_counts.get(&key).copied()
    }

    /// Get the best available token count (prefers accurate over estimated)
    pub fn get_best_token_count(&self) -> usize {
        // First try to get accurate count for the specific provider/model combo
        if let (Some(provider), Some(model)) = (&self.tokenizer_provider, &self.tokenizer_model) {
            let key = format!("{}:{}", provider, model);
            if let Some(count) = self.accurate_token_counts.get(&key) {
                return *count;
            }
        }

        // Fall back to any accurate count
        self.accurate_token_counts
            .values()
            .next()
            .copied()
            .unwrap_or(self.estimated_token_count)
    }

    /// Get the most relevant token count for a given model
    pub fn get_token_count_for_target_model(&self, target_model: &str) -> usize {
        // Try exact provider:model match first
        for (key, count) in &self.accurate_token_counts {
            if key.ends_with(&format!(":{}", target_model)) {
                return *count;
            }
        }

        // Try model name matches (in case provider is different)
        for (key, count) in &self.accurate_token_counts {
            if key.contains(target_model) {
                return *count;
            }
        }

        // Try provider matches for similar models
        if target_model.contains("claude") {
            for (key, count) in &self.accurate_token_counts {
                if key.contains("claude") {
                    return *count;
                }
            }
        } else if target_model.starts_with("gpt") {
            for (key, count) in &self.accurate_token_counts {
                if key.contains("openai") || key.contains("gpt") {
                    return *count;
                }
            }
        }

        // Fall back to best available count
        self.get_best_token_count()
    }

    /// Add an accurate token count for a specific provider and model
    pub fn add_token_count(&mut self, provider: &str, model: &str, count: usize) {
        let key = format!("{}:{}", provider, model);
        self.accurate_token_counts.insert(key, count);

        // Update primary provider/model if not set
        if self.tokenizer_provider.is_none() {
            self.tokenizer_provider = Some(provider.to_string());
            self.tokenizer_model = Some(model.to_string());
        }
    }

    /// Remove token count for a specific provider and model
    pub fn remove_token_count(&mut self, provider: &str, model: &str) {
        let key = format!("{}:{}", provider, model);
        self.accurate_token_counts.remove(&key);
    }

    /// Get all available tokenization info
    pub fn get_tokenization_info(&self) -> TokenizationInfo {
        TokenizationInfo {
            estimated_count: self.estimated_token_count,
            accurate_counts: self.accurate_token_counts.clone(),
            primary_provider: self.tokenizer_provider.clone(),
            primary_model: self.tokenizer_model.clone(),
            best_count: self.get_best_token_count(),
        }
    }

    /// Check if accurate tokenization is available
    pub fn has_accurate_tokenization(&self) -> bool {
        !self.accurate_token_counts.is_empty()
    }

    /// Get the number of accurate tokenization results available
    pub fn accurate_tokenization_count(&self) -> usize {
        self.accurate_token_counts.len()
    }

    /// Check if tokenization is available for a specific provider
    pub fn has_tokenization_for_provider(&self, provider: &str) -> bool {
        self.accurate_token_counts
            .keys()
            .any(|key| key.starts_with(provider))
    }

    /// Get all providers that have tokenization data
    pub fn get_available_providers(&self) -> Vec<String> {
        self.accurate_token_counts
            .keys()
            .map(|key| key.split(':').next().unwrap_or("").to_string())
            .collect::<std::collections::HashSet<_>>()
            .into_iter()
            .collect()
    }

    /// Update the compilation timestamp
    pub fn touch(&mut self) {
        self.compiled_at = Utc::now();
    }
}

/// Comprehensive tokenization information for a compiled prompt
#[derive(Debug, Clone)]
pub struct TokenizationInfo {
    /// Legacy estimated token count
    pub estimated_count: usize,
    /// Accurate token counts per provider:model
    pub accurate_counts: HashMap<String, usize>,
    /// Primary tokenizer provider used
    pub primary_provider: Option<String>,
    /// Primary model used for tokenization
    pub primary_model: Option<String>,
    /// Best available token count
    pub best_count: usize,
}

/// Validation rule for prompt variables
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type", rename_all = "snake_case")]
pub enum ValidationRule {
    /// String validation
    String {
        min_length: Option<usize>,
        max_length: Option<usize>,
        pattern: Option<String>,
    },
    /// Number validation
    Number { min: Option<f64>, max: Option<f64> },
    /// Boolean validation
    Boolean,
    /// Array validation
    Array {
        min_items: Option<usize>,
        max_items: Option<usize>,
        item_type: Option<String>,
    },
    /// Object validation
    Object { required_fields: Vec<String> },
    /// Enum validation
    Enum {
        allowed_values: Vec<serde_json::Value>,
    },
}

impl ValidationRule {
    /// Validate a value against this rule
    pub fn validate(&self, value: &serde_json::Value) -> PromptResult<()> {
        match self {
            Self::String {
                min_length,
                max_length,
                pattern,
            } => {
                if let Some(s) = value.as_str() {
                    if let Some(min) = min_length {
                        if s.len() < *min {
                            return Err(PromptError::context_validation(format!(
                                "String too short: {} < {}",
                                s.len(),
                                min
                            )));
                        }
                    }
                    if let Some(max) = max_length {
                        if s.len() > *max {
                            return Err(PromptError::context_validation(format!(
                                "String too long: {} > {}",
                                s.len(),
                                max
                            )));
                        }
                    }
                    if let Some(pattern) = pattern {
                        let regex = regex::Regex::new(pattern).map_err(|e| {
                            PromptError::context_validation(format!("Invalid regex: {}", e))
                        })?;
                        if !regex.is_match(s) {
                            return Err(PromptError::context_validation(format!(
                                "String does not match pattern: {}",
                                pattern
                            )));
                        }
                    }
                } else {
                    return Err(PromptError::context_validation(
                        "Expected string value".to_string(),
                    ));
                }
            }
            Self::Number { min, max } => {
                if let Some(n) = value.as_f64() {
                    if let Some(min_val) = min {
                        if n < *min_val {
                            return Err(PromptError::context_validation(format!(
                                "Number too small: {} < {}",
                                n, min_val
                            )));
                        }
                    }
                    if let Some(max_val) = max {
                        if n > *max_val {
                            return Err(PromptError::context_validation(format!(
                                "Number too large: {} > {}",
                                n, max_val
                            )));
                        }
                    }
                } else {
                    return Err(PromptError::context_validation(
                        "Expected number value".to_string(),
                    ));
                }
            }
            Self::Boolean => {
                if !value.is_boolean() {
                    return Err(PromptError::context_validation(
                        "Expected boolean value".to_string(),
                    ));
                }
            }
            Self::Array {
                min_items,
                max_items,
                ..
            } => {
                if let Some(arr) = value.as_array() {
                    if let Some(min) = min_items {
                        if arr.len() < *min {
                            return Err(PromptError::context_validation(format!(
                                "Array too short: {} < {}",
                                arr.len(),
                                min
                            )));
                        }
                    }
                    if let Some(max) = max_items {
                        if arr.len() > *max {
                            return Err(PromptError::context_validation(format!(
                                "Array too long: {} > {}",
                                arr.len(),
                                max
                            )));
                        }
                    }
                } else {
                    return Err(PromptError::context_validation(
                        "Expected array value".to_string(),
                    ));
                }
            }
            Self::Object { required_fields } => {
                if let Some(obj) = value.as_object() {
                    for field in required_fields {
                        if !obj.contains_key(field) {
                            return Err(PromptError::context_validation(format!(
                                "Missing required field: {}",
                                field
                            )));
                        }
                    }
                } else {
                    return Err(PromptError::context_validation(
                        "Expected object value".to_string(),
                    ));
                }
            }
            Self::Enum { allowed_values } => {
                if !allowed_values.contains(value) {
                    return Err(PromptError::context_validation(format!(
                        "Value not in allowed set: {:?}",
                        value
                    )));
                }
            }
        }
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_compiled_prompt_creation() {
        let compiled = CompiledPrompt::new(
            "test-template".to_string(),
            150,
            vec!["var1".to_string(), "var2".to_string()],
        );

        assert_eq!(compiled.template_id, "test-template");
        assert_eq!(compiled.estimated_token_count, 150);
        assert_eq!(compiled.extracted_variables.len(), 2);
        assert!(compiled.accurate_token_counts.is_empty());
        assert!(compiled.tokenizer_provider.is_none());
        assert!(compiled.tokenizer_model.is_none());
    }

    #[test]
    fn test_compiled_prompt_with_accurate_tokenization() {
        let compiled = CompiledPrompt::with_accurate_tokenization(
            "test-template".to_string(),
            150,
            vec!["var1".to_string()],
            "claude".to_string(),
            "claude-3-sonnet".to_string(),
            142,
        );

        assert_eq!(compiled.template_id, "test-template");
        assert_eq!(compiled.estimated_token_count, 150);
        assert_eq!(compiled.tokenizer_provider, Some("claude".to_string()));
        assert_eq!(
            compiled.tokenizer_model,
            Some("claude-3-sonnet".to_string())
        );
        assert_eq!(compiled.get_best_token_count(), 142);
        assert!(compiled.has_accurate_tokenization());
    }

    #[test]
    fn test_token_count_methods() {
        let mut compiled = CompiledPrompt::new("test".to_string(), 100, vec![]);

        // Test with no accurate counts - should return estimated
        assert_eq!(compiled.get_best_token_count(), 100);
        assert!(!compiled.has_accurate_tokenization());

        // Add accurate counts
        compiled.add_token_count("claude", "claude-3-sonnet", 95);
        compiled.add_token_count("openai", "gpt-4", 105);

        assert!(compiled.has_accurate_tokenization());
        assert_eq!(compiled.accurate_tokenization_count(), 2);
        assert_eq!(
            compiled.get_token_count_for_model("claude", "claude-3-sonnet"),
            Some(95)
        );
        assert_eq!(
            compiled.get_token_count_for_model("openai", "gpt-4"),
            Some(105)
        );

        // Test provider availability
        assert!(compiled.has_tokenization_for_provider("claude"));
        assert!(compiled.has_tokenization_for_provider("openai"));
        assert!(!compiled.has_tokenization_for_provider("mistral"));

        let providers = compiled.get_available_providers();
        assert!(providers.contains(&"claude".to_string()));
        assert!(providers.contains(&"openai".to_string()));
    }

    #[test]
    fn test_target_model_matching() {
        let mut compiled = CompiledPrompt::new("test".to_string(), 100, vec![]);

        compiled.add_token_count("claude", "claude-3-sonnet", 95);
        compiled.add_token_count("openai", "gpt-4", 105);
        compiled.add_token_count("openai", "gpt-3.5-turbo", 85);

        // Test exact matches
        assert_eq!(
            compiled.get_token_count_for_target_model("claude-3-sonnet"),
            95
        );
        assert_eq!(compiled.get_token_count_for_target_model("gpt-4"), 105);

        // Test partial matches for similar models
        assert_eq!(
            compiled.get_token_count_for_target_model("claude-3-haiku"),
            95
        );
        assert_eq!(
            compiled.get_token_count_for_target_model("gpt-4-turbo"),
            105
        );

        // Test fallback to estimated for unknown models
        assert_eq!(
            compiled.get_token_count_for_target_model("unknown-model"),
            95
        ); // First accurate count
    }

    #[test]
    fn test_tokenization_info() {
        let mut compiled = CompiledPrompt::new("test".to_string(), 120, vec![]);
        compiled.add_token_count("claude", "claude-3-sonnet", 115);

        let info = compiled.get_tokenization_info();
        assert_eq!(info.estimated_count, 120);
        assert_eq!(info.best_count, 115);
        assert_eq!(info.primary_provider, Some("claude".to_string()));
        assert_eq!(info.primary_model, Some("claude-3-sonnet".to_string()));
        assert_eq!(info.accurate_counts.len(), 1);
    }

    #[test]
    fn test_prompt_tokenization_methods() {
        let mut prompt = Prompt::new(
            "test-prompt",
            "1.0",
            "Test prompt",
            PromptType::UserInteraction,
            "Hello {{name}}!",
        );

        // Test before compilation
        assert!(prompt.estimated_token_count().is_none());
        assert!(prompt.best_token_count().is_none());
        assert!(!prompt.has_accurate_tokenization());
        assert!(prompt.tokenization_info().is_none());

        // Add compiled prompt
        let mut compiled = CompiledPrompt::with_accurate_tokenization(
            "test-template".to_string(),
            50,
            vec!["name".to_string()],
            "claude".to_string(),
            "claude-3-sonnet".to_string(),
            48,
        );
        compiled.add_token_count("openai", "gpt-4", 52);
        prompt.compiled = Some(compiled);

        // Test after compilation
        assert_eq!(prompt.estimated_token_count(), Some(50));
        assert_eq!(prompt.best_token_count(), Some(48)); // Prefers accurate over estimated
        assert_eq!(prompt.token_count_for_model("claude-3-sonnet"), Some(48));
        assert_eq!(prompt.token_count_for_model("gpt-4"), Some(52));
        assert!(prompt.has_accurate_tokenization());

        let info = prompt.tokenization_info().unwrap();
        assert_eq!(info.estimated_count, 50);
        assert_eq!(info.best_count, 48);
        assert_eq!(info.accurate_counts.len(), 2);
    }

    #[test]
    fn test_remove_token_count() {
        let mut compiled = CompiledPrompt::new("test".to_string(), 100, vec![]);

        compiled.add_token_count("claude", "claude-3-sonnet", 95);
        compiled.add_token_count("openai", "gpt-4", 105);

        assert_eq!(compiled.accurate_tokenization_count(), 2);

        compiled.remove_token_count("claude", "claude-3-sonnet");
        assert_eq!(compiled.accurate_tokenization_count(), 1);
        assert_eq!(
            compiled.get_token_count_for_model("claude", "claude-3-sonnet"),
            None
        );
        assert_eq!(
            compiled.get_token_count_for_model("openai", "gpt-4"),
            Some(105)
        );
    }

    #[test]
    fn test_touch_compilation_timestamp() {
        let mut compiled = CompiledPrompt::new("test".to_string(), 100, vec![]);
        let original_time = compiled.compiled_at;

        // Wait a small amount to ensure timestamp difference
        std::thread::sleep(std::time::Duration::from_millis(1));
        compiled.touch();

        assert!(compiled.compiled_at > original_time);
    }
}
