//! TokenizerFactory for automatic provider selection
//!
//! This module implements a factory pattern to automatically select the appropriate
//! tokenizer provider based on model names and configuration. It centralizes the logic
//! for choosing between different tokenization strategies and provides extensible
//! support for new models and providers.

use super::{
    ClaudeToken<PERSON>Provider, <PERSON>back<PERSON><PERSON>ider, TiktokenProvider, TokenizationError,
    TokenizationResult, TokenizerConfig, TokenizerProvider,
};
use std::sync::Arc;
use tracing::{debug, info, warn};

/// Factory for creating appropriate tokenizer providers
#[derive(Debug, Clone)]
pub struct TokenizerFactory {
    default_config: TokenizerConfig,
}

impl TokenizerFactory {
    /// Create a new TokenizerFactory with default configuration
    pub fn new() -> Self {
        Self {
            default_config: TokenizerConfig::default(),
        }
    }

    /// Create a new TokenizerFactory with custom configuration
    pub fn with_config(config: TokenizerConfig) -> Self {
        Self {
            default_config: config,
        }
    }

    /// Create a tokenizer provider for the specified model
    pub fn create_provider(&self, model: &str) -> TokenizationResult<Arc<dyn TokenizerProvider>> {
        self.create_provider_with_config(model, &self.default_config)
    }

    /// Create a tokenizer provider with custom configuration
    pub fn create_provider_with_config(
        &self,
        model: &str,
        config: &TokenizerConfig,
    ) -> TokenizationResult<Arc<dyn TokenizerProvider>> {
        let model_lower = model.to_lowercase();

        debug!("Creating tokenizer provider for model: {}", model);

        // Check for Claude models first
        if self.is_claude_model(&model_lower) {
            info!("Using Claude tokenizer for model: {}", model);
            let provider = ClaudeTokenizerProvider::new()?;
            return Ok(Arc::new(provider));
        }

        // Check for OpenAI models
        if self.is_openai_model(&model_lower) {
            info!("Using OpenAI tiktoken tokenizer for model: {}", model);
            let provider = TiktokenProvider::new(model)?;
            return Ok(Arc::new(provider));
        }

        // Check for other known model patterns
        if self.is_google_model(&model_lower) {
            warn!(
                "Google model detected ({}), using fallback tokenizer",
                model
            );
            return Ok(Arc::new(FallbackTokenizerProvider::new()));
        }

        if self.is_meta_model(&model_lower) {
            warn!("Meta model detected ({}), using fallback tokenizer", model);
            return Ok(Arc::new(FallbackTokenizerProvider::new()));
        }

        if self.is_mistral_model(&model_lower) {
            warn!(
                "Mistral model detected ({}), using fallback tokenizer",
                model
            );
            return Ok(Arc::new(FallbackTokenizerProvider::new()));
        }

        // Use fallback tokenizer for unsupported models
        warn!("Unknown model type ({}), using fallback tokenizer", model);
        Ok(Arc::new(FallbackTokenizerProvider::new()))
    }

    /// Check if a tokenizer is available for the model
    /// Always returns true since we have a fallback provider
    pub fn is_available(&self, _model: &str) -> bool {
        true // With fallback tokenizer, all models are supported
    }

    /// Check if a model has a specific tokenizer implementation
    /// Returns false if using the fallback tokenizer
    pub fn has_specific_tokenizer(&self, model: &str) -> bool {
        let model_lower = model.to_lowercase();
        self.is_claude_model(&model_lower) || self.is_openai_model(&model_lower)
    }

    /// Get the recommended configuration for a specific model
    pub fn get_model_config(&self, model: &str) -> TokenizerConfig {
        let model_lower = model.to_lowercase();

        let mut config = self.default_config.clone();
        config.model = model.to_string();

        // Set model-specific defaults
        if self.is_claude_model(&model_lower) {
            config.max_tokens = if model_lower.contains("claude-3") {
                200_000
            } else {
                100_000
            };
        } else if self.is_openai_model(&model_lower) {
            config.max_tokens = if model_lower.contains("gpt-4o") {
                128_000
            } else if model_lower.contains("gpt-4") && model_lower.contains("32k") {
                32_768
            } else if model_lower.contains("gpt-4") {
                8_192
            } else if model_lower.contains("gpt-3.5") && model_lower.contains("16k") {
                16_384
            } else if model_lower.contains("gpt-3.5") {
                4_096
            } else {
                8_192 // Default for other OpenAI models
            };
        } else {
            config.max_tokens = 8_192; // Conservative default
        }

        config
    }

    /// Get list of all supported models (with specific tokenizers)
    pub fn supported_models(&self) -> Vec<&'static str> {
        vec![
            // Claude models
            "claude-3-opus",
            "claude-3-sonnet",
            "claude-3-haiku",
            "claude-3-opus-20240229",
            "claude-3-sonnet-20240229",
            "claude-3-haiku-20240307",
            "claude-2",
            "claude-2.1",
            "claude-instant",
            "claude-instant-1.2",
            // OpenAI GPT-4o models
            "gpt-4o",
            "gpt-4o-mini",
            "gpt-4o-2024-08-06",
            "gpt-4o-mini-2024-07-18",
            // OpenAI GPT-4 models
            "gpt-4",
            "gpt-4-turbo",
            "gpt-4-turbo-preview",
            "gpt-4-0613",
            "gpt-4-32k",
            "gpt-4-32k-0613",
            "gpt-4-1106-preview",
            "gpt-4-0125-preview",
            // OpenAI GPT-3.5 models
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-0125",
            "gpt-3.5-turbo-1106",
            "gpt-3.5-turbo-16k",
            "gpt-3.5-turbo-0613",
            "gpt-3.5-turbo-16k-0613",
            // Legacy OpenAI models
            "text-davinci-003",
            "text-davinci-002",
            "text-curie-001",
            "text-babbage-001",
            "text-ada-001",
            "code-davinci-002",
            "code-cushman-001",
        ]
    }

    /// Get provider type for a model
    pub fn get_provider_type(&self, model: &str) -> &'static str {
        let model_lower = model.to_lowercase();

        if self.is_claude_model(&model_lower) {
            "claude"
        } else if self.is_openai_model(&model_lower) {
            "openai"
        } else if self.is_google_model(&model_lower) {
            "google"
        } else if self.is_meta_model(&model_lower) {
            "meta"
        } else if self.is_mistral_model(&model_lower) {
            "mistral"
        } else {
            "fallback"
        }
    }

    /// Create a provider directly by type and model
    pub fn create_provider_by_type(
        &self,
        provider_type: &str,
        model: &str,
    ) -> TokenizationResult<Arc<dyn TokenizerProvider>> {
        match provider_type.to_lowercase().as_str() {
            "claude" => {
                let provider = ClaudeTokenizerProvider::new()?;
                Ok(Arc::new(provider))
            }
            "openai" | "tiktoken" => {
                let provider = TiktokenProvider::new(model)?;
                Ok(Arc::new(provider))
            }
            "fallback" => Ok(Arc::new(FallbackTokenizerProvider::new())),
            _ => Err(TokenizationError::ConfigurationError {
                reason: format!("Unknown provider type: {}", provider_type),
            }),
        }
    }

    // Private helper methods for model detection

    fn is_claude_model(&self, model: &str) -> bool {
        model.contains("claude")
    }

    fn is_openai_model(&self, model: &str) -> bool {
        model.starts_with("gpt-")
            || model.starts_with("text-")
            || model.starts_with("code-")
            || model.contains("davinci")
            || model.contains("curie")
            || model.contains("babbage")
            || model.contains("ada")
    }

    fn is_google_model(&self, model: &str) -> bool {
        model.contains("gemini")
            || model.contains("palm")
            || model.contains("bard")
            || model.contains("google")
    }

    fn is_meta_model(&self, model: &str) -> bool {
        model.contains("llama") || model.contains("meta") || model.contains("codellama")
    }

    fn is_mistral_model(&self, model: &str) -> bool {
        model.contains("mistral") || model.contains("mixtral")
    }
}

impl Default for TokenizerFactory {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_factory_creation() {
        let factory = TokenizerFactory::new();
        assert_eq!(factory.default_config.model, "default");
        assert_eq!(factory.default_config.max_tokens, 8192);
    }

    #[test]
    fn test_factory_with_config() {
        let config = TokenizerConfig {
            model: "test-model".to_string(),
            max_tokens: 16384,
            ..Default::default()
        };

        let factory = TokenizerFactory::with_config(config.clone());
        assert_eq!(factory.default_config.model, "test-model");
        assert_eq!(factory.default_config.max_tokens, 16384);
    }

    #[test]
    fn test_is_available_always_true() {
        let factory = TokenizerFactory::new();

        // With fallback, all models are available
        assert!(factory.is_available("claude-3-sonnet"));
        assert!(factory.is_available("gpt-4"));
        assert!(factory.is_available("unknown-model"));
        assert!(factory.is_available("any-random-name"));
    }

    #[test]
    fn test_has_specific_tokenizer() {
        let factory = TokenizerFactory::new();

        // Claude models have specific tokenizer
        assert!(factory.has_specific_tokenizer("claude-3-sonnet"));
        assert!(factory.has_specific_tokenizer("claude-2.1"));
        assert!(factory.has_specific_tokenizer("claude-instant"));

        // OpenAI models have specific tokenizer
        assert!(factory.has_specific_tokenizer("gpt-4"));
        assert!(factory.has_specific_tokenizer("gpt-3.5-turbo"));
        assert!(factory.has_specific_tokenizer("text-davinci-003"));

        // Other models use fallback
        assert!(!factory.has_specific_tokenizer("llama-2-70b"));
        assert!(!factory.has_specific_tokenizer("gemini-pro"));
        assert!(!factory.has_specific_tokenizer("unknown-model"));
    }

    #[test]
    fn test_provider_type_detection() {
        let factory = TokenizerFactory::new();

        assert_eq!(factory.get_provider_type("claude-3-sonnet"), "claude");
        assert_eq!(factory.get_provider_type("gpt-4"), "openai");
        assert_eq!(factory.get_provider_type("gemini-pro"), "google");
        assert_eq!(factory.get_provider_type("llama-2-70b"), "meta");
        assert_eq!(factory.get_provider_type("mistral-7b"), "mistral");
        assert_eq!(factory.get_provider_type("unknown-model"), "fallback");
    }

    #[test]
    fn test_model_config_generation() {
        let factory = TokenizerFactory::new();

        // Claude 3 models should get 200K tokens
        let claude3_config = factory.get_model_config("claude-3-sonnet");
        assert_eq!(claude3_config.max_tokens, 200_000);
        assert_eq!(claude3_config.model, "claude-3-sonnet");

        // Claude 2 models should get 100K tokens
        let claude2_config = factory.get_model_config("claude-2");
        assert_eq!(claude2_config.max_tokens, 100_000);

        // GPT-4o models should get 128K tokens
        let gpt4o_config = factory.get_model_config("gpt-4o");
        assert_eq!(gpt4o_config.max_tokens, 128_000);

        // GPT-4 models should get 8K tokens (unless 32k variant)
        let gpt4_config = factory.get_model_config("gpt-4");
        assert_eq!(gpt4_config.max_tokens, 8_192);

        let gpt4_32k_config = factory.get_model_config("gpt-4-32k");
        assert_eq!(gpt4_32k_config.max_tokens, 32_768);

        // Unknown models should get conservative 8K default
        let unknown_config = factory.get_model_config("unknown-model");
        assert_eq!(unknown_config.max_tokens, 8_192);
    }

    #[test]
    fn test_supported_models_list() {
        let factory = TokenizerFactory::new();
        let models = factory.supported_models();

        assert!(!models.is_empty());
        assert!(models.contains(&"claude-3-sonnet"));
        assert!(models.contains(&"gpt-4"));
        assert!(models.contains(&"gpt-3.5-turbo"));
        assert!(models.contains(&"text-davinci-003"));
    }

    #[test]
    fn test_model_detection_methods() {
        let factory = TokenizerFactory::new();

        // Test Claude detection
        assert!(factory.is_claude_model("claude-3-sonnet"));
        assert!(factory.is_claude_model("claude-2"));
        assert!(!factory.is_claude_model("gpt-4"));

        // Test OpenAI detection
        assert!(factory.is_openai_model("gpt-4"));
        assert!(factory.is_openai_model("gpt-3.5-turbo"));
        assert!(factory.is_openai_model("text-davinci-003"));
        assert!(factory.is_openai_model("code-davinci-002"));
        assert!(!factory.is_openai_model("claude-3-sonnet"));

        // Test Google detection
        assert!(factory.is_google_model("gemini-pro"));
        assert!(factory.is_google_model("palm-2"));
        assert!(factory.is_google_model("bard"));
        assert!(!factory.is_google_model("gpt-4"));

        // Test Meta detection
        assert!(factory.is_meta_model("llama-2-70b"));
        assert!(factory.is_meta_model("codellama-34b"));
        assert!(!factory.is_meta_model("gpt-4"));

        // Test Mistral detection
        assert!(factory.is_mistral_model("mistral-7b"));
        assert!(factory.is_mistral_model("mixtral-8x7b"));
        assert!(!factory.is_mistral_model("gpt-4"));
    }

    #[tokio::test]
    async fn test_create_provider_fallback() {
        let factory = TokenizerFactory::new();

        // Test fallback provider creation for unknown models
        let result = factory.create_provider("unknown-model-xyz");
        assert!(result.is_ok());

        let result = factory.create_provider("llama-2-70b");
        assert!(result.is_ok());

        let result = factory.create_provider("gemini-pro");
        assert!(result.is_ok());
    }

    #[test]
    fn test_create_provider_by_type() {
        let factory = TokenizerFactory::new();

        // Test fallback provider creation
        let result = factory.create_provider_by_type("fallback", "any-model");
        assert!(result.is_ok());

        // Test invalid provider type
        let result = factory.create_provider_by_type("invalid-type", "any-model");
        assert!(result.is_err());

        if let Err(TokenizationError::ConfigurationError { reason }) = result {
            assert!(reason.contains("Unknown provider type"));
        } else {
            panic!("Expected ConfigurationError");
        }
    }
}
