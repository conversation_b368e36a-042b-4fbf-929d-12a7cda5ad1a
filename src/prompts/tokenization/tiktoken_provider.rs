//! Tiktoken-based tokenizer provider for OpenAI models
//!
//! This module provides tokenization support for OpenAI models using the tiktoken-rs library.
//! It supports different encodings (o200k_base, cl100k_base, p50k_base) and handles
//! chat completion message formatting for accurate token counting.

use async_trait::async_trait;
use std::collections::HashMap;
use tiktoken_rs::{
    get_chat_completion_max_tokens, ChatCompletionRequestMessage, CoreBPE, FunctionCall,
};

use super::{
    ModelTokenInfo, TokenUsage, TokenizationError, TokenizationResult, TokenizerConfig,
    TokenizerProvider,
};
use crate::llm::Message;

/// Tiktoken-based tokenizer provider for OpenAI models
pub struct TiktokenProvider {
    /// Model name this provider is configured for
    model: String,
    /// The underlying BPE encoder
    encoder: CoreBPE,
}

impl TiktokenProvider {
    /// Create a new TiktokenProvider for the specified model
    pub fn new(model: &str) -> TokenizationResult<Self> {
        // Determine the appropriate encoding for the model
        let encoding_name = Self::get_encoding_for_model(model)?;

        // Create the encoder based on the encoding name
        let encoder = match encoding_name {
            "o200k_base" => {
                tiktoken_rs::o200k_base().map_err(|e| TokenizationError::InitializationFailed {
                    reason: format!("Failed to initialize o200k_base encoder: {}", e),
                })?
            }
            "cl100k_base" => {
                tiktoken_rs::cl100k_base().map_err(|e| TokenizationError::InitializationFailed {
                    reason: format!("Failed to initialize cl100k_base encoder: {}", e),
                })?
            }
            "p50k_base" => {
                tiktoken_rs::p50k_base().map_err(|e| TokenizationError::InitializationFailed {
                    reason: format!("Failed to initialize p50k_base encoder: {}", e),
                })?
            }
            "p50k_edit" => {
                tiktoken_rs::p50k_edit().map_err(|e| TokenizationError::InitializationFailed {
                    reason: format!("Failed to initialize p50k_edit encoder: {}", e),
                })?
            }
            _ => {
                return Err(TokenizationError::ConfigurationError {
                    reason: format!("Unsupported encoding: {}", encoding_name),
                });
            }
        };

        Ok(Self {
            model: model.to_string(),
            encoder,
        })
    }

    /// Get the appropriate encoding name for a given model
    fn get_encoding_for_model(model: &str) -> TokenizationResult<&'static str> {
        // Map models to their encodings based on OpenAI's tiktoken
        match model {
            // GPT-4o models use o200k_base
            m if m.starts_with("gpt-4o") || m == "o1" || m == "o1-mini" => Ok("o200k_base"),
            // GPT-4 and GPT-3.5 models use cl100k_base
            m if m.starts_with("gpt-4") || m.starts_with("gpt-3.5") || m.starts_with("gpt-35") => {
                Ok("cl100k_base")
            }
            // Text completion models
            m if m.starts_with("text-davinci") => Ok("p50k_base"),
            // Code models
            m if m.starts_with("code-") => Ok("p50k_base"),
            // Edit models
            m if m.contains("-edit-") => Ok("p50k_edit"),
            // Default to cl100k_base for unknown models
            _ => Ok("cl100k_base"),
        }
    }

    /// Get token limits for specific OpenAI models
    fn get_model_limits(model: &str) -> (usize, usize) {
        match model {
            // GPT-4o models
            "gpt-4o" | "gpt-4o-2024-08-06" => (128000, 16384),
            "gpt-4o-mini" | "gpt-4o-mini-2024-07-18" => (128000, 16384),

            // GPT-4 Turbo models
            m if m.starts_with("gpt-4-turbo") => (128000, 4096),

            // GPT-4 models
            "gpt-4" | "gpt-4-0613" => (8192, 4096),
            "gpt-4-32k" | "gpt-4-32k-0613" => (32768, 4096),

            // GPT-3.5 Turbo models
            "gpt-3.5-turbo" | "gpt-3.5-turbo-0125" => (16385, 4096),
            "gpt-3.5-turbo-16k" => (16385, 4096),

            // Legacy models
            "text-davinci-003" => (4097, 4097),
            "text-davinci-002" => (4097, 4097),

            // Default limits
            _ => (8192, 4096),
        }
    }

    /// Count tokens for chat messages using OpenAI's chat format
    ///
    /// This method properly handles the special formatting used by OpenAI's chat models,
    /// including role prefixes, message separators, and tool-related fields.
    pub fn count_chat_tokens(&self, messages: &[Message]) -> TokenizationResult<usize> {
        // Convert our Message format to tiktoken's expected format
        let chat_messages: Vec<ChatCompletionRequestMessage> = messages
            .iter()
            .map(|msg| {
                // Handle tool calls - convert to function_call format if present
                let function_call = if let Some(tool_calls) = &msg.tool_calls {
                    // For simplicity, we'll just count the JSON representation
                    tool_calls.first().map(|tc| FunctionCall {
                        name: tc.name.clone(),
                        arguments: tc.input.to_string(),
                    })
                } else {
                    None
                };

                ChatCompletionRequestMessage {
                    role: msg.role.clone(),
                    content: Some(msg.content.clone()),
                    name: None,
                    function_call,
                }
            })
            .collect();

        // Calculate max tokens (which gives us remaining tokens after messages)
        let max_tokens_result = get_chat_completion_max_tokens(&self.model, &chat_messages)
            .map_err(|e| TokenizationError::EncodingFailed {
                reason: format!("Failed to count chat tokens: {}", e),
            })?;

        // Get total model context to calculate used tokens
        let (max_context, _) = Self::get_model_limits(&self.model);
        let used_tokens = max_context.saturating_sub(max_tokens_result);

        Ok(used_tokens)
    }
}

#[async_trait]
impl TokenizerProvider for TiktokenProvider {
    async fn count_tokens(
        &self,
        text: &str,
        _config: &TokenizerConfig,
    ) -> TokenizationResult<usize> {
        let tokens = self.encoder.encode_ordinary(text);
        Ok(tokens.len())
    }

    async fn encode(&self, text: &str, _config: &TokenizerConfig) -> TokenizationResult<Vec<u32>> {
        let tokens = self.encoder.encode_ordinary(text);
        Ok(tokens.into_iter().map(|t| t as u32).collect())
    }

    async fn decode(
        &self,
        tokens: &[u32],
        _config: &TokenizerConfig,
    ) -> TokenizationResult<String> {
        let tokens_vec: Vec<u32> = tokens.to_vec();

        self.encoder
            .decode(tokens_vec)
            .map_err(|e| TokenizationError::DecodingFailed {
                reason: format!("Failed to decode tokens: {}", e),
            })
    }

    async fn get_model_info(&self, model: &str) -> TokenizationResult<ModelTokenInfo> {
        let (max_context, max_output) = Self::get_model_limits(model);

        // Get pricing information (in USD per 1K tokens)
        let (input_cost, output_cost) = match model {
            // GPT-4o models
            "gpt-4o" | "gpt-4o-2024-08-06" => (Some(0.0025), Some(0.01)),
            "gpt-4o-mini" | "gpt-4o-mini-2024-07-18" => (Some(0.00015), Some(0.0006)),

            // GPT-4 Turbo
            m if m.starts_with("gpt-4-turbo") => (Some(0.01), Some(0.03)),

            // GPT-4
            "gpt-4" | "gpt-4-0613" => (Some(0.03), Some(0.06)),
            "gpt-4-32k" => (Some(0.06), Some(0.12)),

            // GPT-3.5 Turbo
            m if m.starts_with("gpt-3.5-turbo") => (Some(0.0005), Some(0.0015)),

            _ => (None, None),
        };

        // Create special tokens map based on encoding
        let encoding_name = Self::get_encoding_for_model(model)?;
        let mut special_tokens = HashMap::new();

        // Add common special tokens
        special_tokens.insert("<|endoftext|>".to_string(), 50256);

        if encoding_name == "cl100k_base" || encoding_name == "o200k_base" {
            special_tokens.insert("<|im_start|>".to_string(), 100264);
            special_tokens.insert("<|im_end|>".to_string(), 100265);
            special_tokens.insert("<|im_sep|>".to_string(), 100266);
        }

        Ok(ModelTokenInfo {
            model: model.to_string(),
            max_context_tokens: max_context,
            max_output_tokens: max_output,
            input_token_cost: input_cost,
            output_token_cost: output_cost,
            special_tokens,
        })
    }

    async fn calculate_usage(
        &self,
        text: &str,
        config: &TokenizerConfig,
    ) -> TokenizationResult<TokenUsage> {
        let token_count = self.count_tokens(text, config).await?;
        let model_info = self.get_model_info(&self.model).await?;

        let usage_percentage = (token_count as f32 / model_info.max_context_tokens as f32) * 100.0;
        let remaining_tokens = model_info.max_context_tokens.saturating_sub(token_count);

        Ok(TokenUsage {
            total_tokens: token_count,
            prompt_tokens: token_count,
            completion_tokens: None,
            usage_percentage,
            remaining_tokens,
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_tiktoken_provider_creation() {
        let provider = TiktokenProvider::new("gpt-4").unwrap();
        assert_eq!(provider.model, "gpt-4");
    }

    #[tokio::test]
    async fn test_encoding_detection() {
        assert_eq!(
            TiktokenProvider::get_encoding_for_model("gpt-4o").unwrap(),
            "o200k_base"
        );
        assert_eq!(
            TiktokenProvider::get_encoding_for_model("gpt-4").unwrap(),
            "cl100k_base"
        );
        assert_eq!(
            TiktokenProvider::get_encoding_for_model("gpt-3.5-turbo").unwrap(),
            "cl100k_base"
        );
        assert_eq!(
            TiktokenProvider::get_encoding_for_model("text-davinci-003").unwrap(),
            "p50k_base"
        );
    }

    #[tokio::test]
    async fn test_token_counting() {
        let provider = TiktokenProvider::new("gpt-4").unwrap();
        let config = TokenizerConfig::default();

        let count = provider
            .count_tokens("Hello, world!", &config)
            .await
            .unwrap();
        assert!(count > 0);
        assert!(count < 10); // Simple text should have few tokens
    }

    #[tokio::test]
    async fn test_encode_decode_roundtrip() {
        let provider = TiktokenProvider::new("gpt-4").unwrap();
        let config = TokenizerConfig::default();
        let text = "This is a test of the tokenization system.";

        let tokens = provider.encode(text, &config).await.unwrap();
        let decoded = provider.decode(&tokens, &config).await.unwrap();

        assert_eq!(text, decoded);
    }

    #[tokio::test]
    async fn test_model_info() {
        let provider = TiktokenProvider::new("gpt-4").unwrap();
        let info = provider.get_model_info("gpt-4").await.unwrap();

        assert_eq!(info.model, "gpt-4");
        assert_eq!(info.max_context_tokens, 8192);
        assert_eq!(info.max_output_tokens, 4096);
        assert_eq!(info.input_token_cost, Some(0.03));
        assert_eq!(info.output_token_cost, Some(0.06));
    }

    #[tokio::test]
    async fn test_chat_message_counting() {
        let provider = TiktokenProvider::new("gpt-4").unwrap();

        let messages = vec![
            Message::system("You are a helpful assistant."),
            Message::user("Hello!"),
            Message::assistant("Hi there! How can I help you today?"),
        ];

        let count = provider.count_chat_tokens(&messages).unwrap();
        assert!(count > 0);
        // Chat formatting adds overhead beyond just the text
        assert!(count > 15);
    }
}
