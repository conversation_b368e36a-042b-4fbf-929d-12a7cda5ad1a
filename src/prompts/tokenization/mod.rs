//! Tokenization support for prompt management
//!
//! This module provides tokenization capabilities for accurate token counting,
//! encoding, and decoding across different language models. It supports model-specific
//! tokenizers and handles special tokens appropriately.

pub mod claude;
pub mod factory;
pub mod fallback_provider;
pub mod tiktoken_provider;

// Re-export providers
pub use claude::ClaudeTokenizerProvider;
pub use factory::TokenizerFactory;
pub use fallback_provider::{FallbackTokenizerExt, FallbackTokenizerProvider};
pub use tiktoken_provider::TiktokenProvider;

use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use thiserror::Error;

/// Result type for tokenization operations
pub type TokenizationResult<T> = std::result::Result<T, TokenizationError>;

/// Error types for tokenization operations
#[derive(Error, Debug)]
pub enum TokenizationError {
    #[error("Tokenizer not available for model: {model}")]
    TokenizerNotAvailable { model: String },

    #[error("Failed to encode text: {reason}")]
    EncodingFailed { reason: String },

    #[error("Failed to decode tokens: {reason}")]
    DecodingFailed { reason: String },

    #[error("Invalid token ID: {id}")]
    InvalidTokenId { id: u32 },

    #[error("Model configuration error: {reason}")]
    ConfigurationError { reason: String },

    #[error("Token limit exceeded: {used} > {limit}")]
    TokenLimitExceeded { used: usize, limit: usize },

    #[error("Special token handling error: {reason}")]
    SpecialTokenError { reason: String },

    #[error("Tokenizer initialization failed: {reason}")]
    InitializationFailed { reason: String },

    #[error("I/O error: {0}")]
    Io(#[from] std::io::Error),

    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),
}

/// Configuration for tokenizer behavior
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenizerConfig {
    /// Model identifier (e.g., "claude-3-sonnet", "gpt-4")
    pub model: String,

    /// Maximum tokens allowed for this model
    pub max_tokens: usize,

    /// Whether to include special tokens in counting
    pub include_special_tokens: bool,

    /// Custom token limits for specific use cases
    pub custom_limits: HashMap<String, usize>,

    /// Padding strategy
    pub padding_strategy: PaddingStrategy,

    /// Truncation strategy
    pub truncation_strategy: TruncationStrategy,
}

impl Default for TokenizerConfig {
    fn default() -> Self {
        Self {
            model: String::from("default"),
            max_tokens: 8192,
            include_special_tokens: true,
            custom_limits: HashMap::new(),
            padding_strategy: PaddingStrategy::None,
            truncation_strategy: TruncationStrategy::End,
        }
    }
}

/// Padding strategy for tokenization
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq)]
pub enum PaddingStrategy {
    /// No padding
    None,
    /// Pad to maximum length
    MaxLength,
    /// Pad to longest sequence in batch
    Longest,
    /// Pad to specific length
    Fixed(usize),
}

/// Truncation strategy for tokenization
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq)]
pub enum TruncationStrategy {
    /// Truncate from the end
    End,
    /// Truncate from the beginning
    Beginning,
    /// Truncate from both ends equally
    Both,
    /// No truncation (may fail if exceeds limit)
    None,
}

/// Information about token usage
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenUsage {
    /// Total number of tokens used
    pub total_tokens: usize,

    /// Number of prompt tokens
    pub prompt_tokens: usize,

    /// Number of completion tokens (if applicable)
    pub completion_tokens: Option<usize>,

    /// Percentage of model limit used
    pub usage_percentage: f32,

    /// Remaining tokens available
    pub remaining_tokens: usize,
}

/// Model-specific token information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelTokenInfo {
    /// Model identifier
    pub model: String,

    /// Maximum context window size
    pub max_context_tokens: usize,

    /// Maximum output tokens
    pub max_output_tokens: usize,

    /// Cost per 1K input tokens (if applicable)
    pub input_token_cost: Option<f64>,

    /// Cost per 1K output tokens (if applicable)
    pub output_token_cost: Option<f64>,

    /// Special token mappings
    pub special_tokens: HashMap<String, u32>,
}

/// Trait for tokenizer providers
#[async_trait]
pub trait TokenizerProvider: Send + Sync {
    /// Count tokens in the given text
    async fn count_tokens(&self, text: &str, config: &TokenizerConfig)
        -> TokenizationResult<usize>;

    /// Encode text into token IDs
    async fn encode(&self, text: &str, config: &TokenizerConfig) -> TokenizationResult<Vec<u32>>;

    /// Decode token IDs back to text
    async fn decode(&self, tokens: &[u32], config: &TokenizerConfig) -> TokenizationResult<String>;

    /// Get model-specific token information
    async fn get_model_info(&self, model: &str) -> TokenizationResult<ModelTokenInfo>;

    /// Calculate token usage for given text
    async fn calculate_usage(
        &self,
        text: &str,
        config: &TokenizerConfig,
    ) -> TokenizationResult<TokenUsage> {
        let token_count = self.count_tokens(text, config).await?;
        let model_info = self.get_model_info(&config.model).await?;

        let usage_percentage = (token_count as f32 / model_info.max_context_tokens as f32) * 100.0;
        let remaining_tokens = model_info.max_context_tokens.saturating_sub(token_count);

        Ok(TokenUsage {
            total_tokens: token_count,
            prompt_tokens: token_count,
            completion_tokens: None,
            usage_percentage,
            remaining_tokens,
        })
    }

    /// Validate that text fits within token limits
    async fn validate_token_limit(
        &self,
        text: &str,
        config: &TokenizerConfig,
    ) -> TokenizationResult<bool> {
        let token_count = self.count_tokens(text, config).await?;
        let model_info = self.get_model_info(&config.model).await?;

        if token_count > model_info.max_context_tokens {
            return Err(TokenizationError::TokenLimitExceeded {
                used: token_count,
                limit: model_info.max_context_tokens,
            });
        }

        Ok(true)
    }

    /// Truncate text to fit within token limit
    async fn truncate_to_limit(
        &self,
        text: &str,
        max_tokens: usize,
        config: &TokenizerConfig,
    ) -> TokenizationResult<String> {
        let tokens = self.encode(text, config).await?;

        if tokens.len() <= max_tokens {
            return Ok(text.to_string());
        }

        let truncated_tokens = match config.truncation_strategy {
            TruncationStrategy::End => &tokens[..max_tokens],
            TruncationStrategy::Beginning => &tokens[tokens.len() - max_tokens..],
            TruncationStrategy::Both => {
                let start = (tokens.len() - max_tokens) / 2;
                let end = start + max_tokens;
                &tokens[start..end]
            }
            TruncationStrategy::None => {
                return Err(TokenizationError::TokenLimitExceeded {
                    used: tokens.len(),
                    limit: max_tokens,
                });
            }
        };

        self.decode(truncated_tokens, config).await
    }

    /// Split text into chunks that fit within token limit
    async fn split_by_tokens(
        &self,
        text: &str,
        max_tokens_per_chunk: usize,
        config: &TokenizerConfig,
    ) -> TokenizationResult<Vec<String>> {
        let tokens = self.encode(text, config).await?;
        let mut chunks = Vec::new();

        for chunk_tokens in tokens.chunks(max_tokens_per_chunk) {
            let chunk_text = self.decode(chunk_tokens, config).await?;
            chunks.push(chunk_text);
        }

        Ok(chunks)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_tokenizer_config_default() {
        let config = TokenizerConfig::default();
        assert_eq!(config.model, "default");
        assert_eq!(config.max_tokens, 8192);
        assert!(config.include_special_tokens);
    }

    #[test]
    fn test_token_usage_calculation() {
        let usage = TokenUsage {
            total_tokens: 1000,
            prompt_tokens: 1000,
            completion_tokens: None,
            usage_percentage: 12.2,
            remaining_tokens: 7192,
        };

        assert_eq!(usage.total_tokens, 1000);
        assert_eq!(usage.remaining_tokens, 7192);
    }

    #[test]
    fn test_supported_models() {
        let factory = TokenizerFactory::new();
        let models = factory.supported_models();
        assert!(!models.is_empty());
        assert!(models.contains(&"claude-3-sonnet"));
    }

    #[test]
    fn test_fallback_tokenizer_creation() {
        let factory = TokenizerFactory::new();

        // Test that unsupported models get fallback tokenizer
        let result = factory.create_provider("unknown-model-xyz");
        assert!(result.is_ok());

        let result = factory.create_provider("llama-2-70b");
        assert!(result.is_ok());
    }

    #[test]
    fn test_is_available_always_true() {
        let factory = TokenizerFactory::new();

        // With fallback, all models are available
        assert!(factory.is_available("claude-3-sonnet"));
        assert!(factory.is_available("gpt-4"));
        assert!(factory.is_available("unknown-model"));
        assert!(factory.is_available("any-model-name"));
    }

    #[test]
    fn test_has_specific_tokenizer() {
        let factory = TokenizerFactory::new();

        // Claude models have specific tokenizer
        assert!(factory.has_specific_tokenizer("claude-3-sonnet"));
        assert!(factory.has_specific_tokenizer("claude-2.1"));

        // OpenAI models have specific tokenizer
        assert!(factory.has_specific_tokenizer("gpt-4"));
        assert!(factory.has_specific_tokenizer("gpt-3.5-turbo"));
        assert!(factory.has_specific_tokenizer("text-davinci-003"));

        // Other models use fallback
        assert!(!factory.has_specific_tokenizer("llama-2-70b"));
        assert!(!factory.has_specific_tokenizer("gemini-pro"));
        assert!(!factory.has_specific_tokenizer("unknown-model"));
    }
}
