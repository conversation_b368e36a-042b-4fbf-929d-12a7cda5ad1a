//! Fallback tokenizer provider for unsupported models
//!
//! This module provides a simple word-counting based tokenizer that serves as a fallback
//! for models that don't have specific tokenization support. It uses conservative
//! estimates to ensure prompts don't exceed model limits.

use async_trait::async_trait;
use std::collections::HashMap;

use crate::llm::Message;
use crate::prompts::tokenization::{
    ModelTokenInfo, TokenizationError, TokenizationResult, TokenizerConfig, TokenizerProvider,
    TruncationStrategy,
};
use crate::prompts::PromptResult;

/// Fallback tokenizer provider using word counting
///
/// This provider estimates token counts using a simple word-counting approach
/// with a conservative multiplier. It provides basic functionality for models
/// without specific tokenization support.
#[derive(Debug, <PERSON>lone)]
pub struct FallbackTokenizerProvider {
    /// Token estimation multiplier (words to tokens ratio)
    token_multiplier: f32,
}

impl FallbackTokenizerProvider {
    /// Create a new fallback tokenizer provider
    pub fn new() -> Self {
        Self {
            // Conservative estimate: 1.3 tokens per word on average
            // This accounts for punctuation, special characters, and subword tokenization
            token_multiplier: 1.3,
        }
    }

    /// Create a fallback tokenizer with custom multiplier
    pub fn with_multiplier(multiplier: f32) -> Self {
        Self {
            token_multiplier: multiplier,
        }
    }

    /// Count words in text (used as base for token estimation)
    fn count_words(&self, text: &str) -> usize {
        text.split_whitespace().count()
    }

    /// Estimate tokens based on word count
    fn estimate_tokens(&self, text: &str) -> usize {
        let word_count = self.count_words(text);
        (word_count as f32 * self.token_multiplier).ceil() as usize
    }

    /// Get default model info for fallback models
    fn get_default_model_info(&self, model: &str) -> ModelTokenInfo {
        // Conservative defaults based on common model limits
        let (max_context, max_output) = if model.contains("gpt-4") {
            (128_000, 4_096)
        } else if model.contains("claude") {
            (200_000, 4_096)
        } else if model.contains("gemini") {
            (128_000, 8_192)
        } else {
            // Very conservative default
            (8_192, 2_048)
        };

        ModelTokenInfo {
            model: model.to_string(),
            max_context_tokens: max_context,
            max_output_tokens: max_output,
            input_token_cost: None,
            output_token_cost: None,
            special_tokens: HashMap::new(),
        }
    }

    /// Split text by word boundaries for truncation
    fn split_by_words<'a>(&self, text: &'a str) -> Vec<&'a str> {
        text.split_whitespace().collect()
    }

    /// Join words back into text
    fn join_words(&self, words: &[&str]) -> String {
        words.join(" ")
    }
}

impl Default for FallbackTokenizerProvider {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl TokenizerProvider for FallbackTokenizerProvider {
    /// Count tokens in the given text using word-based estimation
    async fn count_tokens(
        &self,
        text: &str,
        _config: &TokenizerConfig,
    ) -> TokenizationResult<usize> {
        Ok(self.estimate_tokens(text))
    }

    /// Encode text into token IDs (not supported for fallback)
    async fn encode(&self, _text: &str, _config: &TokenizerConfig) -> TokenizationResult<Vec<u32>> {
        Err(TokenizationError::EncodingFailed {
            reason: "Fallback tokenizer does not support encoding to token IDs".to_string(),
        })
    }

    /// Decode token IDs back to text (not supported for fallback)
    async fn decode(
        &self,
        _tokens: &[u32],
        _config: &TokenizerConfig,
    ) -> TokenizationResult<String> {
        Err(TokenizationError::DecodingFailed {
            reason: "Fallback tokenizer does not support decoding from token IDs".to_string(),
        })
    }

    /// Get model-specific token information
    async fn get_model_info(&self, model: &str) -> TokenizationResult<ModelTokenInfo> {
        Ok(self.get_default_model_info(model))
    }

    /// Truncate text to fit within token limit
    async fn truncate_to_limit(
        &self,
        text: &str,
        max_tokens: usize,
        config: &TokenizerConfig,
    ) -> TokenizationResult<String> {
        let estimated_tokens = self.estimate_tokens(text);

        if estimated_tokens <= max_tokens {
            return Ok(text.to_string());
        }

        // Calculate target word count based on token limit
        let target_words = (max_tokens as f32 / self.token_multiplier).floor() as usize;
        let words: Vec<&str> = self.split_by_words(text);

        if words.len() <= target_words {
            return Ok(text.to_string());
        }

        let truncated_words = match config.truncation_strategy {
            TruncationStrategy::End => &words[..target_words],
            TruncationStrategy::Beginning => &words[words.len() - target_words..],
            TruncationStrategy::Both => {
                let start = (words.len() - target_words) / 2;
                let end = start + target_words;
                &words[start..end]
            }
            TruncationStrategy::None => {
                return Err(TokenizationError::TokenLimitExceeded {
                    used: estimated_tokens,
                    limit: max_tokens,
                });
            }
        };

        Ok(self.join_words(truncated_words))
    }

    /// Split text into chunks that fit within token limit
    async fn split_by_tokens(
        &self,
        text: &str,
        max_tokens_per_chunk: usize,
        _config: &TokenizerConfig,
    ) -> TokenizationResult<Vec<String>> {
        let target_words_per_chunk =
            (max_tokens_per_chunk as f32 / self.token_multiplier).floor() as usize;
        let words: Vec<&str> = self.split_by_words(text);
        let mut chunks = Vec::new();

        for chunk_words in words.chunks(target_words_per_chunk) {
            chunks.push(self.join_words(chunk_words));
        }

        Ok(chunks)
    }
}

/// Extension trait for PromptResult compatibility
pub trait FallbackTokenizerExt {
    /// Count tokens in text using the fallback method (synchronous)
    fn count_tokens_sync(&self, text: &str) -> PromptResult<usize>;

    /// Count tokens for messages (synchronous)
    fn count_tokens_for_messages_sync(&self, messages: &[Message]) -> PromptResult<usize>;

    /// Get provider name
    fn provider_name(&self) -> &str;

    /// Check if this provider supports a model (always true for fallback)
    fn supports_model(&self, model: &str) -> bool;
}

impl FallbackTokenizerExt for FallbackTokenizerProvider {
    fn count_tokens_sync(&self, text: &str) -> PromptResult<usize> {
        Ok(self.estimate_tokens(text))
    }

    fn count_tokens_for_messages_sync(&self, messages: &[Message]) -> PromptResult<usize> {
        let total_text = messages
            .iter()
            .map(|m| format!("{}: {}", m.role, m.content))
            .collect::<Vec<_>>()
            .join("\n");
        self.count_tokens_sync(&total_text)
    }

    fn provider_name(&self) -> &str {
        "fallback"
    }

    fn supports_model(&self, _model: &str) -> bool {
        // Fallback provider supports all models
        true
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_word_counting() {
        let provider = FallbackTokenizerProvider::new();
        assert_eq!(provider.count_words("Hello world"), 2);
        assert_eq!(provider.count_words("The quick brown fox"), 4);
        assert_eq!(provider.count_words("  Multiple   spaces  "), 2);
        assert_eq!(provider.count_words(""), 0);
    }

    #[tokio::test]
    async fn test_token_estimation() {
        let provider = FallbackTokenizerProvider::new();
        let config = TokenizerConfig::default();

        // Test with default multiplier (1.3)
        let tokens = provider.count_tokens("Hello world", &config).await.unwrap();
        assert_eq!(tokens, 3); // 2 words * 1.3 = 2.6, rounded up to 3

        let tokens = provider
            .count_tokens("The quick brown fox", &config)
            .await
            .unwrap();
        assert_eq!(tokens, 6); // 4 words * 1.3 = 5.2, rounded up to 6
    }

    #[tokio::test]
    async fn test_custom_multiplier() {
        let provider = FallbackTokenizerProvider::with_multiplier(2.0);
        let config = TokenizerConfig::default();

        let tokens = provider.count_tokens("Hello world", &config).await.unwrap();
        assert_eq!(tokens, 4); // 2 words * 2.0 = 4
    }

    #[tokio::test]
    async fn test_encode_not_supported() {
        let provider = FallbackTokenizerProvider::new();
        let config = TokenizerConfig::default();

        let result = provider.encode("Hello world", &config).await;
        assert!(result.is_err());
        assert!(matches!(
            result,
            Err(TokenizationError::EncodingFailed { .. })
        ));
    }

    #[tokio::test]
    async fn test_decode_not_supported() {
        let provider = FallbackTokenizerProvider::new();
        let config = TokenizerConfig::default();

        let result = provider.decode(&[1, 2, 3], &config).await;
        assert!(result.is_err());
        assert!(matches!(
            result,
            Err(TokenizationError::DecodingFailed { .. })
        ));
    }

    #[tokio::test]
    async fn test_model_info() {
        let provider = FallbackTokenizerProvider::new();

        // Test Claude model
        let info = provider.get_model_info("claude-3-sonnet").await.unwrap();
        assert_eq!(info.max_context_tokens, 200_000);
        assert_eq!(info.max_output_tokens, 4_096);

        // Test GPT-4 model
        let info = provider.get_model_info("gpt-4-turbo").await.unwrap();
        assert_eq!(info.max_context_tokens, 128_000);
        assert_eq!(info.max_output_tokens, 4_096);

        // Test unknown model (conservative defaults)
        let info = provider.get_model_info("unknown-model").await.unwrap();
        assert_eq!(info.max_context_tokens, 8_192);
        assert_eq!(info.max_output_tokens, 2_048);
    }

    #[tokio::test]
    async fn test_truncation_end() {
        let provider = FallbackTokenizerProvider::new();
        let mut config = TokenizerConfig::default();
        config.truncation_strategy = TruncationStrategy::End;

        let text = "The quick brown fox jumps over the lazy dog";
        // 9 words * 1.3 = 11.7 tokens
        // Truncate to 7 tokens = ~5 words
        let truncated = provider.truncate_to_limit(text, 7, &config).await.unwrap();
        assert_eq!(truncated, "The quick brown fox jumps");
    }

    #[tokio::test]
    async fn test_truncation_beginning() {
        let provider = FallbackTokenizerProvider::new();
        let mut config = TokenizerConfig::default();
        config.truncation_strategy = TruncationStrategy::Beginning;

        let text = "The quick brown fox jumps over the lazy dog";
        let truncated = provider.truncate_to_limit(text, 7, &config).await.unwrap();
        assert_eq!(truncated, "jumps over the lazy dog");
    }

    #[tokio::test]
    async fn test_truncation_both() {
        let provider = FallbackTokenizerProvider::new();
        let mut config = TokenizerConfig::default();
        config.truncation_strategy = TruncationStrategy::Both;

        let text = "The quick brown fox jumps over the lazy dog";
        let truncated = provider.truncate_to_limit(text, 7, &config).await.unwrap();
        assert_eq!(truncated, "brown fox jumps over the");
    }

    #[tokio::test]
    async fn test_no_truncation_needed() {
        let provider = FallbackTokenizerProvider::new();
        let config = TokenizerConfig::default();

        let text = "Hello world";
        let truncated = provider.truncate_to_limit(text, 10, &config).await.unwrap();
        assert_eq!(truncated, "Hello world");
    }

    #[tokio::test]
    async fn test_split_by_tokens() {
        let provider = FallbackTokenizerProvider::new();
        let config = TokenizerConfig::default();

        let text = "The quick brown fox jumps over the lazy dog and runs away";
        // 12 words, ~15.6 tokens
        // Split into chunks of max 8 tokens each (~6 words)
        let chunks = provider.split_by_tokens(text, 8, &config).await.unwrap();

        assert_eq!(chunks.len(), 2);
        assert_eq!(chunks[0], "The quick brown fox jumps over");
        assert_eq!(chunks[1], "the lazy dog and runs away");
    }

    #[tokio::test]
    async fn test_fallback_ext_trait() {
        let provider = FallbackTokenizerProvider::new();

        // Test count_tokens_sync
        let tokens = provider.count_tokens_sync("Hello world").unwrap();
        assert_eq!(tokens, 3);

        // Test count_tokens_for_messages_sync
        let messages = vec![Message::user("Hello"), Message::assistant("Hi there")];
        let tokens = provider.count_tokens_for_messages_sync(&messages).unwrap();
        assert!(tokens > 0);

        // Test provider_name
        assert_eq!(provider.provider_name(), "fallback");

        // Test supports_model (always true)
        assert!(provider.supports_model("any-model"));
        assert!(provider.supports_model("unknown-model"));
    }
}
