//! Template engine implementation for prompt rendering

use serde_json::Value;
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use tera::{Context as TeraContext, Error as <PERSON>raError, <PERSON>ra};

use crate::prompts::error::{PromptError, PromptResult};

/// Template engine error
#[derive(Debug, thiserror::Error)]
pub enum TemplateError {
    #[error("Tera error: {0}")]
    Tera(#[from] TeraError),

    #[error("Template not found: {name}")]
    TemplateNotFound { name: String },

    #[error("Variable not found: {variable}")]
    VariableNotFound { variable: String },

    #[error("Invalid template syntax: {reason}")]
    InvalidSyntax { reason: String },
}

/// Template engine wrapper for prompt rendering
pub struct TemplateEngine {
    /// Tera instance
    tera: Tera,
    /// Base directory for template resolution
    base_dir: Option<PathBuf>,
    /// Custom functions and filters
    custom_functions: HashMap<String, Box<dyn tera::Function + Send + Sync>>,
}

impl std::fmt::Debug for TemplateEngine {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("TemplateEngine")
            .field("base_dir", &self.base_dir)
            .field("custom_functions_count", &self.custom_functions.len())
            .finish()
    }
}

impl TemplateEngine {
    /// Create a new template engine
    pub fn new() -> Self {
        let mut tera = Tera::default();
        Self::register_built_in_functions(&mut tera);

        Self {
            tera,
            base_dir: None,
            custom_functions: HashMap::new(),
        }
    }

    /// Create a template engine with a base directory
    pub fn with_base_dir<P: AsRef<Path>>(base_dir: P) -> PromptResult<Self> {
        let base_dir = base_dir.as_ref().to_path_buf();
        let mut tera = Tera::default();
        Self::register_built_in_functions(&mut tera);

        Ok(Self {
            tera,
            base_dir: Some(base_dir),
            custom_functions: HashMap::new(),
        })
    }

    /// Create from a glob pattern
    pub fn from_glob<P: AsRef<Path>>(pattern: P) -> PromptResult<Self> {
        let tera = Tera::new(
            pattern
                .as_ref()
                .to_str()
                .ok_or_else(|| PromptError::invalid_structure("Invalid path in glob pattern"))?,
        )?;

        Ok(Self {
            tera,
            base_dir: None,
            custom_functions: HashMap::new(),
        })
    }

    /// Add a template from string content
    pub fn add_template(&mut self, name: &str, content: &str) -> PromptResult<()> {
        self.tera.add_raw_template(name, content)?;
        Ok(())
    }

    /// Add a template from file
    pub fn add_template_file<P: AsRef<Path>>(&mut self, name: &str, path: P) -> PromptResult<()> {
        let content = std::fs::read_to_string(path)?;
        self.add_template(name, &content)
    }

    /// Render a template with variables
    pub fn render(
        &self,
        template_name: &str,
        variables: &HashMap<String, Value>,
    ) -> PromptResult<String> {
        let mut context = TeraContext::new();
        for (key, value) in variables {
            context.insert(key, value);
        }

        let result = self.tera.render(template_name, &context)?;
        Ok(result)
    }

    /// Render a template string (one-off rendering)
    pub fn render_string(
        &self,
        template: &str,
        variables: &HashMap<String, Value>,
    ) -> PromptResult<String> {
        let mut context = TeraContext::new();
        for (key, value) in variables {
            context.insert(key, value);
        }

        let result = Tera::one_off(template, &context, true)?;
        Ok(result)
    }

    /// Check if a template exists
    pub fn has_template(&self, name: &str) -> bool {
        self.tera.get_template(name).is_ok()
    }

    /// Get all template names
    pub fn get_template_names(&self) -> Vec<String> {
        self.tera
            .get_template_names()
            .map(|s| s.to_string())
            .collect()
    }

    /// Add a custom filter
    pub fn add_filter<F>(&mut self, name: &str, filter: F) -> PromptResult<()>
    where
        F: tera::Filter + 'static,
    {
        self.tera.register_filter(name, filter);
        Ok(())
    }

    /// Add a custom function
    pub fn add_function<F>(&mut self, name: &str, function: F) -> PromptResult<()>
    where
        F: tera::Function + Send + Sync + 'static,
    {
        self.tera.register_function(name, function);
        Ok(())
    }

    /// Process includes in template content
    pub fn process_includes(&self, content: &str) -> PromptResult<String> {
        let mut processed = content.to_string();
        let include_regex = regex::Regex::new(r#"!\s*include\s+['"]([^'"]+)['"]"#)
            .map_err(|e| PromptError::compilation_error(format!("Invalid include regex: {}", e)))?;

        // Track included files to prevent infinite recursion
        let mut included_files = std::collections::HashSet::new();

        // Process includes recursively (with depth limit)
        for _ in 0..10 {
            // Max 10 levels of includes
            let mut found_includes = false;

            for captures in include_regex.captures_iter(&processed.clone()) {
                if let Some(path_match) = captures.get(1) {
                    let include_path = path_match.as_str();

                    // Prevent circular includes
                    if included_files.contains(include_path) {
                        return Err(PromptError::CircularDependency {
                            path: include_path.to_string(),
                        });
                    }

                    // Resolve path relative to base_dir if available
                    let full_path = if let Some(base_dir) = &self.base_dir {
                        base_dir.join(include_path)
                    } else {
                        PathBuf::from(include_path)
                    };

                    // Read the included file
                    let included_content =
                        std::fs::read_to_string(&full_path).map_err(|e| PromptError::Io(e))?;

                    // Replace the include directive with the file content
                    let include_directive = captures.get(0).unwrap().as_str();
                    processed = processed.replace(include_directive, &included_content);

                    included_files.insert(include_path.to_string());
                    found_includes = true;
                }
            }

            if !found_includes {
                break;
            }
        }

        Ok(processed)
    }

    /// Validate template syntax
    pub fn validate_template(&self, content: &str) -> PromptResult<Vec<String>> {
        match Tera::one_off(content, &TeraContext::new(), true) {
            Ok(_) => Ok(Vec::new()),
            Err(e) => {
                // Extract variable names from the error if it's about missing variables
                let mut missing_vars = Vec::new();
                let error_msg = e.to_string();

                // Simple parsing of Tera error messages to extract variable names
                if error_msg.contains("not found") {
                    // This is a simplified approach - in practice, you might want more sophisticated parsing
                    for word in error_msg.split_whitespace() {
                        if word.starts_with('`') && word.ends_with('`') {
                            let var_name = word.trim_matches('`');
                            if !var_name.is_empty() {
                                missing_vars.push(var_name.to_string());
                            }
                        }
                    }
                }

                if missing_vars.is_empty() {
                    return Err(PromptError::Template(e));
                }

                Ok(missing_vars)
            }
        }
    }

    /// Extract variable names from template content
    pub fn extract_variables(&self, content: &str) -> PromptResult<Vec<String>> {
        // Use regex to find template variables
        let var_regex =
            regex::Regex::new(r"\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\}\}").map_err(|e| {
                PromptError::compilation_error(format!("Invalid variable regex: {}", e))
            })?;

        let mut variables = std::collections::HashSet::new();

        for captures in var_regex.captures_iter(content) {
            if let Some(var_match) = captures.get(1) {
                variables.insert(var_match.as_str().to_string());
            }
        }

        // Also look for complex expressions like {{ var.field }} or {{ var | filter }}
        let complex_var_regex =
            regex::Regex::new(r"\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)").map_err(|e| {
                PromptError::compilation_error(format!("Invalid complex variable regex: {}", e))
            })?;

        for captures in complex_var_regex.captures_iter(content) {
            if let Some(var_match) = captures.get(1) {
                variables.insert(var_match.as_str().to_string());
            }
        }

        Ok(variables.into_iter().collect())
    }

    /// Register built-in functions and filters
    fn register_built_in_functions(tera: &mut Tera) {
        // Add useful filters for prompt generation
        tera.register_filter("trim_lines", trim_lines_filter);
        tera.register_filter("indent", indent_filter);
        tera.register_filter("max_length", max_length_filter);
        tera.register_filter("word_wrap", word_wrap_filter);

        // Add useful functions
        tera.register_function("now", now_function);
        tera.register_function("uuid", uuid_function);
        tera.register_function("env", env_function);
    }
}

impl Default for TemplateEngine {
    fn default() -> Self {
        Self::new()
    }
}

// Built-in filters and functions

fn trim_lines_filter(value: &Value, _: &HashMap<String, Value>) -> tera::Result<Value> {
    match value {
        Value::String(s) => {
            let trimmed = s
                .lines()
                .map(|line| line.trim())
                .collect::<Vec<_>>()
                .join("\n");
            Ok(Value::String(trimmed))
        }
        _ => Err(tera::Error::msg(
            "trim_lines filter can only be applied to strings",
        )),
    }
}

fn indent_filter(value: &Value, args: &HashMap<String, Value>) -> tera::Result<Value> {
    let spaces = args.get("spaces").and_then(|v| v.as_u64()).unwrap_or(4) as usize;

    match value {
        Value::String(s) => {
            let indent = " ".repeat(spaces);
            let indented = s
                .lines()
                .map(|line| {
                    if line.trim().is_empty() {
                        line.to_string()
                    } else {
                        format!("{}{}", indent, line)
                    }
                })
                .collect::<Vec<_>>()
                .join("\n");
            Ok(Value::String(indented))
        }
        _ => Err(tera::Error::msg(
            "indent filter can only be applied to strings",
        )),
    }
}

fn max_length_filter(value: &Value, args: &HashMap<String, Value>) -> tera::Result<Value> {
    let max_len = args.get("length").and_then(|v| v.as_u64()).unwrap_or(100) as usize;

    match value {
        Value::String(s) => {
            if s.len() <= max_len {
                Ok(value.clone())
            } else {
                let truncated = format!("{}...", &s[..max_len.saturating_sub(3)]);
                Ok(Value::String(truncated))
            }
        }
        _ => Err(tera::Error::msg(
            "max_length filter can only be applied to strings",
        )),
    }
}

fn word_wrap_filter(value: &Value, args: &HashMap<String, Value>) -> tera::Result<Value> {
    let width = args.get("width").and_then(|v| v.as_u64()).unwrap_or(80) as usize;

    match value {
        Value::String(s) => {
            let wrapped = textwrap::fill(s, width);
            Ok(Value::String(wrapped))
        }
        _ => Err(tera::Error::msg(
            "word_wrap filter can only be applied to strings",
        )),
    }
}

fn now_function(_: &HashMap<String, Value>) -> tera::Result<Value> {
    let now = chrono::Utc::now();
    Ok(Value::String(
        now.format("%Y-%m-%d %H:%M:%S UTC").to_string(),
    ))
}

fn uuid_function(_: &HashMap<String, Value>) -> tera::Result<Value> {
    let uuid = uuid::Uuid::new_v4();
    Ok(Value::String(uuid.to_string()))
}

fn env_function(args: &HashMap<String, Value>) -> tera::Result<Value> {
    let var_name = args
        .get("name")
        .and_then(|v| v.as_str())
        .ok_or_else(|| tera::Error::msg("env function requires a 'name' argument"))?;

    match std::env::var(var_name) {
        Ok(value) => Ok(Value::String(value)),
        Err(_) => {
            let default = args.get("default").cloned().unwrap_or(Value::Null);
            Ok(default)
        }
    }
}

// Add textwrap as a dependency or implement a simple word wrap function
mod textwrap {
    pub fn fill(text: &str, width: usize) -> String {
        let mut result = String::new();
        for line in text.lines() {
            if line.len() <= width {
                result.push_str(line);
                result.push('\n');
            } else {
                let mut current_line = String::new();
                for word in line.split_whitespace() {
                    if current_line.is_empty() {
                        current_line = word.to_string();
                    } else if current_line.len() + word.len() + 1 <= width {
                        current_line.push(' ');
                        current_line.push_str(word);
                    } else {
                        result.push_str(&current_line);
                        result.push('\n');
                        current_line = word.to_string();
                    }
                }
                if !current_line.is_empty() {
                    result.push_str(&current_line);
                    result.push('\n');
                }
            }
        }
        result.trim_end().to_string()
    }
}
