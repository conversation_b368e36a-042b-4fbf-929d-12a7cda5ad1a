//! Prompt manager for loading, organizing, and rendering prompts

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::sync::{Arc, RwLock};
use tracing::{debug, info, warn};
use walkdir::WalkDir;

use crate::prompts::context::PromptContext;
use crate::prompts::core::{CompiledPrompt, Prompt, PromptMessage, PromptMetadata, PromptType};
use crate::prompts::error::{PromptError, PromptResult};
use crate::prompts::templates::TemplateEngine;
use crate::prompts::tokenization::{TokenizerConfig, TokenizerFactory};

/// Prompt manager for centralized prompt handling
#[derive(Debug)]
pub struct PromptManager {
    /// Registry of loaded prompts
    prompts: Arc<RwLock<HashMap<String, Prompt>>>,
    /// Template engine for rendering
    template_engine: TemplateEngine,
    /// Base directories for prompt discovery
    base_directories: Vec<PathBuf>,
    /// Prompt cache for compiled templates
    compiled_cache: Arc<RwLock<HashMap<String, CompiledPrompt>>>,
    /// Configuration
    config: PromptManagerConfig,
    /// Tokenizer factory for accurate token counting
    tokenizer_factory: TokenizerFactory,
    /// Current model for tokenization (can be overridden per operation)
    current_model: Option<String>,
}

/// Configuration for the prompt manager
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PromptManagerConfig {
    /// Enable automatic prompt discovery
    pub auto_discovery: bool,
    /// Enable prompt caching
    pub enable_caching: bool,
    /// Maximum cache size (number of compiled prompts)
    pub max_cache_size: usize,
    /// Enable hot reloading of prompts
    pub hot_reload: bool,
    /// File extensions to search for prompts
    pub prompt_extensions: Vec<String>,
    /// Maximum include depth for template processing
    pub max_include_depth: usize,
    /// Enable validation on load
    pub validate_on_load: bool,
    /// Enable accurate tokenization (falls back to word count if disabled)
    pub enable_tokenization: bool,
    /// Default model for tokenization if not specified
    pub default_tokenization_model: String,
    /// Tokenizer configuration
    pub tokenizer_config: TokenizerConfig,
}

impl Default for PromptManagerConfig {
    fn default() -> Self {
        Self {
            auto_discovery: true,
            enable_caching: true,
            max_cache_size: 1000,
            hot_reload: false,
            prompt_extensions: vec!["json".to_string(), "toml".to_string(), "yaml".to_string()],
            max_include_depth: 10,
            validate_on_load: true,
            enable_tokenization: true,
            default_tokenization_model: "claude-3-sonnet".to_string(),
            tokenizer_config: TokenizerConfig::default(),
        }
    }
}

impl PromptManager {
    /// Create a new prompt manager
    pub fn new() -> PromptResult<Self> {
        Self::with_config(PromptManagerConfig::default())
    }

    /// Create a prompt manager with custom configuration
    pub fn with_config(config: PromptManagerConfig) -> PromptResult<Self> {
        let template_engine = TemplateEngine::new();
        let tokenizer_factory = TokenizerFactory::with_config(config.tokenizer_config.clone());

        Ok(Self {
            prompts: Arc::new(RwLock::new(HashMap::new())),
            template_engine,
            base_directories: Vec::new(),
            compiled_cache: Arc::new(RwLock::new(HashMap::new())),
            tokenizer_factory,
            current_model: Some(config.default_tokenization_model.clone()),
            config,
        })
    }

    /// Set the current model for tokenization
    pub fn set_current_model(&mut self, model: impl Into<String>) {
        self.current_model = Some(model.into());
    }

    /// Get the current model for tokenization
    pub fn get_current_model(&self) -> Option<String> {
        self.current_model.clone()
    }

    /// Add a base directory for prompt discovery
    pub fn add_base_directory<P: AsRef<Path>>(&mut self, path: P) -> PromptResult<()> {
        let path = path.as_ref().to_path_buf();
        if !path.exists() {
            return Err(PromptError::Io(std::io::Error::new(
                std::io::ErrorKind::NotFound,
                format!("Directory not found: {:?}", path),
            )));
        }
        self.base_directories.push(path);
        Ok(())
    }

    /// Load prompts from all base directories
    pub fn load_all_prompts(&mut self) -> PromptResult<usize> {
        let mut total_loaded = 0;

        for base_dir in &self.base_directories.clone() {
            total_loaded += self.load_prompts_from_directory(base_dir)?;
        }

        Ok(total_loaded)
    }

    /// Load prompts from a specific directory
    pub fn load_prompts_from_directory<P: AsRef<Path>>(&mut self, dir: P) -> PromptResult<usize> {
        let dir = dir.as_ref();
        if !dir.is_dir() {
            return Err(PromptError::Io(std::io::Error::new(
                std::io::ErrorKind::NotFound,
                format!("Directory not found: {:?}", dir),
            )));
        }

        let mut loaded_count = 0;

        for entry in WalkDir::new(dir).into_iter().filter_map(|e| e.ok()) {
            let path = entry.path();

            if path.is_file() {
                if let Some(extension) = path.extension().and_then(|e| e.to_str()) {
                    if self
                        .config
                        .prompt_extensions
                        .contains(&extension.to_string())
                    {
                        match self.load_prompt_file(path) {
                            Ok(_) => {
                                loaded_count += 1;
                                tracing::debug!("Loaded prompt from: {:?}", path);
                            }
                            Err(e) => {
                                tracing::warn!("Failed to load prompt from {:?}: {}", path, e);
                                if self.config.validate_on_load {
                                    return Err(e);
                                }
                            }
                        }
                    }
                }
            }
        }

        Ok(loaded_count)
    }

    /// Load a single prompt from a file
    pub fn load_prompt_file<P: AsRef<Path>>(&mut self, path: P) -> PromptResult<()> {
        let path = path.as_ref();
        let content = std::fs::read_to_string(path)?;
        let extension = path.extension().and_then(|e| e.to_str()).unwrap_or("json");

        let mut prompt: Prompt = match extension {
            "json" => serde_json::from_str(&content)?,
            "toml" => toml::from_str(&content)?,
            "yaml" | "yml" => serde_yaml::from_str(&content)
                .map_err(|e| PromptError::invalid_structure(format!("YAML parse error: {}", e)))?,
            _ => {
                return Err(PromptError::invalid_structure(format!(
                    "Unsupported file extension: {}",
                    extension
                )))
            }
        };

        // Set file path in metadata
        prompt.metadata.file_path = Some(path.to_path_buf());
        prompt.metadata.touch();

        // Process includes in template content
        prompt.template = self.template_engine.process_includes(&prompt.template)?;
        if let Some(ref mut system_template) = prompt.system_template {
            *system_template = self.template_engine.process_includes(system_template)?;
        }

        // Extract variables from template
        let mut extracted_vars = self.template_engine.extract_variables(&prompt.template)?;
        if let Some(ref system_template) = prompt.system_template {
            let system_vars = self.template_engine.extract_variables(system_template)?;
            for var in system_vars {
                if !extracted_vars.contains(&var) {
                    extracted_vars.push(var);
                }
            }
        }

        // Update required variables if not explicitly set
        if prompt.metadata.required_variables.is_empty() {
            prompt.metadata.required_variables = extracted_vars;
        }

        // Validate prompt structure if enabled
        if self.config.validate_on_load {
            self.validate_prompt(&prompt)?;
        }

        // Compile template
        if self.config.enable_caching {
            self.compile_prompt(&mut prompt)?;
        }

        // Register templates with the template engine
        let template_name = format!("prompt_{}", prompt.metadata.name);
        self.template_engine
            .add_template(&template_name, &prompt.template)?;

        if let Some(ref system_template) = prompt.system_template {
            let system_template_name = format!("system_{}", prompt.metadata.name);
            self.template_engine
                .add_template(&system_template_name, system_template)?;
        }

        // Store the prompt
        let prompt_name = prompt.metadata.name.clone();
        {
            let mut prompts = self.prompts.write().map_err(|_| {
                PromptError::registry("Failed to acquire write lock on prompts registry")
            })?;
            prompts.insert(prompt_name, prompt);
        }

        Ok(())
    }

    /// Register a prompt directly
    pub fn register_prompt(&mut self, prompt: Prompt) -> PromptResult<()> {
        let prompt_name = prompt.metadata.name.clone();

        // Validate prompt
        if self.config.validate_on_load {
            self.validate_prompt(&prompt)?;
        }

        let mut prompt = prompt;

        // Compile if caching is enabled
        if self.config.enable_caching {
            self.compile_prompt(&mut prompt)?;
        }

        // Register templates
        let template_name = format!("prompt_{}", prompt.metadata.name);
        self.template_engine
            .add_template(&template_name, &prompt.template)?;

        if let Some(ref system_template) = prompt.system_template {
            let system_template_name = format!("system_{}", prompt.metadata.name);
            self.template_engine
                .add_template(&system_template_name, system_template)?;
        }

        // Store the prompt
        {
            let mut prompts = self.prompts.write().map_err(|_| {
                PromptError::registry("Failed to acquire write lock on prompts registry")
            })?;
            prompts.insert(prompt_name, prompt);
        }

        Ok(())
    }

    /// Get a prompt by name
    pub fn get_prompt(&self, name: &str) -> PromptResult<Prompt> {
        let prompts = self.prompts.read().map_err(|_| {
            PromptError::registry("Failed to acquire read lock on prompts registry")
        })?;

        prompts
            .get(name)
            .cloned()
            .ok_or_else(|| PromptError::prompt_not_found(name))
    }

    /// Check if a prompt exists
    pub fn has_prompt(&self, name: &str) -> bool {
        self.prompts
            .read()
            .map(|prompts| prompts.contains_key(name))
            .unwrap_or(false)
    }

    /// List all available prompts
    pub fn list_prompts(&self) -> PromptResult<Vec<PromptMetadata>> {
        let prompts = self.prompts.read().map_err(|_| {
            PromptError::registry("Failed to acquire read lock on prompts registry")
        })?;

        Ok(prompts.values().map(|p| p.metadata.clone()).collect())
    }

    /// Find prompts by type
    pub fn find_prompts_by_type(
        &self,
        prompt_type: &PromptType,
    ) -> PromptResult<Vec<PromptMetadata>> {
        let prompts = self.prompts.read().map_err(|_| {
            PromptError::registry("Failed to acquire read lock on prompts registry")
        })?;

        Ok(prompts
            .values()
            .filter(|p| &p.metadata.prompt_type == prompt_type)
            .map(|p| p.metadata.clone())
            .collect())
    }

    /// Find prompts by tag
    pub fn find_prompts_by_tag(&self, tag: &str) -> PromptResult<Vec<PromptMetadata>> {
        let prompts = self.prompts.read().map_err(|_| {
            PromptError::registry("Failed to acquire read lock on prompts registry")
        })?;

        Ok(prompts
            .values()
            .filter(|p| p.metadata.tags.contains(&tag.to_string()))
            .map(|p| p.metadata.clone())
            .collect())
    }

    /// Render a prompt with the given context
    pub fn render_prompt(
        &self,
        name: &str,
        context: &PromptContext,
    ) -> PromptResult<Vec<PromptMessage>> {
        let prompt = self.get_prompt(name)?;

        // Validate context has required variables
        prompt.validate_variables(&context.variables)?;

        let mut messages = Vec::new();

        // Render system message if present
        if let Some(ref system_template) = prompt.system_template {
            let system_template_name = format!("system_{}", prompt.metadata.name);
            let template_vars = context.to_template_variables()?;
            let rendered_system = self
                .template_engine
                .render(&system_template_name, &template_vars)?;

            messages.push(PromptMessage::system(rendered_system));
        }

        // Add conversation history
        messages.extend(context.history.clone());

        // Add few-shot examples
        messages.extend(prompt.examples.clone());

        // Render main prompt
        let template_name = format!("prompt_{}", prompt.metadata.name);
        let template_vars = context.to_template_variables()?;
        let rendered_prompt = self
            .template_engine
            .render(&template_name, &template_vars)?;

        messages.push(PromptMessage::user(rendered_prompt));

        Ok(messages)
    }

    /// Render a prompt string directly (for ad-hoc prompts)
    pub fn render_string(&self, template: &str, context: &PromptContext) -> PromptResult<String> {
        let template_vars = context.to_template_variables()?;
        self.template_engine.render_string(template, &template_vars)
    }

    /// Build full prompt text for tokenization
    fn build_full_prompt_text(&self, prompt: &Prompt) -> PromptResult<String> {
        let mut full_text = String::new();

        // Add system template if present
        if let Some(ref system_template) = prompt.system_template {
            full_text.push_str(system_template);
            full_text.push('\n');
        }

        // Add main template
        full_text.push_str(&prompt.template);

        // Add examples if present
        for example in &prompt.examples {
            full_text.push('\n');
            full_text.push_str(&example.content);
        }

        Ok(full_text)
    }

    /// Validate a prompt
    fn validate_prompt(&self, prompt: &Prompt) -> PromptResult<()> {
        // Check template syntax
        let missing_vars = self.template_engine.validate_template(&prompt.template)?;
        if !missing_vars.is_empty() {
            // This is actually OK - the variables will be provided at render time
            tracing::debug!(
                "Prompt {} has template variables: {:?}",
                prompt.metadata.name,
                missing_vars
            );
        }

        // Validate system template if present
        if let Some(ref _system_template) = prompt.system_template {
            let _system_missing_vars = self.template_engine.validate_template(_system_template)?;
            if !_system_missing_vars.is_empty() {
                tracing::debug!(
                    "Prompt {} system template has variables: {:?}",
                    prompt.metadata.name,
                    _system_missing_vars
                );
            }
        }

        // Check metadata consistency
        if prompt.metadata.name.is_empty() {
            return Err(PromptError::invalid_structure(
                "Prompt name cannot be empty",
            ));
        }

        if prompt.metadata.version.is_empty() {
            return Err(PromptError::invalid_structure(
                "Prompt version cannot be empty",
            ));
        }

        Ok(())
    }

    /// Compile a prompt for faster rendering
    fn compile_prompt(&self, prompt: &mut Prompt) -> PromptResult<()> {
        let template_id = format!("compiled_{}", prompt.metadata.name);

        // Extract variables from template
        let extracted_vars = self.template_engine.extract_variables(&prompt.template)?;

        // Calculate estimated token count (fallback method)
        let estimated_tokens = prompt.template.split_whitespace().count()
            + prompt
                .system_template
                .as_ref()
                .map(|t| t.split_whitespace().count())
                .unwrap_or(0);

        // Attempt accurate tokenization if enabled
        let mut compiled = CompiledPrompt::new(template_id, estimated_tokens, extracted_vars);

        if self.config.enable_tokenization {
            let model = self
                .get_current_model()
                .unwrap_or_else(|| self.config.default_tokenization_model.clone());

            match self.tokenizer_factory.create_provider(&model) {
                Ok(tokenizer) => {
                    match self.build_full_prompt_text(prompt) {
                        Ok(full_text) => {
                            // Create tokenizer config for this model
                            let mut tokenizer_config = self.config.tokenizer_config.clone();
                            tokenizer_config.model = model.clone();

                            // Perform accurate tokenization
                            match tokio::runtime::Handle::try_current() {
                                Ok(handle) => {
                                    let tokenizer_clone = tokenizer.clone();
                                    let text_clone = full_text.clone();
                                    let config_clone = tokenizer_config.clone();

                                    match handle.block_on(async {
                                        tokenizer_clone
                                            .count_tokens(&text_clone, &config_clone)
                                            .await
                                    }) {
                                        Ok(accurate_count) => {
                                            let provider_type =
                                                self.tokenizer_factory.get_provider_type(&model);
                                            compiled.add_token_count(
                                                provider_type,
                                                &model,
                                                accurate_count,
                                            );
                                            info!("Accurate tokenization for prompt '{}' ({}): {} tokens", 
                                                  prompt.metadata.name, model, accurate_count);
                                        }
                                        Err(e) => {
                                            warn!("Tokenization failed for prompt '{}': {}. Using word count fallback.", 
                                                  prompt.metadata.name, e);
                                        }
                                    }
                                }
                                Err(_) => {
                                    // No async runtime available, create one temporarily
                                    let rt = tokio::runtime::Runtime::new().map_err(|e| {
                                        PromptError::invalid_structure(format!(
                                            "Failed to create async runtime: {}",
                                            e
                                        ))
                                    })?;

                                    match rt.block_on(async {
                                        tokenizer.count_tokens(&full_text, &tokenizer_config).await
                                    }) {
                                        Ok(accurate_count) => {
                                            let provider_type =
                                                self.tokenizer_factory.get_provider_type(&model);
                                            compiled.add_token_count(
                                                provider_type,
                                                &model,
                                                accurate_count,
                                            );
                                            info!("Accurate tokenization for prompt '{}' ({}): {} tokens", 
                                                  prompt.metadata.name, model, accurate_count);
                                        }
                                        Err(e) => {
                                            warn!("Tokenization failed for prompt '{}': {}. Using word count fallback.", 
                                                  prompt.metadata.name, e);
                                        }
                                    }
                                }
                            }
                        }
                        Err(e) => {
                            warn!("Failed to build full prompt text for '{}': {}. Using word count fallback.", 
                                  prompt.metadata.name, e);
                        }
                    }
                }
                Err(e) => {
                    warn!(
                        "Failed to create tokenizer for model '{}': {}. Using word count fallback.",
                        model, e
                    );
                }
            }
        } else {
            debug!(
                "Accurate tokenization disabled, using word count for prompt '{}'",
                prompt.metadata.name
            );
        }

        prompt.compiled = Some(compiled.clone());

        // Cache compiled prompt
        if self.config.enable_caching {
            let mut cache = self.compiled_cache.write().map_err(|_| {
                PromptError::registry("Failed to acquire write lock on compiled cache")
            })?;

            // Implement LRU-like behavior
            if cache.len() >= self.config.max_cache_size {
                // Remove oldest entry (simplified - in practice you'd want proper LRU)
                if let Some(key) = cache.keys().next().cloned() {
                    cache.remove(&key);
                }
            }

            cache.insert(prompt.metadata.name.clone(), compiled);
        }

        Ok(())
    }

    /// Clear all prompts
    pub fn clear(&mut self) -> PromptResult<()> {
        {
            let mut prompts = self.prompts.write().map_err(|_| {
                PromptError::registry("Failed to acquire write lock on prompts registry")
            })?;
            prompts.clear();
        }

        {
            let mut cache = self.compiled_cache.write().map_err(|_| {
                PromptError::registry("Failed to acquire write lock on compiled cache")
            })?;
            cache.clear();
        }

        Ok(())
    }

    /// Get cache statistics
    pub fn cache_stats(&self) -> PromptResult<CacheStats> {
        let cache = self
            .compiled_cache
            .read()
            .map_err(|_| PromptError::registry("Failed to acquire read lock on compiled cache"))?;

        let prompts = self.prompts.read().map_err(|_| {
            PromptError::registry("Failed to acquire read lock on prompts registry")
        })?;

        Ok(CacheStats {
            total_prompts: prompts.len(),
            compiled_prompts: cache.len(),
            cache_hit_ratio: if prompts.is_empty() {
                0.0
            } else {
                cache.len() as f64 / prompts.len() as f64
            },
            max_cache_size: self.config.max_cache_size,
        })
    }
}

impl Default for PromptManager {
    fn default() -> Self {
        Self::new().expect("Failed to create default PromptManager")
    }
}

/// Cache statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheStats {
    pub total_prompts: usize,
    pub compiled_prompts: usize,
    pub cache_hit_ratio: f64,
    pub max_cache_size: usize,
}

/// Registry trait for prompt discovery and management
pub trait PromptRegistry {
    /// Register a new prompt
    fn register(&mut self, prompt: Prompt) -> PromptResult<()>;

    /// Get a prompt by name
    fn get(&self, name: &str) -> PromptResult<&Prompt>;

    /// List all available prompts
    fn list(&self) -> Vec<&PromptMetadata>;

    /// Check if a prompt exists
    fn exists(&self, name: &str) -> bool;

    /// Remove a prompt
    fn remove(&mut self, name: &str) -> PromptResult<()>;
}

impl PromptRegistry for PromptManager {
    fn register(&mut self, prompt: Prompt) -> PromptResult<()> {
        self.register_prompt(prompt)
    }

    fn get(&self, _name: &str) -> PromptResult<&Prompt> {
        // Note: This doesn't work with the current implementation due to lifetime issues
        // In practice, you'd use get_prompt() which returns a cloned Prompt
        unimplemented!("Use get_prompt() instead for a cloned Prompt")
    }

    fn list(&self) -> Vec<&PromptMetadata> {
        // Note: This doesn't work with the current implementation due to lifetime issues
        // In practice, you'd use list_prompts() which returns owned PromptMetadata
        unimplemented!("Use list_prompts() instead for owned PromptMetadata")
    }

    fn exists(&self, name: &str) -> bool {
        self.has_prompt(name)
    }

    fn remove(&mut self, name: &str) -> PromptResult<()> {
        let mut prompts = self.prompts.write().map_err(|_| {
            PromptError::registry("Failed to acquire write lock on prompts registry")
        })?;

        prompts
            .remove(name)
            .map(|_| ())
            .ok_or_else(|| PromptError::prompt_not_found(name))
    }
}
