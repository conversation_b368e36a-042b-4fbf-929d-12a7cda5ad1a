//! Folder provider for directory analysis and navigation
//!
//! Provides context resolution for @folder commands with fuzzy matching,
//! directory traversal, and file system analysis.

use super::super::{
    ContextCompletion, ContextContent, ContextData, ContextProvider, ContextType, FileEntry,
    FileType, ProviderStats,
};
use crate::errors::{AutorunError, Result};
use crate::tools::ExecutionContext;
use fuzzy_matcher::{skim::SkimMatcherV2, FuzzyMatcher};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::sync::atomic::{AtomicUsize, Ordering};
use std::time::SystemTime;
use tokio::fs;
use uuid::Uuid;

/// Folder provider implementation
pub struct FolderProvider {
    matcher: SkimMatcherV2,
    stats: FolderProviderStats,
    config: FolderProviderConfig,
}

/// Folder provider configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct FolderProviderConfig {
    /// Maximum depth for directory traversal
    pub max_depth: usize,
    /// Maximum number of files to list
    pub max_files: usize,
    /// Include hidden files/directories
    pub include_hidden: bool,
    /// Follow symbolic links
    pub follow_symlinks: bool,
    /// Exclude patterns (glob patterns)
    pub exclude_patterns: Vec<String>,
    /// Include patterns (glob patterns)
    pub include_patterns: Vec<String>,
    /// Enable size calculation
    pub calculate_sizes: bool,
    /// Cache directory listings
    pub enable_caching: bool,
}

impl Default for FolderProviderConfig {
    fn default() -> Self {
        Self {
            max_depth: 10,
            max_files: 1000,
            include_hidden: false,
            follow_symlinks: false,
            exclude_patterns: vec![
                ".git/".to_string(),
                "target/".to_string(),
                "node_modules/".to_string(),
                "*.tmp".to_string(),
                "*.log".to_string(),
            ],
            include_patterns: vec!["*".to_string()],
            calculate_sizes: true,
            enable_caching: true,
        }
    }
}

/// Folder provider statistics
#[derive(Debug, Default)]
struct FolderProviderStats {
    total_resolutions: AtomicUsize,
    successful_resolutions: AtomicUsize,
    failed_resolutions: AtomicUsize,
    cache_hits: AtomicUsize,
    cache_misses: AtomicUsize,
}

impl FolderProvider {
    /// Create a new folder provider
    pub async fn new() -> Result<Self> {
        Ok(Self {
            matcher: SkimMatcherV2::default(),
            stats: FolderProviderStats::default(),
            config: FolderProviderConfig::default(),
        })
    }

    /// Create with custom configuration
    pub async fn new_with_config(config: FolderProviderConfig) -> Result<Self> {
        Ok(Self {
            matcher: SkimMatcherV2::default(),
            stats: FolderProviderStats::default(),
            config,
        })
    }

    /// List directory contents
    async fn list_directory(
        &self,
        path: &Path,
        execution_context: &ExecutionContext,
    ) -> Result<Vec<FileEntry>> {
        if !path.is_dir() {
            return Err(AutorunError::ContextError(format!(
                "Path is not a directory: {}",
                path.display()
            )));
        }

        let mut entries = Vec::new();
        let mut dir_reader = fs::read_dir(path).await.map_err(|e| {
            AutorunError::IoError(format!(
                "Failed to read directory {}: {}",
                path.display(),
                e
            ))
        })?;

        while let Some(entry) = dir_reader
            .next_entry()
            .await
            .map_err(|e| AutorunError::IoError(format!("Failed to read directory entry: {}", e)))?
        {
            if entries.len() >= self.config.max_files {
                break;
            }

            let path = entry.path();
            let file_name = entry.file_name().to_string_lossy().to_string();

            // Skip hidden files if not included
            if !self.config.include_hidden && file_name.starts_with('.') {
                continue;
            }

            // Check exclude patterns
            if self.should_exclude(&path, &file_name)? {
                continue;
            }

            // Check include patterns
            if !self.should_include(&path, &file_name)? {
                continue;
            }

            let metadata = entry.metadata().await.map_err(|e| {
                AutorunError::IoError(format!(
                    "Failed to get metadata for {}: {}",
                    path.display(),
                    e
                ))
            })?;

            let file_type = if metadata.is_dir() {
                FileType::Directory
            } else if metadata.is_file() {
                FileType::File
            } else if metadata.file_type().is_symlink() {
                FileType::SymbolicLink
            } else {
                FileType::Unknown
            };

            let size = if self.config.calculate_sizes && file_type == FileType::File {
                metadata.len()
            } else {
                0
            };

            let modified = metadata.modified().unwrap_or(SystemTime::UNIX_EPOCH);

            let mut entry_metadata = HashMap::new();
            entry_metadata.insert(
                "permissions".to_string(),
                format!("{:o}", metadata.permissions()),
            );

            // Add file extension for files
            if let FileType::File = file_type {
                if let Some(extension) = path.extension() {
                    entry_metadata.insert(
                        "extension".to_string(),
                        extension.to_string_lossy().to_string(),
                    );
                }
            }

            entries.push(FileEntry {
                path: path.clone(),
                name: file_name,
                file_type,
                size,
                modified,
                metadata: entry_metadata,
            });
        }

        // Sort entries: directories first, then by name
        entries.sort_by(|a, b| match (&a.file_type, &b.file_type) {
            (FileType::Directory, FileType::Directory) => a.name.cmp(&b.name),
            (FileType::Directory, _) => std::cmp::Ordering::Less,
            (_, FileType::Directory) => std::cmp::Ordering::Greater,
            _ => a.name.cmp(&b.name),
        });

        Ok(entries)
    }

    /// Recursively search directories
    async fn search_directories(
        &self,
        base_path: &Path,
        query: &str,
        execution_context: &ExecutionContext,
        depth: usize,
    ) -> Result<Vec<FileEntry>> {
        if depth > self.config.max_depth {
            return Ok(Vec::new());
        }

        let mut results = Vec::new();
        let mut dir_reader = fs::read_dir(base_path).await.map_err(|e| {
            AutorunError::IoError(format!(
                "Failed to read directory {}: {}",
                base_path.display(),
                e
            ))
        })?;

        while let Some(entry) = dir_reader
            .next_entry()
            .await
            .map_err(|e| AutorunError::IoError(format!("Failed to read directory entry: {}", e)))?
        {
            if results.len() >= self.config.max_files {
                break;
            }

            let path = entry.path();
            let file_name = entry.file_name().to_string_lossy().to_string();

            // Skip hidden files if not included
            if !self.config.include_hidden && file_name.starts_with('.') {
                continue;
            }

            // Check exclude patterns
            if self.should_exclude(&path, &file_name)? {
                continue;
            }

            let metadata = entry.metadata().await.map_err(|e| {
                AutorunError::IoError(format!(
                    "Failed to get metadata for {}: {}",
                    path.display(),
                    e
                ))
            })?;

            let file_type = if metadata.is_dir() {
                FileType::Directory
            } else if metadata.is_file() {
                FileType::File
            } else if metadata.file_type().is_symlink() {
                FileType::SymbolicLink
            } else {
                FileType::Unknown
            };

            // Check if this entry matches the query
            if let Some(score) = self.matcher.fuzzy_match(&file_name, query) {
                let size = if self.config.calculate_sizes && file_type == FileType::File {
                    metadata.len()
                } else {
                    0
                };

                let modified = metadata.modified().unwrap_or(SystemTime::UNIX_EPOCH);

                let mut entry_metadata = HashMap::new();
                entry_metadata.insert("score".to_string(), score.to_string());
                entry_metadata.insert("depth".to_string(), depth.to_string());

                results.push(FileEntry {
                    path: path.clone(),
                    name: file_name.clone(),
                    file_type: file_type.clone(),
                    size,
                    modified,
                    metadata: entry_metadata,
                });
            }

            // Recursively search subdirectories
            if file_type == FileType::Directory
                && (self.config.follow_symlinks || !metadata.file_type().is_symlink())
            {
                let mut subresults = self
                    .search_directories(&path, query, execution_context, depth + 1)
                    .await?;
                results.append(&mut subresults);
            }
        }

        Ok(results)
    }

    /// Check if path should be excluded
    fn should_exclude(&self, path: &Path, name: &str) -> Result<bool> {
        for pattern in &self.config.exclude_patterns {
            if self.matches_pattern(pattern, path, name)? {
                return Ok(true);
            }
        }
        Ok(false)
    }

    /// Check if path should be included
    fn should_include(&self, path: &Path, name: &str) -> Result<bool> {
        if self.config.include_patterns.is_empty() {
            return Ok(true);
        }

        for pattern in &self.config.include_patterns {
            if self.matches_pattern(pattern, path, name)? {
                return Ok(true);
            }
        }
        Ok(false)
    }

    /// Check if path matches a glob pattern
    fn matches_pattern(&self, pattern: &str, path: &Path, name: &str) -> Result<bool> {
        // Simple pattern matching - could be enhanced with proper glob library
        if pattern == "*" {
            return Ok(true);
        }

        if pattern.starts_with("*.") {
            let extension = &pattern[2..];
            if let Some(file_ext) = path.extension() {
                return Ok(file_ext.to_string_lossy() == extension);
            }
        }

        if pattern.ends_with('/') {
            let dir_pattern = &pattern[..pattern.len() - 1];
            return Ok(name == dir_pattern || path.to_string_lossy().contains(dir_pattern));
        }

        Ok(name == pattern || path.to_string_lossy().contains(pattern))
    }

    /// Get relative path from working directory
    fn get_relative_path(&self, path: &Path, working_dir: &Path) -> PathBuf {
        path.strip_prefix(working_dir).unwrap_or(path).to_path_buf()
    }

    /// Generate completion suggestions for directory navigation
    async fn get_directory_completions(
        &self,
        partial_path: &str,
        execution_context: &ExecutionContext,
        limit: usize,
    ) -> Result<Vec<ContextCompletion>> {
        let working_dir = &execution_context.working_directory;
        let mut completions = Vec::new();

        // Determine the base directory and search term
        let (base_dir, search_term) = if partial_path.contains('/') {
            let path = PathBuf::from(partial_path);
            if let Some(parent) = path.parent() {
                let base = if parent.is_absolute() {
                    parent.to_path_buf()
                } else {
                    working_dir.join(parent)
                };
                let term = path
                    .file_name()
                    .map(|n| n.to_string_lossy().to_string())
                    .unwrap_or_default();
                (base, term)
            } else {
                (working_dir.clone(), partial_path.to_string())
            }
        } else {
            (working_dir.clone(), partial_path.to_string())
        };

        // Search for matching directories
        if base_dir.exists() && base_dir.is_dir() {
            let entries = self
                .search_directories(&base_dir, &search_term, execution_context, 0)
                .await?;

            for entry in entries.into_iter().take(limit) {
                let relative_path = self.get_relative_path(&entry.path, working_dir);
                let score = entry
                    .metadata
                    .get("score")
                    .and_then(|s| s.parse::<i64>().ok())
                    .unwrap_or(0) as f64
                    / 100.0;

                let description = match entry.file_type {
                    FileType::Directory => format!("Directory: {}", relative_path.display()),
                    FileType::File => {
                        format!("File: {} ({} bytes)", relative_path.display(), entry.size)
                    }
                    FileType::SymbolicLink => format!("Symlink: {}", relative_path.display()),
                    FileType::Unknown => format!("Unknown: {}", relative_path.display()),
                };

                completions.push(ContextCompletion {
                    text: relative_path.to_string_lossy().to_string(),
                    display_text: format!("{} {}", entry.file_type.icon(), entry.name),
                    description,
                    context_type: ContextType::Folder,
                    score,
                    metadata: entry.metadata,
                });
            }
        }

        // Sort by score
        completions.sort_by(|a, b| {
            b.score
                .partial_cmp(&a.score)
                .unwrap_or(std::cmp::Ordering::Equal)
        });

        Ok(completions)
    }
}

impl FileType {
    fn icon(&self) -> &'static str {
        match self {
            FileType::File => "📄",
            FileType::Directory => "📁",
            FileType::SymbolicLink => "🔗",
            FileType::Unknown => "❓",
        }
    }
}

#[async_trait::async_trait]
impl ContextProvider for FolderProvider {
    fn name(&self) -> &str {
        "folder"
    }

    fn description(&self) -> &str {
        "Provides context for folder and directory references with fuzzy matching and file system analysis"
    }

    fn supported_types(&self) -> Vec<ContextType> {
        vec![ContextType::Folder, ContextType::File]
    }

    async fn initialize(&mut self) -> Result<()> {
        // No initialization needed for folder provider
        Ok(())
    }

    async fn can_handle(&self, context_ref: &str, context_type: ContextType) -> Result<bool> {
        match context_type {
            ContextType::Folder | ContextType::File => Ok(true),
            _ => Ok(false),
        }
    }

    async fn resolve_context(
        &self,
        context_ref: &str,
        context_type: ContextType,
        execution_context: &ExecutionContext,
    ) -> Result<ContextData> {
        self.stats.total_resolutions.fetch_add(1, Ordering::Relaxed);

        let working_dir = &execution_context.working_directory;
        let target_path = if PathBuf::from(context_ref).is_absolute() {
            PathBuf::from(context_ref)
        } else {
            working_dir.join(context_ref)
        };

        if !target_path.exists() {
            self.stats
                .failed_resolutions
                .fetch_add(1, Ordering::Relaxed);
            return Err(AutorunError::ContextError(format!(
                "Path does not exist: {}",
                target_path.display()
            )));
        }

        let content = match context_type {
            ContextType::Folder => {
                if !target_path.is_dir() {
                    self.stats
                        .failed_resolutions
                        .fetch_add(1, Ordering::Relaxed);
                    return Err(AutorunError::ContextError(format!(
                        "Path is not a directory: {}",
                        target_path.display()
                    )));
                }

                let entries = self.list_directory(&target_path, execution_context).await?;
                ContextContent::FileList(entries)
            }
            ContextType::File => {
                if !target_path.is_file() {
                    self.stats
                        .failed_resolutions
                        .fetch_add(1, Ordering::Relaxed);
                    return Err(AutorunError::ContextError(format!(
                        "Path is not a file: {}",
                        target_path.display()
                    )));
                }

                let content = fs::read_to_string(&target_path).await.map_err(|e| {
                    AutorunError::IoError(format!(
                        "Failed to read file {}: {}",
                        target_path.display(),
                        e
                    ))
                })?;

                ContextContent::Text(content)
            }
            _ => {
                self.stats
                    .failed_resolutions
                    .fetch_add(1, Ordering::Relaxed);
                return Err(AutorunError::ContextError(format!(
                    "Unsupported context type: {:?}",
                    context_type
                )));
            }
        };

        let mut metadata = HashMap::new();
        metadata.insert(
            "path".to_string(),
            target_path.to_string_lossy().to_string(),
        );
        metadata.insert(
            "relative_path".to_string(),
            self.get_relative_path(&target_path, working_dir)
                .to_string_lossy()
                .to_string(),
        );

        if let Ok(file_metadata) = fs::metadata(&target_path).await {
            metadata.insert("size".to_string(), file_metadata.len().to_string());
            if let Ok(modified) = file_metadata.modified() {
                metadata.insert("modified".to_string(), format!("{:?}", modified));
            }
        }

        self.stats
            .successful_resolutions
            .fetch_add(1, Ordering::Relaxed);

        Ok(ContextData {
            id: Uuid::new_v4(),
            context_type,
            reference: context_ref.to_string(),
            content,
            metadata,
            resolved_at: SystemTime::now(),
            provider: self.name().to_string(),
            valid_until: Some(SystemTime::now() + std::time::Duration::from_secs(300)),
        })
    }

    async fn get_completions(
        &self,
        partial_ref: &str,
        context_type: ContextType,
        execution_context: &ExecutionContext,
        limit: usize,
    ) -> Result<Vec<ContextCompletion>> {
        match context_type {
            ContextType::Folder | ContextType::File => {
                self.get_directory_completions(partial_ref, execution_context, limit)
                    .await
            }
            _ => Ok(Vec::new()),
        }
    }

    async fn get_stats(&self) -> Result<ProviderStats> {
        let total = self.stats.total_resolutions.load(Ordering::Relaxed);
        let successful = self.stats.successful_resolutions.load(Ordering::Relaxed);
        let failed = self.stats.failed_resolutions.load(Ordering::Relaxed);
        let cache_hits = self.stats.cache_hits.load(Ordering::Relaxed);
        let cache_misses = self.stats.cache_misses.load(Ordering::Relaxed);

        let cache_hit_rate = if cache_hits + cache_misses > 0 {
            cache_hits as f64 / (cache_hits + cache_misses) as f64
        } else {
            0.0
        };

        Ok(ProviderStats {
            total_resolutions: total,
            successful_resolutions: successful,
            failed_resolutions: failed,
            average_resolution_time_ms: 0.0, // TODO: Implement timing
            cache_hit_rate,
            last_resolution: None, // TODO: Track last resolution time
        })
    }

    async fn cleanup(&mut self) -> Result<()> {
        // No cleanup needed for folder provider
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_folder_provider_creation() {
        let provider = FolderProvider::new().await.unwrap();
        assert_eq!(provider.name(), "folder");
        assert!(provider.supported_types().contains(&ContextType::Folder));
    }

    #[tokio::test]
    async fn test_directory_listing() {
        let temp_dir = TempDir::new().unwrap();
        let provider = FolderProvider::new().await.unwrap();

        // Create test files
        let test_file = temp_dir.path().join("test.txt");
        tokio::fs::write(&test_file, "test content").await.unwrap();

        let test_dir = temp_dir.path().join("subdir");
        tokio::fs::create_dir(&test_dir).await.unwrap();

        let execution_context = ExecutionContext {
            working_directory: temp_dir.path().to_path_buf(),
            permissions: crate::tools::PermissionLevel::Full,
            session_data: std::collections::HashMap::new(),
            environment: std::collections::HashMap::new(),
            tool_registry: None,
        };

        let entries = provider
            .list_directory(temp_dir.path(), &execution_context)
            .await
            .unwrap();
        assert!(entries.len() >= 2);

        let has_file = entries.iter().any(|e| e.name == "test.txt");
        let has_dir = entries.iter().any(|e| e.name == "subdir");
        assert!(has_file);
        assert!(has_dir);
    }

    #[tokio::test]
    async fn test_pattern_matching() {
        let provider = FolderProvider::new().await.unwrap();
        let path = PathBuf::from("test.txt");

        assert!(provider
            .matches_pattern("*.txt", &path, "test.txt")
            .unwrap());
        assert!(!provider.matches_pattern("*.rs", &path, "test.txt").unwrap());
        assert!(provider.matches_pattern("*", &path, "test.txt").unwrap());
    }
}
