//! Context providers for different types of context resolution
//!
//! This module contains implementations of the ContextProvider trait
//! for various types of context references.

pub mod codebase_provider;
pub mod folder_provider;
pub mod git_provider;
pub mod symbol_provider;
pub mod web_provider;

// Re-export the providers
pub use codebase_provider::CodebaseProvider;
pub use folder_provider::FolderProvider;
pub use git_provider::GitProvider;
pub use symbol_provider::SymbolProvider;
pub use web_provider::WebProvider;
