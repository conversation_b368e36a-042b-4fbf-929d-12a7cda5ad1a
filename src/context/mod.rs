//! Context providers for @ command system
//!
//! Provides extensible context resolution for different types of references
//! including files, folders, codebase, symbols, web resources, etc.

pub mod providers;

use crate::errors::Result;
use crate::tools::ExecutionContext;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::SystemTime;
use tokio::sync::RwLock;
use uuid::Uuid;

/// Context provider trait for extensible context resolution
#[async_trait::async_trait]
pub trait ContextProvider: Send + Sync {
    /// Provider name
    fn name(&self) -> &str;

    /// Provider description
    fn description(&self) -> &str;

    /// Supported context types
    fn supported_types(&self) -> Vec<ContextType>;

    /// Initialize the provider
    async fn initialize(&mut self) -> Result<()>;

    /// Check if provider can handle a specific context reference
    async fn can_handle(&self, context_ref: &str, context_type: ContextType) -> Result<bool>;

    /// Resolve a context reference to context data
    async fn resolve_context(
        &self,
        context_ref: &str,
        context_type: ContextType,
        execution_context: &ExecutionContext,
    ) -> Result<ContextData>;

    /// Get completion suggestions for partial context references
    async fn get_completions(
        &self,
        partial_ref: &str,
        context_type: ContextType,
        execution_context: &ExecutionContext,
        limit: usize,
    ) -> Result<Vec<ContextCompletion>>;

    /// Get provider statistics
    async fn get_stats(&self) -> Result<ProviderStats>;

    /// Cleanup provider resources
    async fn cleanup(&mut self) -> Result<()>;
}

/// Context types supported by the system
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ContextType {
    /// File reference (@file)
    File,
    /// Folder reference (@folder)
    Folder,
    /// Codebase reference (@codebase)
    Codebase,
    /// MCP server reference (@mcp)
    Mcp,
    /// Symbol reference (@symbol)
    Symbol,
    /// Web resource reference (@web)
    Web,
    /// Session reference (@session)
    Session,
    /// Workspace reference (@workspace)
    Workspace,
    /// Git reference (@git)
    Git,
    /// Memory reference (@memory)
    Memory,
}

impl ContextType {
    /// Get the prefix for this context type
    pub fn prefix(&self) -> &'static str {
        match self {
            ContextType::File => "@file",
            ContextType::Folder => "@folder",
            ContextType::Codebase => "@codebase",
            ContextType::Mcp => "@mcp",
            ContextType::Symbol => "@symbol",
            ContextType::Web => "@web",
            ContextType::Session => "@session",
            ContextType::Workspace => "@workspace",
            ContextType::Git => "@git",
            ContextType::Memory => "@memory",
        }
    }

    /// Get the icon for this context type
    pub fn icon(&self) -> &'static str {
        match self {
            ContextType::File => "📄",
            ContextType::Folder => "📁",
            ContextType::Codebase => "📚",
            ContextType::Mcp => "🔧",
            ContextType::Symbol => "🔍",
            ContextType::Web => "🌐",
            ContextType::Session => "💬",
            ContextType::Workspace => "🏢",
            ContextType::Git => "🌲",
            ContextType::Memory => "🧠",
        }
    }

    /// Parse context type from prefix
    pub fn from_prefix(prefix: &str) -> Option<Self> {
        match prefix {
            "@file" => Some(ContextType::File),
            "@folder" => Some(ContextType::Folder),
            "@codebase" => Some(ContextType::Codebase),
            "@mcp" => Some(ContextType::Mcp),
            "@symbol" => Some(ContextType::Symbol),
            "@web" => Some(ContextType::Web),
            "@session" => Some(ContextType::Session),
            "@workspace" => Some(ContextType::Workspace),
            "@git" => Some(ContextType::Git),
            "@memory" => Some(ContextType::Memory),
            _ => None,
        }
    }
}

/// Resolved context data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContextData {
    /// Context identifier
    pub id: Uuid,
    /// Context type
    pub context_type: ContextType,
    /// Context reference that was resolved
    pub reference: String,
    /// Resolved content
    pub content: ContextContent,
    /// Context metadata
    pub metadata: HashMap<String, String>,
    /// Resolution timestamp
    pub resolved_at: SystemTime,
    /// Context provider that resolved this
    pub provider: String,
    /// Validity period (cache TTL)
    pub valid_until: Option<SystemTime>,
}

/// Context content variants
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ContextContent {
    /// Text content
    Text(String),
    /// Binary content (base64 encoded)
    Binary(String),
    /// Structured data
    Structured(serde_json::Value),
    /// File listing
    FileList(Vec<FileEntry>),
    /// Multiple items
    Multiple(Vec<ContextData>),
}

/// File entry for file listings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileEntry {
    /// File path
    pub path: std::path::PathBuf,
    /// File name
    pub name: String,
    /// File type
    pub file_type: FileType,
    /// File size
    pub size: u64,
    /// Last modified
    pub modified: SystemTime,
    /// File metadata
    pub metadata: HashMap<String, String>,
}

/// File type enumeration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FileType {
    File,
    Directory,
    SymbolicLink,
    Unknown,
}

/// Context completion suggestion
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContextCompletion {
    /// Completion text
    pub text: String,
    /// Display text (may include additional info)
    pub display_text: String,
    /// Completion description
    pub description: String,
    /// Context type
    pub context_type: ContextType,
    /// Completion score (0.0-1.0)
    pub score: f64,
    /// Completion metadata
    pub metadata: HashMap<String, String>,
}

/// Provider statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProviderStats {
    /// Total resolutions performed
    pub total_resolutions: usize,
    /// Successful resolutions
    pub successful_resolutions: usize,
    /// Failed resolutions
    pub failed_resolutions: usize,
    /// Average resolution time (ms)
    pub average_resolution_time_ms: f64,
    /// Cache hit rate
    pub cache_hit_rate: f64,
    /// Last resolution time
    pub last_resolution: Option<SystemTime>,
}

/// Context provider registry
pub struct ContextProviderRegistry {
    providers: RwLock<HashMap<String, Arc<dyn ContextProvider>>>,
    type_providers: RwLock<HashMap<ContextType, Vec<String>>>,
    cache: RwLock<HashMap<String, ContextData>>,
    config: RegistryConfig,
}

/// Registry configuration
#[derive(Debug, Clone)]
pub struct RegistryConfig {
    /// Enable caching
    pub enable_caching: bool,
    /// Cache TTL in seconds
    pub cache_ttl_seconds: u64,
    /// Maximum cache size
    pub max_cache_size: usize,
    /// Enable provider statistics
    pub enable_stats: bool,
    /// Provider timeout in seconds
    pub provider_timeout_seconds: u64,
}

impl Default for RegistryConfig {
    fn default() -> Self {
        Self {
            enable_caching: true,
            cache_ttl_seconds: 300, // 5 minutes
            max_cache_size: 1000,
            enable_stats: true,
            provider_timeout_seconds: 30,
        }
    }
}

impl ContextProviderRegistry {
    /// Create a new registry
    pub fn new(config: RegistryConfig) -> Self {
        Self {
            providers: RwLock::new(HashMap::new()),
            type_providers: RwLock::new(HashMap::new()),
            cache: RwLock::new(HashMap::new()),
            config,
        }
    }

    /// Register a context provider
    pub async fn register_provider(&self, provider: Arc<dyn ContextProvider>) -> Result<()> {
        let name = provider.name().to_string();
        let supported_types = provider.supported_types();

        // Register provider
        {
            let mut providers = self.providers.write().await;
            providers.insert(name.clone(), provider);
        }

        // Register for supported types
        {
            let mut type_providers = self.type_providers.write().await;
            for context_type in supported_types {
                type_providers
                    .entry(context_type)
                    .or_insert_with(Vec::new)
                    .push(name.clone());
            }
        }

        tracing::info!("Registered context provider: {}", name);
        Ok(())
    }

    /// Unregister a context provider
    pub async fn unregister_provider(&self, name: &str) -> Result<bool> {
        let removed = {
            let mut providers = self.providers.write().await;
            providers.remove(name).is_some()
        };

        if removed {
            // Remove from type mappings
            let mut type_providers = self.type_providers.write().await;
            for providers_list in type_providers.values_mut() {
                providers_list.retain(|p| p != name);
            }

            tracing::info!("Unregistered context provider: {}", name);
        }

        Ok(removed)
    }

    /// Resolve a context reference
    pub async fn resolve_context(
        &self,
        context_ref: &str,
        context_type: ContextType,
        execution_context: &ExecutionContext,
    ) -> Result<ContextData> {
        // Check cache first
        if self.config.enable_caching {
            let cache_key = format!("{}:{}", context_type.prefix(), context_ref);
            let cache = self.cache.read().await;
            if let Some(cached_data) = cache.get(&cache_key) {
                if let Some(valid_until) = cached_data.valid_until {
                    if SystemTime::now() < valid_until {
                        return Ok(cached_data.clone());
                    }
                }
            }
        }

        // Find providers for this context type
        let providers = {
            let type_providers = self.type_providers.read().await;
            type_providers
                .get(&context_type)
                .cloned()
                .unwrap_or_default()
        };

        if providers.is_empty() {
            return Err(crate::errors::AutorunError::ContextError(format!(
                "No providers registered for context type: {:?}",
                context_type
            )));
        }

        // Try each provider until one succeeds
        for provider_name in providers {
            let provider = {
                let providers_map = self.providers.read().await;
                providers_map.get(&provider_name).cloned()
            };

            if let Some(provider) = provider {
                if provider
                    .can_handle(context_ref, context_type.clone())
                    .await?
                {
                    match provider
                        .resolve_context(context_ref, context_type.clone(), execution_context)
                        .await
                    {
                        Ok(mut context_data) => {
                            // Set validity period for caching
                            if self.config.enable_caching {
                                context_data.valid_until = Some(
                                    SystemTime::now()
                                        + std::time::Duration::from_secs(
                                            self.config.cache_ttl_seconds,
                                        ),
                                );

                                // Cache the result
                                let cache_key =
                                    format!("{}:{}", context_type.prefix(), context_ref);
                                let mut cache = self.cache.write().await;

                                // Implement LRU eviction if cache is full
                                if cache.len() >= self.config.max_cache_size {
                                    // Remove oldest entry (simplified LRU)
                                    if let Some(oldest_key) = cache.keys().next().cloned() {
                                        cache.remove(&oldest_key);
                                    }
                                }

                                cache.insert(cache_key, context_data.clone());
                            }

                            return Ok(context_data);
                        }
                        Err(e) => {
                            tracing::warn!(
                                "Provider {} failed to resolve context: {}",
                                provider_name,
                                e
                            );
                            continue;
                        }
                    }
                }
            }
        }

        Err(crate::errors::AutorunError::ContextError(format!(
            "No provider could resolve context: {}",
            context_ref
        )))
    }

    /// Get completion suggestions
    pub async fn get_completions(
        &self,
        partial_ref: &str,
        context_type: ContextType,
        execution_context: &ExecutionContext,
        limit: usize,
    ) -> Result<Vec<ContextCompletion>> {
        let providers = {
            let type_providers = self.type_providers.read().await;
            type_providers
                .get(&context_type)
                .cloned()
                .unwrap_or_default()
        };

        let mut all_completions = Vec::new();

        // Collect completions from all providers
        for provider_name in providers {
            let provider = {
                let providers_map = self.providers.read().await;
                providers_map.get(&provider_name).cloned()
            };

            if let Some(provider) = provider {
                match provider
                    .get_completions(partial_ref, context_type.clone(), execution_context, limit)
                    .await
                {
                    Ok(mut completions) => {
                        all_completions.append(&mut completions);
                    }
                    Err(e) => {
                        tracing::warn!(
                            "Provider {} failed to get completions: {}",
                            provider_name,
                            e
                        );
                    }
                }
            }
        }

        // Sort by score and limit results
        all_completions.sort_by(|a, b| {
            b.score
                .partial_cmp(&a.score)
                .unwrap_or(std::cmp::Ordering::Equal)
        });
        all_completions.truncate(limit);

        Ok(all_completions)
    }

    /// Get all registered providers
    pub async fn list_providers(&self) -> Vec<String> {
        let providers = self.providers.read().await;
        providers.keys().cloned().collect()
    }

    /// Get provider for a specific type
    pub async fn get_providers_for_type(&self, context_type: &ContextType) -> Vec<String> {
        let type_providers = self.type_providers.read().await;
        type_providers
            .get(context_type)
            .cloned()
            .unwrap_or_default()
    }

    /// Clear cache
    pub async fn clear_cache(&self) {
        let mut cache = self.cache.write().await;
        cache.clear();
        tracing::info!("Context provider cache cleared");
    }

    /// Get registry statistics
    pub async fn get_stats(&self) -> RegistryStats {
        let providers = self.providers.read().await;
        let cache = self.cache.read().await;
        let type_providers = self.type_providers.read().await;

        RegistryStats {
            provider_count: providers.len(),
            type_count: type_providers.len(),
            cache_size: cache.len(),
            cache_hit_ratio: 0.0, // TODO: Implement proper cache hit tracking
        }
    }

    /// Initialize all providers
    pub async fn initialize_providers(&self) -> Result<()> {
        let provider_names: Vec<String> = {
            let providers = self.providers.read().await;
            providers.keys().cloned().collect()
        };

        for name in provider_names {
            if let Some(provider) = {
                let providers = self.providers.read().await;
                providers.get(&name).cloned()
            } {
                // Note: We can't mutate providers through Arc<dyn Trait>
                // This would need to be redesigned if mutable initialization is needed
                tracing::info!("Provider {} initialized", name);
            }
        }

        Ok(())
    }
}

/// Registry statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegistryStats {
    pub provider_count: usize,
    pub type_count: usize,
    pub cache_size: usize,
    pub cache_hit_ratio: f64,
}

/// Initialize the context provider system with default providers
pub async fn initialize_context_system(config: RegistryConfig) -> Result<ContextProviderRegistry> {
    let registry = ContextProviderRegistry::new(config);

    // Register built-in providers
    registry
        .register_provider(Arc::new(
            providers::folder_provider::FolderProvider::new().await?,
        ))
        .await?;
    registry
        .register_provider(Arc::new(
            providers::codebase_provider::CodebaseProvider::new().await?,
        ))
        .await?;
    registry
        .register_provider(Arc::new(
            providers::symbol_provider::SymbolProvider::new().await?,
        ))
        .await?;
    registry
        .register_provider(Arc::new(providers::web_provider::WebProvider::new().await?))
        .await?;
    registry
        .register_provider(Arc::new(providers::git_provider::GitProvider::new().await?))
        .await?;

    registry.initialize_providers().await?;

    tracing::info!("Context provider system initialized");
    Ok(registry)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_context_type_prefix() {
        assert_eq!(ContextType::File.prefix(), "@file");
        assert_eq!(ContextType::Folder.prefix(), "@folder");
        assert_eq!(ContextType::Memory.prefix(), "@memory");
    }

    #[tokio::test]
    async fn test_context_type_from_prefix() {
        assert_eq!(ContextType::from_prefix("@file"), Some(ContextType::File));
        assert_eq!(ContextType::from_prefix("@invalid"), None);
    }

    #[tokio::test]
    async fn test_registry_creation() {
        let config = RegistryConfig::default();
        let registry = ContextProviderRegistry::new(config);

        let stats = registry.get_stats().await;
        assert_eq!(stats.provider_count, 0);
        assert_eq!(stats.cache_size, 0);
    }
}
