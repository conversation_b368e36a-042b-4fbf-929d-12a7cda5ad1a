use crate::errors::{AutorunError, Result};
use crate::ui::enhanced::state::UiMode;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio::sync::RwLock;
use tracing::{info, debug, warn};

/// Mode management system for UI mode switching with state preservation
#[derive(Debug)]
pub struct ModeManager {
    /// Current UI mode
    current_mode: RwLock<UiMode>,
    
    /// Mode controllers for each mode
    controllers: HashMap<UiMode, Box<dyn ModeController + Send + Sync>>,
    
    /// Mode persistence manager
    persistence: ModePersistence,
    
    /// Mode transition history
    history: RwLock<Vec<ModeTransition>>,
}

/// Mode transition record
#[derive(Debug, Clone, Serialize, Deserialize)]
struct ModeTransition {
    /// From mode
    from_mode: UiMode,
    
    /// To mode
    to_mode: UiMode,
    
    /// Transition timestamp
    timestamp: chrono::DateTime<chrono::Utc>,
    
    /// Transition reason
    reason: String,
    
    /// Transition duration (for performance monitoring)
    duration_ms: u64,
}

/// Mode controller trait for handling mode-specific behavior
pub trait ModeController {
    /// Enter this mode
    async fn enter(&mut self, previous_mode: UiMode) -> Result<()>;
    
    /// Exit this mode
    async fn exit(&mut self, next_mode: UiMode) -> Result<()>;
    
    /// Save current mode state
    async fn save_state(&self) -> Result<ModeState>;
    
    /// Restore mode state
    async fn restore_state(&mut self, state: ModeState) -> Result<()>;
    
    /// Get mode-specific configuration
    fn get_config(&self) -> ModeConfig;
    
    /// Validate mode transition
    fn can_transition_to(&self, target_mode: UiMode) -> bool;
}

/// Generic mode state for persistence
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModeState {
    /// Mode type
    mode: UiMode,
    
    /// State data
    data: serde_json::Value,
    
    /// Last saved timestamp
    timestamp: chrono::DateTime<chrono::Utc>,
}

/// Mode-specific configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModeConfig {
    /// Mode name
    name: String,
    
    /// Mode description
    description: String,
    
    /// Keyboard shortcuts
    shortcuts: HashMap<String, String>,
    
    /// UI layout preferences
    layout: LayoutConfig,
    
    /// Mode-specific settings
    settings: HashMap<String, serde_json::Value>,
}

/// Layout configuration for mode
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LayoutConfig {
    /// Show status bar
    show_status_bar: bool,
    
    /// Show line numbers (for vim mode)
    show_line_numbers: bool,
    
    /// Show mode indicator
    show_mode_indicator: bool,
    
    /// Color scheme adjustments
    color_overrides: HashMap<String, String>,
}

impl ModeManager {
    /// Create a new mode manager
    pub fn new() -> Self {
        let mut controllers: HashMap<UiMode, Box<dyn ModeController + Send + Sync>> = HashMap::new();
        
        // Register mode controllers
        controllers.insert(UiMode::Normal, Box::new(NormalModeController::new()));
        controllers.insert(UiMode::Vim, Box::new(VimModeController::new()));
        controllers.insert(UiMode::Debug, Box::new(DebugModeController::new()));
        
        Self {
            current_mode: RwLock::new(UiMode::Normal),
            controllers,
            persistence: ModePersistence::new(),
            history: RwLock::new(vec![]),
        }
    }
    
    /// Get current mode
    pub async fn current_mode(&self) -> UiMode {
        *self.current_mode.read().await
    }
    
    /// Switch to a new mode
    pub async fn switch_mode(&self, new_mode: UiMode) -> Result<()> {
        let start_time = std::time::Instant::now();
        let old_mode = *self.current_mode.read().await;
        
        if old_mode == new_mode {
            debug!("Already in {} mode", format!("{:?}", new_mode).to_lowercase());
            return Ok(());
        }
        
        info!("Switching from {:?} to {:?} mode", old_mode, new_mode);
        
        // Validate transition
        if let Some(controller) = self.controllers.get(&old_mode) {
            if !controller.can_transition_to(new_mode.clone()) {
                return Err(AutorunError::ModeError(
                    format!("Cannot transition from {:?} to {:?}", old_mode, new_mode)
                ));
            }
        }
        
        // Save current mode state
        if let Some(controller) = self.controllers.get(&old_mode) {
            let state = controller.save_state().await?;
            self.persistence.save_mode_state(&state).await?;
        }
        
        // Exit current mode
        if let Some(controller) = self.controllers.get_mut(&old_mode) {
            controller.exit(new_mode.clone()).await?;
        }
        
        // Enter new mode
        if let Some(controller) = self.controllers.get_mut(&new_mode) {
            controller.enter(old_mode).await?;
            
            // Restore saved state if available
            if let Ok(state) = self.persistence.load_mode_state(&new_mode).await {
                controller.restore_state(state).await?;
            }
        }
        
        // Update current mode
        {
            let mut current = self.current_mode.write().await;
            *current = new_mode.clone();
        }
        
        // Record transition
        let duration = start_time.elapsed().as_millis() as u64;
        let transition = ModeTransition {
            from_mode: old_mode,
            to_mode: new_mode,
            timestamp: chrono::Utc::now(),
            reason: "user_command".to_string(),
            duration_ms: duration,
        };
        
        {
            let mut history = self.history.write().await;
            history.push(transition);
            
            // Keep only last 100 transitions
            if history.len() > 100 {
                history.remove(0);
            }
        }
        
        // Save preferences
        self.persistence.save_mode_preference(&new_mode).await?;
        
        info!("Mode switch completed in {}ms", duration);
        Ok(())
    }
    
    /// Get mode configuration
    pub async fn get_mode_config(&self, mode: &UiMode) -> Option<ModeConfig> {
        self.controllers.get(mode).map(|c| c.get_config())
    }
    
    /// Get current mode configuration
    pub async fn get_current_config(&self) -> Option<ModeConfig> {
        let current = self.current_mode().await;
        self.get_mode_config(&current).await
    }
    
    /// Get mode transition history
    pub async fn get_history(&self) -> Vec<ModeTransition> {
        self.history.read().await.clone()
    }
    
    /// Get available modes
    pub fn get_available_modes(&self) -> Vec<UiMode> {
        self.controllers.keys().cloned().collect()
    }
    
    /// Initialize mode manager with saved preferences
    pub async fn initialize(&self) -> Result<()> {
        // Load saved mode preference
        if let Ok(preferred_mode) = self.persistence.load_mode_preference().await {
            self.switch_mode(preferred_mode).await?;
        }
        
        info!("Mode manager initialized with mode: {:?}", self.current_mode().await);
        Ok(())
    }
}

/// Normal mode controller
#[derive(Debug)]
struct NormalModeController {
    state: HashMap<String, serde_json::Value>,
}

impl NormalModeController {
    fn new() -> Self {
        Self {
            state: HashMap::new(),
        }
    }
}

#[async_trait::async_trait]
impl ModeController for NormalModeController {
    async fn enter(&mut self, _previous_mode: UiMode) -> Result<()> {
        debug!("Entering normal mode");
        Ok(())
    }
    
    async fn exit(&mut self, _next_mode: UiMode) -> Result<()> {
        debug!("Exiting normal mode");
        Ok(())
    }
    
    async fn save_state(&self) -> Result<ModeState> {
        Ok(ModeState {
            mode: UiMode::Normal,
            data: serde_json::json!(self.state),
            timestamp: chrono::Utc::now(),
        })
    }
    
    async fn restore_state(&mut self, state: ModeState) -> Result<()> {
        if let Ok(state_map) = serde_json::from_value::<HashMap<String, serde_json::Value>>(state.data) {
            self.state = state_map;
        }
        Ok(())
    }
    
    fn get_config(&self) -> ModeConfig {
        ModeConfig {
            name: "Normal".to_string(),
            description: "Standard interactive mode".to_string(),
            shortcuts: HashMap::from([
                ("Enter".to_string(), "Send message".to_string()),
                ("Ctrl+C".to_string(), "Cancel operation".to_string()),
                ("Ctrl+L".to_string(), "Clear screen".to_string()),
            ]),
            layout: LayoutConfig {
                show_status_bar: true,
                show_line_numbers: false,
                show_mode_indicator: true,
                color_overrides: HashMap::new(),
            },
            settings: HashMap::new(),
        }
    }
    
    fn can_transition_to(&self, _target_mode: UiMode) -> bool {
        true // Normal mode can transition to any mode
    }
}

/// Vim mode controller
#[derive(Debug)]
struct VimModeController {
    vim_state: VimState,
}

#[derive(Debug, Serialize, Deserialize)]
struct VimState {
    mode: VimMode,
    command_buffer: String,
    visual_selection: Option<(usize, usize)>,
    registers: HashMap<char, String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
enum VimMode {
    Normal,
    Insert,
    Visual,
    Command,
}

impl VimModeController {
    fn new() -> Self {
        Self {
            vim_state: VimState {
                mode: VimMode::Normal,
                command_buffer: String::new(),
                visual_selection: None,
                registers: HashMap::new(),
            },
        }
    }
}

#[async_trait::async_trait]
impl ModeController for VimModeController {
    async fn enter(&mut self, _previous_mode: UiMode) -> Result<()> {
        debug!("Entering vim mode");
        self.vim_state.mode = VimMode::Normal;
        Ok(())
    }
    
    async fn exit(&mut self, _next_mode: UiMode) -> Result<()> {
        debug!("Exiting vim mode");
        Ok(())
    }
    
    async fn save_state(&self) -> Result<ModeState> {
        Ok(ModeState {
            mode: UiMode::Vim,
            data: serde_json::to_value(&self.vim_state)?,
            timestamp: chrono::Utc::now(),
        })
    }
    
    async fn restore_state(&mut self, state: ModeState) -> Result<()> {
        if let Ok(vim_state) = serde_json::from_value::<VimState>(state.data) {
            self.vim_state = vim_state;
        }
        Ok(())
    }
    
    fn get_config(&self) -> ModeConfig {
        ModeConfig {
            name: "Vim".to_string(),
            description: "Vim-like modal editing".to_string(),
            shortcuts: HashMap::from([
                ("i".to_string(), "Enter insert mode".to_string()),
                ("Esc".to_string(), "Return to normal mode".to_string()),
                (":".to_string(), "Enter command mode".to_string()),
                ("v".to_string(), "Enter visual mode".to_string()),
            ]),
            layout: LayoutConfig {
                show_status_bar: true,
                show_line_numbers: true,
                show_mode_indicator: true,
                color_overrides: HashMap::from([
                    ("mode_indicator".to_string(), "#ff6600".to_string()),
                ]),
            },
            settings: HashMap::from([
                ("vim_mode".to_string(), serde_json::json!(format!("{:?}", self.vim_state.mode))),
            ]),
        }
    }
    
    fn can_transition_to(&self, target_mode: UiMode) -> bool {
        match target_mode {
            UiMode::Normal | UiMode::Debug => true,
            UiMode::Vim => false, // Already in vim mode
        }
    }
}

/// Debug mode controller
#[derive(Debug)]
struct DebugModeController {
    debug_level: String,
    show_internals: bool,
}

impl DebugModeController {
    fn new() -> Self {
        Self {
            debug_level: "info".to_string(),
            show_internals: false,
        }
    }
}

#[async_trait::async_trait]
impl ModeController for DebugModeController {
    async fn enter(&mut self, _previous_mode: UiMode) -> Result<()> {
        debug!("Entering debug mode");
        self.show_internals = true;
        Ok(())
    }
    
    async fn exit(&mut self, _next_mode: UiMode) -> Result<()> {
        debug!("Exiting debug mode");
        self.show_internals = false;
        Ok(())
    }
    
    async fn save_state(&self) -> Result<ModeState> {
        Ok(ModeState {
            mode: UiMode::Debug,
            data: serde_json::json!({
                "debug_level": self.debug_level,
                "show_internals": self.show_internals,
            }),
            timestamp: chrono::Utc::now(),
        })
    }
    
    async fn restore_state(&mut self, state: ModeState) -> Result<()> {
        if let Some(level) = state.data.get("debug_level").and_then(|v| v.as_str()) {
            self.debug_level = level.to_string();
        }
        if let Some(show) = state.data.get("show_internals").and_then(|v| v.as_bool()) {
            self.show_internals = show;
        }
        Ok(())
    }
    
    fn get_config(&self) -> ModeConfig {
        ModeConfig {
            name: "Debug".to_string(),
            description: "Enhanced debugging mode with internal visibility".to_string(),
            shortcuts: HashMap::from([
                ("F1".to_string(), "Toggle debug level".to_string()),
                ("F2".to_string(), "Toggle internals".to_string()),
                ("Ctrl+D".to_string(), "Dump state".to_string()),
            ]),
            layout: LayoutConfig {
                show_status_bar: true,
                show_line_numbers: true,
                show_mode_indicator: true,
                color_overrides: HashMap::from([
                    ("debug_text".to_string(), "#00ff00".to_string()),
                    ("error_text".to_string(), "#ff0000".to_string()),
                ]),
            },
            settings: HashMap::from([
                ("debug_level".to_string(), serde_json::json!(self.debug_level)),
                ("show_internals".to_string(), serde_json::json!(self.show_internals)),
            ]),
        }
    }
    
    fn can_transition_to(&self, _target_mode: UiMode) -> bool {
        true // Debug mode can transition to any mode
    }
}

/// Mode persistence manager
#[derive(Debug)]
struct ModePersistence;

impl ModePersistence {
    fn new() -> Self { Self }
    
    async fn save_mode_state(&self, _state: &ModeState) -> Result<()> {
        // TODO: Implement actual persistence
        Ok(())
    }
    
    async fn load_mode_state(&self, _mode: &UiMode) -> Result<ModeState> {
        // TODO: Implement actual loading
        Err(AutorunError::ModeError("No saved state".to_string()))
    }
    
    async fn save_mode_preference(&self, _mode: &UiMode) -> Result<()> {
        // TODO: Implement preference saving
        Ok(())
    }
    
    async fn load_mode_preference(&self) -> Result<UiMode> {
        // TODO: Implement preference loading
        Ok(UiMode::Normal)
    }
}