//! UI mode management system
//! 
//! This module provides mode switching with state preservation,
//! supporting normal, vim, and debug modes with their specific behaviors.

pub mod mode_manager;
pub mod vim_mode_controller;
pub mod debug_mode_controller;
pub mod mode_persistence;

// Re-export main types
pub use mode_manager::{
    ModeManager, ModeController, ModeState, ModeConfig, ModeTransition,
    LayoutConfig, ActiveOperation, PendingTask, TaskPriority,
};

use crate::ui::enhanced::state::UiMode;
use crate::errors::Result;

/// Initialize the mode management system
pub async fn initialize_mode_system() -> Result<ModeManager> {
    let manager = ModeManager::new();
    manager.initialize().await?;
    Ok(manager)
}

/// Mode change event for UI updates
#[derive(Debug, Clone)]
pub struct ModeChangeEvent {
    /// Previous mode
    pub from_mode: UiMode,
    
    /// New mode
    pub to_mode: UiMode,
    
    /// Transition timestamp
    pub timestamp: chrono::DateTime<chrono::Utc>,
    
    /// Transition reason
    pub reason: String,
    
    /// Whether transition was successful
    pub successful: bool,
    
    /// Any error message
    pub error: Option<String>,
}

/// Mode capability flags
#[derive(Debug, Clone)]
pub struct ModeCapabilities {
    /// Supports vim-style keybindings
    pub vim_keybindings: bool,
    
    /// Supports modal editing
    pub modal_editing: bool,
    
    /// Supports command mode
    pub command_mode: bool,
    
    /// Supports debugging features
    pub debugging: bool,
    
    /// Supports advanced editing
    pub advanced_editing: bool,
    
    /// Supports custom shortcuts
    pub custom_shortcuts: bool,
}

/// Mode statistics for analytics
#[derive(Debug, Clone)]
pub struct ModeStatistics {
    /// Total time spent in mode
    pub total_time: chrono::Duration,
    
    /// Number of times mode was entered
    pub entry_count: u32,
    
    /// Average session duration
    pub average_session: chrono::Duration,
    
    /// Most common transitions from this mode
    pub common_transitions: Vec<(UiMode, u32)>,
    
    /// User efficiency metrics
    pub efficiency_metrics: EfficiencyMetrics,
}

/// Efficiency metrics for mode usage
#[derive(Debug, Clone)]
pub struct EfficiencyMetrics {
    /// Commands per minute
    pub commands_per_minute: f64,
    
    /// Error rate
    pub error_rate: f64,
    
    /// Completion rate
    pub completion_rate: f64,
    
    /// User satisfaction score
    pub satisfaction_score: Option<f32>,
}