use crate::commands::{CommandResponse, ScrollDirection, UiAction};
use crate::core::{App<PERSON><PERSON>, AppCoreMessage};
use crate::errors::Result;
use crate::ui::completion::{
    CompletionEngine, CompletionPopup, MentionCompletionEngine, MentionPopupState,
};
use crate::ui::widgets::{WidgetFactory, WidgetRegistry};
use crossterm::event::KeyEvent;
use ratatui::layout::Rect;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{mpsc, Mutex};
use tracing::info;
use tui_textarea::TextArea;

#[derive(Debug, Clone, PartialEq)]
pub enum Mode {
    Normal,
    Input,
    Widget, // Widget interaction mode
}

#[derive(Debug, Clone, PartialEq)]
pub enum FocusState {
    Chat,
    Input,
    Widget(String), // Widget ID
}

/// TUI-specific interface that handles rendering and input
pub struct TuiInterface {
    pub app_core: Arc<Mutex<AppCore>>,
    pub mode: Mode,
    pub vim_mode: bool,
    pub input_textarea: TextArea<'static>,
    pub completion_engine: CompletionEngine,
    pub completion_popup: CompletionPopup,
    pub mention_completion_engine: Arc<Mutex<MentionCompletionEngine>>,
    pub mention_popup_state: MentionPopupState,
    pub scroll_offset: usize,
    // Widget system
    pub widget_factory: Arc<WidgetFactory>,
    pub widget_registry: Arc<WidgetRegistry>,
    pub focus_state: FocusState,
    pub active_widgets: Vec<String>, // Widget IDs in display order
    pub widget_area_cache: HashMap<String, Rect>,
    // Message handling
    pub agent_tx: mpsc::Sender<AppCoreMessage>,
    pub agent_rx: mpsc::Receiver<AppCoreMessage>,
}

impl TuiInterface {
    pub async fn new(app_core: Arc<Mutex<AppCore>>, vim_mode: bool) -> Result<Self> {
        let (agent_tx, agent_rx) = mpsc::channel(100);

        // Get required dependencies from app_core
        let (command_registry, tool_registry) = {
            let core = app_core.lock().await;
            (core.command_registry.clone(), core.tool_registry.clone())
        };

        // Initialize completion engines
        let completion_engine = CompletionEngine::new(command_registry);
        let completion_popup = CompletionPopup::new();
        let mention_completion_engine =
            Arc::new(Mutex::new(MentionCompletionEngine::new(tool_registry)));
        let mention_popup_state = MentionPopupState::new();

        // Initialize widget system
        let widget_factory = Arc::new(WidgetFactory::new());
        let widget_registry = Arc::new(WidgetRegistry::new(widget_factory.clone()));

        // Initialize input textarea
        let mut input_textarea = TextArea::default();
        input_textarea.set_placeholder_text("Type your message here...");

        Ok(Self {
            app_core,
            mode: Mode::Normal,
            vim_mode,
            input_textarea,
            completion_engine,
            completion_popup,
            mention_completion_engine,
            mention_popup_state,
            scroll_offset: 0,
            widget_factory,
            widget_registry,
            focus_state: FocusState::Input,
            active_widgets: Vec::new(),
            widget_area_cache: HashMap::new(),
            agent_tx,
            agent_rx,
        })
    }

    /// Handle keyboard input based on current mode
    pub async fn handle_key_event(&mut self, key: KeyEvent) -> Result<Option<UiAction>> {
        match self.mode {
            Mode::Normal => self.handle_normal_mode_key(key).await,
            Mode::Input => self.handle_input_mode_key(key).await,
            Mode::Widget => self.handle_widget_mode_key(key).await,
        }
    }

    async fn handle_normal_mode_key(&mut self, key: KeyEvent) -> Result<Option<UiAction>> {
        use crossterm::event::KeyCode;

        match key.code {
            KeyCode::Char('q') => Ok(Some(UiAction::Exit)),
            KeyCode::Char('i') => {
                self.mode = Mode::Input;
                self.focus_state = FocusState::Input;
                Ok(None)
            }
            KeyCode::Char('j') | KeyCode::Down => {
                Ok(Some(UiAction::ScrollMessages(ScrollDirection::Down(1))))
            }
            KeyCode::Char('k') | KeyCode::Up => {
                Ok(Some(UiAction::ScrollMessages(ScrollDirection::Up(1))))
            }
            KeyCode::Char('G') => Ok(Some(UiAction::ScrollMessages(ScrollDirection::Bottom))),
            KeyCode::Char('g') => Ok(Some(UiAction::ScrollMessages(ScrollDirection::Top))),
            KeyCode::Char('w') if !self.active_widgets.is_empty() => {
                self.mode = Mode::Widget;
                if let Some(first_widget) = self.active_widgets.first() {
                    self.focus_state = FocusState::Widget(first_widget.clone());
                }
                Ok(None)
            }
            _ => Ok(None),
        }
    }

    async fn handle_input_mode_key(&mut self, key: KeyEvent) -> Result<Option<UiAction>> {
        use crossterm::event::{KeyCode, KeyModifiers};

        match (key.code, key.modifiers) {
            (KeyCode::Esc, _) => {
                self.mode = Mode::Normal;
                self.focus_state = FocusState::Chat;
                Ok(None)
            }
            (KeyCode::Enter, KeyModifiers::NONE) => {
                let input = self.input_textarea.lines().join("\n");
                if !input.trim().is_empty() {
                    self.input_textarea.select_all();
                    self.input_textarea.delete_char();

                    // Send message directly
                    let core = self.app_core.lock().await;
                    let tx = self.agent_tx.clone();
                    drop(core); // Release lock before async operation

                    let mut core = self.app_core.lock().await;
                    core.process_user_message(input, tx).await?;
                }
                Ok(None)
            }
            (KeyCode::Tab, _) => {
                self.handle_tab_completion();
                Ok(None)
            }
            _ => {
                self.input_textarea.input(key);
                self.update_mention_completion().await;
                Ok(None)
            }
        }
    }

    async fn handle_widget_mode_key(&mut self, key: KeyEvent) -> Result<Option<UiAction>> {
        use crossterm::event::KeyCode;

        match key.code {
            KeyCode::Esc => {
                self.mode = Mode::Normal;
                self.focus_state = FocusState::Chat;
                Ok(None)
            }
            KeyCode::Tab => {
                // Cycle through widgets
                if let FocusState::Widget(current_id) = &self.focus_state {
                    if let Some(pos) = self.active_widgets.iter().position(|id| id == current_id) {
                        let next_pos = (pos + 1) % self.active_widgets.len();
                        if let Some(next_id) = self.active_widgets.get(next_pos) {
                            self.focus_state = FocusState::Widget(next_id.clone());
                        }
                    }
                }
                Ok(None)
            }
            _ => {
                // Pass key event to the focused widget
                if let FocusState::Widget(widget_id) = &self.focus_state {
                    // For now, skip widget key handling
                    // TODO: Implement proper widget key handling
                }
                Ok(None)
            }
        }
    }

    fn handle_tab_completion(&mut self) {
        // Get the current word being typed
        let cursor_col = self.input_textarea.cursor().0;
        let current_line = self.input_textarea.lines()[self.input_textarea.cursor().1].clone();

        // Find word boundaries
        let before_cursor = &current_line[..cursor_col.min(current_line.len())];
        let word_start = before_cursor.rfind(' ').map(|i| i + 1).unwrap_or(0);
        let current_word = &before_cursor[word_start..];

        // Check if we have a completion popup active
        if self.completion_popup.is_visible() {
            // Navigate in the popup
            self.completion_popup.move_selection_down();

            // Apply the selected completion
            if let Some(selected) = self.completion_popup.get_selected() {
                // Delete the current partial word
                for _ in 0..current_word.len() {
                    self.input_textarea.delete_char();
                }

                // Insert the completion
                for ch in selected.text.chars() {
                    self.input_textarea.insert_char(ch);
                }

                // Hide the popup after applying
                self.completion_popup.hide();
            }
        } else if !current_word.is_empty() {
            // For now, skip complex completion logic
            // TODO: Implement proper completion system
        }
    }

    async fn update_mention_completion(&mut self) {
        let cursor_col = self.input_textarea.cursor().0;
        let current_line = self.input_textarea.lines()[self.input_textarea.cursor().1].clone();

        // Check if we're in a mention context (after @)
        if let Some(at_pos) = current_line[..cursor_col.min(current_line.len())].rfind('@') {
            let mention_text = &current_line[at_pos + 1..cursor_col.min(current_line.len())];

            // For now, skip complex mention completion
            // TODO: Implement proper mention completion system
        } else {
            self.mention_popup_state.hide();
        }
    }

    /// Handle command responses from the command system
    pub async fn handle_command_response(&mut self, response: CommandResponse) -> Result<()> {
        match response {
            CommandResponse::Message(msg) => {
                info!("Command message: {}", msg);
                // Add message to chat
                let mut core = self.app_core.lock().await;
                core.messages.push(format!("Command: {}", msg));
            }
            CommandResponse::UiAction(action) => {
                self.handle_ui_action(action).await?;
            }
            CommandResponse::AgentPrompt(prompt) => {
                // Send prompt to agent
                let core = self.app_core.lock().await;
                let tx = self.agent_tx.clone();
                drop(core);

                let mut core = self.app_core.lock().await;
                core.process_user_message(prompt, tx).await?;
            }
            CommandResponse::Multiple(responses) => {
                for response in responses {
                    Box::pin(self.handle_command_response(response)).await?;
                }
            }
            CommandResponse::None => {
                // Nothing to do
            }
        }
        Ok(())
    }

    async fn handle_ui_action(&mut self, action: UiAction) -> Result<()> {
        match action {
            UiAction::ScrollMessages(direction) => {
                let core = self.app_core.lock().await;
                let message_count = core.get_messages().len();
                drop(core);

                match direction {
                    ScrollDirection::Up(n) => {
                        self.scroll_offset = self.scroll_offset.saturating_sub(n);
                    }
                    ScrollDirection::Down(n) => {
                        self.scroll_offset = self.scroll_offset.saturating_add(n);
                    }
                    ScrollDirection::Top => {
                        self.scroll_offset = 0;
                    }
                    ScrollDirection::Bottom => {
                        self.scroll_offset = message_count.saturating_sub(10);
                    }
                }
            }
            UiAction::Exit => {
                let _ = self.agent_tx.send(AppCoreMessage::Exit).await;
            }
            UiAction::ClearMessages => {
                // Clear messages in the core
                let mut core = self.app_core.lock().await;
                // TODO: Implement clear messages in AppCore
            }
            _ => {}
        }
        Ok(())
    }

    /// Process messages from the agent core
    pub async fn process_agent_messages(&mut self) -> Result<()> {
        while let Ok(message) = self.agent_rx.try_recv() {
            let mut core = self.app_core.lock().await;
            match &message {
                AppCoreMessage::WidgetGeneration(request) => {
                    // Handle widget generation
                    if let Ok(widget_id) = self.widget_registry.create_widget(&request).await {
                        self.active_widgets.push(widget_id);
                    }
                }
                AppCoreMessage::WidgetUpdate(event) => {
                    // Handle widget updates
                    let _ = self.widget_registry.update_widget(&event).await;
                }
                _ => {}
            }
            core.handle_agent_message(message);
        }
        Ok(())
    }

    /// Add a widget to the active display
    pub async fn add_widget(&mut self, widget_id: String) {
        self.active_widgets.push(widget_id);
    }

    /// Remove a widget from the active display
    pub async fn remove_widget(&mut self, widget_id: &str) {
        self.active_widgets.retain(|id| id != widget_id);
        self.widget_area_cache.remove(widget_id);
    }
}
