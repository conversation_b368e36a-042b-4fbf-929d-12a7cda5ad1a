// Parsers for LLM responses and JSON validation

use crate::agent::core::{WidgetGenerationRequest, WidgetUpdateEvent};
use crate::errors::{AutorunError, Result};
use crate::ui::widgets::{LayoutConstraint, LayoutDirection, WidgetConfig, WidgetLayout};
use regex::Regex;
use serde_json::{json, Value};
use std::collections::HashMap;
use tracing::{debug, error, warn};

/// Parser for widget configurations from LLM responses
pub struct WidgetConfigParser {
    json_extractor: JsonExtractor,
    validator: ConfigValidator,
}

impl WidgetConfigParser {
    pub fn new() -> Self {
        Self {
            json_extractor: JsonExtractor::new(),
            validator: ConfigValidator::new(),
        }
    }

    /// Parse LLM response into widget configuration
    pub async fn parse_response(
        &self,
        response: &str,
        request: &WidgetGenerationRequest,
    ) -> Result<WidgetConfig> {
        debug!("Parsing LLM response for widget configuration");

        // Extract JSON from response
        let json_value = self.json_extractor.extract_json(response)?;

        // Try to parse as WidgetConfig directly
        match serde_json::from_value::<WidgetConfig>(json_value.clone()) {
            Ok(mut config) => {
                // Ensure widget type matches request
                if config.widget_type != request.widget_type {
                    warn!(
                        "Widget type mismatch: expected {}, got {}",
                        request.widget_type, config.widget_type
                    );
                    config.widget_type = request.widget_type.clone();
                }

                // Validate configuration
                self.validator.validate_config(&config)?;

                Ok(config)
            }
            Err(e) => {
                error!("Failed to parse widget config directly: {}", e);
                // Try to construct from partial data
                self.construct_config_from_json(json_value, request)
            }
        }
    }

    /// Parse update response from LLM
    pub async fn parse_update_response(
        &self,
        response: &str,
        widget_id: &str,
    ) -> Result<WidgetUpdateEvent> {
        debug!("Parsing LLM response for widget update");

        let json_value = self.json_extractor.extract_json(response)?;

        // Try to parse as WidgetUpdateEvent
        match serde_json::from_value::<WidgetUpdateEvent>(json_value.clone()) {
            Ok(mut event) => {
                // Ensure widget ID matches
                if event.widget_id != widget_id {
                    event.widget_id = widget_id.to_string();
                }
                Ok(event)
            }
            Err(e) => {
                error!("Failed to parse update event directly: {}", e);
                // Construct from partial data
                self.construct_update_event(json_value, widget_id)
            }
        }
    }

    /// Construct widget config from partial JSON data
    fn construct_config_from_json(
        &self,
        json: Value,
        request: &WidgetGenerationRequest,
    ) -> Result<WidgetConfig> {
        let obj = json
            .as_object()
            .ok_or_else(|| AutorunError::ParseError("Expected JSON object".to_string()))?;

        // Extract or generate ID
        let id = obj
            .get("id")
            .and_then(|v| v.as_str())
            .map(String::from)
            .unwrap_or_else(|| format!("{}-{}", request.widget_type, uuid::Uuid::new_v4()));

        // Extract title
        let title = obj.get("title").and_then(|v| v.as_str()).map(String::from);

        // Extract properties
        let properties = obj
            .get("properties")
            .and_then(|v| v.as_object())
            .map(|props| {
                props
                    .iter()
                    .map(|(k, v)| (k.clone(), v.clone()))
                    .collect::<HashMap<_, _>>()
            })
            .unwrap_or_default();

        // Extract or create layout
        let layout = obj
            .get("layout")
            .and_then(|v| self.parse_layout(v).ok())
            .unwrap_or_else(|| WidgetLayout {
                constraints: vec![LayoutConstraint::Percentage(100)],
                direction: LayoutDirection::Vertical,
            });

        let config = WidgetConfig {
            widget_type: request.widget_type.clone(),
            id,
            title,
            properties,
            layout,
        };

        // Validate before returning
        self.validator.validate_config(&config)?;
        Ok(config)
    }

    /// Construct update event from JSON
    fn construct_update_event(&self, json: Value, widget_id: &str) -> Result<WidgetUpdateEvent> {
        let obj = json
            .as_object()
            .ok_or_else(|| AutorunError::ParseError("Expected JSON object".to_string()))?;

        let event_type = obj
            .get("event_type")
            .and_then(|v| v.as_str())
            .unwrap_or("update")
            .to_string();

        let data = obj.get("data").cloned().unwrap_or_else(|| json!({}));

        Ok(WidgetUpdateEvent {
            widget_id: widget_id.to_string(),
            event_type,
            data,
        })
    }

    /// Parse layout configuration
    fn parse_layout(&self, value: &Value) -> Result<WidgetLayout> {
        let obj = value
            .as_object()
            .ok_or_else(|| AutorunError::ParseError("Layout must be an object".to_string()))?;

        // Parse constraints
        let constraints = obj
            .get("constraints")
            .and_then(|v| v.as_array())
            .map(|arr| {
                arr.iter()
                    .filter_map(|c| self.parse_constraint(c).ok())
                    .collect()
            })
            .unwrap_or_else(|| vec![LayoutConstraint::Percentage(100)]);

        // Parse direction
        let direction = obj
            .get("direction")
            .and_then(|v| v.as_str())
            .and_then(|s| match s {
                "Horizontal" => Some(LayoutDirection::Horizontal),
                "Vertical" => Some(LayoutDirection::Vertical),
                _ => None,
            })
            .unwrap_or(LayoutDirection::Vertical);

        Ok(WidgetLayout {
            constraints,
            direction,
        })
    }

    /// Parse individual constraint
    fn parse_constraint(&self, value: &Value) -> Result<LayoutConstraint> {
        if let Some(obj) = value.as_object() {
            if let Some((key, val)) = obj.iter().next() {
                match key.as_str() {
                    "Length" => {
                        let n = val.as_u64().ok_or_else(|| {
                            AutorunError::ParseError("Length must be a number".to_string())
                        })? as u16;
                        return Ok(LayoutConstraint::Length(n));
                    }
                    "Min" => {
                        let n = val.as_u64().ok_or_else(|| {
                            AutorunError::ParseError("Min must be a number".to_string())
                        })? as u16;
                        return Ok(LayoutConstraint::Min(n));
                    }
                    "Max" => {
                        let n = val.as_u64().ok_or_else(|| {
                            AutorunError::ParseError("Max must be a number".to_string())
                        })? as u16;
                        return Ok(LayoutConstraint::Max(n));
                    }
                    "Percentage" => {
                        let n = val.as_u64().ok_or_else(|| {
                            AutorunError::ParseError("Percentage must be a number".to_string())
                        })? as u16;
                        return Ok(LayoutConstraint::Percentage(n));
                    }
                    "Ratio" => {
                        if let Some(arr) = val.as_array() {
                            if arr.len() == 2 {
                                let a = arr[0].as_u64().unwrap_or(1) as u32;
                                let b = arr[1].as_u64().unwrap_or(1) as u32;
                                return Ok(LayoutConstraint::Ratio(a, b));
                            }
                        }
                    }
                    "Fill" => {
                        let n = val.as_u64().ok_or_else(|| {
                            AutorunError::ParseError("Fill must be a number".to_string())
                        })? as u16;
                        return Ok(LayoutConstraint::Fill(n));
                    }
                    _ => {}
                }
            }
        }

        Err(AutorunError::ParseError(
            "Invalid constraint format".to_string(),
        ))
    }
}

/// JSON extractor for finding and parsing JSON in text
struct JsonExtractor {
    json_regex: Regex,
    code_block_regex: Regex,
}

impl JsonExtractor {
    fn new() -> Self {
        Self {
            json_regex: Regex::new(r"\{[\s\S]*\}").unwrap(),
            code_block_regex: Regex::new(r"```(?:json)?\s*([\s\S]*?)\s*```").unwrap(),
        }
    }

    /// Extract JSON from text response
    fn extract_json(&self, text: &str) -> Result<Value> {
        // First try to parse the entire response as JSON
        if let Ok(value) = serde_json::from_str::<Value>(text) {
            return Ok(value);
        }

        // Try to extract from code blocks
        if let Some(captures) = self.code_block_regex.captures(text) {
            if let Some(json_str) = captures.get(1) {
                if let Ok(value) = serde_json::from_str::<Value>(json_str.as_str()) {
                    return Ok(value);
                }
            }
        }

        // Try to find JSON object in the text
        if let Some(json_match) = self.json_regex.find(text) {
            let json_str = json_match.as_str();
            if let Ok(value) = serde_json::from_str::<Value>(json_str) {
                return Ok(value);
            }
        }

        // Try line by line parsing for JSON objects
        for line in text.lines() {
            let trimmed = line.trim();
            if trimmed.starts_with('{') && trimmed.ends_with('}') {
                if let Ok(value) = serde_json::from_str::<Value>(trimmed) {
                    return Ok(value);
                }
            }
        }

        Err(AutorunError::ParseError(
            "No valid JSON found in response".to_string(),
        ))
    }
}

/// Configuration validator
struct ConfigValidator;

impl ConfigValidator {
    fn new() -> Self {
        Self
    }

    /// Validate widget configuration
    fn validate_config(&self, config: &WidgetConfig) -> Result<()> {
        // Validate ID
        if config.id.is_empty() {
            return Err(AutorunError::ValidationError(
                "Widget ID cannot be empty".to_string(),
            ));
        }

        // Validate widget type
        if config.widget_type.is_empty() {
            return Err(AutorunError::ValidationError(
                "Widget type cannot be empty".to_string(),
            ));
        }

        // Validate layout
        if config.layout.constraints.is_empty() {
            return Err(AutorunError::ValidationError(
                "Layout must have at least one constraint".to_string(),
            ));
        }

        // Validate constraint values
        for constraint in &config.layout.constraints {
            match constraint {
                LayoutConstraint::Percentage(p) => {
                    if *p > 100 {
                        return Err(AutorunError::ValidationError(
                            "Percentage cannot exceed 100".to_string(),
                        ));
                    }
                }
                LayoutConstraint::Ratio(a, b) => {
                    if *a == 0 || *b == 0 {
                        return Err(AutorunError::ValidationError(
                            "Ratio values must be non-zero".to_string(),
                        ));
                    }
                }
                _ => {}
            }
        }

        Ok(())
    }
}

/// Response sanitizer for cleaning up LLM responses
pub struct ResponseSanitizer;

impl ResponseSanitizer {
    /// Clean and sanitize LLM response
    pub fn sanitize(response: &str) -> String {
        let mut cleaned = response.trim().to_string();

        // Remove markdown formatting
        cleaned = cleaned.replace("```json", "");
        cleaned = cleaned.replace("```", "");

        // Remove common LLM response artifacts
        let artifacts = [
            "Here is the JSON configuration:",
            "Here's the widget configuration:",
            "The following JSON represents",
            "JSON response:",
        ];

        for artifact in &artifacts {
            if let Some(idx) = cleaned.find(artifact) {
                cleaned = cleaned[idx + artifact.len()..].trim().to_string();
            }
        }

        cleaned
    }
}

/// Error recovery helper
pub struct ErrorRecovery;

impl ErrorRecovery {
    /// Attempt to recover from parsing errors
    pub fn recover_widget_config(
        error: &AutorunError,
        request: &WidgetGenerationRequest,
    ) -> Option<WidgetConfig> {
        warn!("Attempting to recover from error: {}", error);

        // Create minimal valid configuration
        Some(WidgetConfig {
            widget_type: request.widget_type.clone(),
            id: format!("{}-recovery-{}", request.widget_type, uuid::Uuid::new_v4()),
            title: Some(format!("Recovered {}", request.widget_type)),
            properties: HashMap::new(),
            layout: WidgetLayout {
                constraints: vec![LayoutConstraint::Percentage(100)],
                direction: LayoutDirection::Vertical,
            },
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_json_extraction() {
        let extractor = JsonExtractor::new();

        // Test direct JSON
        let direct_json = r#"{"widget_type": "test", "id": "test-1"}"#;
        assert!(extractor.extract_json(direct_json).is_ok());

        // Test JSON in code block
        let code_block = r#"```json
        {
            "widget_type": "test",
            "id": "test-2"
        }
        ```"#;
        assert!(extractor.extract_json(code_block).is_ok());

        // Test JSON with text
        let mixed = r#"Here is the configuration:
        {"widget_type": "test", "id": "test-3"}
        That's the JSON."#;
        assert!(extractor.extract_json(mixed).is_ok());
    }

    #[test]
    fn test_config_validation() {
        let validator = ConfigValidator::new();

        // Valid config
        let valid_config = WidgetConfig {
            widget_type: "test".to_string(),
            id: "test-1".to_string(),
            title: None,
            properties: HashMap::new(),
            layout: WidgetLayout {
                constraints: vec![LayoutConstraint::Percentage(50)],
                direction: LayoutDirection::Vertical,
            },
        };
        assert!(validator.validate_config(&valid_config).is_ok());

        // Invalid config - empty ID
        let mut invalid_config = valid_config.clone();
        invalid_config.id = String::new();
        assert!(validator.validate_config(&invalid_config).is_err());

        // Invalid config - percentage > 100
        let mut invalid_config = valid_config.clone();
        invalid_config.layout.constraints = vec![LayoutConstraint::Percentage(150)];
        assert!(validator.validate_config(&invalid_config).is_err());
    }

    #[test]
    fn test_response_sanitizer() {
        let response = r#"Here is the JSON configuration:
        ```json
        {"test": "value"}
        ```"#;

        let sanitized = ResponseSanitizer::sanitize(response);
        assert!(!sanitized.contains("```"));
        assert!(!sanitized.contains("Here is the JSON"));
    }
}
