// LLM Widget Generation Integration
// Bridges AI agent with widget factory for dynamic UI generation

use crate::agent::core::{AgentUpdate, WidgetGenerationRequest, WidgetUpdateEvent};
use crate::errors::{AutorunError, Result};
use crate::llm::{LL<PERSON>rovider, Message};
use crate::prompts::PromptIntegration;
use crate::ui::widgets::{WidgetConfig, WidgetFactory, WidgetRegistry};
use dashmap::DashMap;
use serde::Serialize;
use serde_json::{json, Value};
use std::sync::Arc;
use tokio::sync::mpsc;
use tracing::{debug, error, info};

pub mod parsers;
pub mod prompts;

pub use parsers::*;
pub use prompts::*;

/// Cache entry for widget configurations
#[derive(Debug, Clone)]
struct CacheEntry {
    config: WidgetConfig,
    timestamp: std::time::Instant,
    request_hash: u64,
}

/// LLM Widget Integration Service with prompt system integration
pub struct LLMWidgetIntegration {
    llm_provider: Arc<dyn LLMProvider>,
    widget_factory: Arc<WidgetFactory>,
    widget_registry: Arc<WidgetRegistry>,
    prompt_integration: Arc<PromptIntegration>,
    config_cache: Arc<DashMap<String, CacheEntry>>,
    cache_ttl: std::time::Duration,
    ui_update_tx: Option<mpsc::Sender<AgentUpdate>>,
}

impl LLMWidgetIntegration {
    pub fn new(
        llm_provider: Arc<dyn LLMProvider>,
        widget_factory: Arc<WidgetFactory>,
        widget_registry: Arc<WidgetRegistry>,
    ) -> Result<Self> {
        let prompt_integration = Arc::new(PromptIntegration::new().map_err(|e| {
            AutorunError::Unknown(format!("Failed to initialize prompt system: {}", e))
        })?);

        Ok(Self {
            llm_provider,
            widget_factory,
            widget_registry,
            prompt_integration,
            config_cache: Arc::new(DashMap::new()),
            cache_ttl: std::time::Duration::from_secs(300), // 5 minute cache
            ui_update_tx: None,
        })
    }

    /// Create a new instance with custom prompt integration
    pub fn with_prompt_integration(
        llm_provider: Arc<dyn LLMProvider>,
        widget_factory: Arc<WidgetFactory>,
        widget_registry: Arc<WidgetRegistry>,
        prompt_integration: Arc<PromptIntegration>,
    ) -> Self {
        Self {
            llm_provider,
            widget_factory,
            widget_registry,
            prompt_integration,
            config_cache: Arc::new(DashMap::new()),
            cache_ttl: std::time::Duration::from_secs(300),
            ui_update_tx: None,
        }
    }

    /// Set UI update channel
    pub fn set_ui_update_channel(&mut self, tx: mpsc::Sender<AgentUpdate>) {
        self.ui_update_tx = Some(tx);
    }

    /// Generate widget using the new prompt system
    pub async fn generate_widget_v2(&mut self, request: WidgetGenerationRequest) -> Result<String> {
        info!(
            "Generating widget of type: {} using new prompt system",
            request.widget_type
        );
        debug!("Widget context: {}", request.context);

        // Check cache first
        let request_hash = self.hash_request(&request);
        if let Some(cached) = self.get_cached_config(&request.widget_type, request_hash) {
            info!(
                "Using cached widget configuration for {}",
                request.widget_type
            );
            return Ok(cached.id.clone());
        }

        // Get available widget schemas
        let available_schemas = self.widget_factory.get_available_widgets();

        // Create prompt builder with our prompt integration
        let prompt_builder =
            WidgetPromptBuilder::with_prompt_integration(self.prompt_integration.clone());

        // Get system prompt
        let system_prompt = prompt_builder
            .get_system_prompt()
            .map_err(|e| AutorunError::Unknown(format!("Failed to get system prompt: {}", e)))?;

        // Build generation prompt
        let user_prompt = prompt_builder
            .build_generation_prompt(&request, &available_schemas)
            .map_err(|e| {
                AutorunError::Unknown(format!("Failed to build generation prompt: {}", e))
            })?;

        // Send to LLM
        let messages = vec![Message::system(&system_prompt), Message::user(&user_prompt)];

        if let Some(ref tx) = self.ui_update_tx {
            let _ = tx
                .send(AgentUpdate::LlmText(
                    "Generating widget configuration...".to_string(),
                ))
                .await;
        }

        let response = self.llm_provider.complete(messages).await?;

        // Parse and validate the response
        let widget_config = self.parse_widget_response(&response, &request.widget_type)?;

        // Cache the result
        self.cache_config(&request.widget_type, request_hash, widget_config.clone());

        // Create the widget using the factory
        let widget_request = WidgetGenerationRequest {
            widget_type: widget_config.widget_type.clone(),
            context: serde_json::to_string(&widget_config).unwrap_or_default(),
            config: serde_json::to_value(&widget_config)?,
        };
        let _widget_result = self.widget_factory.create_widget(&widget_request).await?;

        if let Some(ref tx) = self.ui_update_tx {
            let _ = tx
                .send(AgentUpdate::LlmText(format!(
                    "Widget '{}' generated successfully!",
                    widget_config.id
                )))
                .await;
        }

        Ok(widget_config.id)
    }

    /// Generate widget configuration from LLM
    pub async fn generate_widget(&mut self, request: WidgetGenerationRequest) -> Result<String> {
        info!("Generating widget of type: {}", request.widget_type);
        debug!("Widget context: {}", request.context);

        // Check cache first
        let request_hash = self.hash_request(&request);
        if let Some(cached) = self.get_cached_config(&request.widget_type, request_hash) {
            info!(
                "Using cached widget configuration for {}",
                request.widget_type
            );
            return self.create_widget_from_config(cached).await;
        }

        // Generate prompt for LLM
        let prompt = self.create_widget_generation_prompt(&request)?;
        debug!("Generated prompt: {}", prompt);

        // Call LLM to generate widget configuration
        let llm_response = self.call_llm_for_widget_config(prompt).await?;
        debug!("LLM response received: {} chars", llm_response.len());

        // Parse and validate LLM response
        let widget_config = match self.parse_llm_response(&llm_response, &request).await {
            Ok(config) => config,
            Err(e) => {
                error!("Failed to parse LLM response: {}", e);
                // Fallback to default configuration
                self.create_fallback_config(&request)?
            }
        };

        // Validate configuration
        self.widget_factory.validate_widget_config(&widget_config)?;

        // Cache the configuration
        self.cache_config(&request.widget_type, request_hash, widget_config.clone());

        // Create widget from configuration
        self.create_widget_from_config(widget_config).await
    }

    /// Handle widget update events
    pub async fn handle_widget_update(&self, event: WidgetUpdateEvent) -> Result<()> {
        info!(
            "Handling widget update for {}: {}",
            event.widget_id, event.event_type
        );

        // Update widget through registry
        self.widget_registry.update_widget(&event).await?;

        // Send UI update notification
        if let Some(tx) = &self.ui_update_tx {
            let _ = tx.send(AgentUpdate::WidgetUpdate(event.clone())).await;
        }

        Ok(())
    }

    /// Generate widget configuration incrementally
    pub async fn update_widget_incrementally(
        &mut self,
        widget_id: String,
        update_context: String,
    ) -> Result<()> {
        info!("Incrementally updating widget: {}", widget_id);

        // Get current widget configuration
        let widget = self
            .widget_registry
            .get_widget(&widget_id)
            .ok_or_else(|| AutorunError::NotFound(format!("Widget not found: {}", widget_id)))?;

        let current_config = widget.get_config();
        let current_state = widget.get_state();

        // Generate update prompt
        let prompt =
            self.create_widget_update_prompt(&current_config, &current_state, &update_context)?;

        // Call LLM for incremental update
        let llm_response = self.call_llm_for_widget_config(prompt).await?;

        // Parse update instructions
        let update_event = self
            .parse_update_response(&llm_response, &widget_id)
            .await?;

        // Apply update
        self.handle_widget_update(update_event).await
    }

    /// Create widget from configuration
    async fn create_widget_from_config(&self, config: WidgetConfig) -> Result<String> {
        let request = WidgetGenerationRequest {
            widget_type: config.widget_type.clone(),
            context: serde_json::to_string(&config).unwrap_or_default(),
            config: serde_json::to_value(&config)?,
        };

        self.widget_registry.create_widget(&request).await
    }

    /// Call LLM to generate widget configuration
    async fn call_llm_for_widget_config(&self, prompt: String) -> Result<String> {
        // Use the prompt system to get the system prompt
        let system_prompt = "You are an AI assistant specialized in generating Ratatui-based TUI widget configurations. Generate valid JSON configurations that follow the widget schema requirements.";

        let messages = vec![
            Message {
                role: "system".to_string(),
                content: system_prompt.to_string(),
                tool_calls: None,
                tool_call_id: None,
            },
            Message {
                role: "user".to_string(),
                content: prompt,
                tool_calls: None,
                tool_call_id: None,
            },
        ];

        self.llm_provider.complete(messages).await
    }

    /// Parse LLM response into widget configuration
    async fn parse_llm_response(
        &self,
        response: &str,
        request: &WidgetGenerationRequest,
    ) -> Result<WidgetConfig> {
        let parser = WidgetConfigParser::new();
        parser.parse_response(response, request).await
    }

    /// Parse update response from LLM
    async fn parse_update_response(
        &self,
        response: &str,
        widget_id: &str,
    ) -> Result<WidgetUpdateEvent> {
        let parser = WidgetConfigParser::new();
        parser.parse_update_response(response, widget_id).await
    }

    /// Parse widget response from LLM into widget configuration
    fn parse_widget_response(&self, response: &str, widget_type: &str) -> Result<WidgetConfig> {
        let parser = WidgetConfigParser::new();

        // Try to extract JSON from the response
        let json_start = response.find('{').unwrap_or(0);
        let json_end = response.rfind('}').map(|i| i + 1).unwrap_or(response.len());
        let json_str = &response[json_start..json_end];

        // Parse as JSON
        let config_value: serde_json::Value = serde_json::from_str(json_str).map_err(|e| {
            AutorunError::ParseError(format!("Failed to parse widget config JSON: {}", e))
        })?;

        // Convert to WidgetConfig
        let config: WidgetConfig = serde_json::from_value(config_value).map_err(|e| {
            AutorunError::ParseError(format!("Failed to deserialize widget config: {}", e))
        })?;

        // Validate widget type matches
        if config.widget_type != widget_type {
            return Err(AutorunError::ValidationError(format!(
                "Widget type mismatch: expected {}, got {}",
                widget_type, config.widget_type
            )));
        }

        Ok(config)
    }

    /// Create fallback configuration when LLM fails
    fn create_fallback_config(&self, request: &WidgetGenerationRequest) -> Result<WidgetConfig> {
        // Get default configuration from factory
        let available_widgets = self.widget_factory.get_available_widgets();
        let schema = available_widgets.get(&request.widget_type).ok_or_else(|| {
            AutorunError::NotFound(format!("Unknown widget type: {}", request.widget_type))
        })?;

        // Create minimal valid configuration
        let config = WidgetConfig {
            widget_type: request.widget_type.clone(),
            id: format!("{}-{}", request.widget_type, uuid::Uuid::new_v4()),
            title: Some(format!("Generated {}", request.widget_type)),
            properties: self.extract_default_properties(schema)?,
            layout: crate::ui::widgets::WidgetLayout {
                constraints: vec![crate::ui::widgets::LayoutConstraint::Percentage(100)],
                direction: crate::ui::widgets::LayoutDirection::Vertical,
            },
        };

        Ok(config)
    }

    /// Extract default properties from schema
    fn extract_default_properties(
        &self,
        schema: &Value,
    ) -> Result<std::collections::HashMap<String, Value>> {
        let mut properties = std::collections::HashMap::new();

        if let Some(props) = schema.get("properties").and_then(|p| p.as_object()) {
            for (key, prop_schema) in props {
                if let Some(default) = prop_schema.get("default") {
                    properties.insert(key.clone(), default.clone());
                } else if let Some(type_str) = prop_schema.get("type").and_then(|t| t.as_str()) {
                    // Provide sensible defaults based on type
                    let default_value = match type_str {
                        "string" => json!(""),
                        "number" => json!(0),
                        "boolean" => json!(false),
                        "array" => json!([]),
                        "object" => json!({}),
                        _ => json!(null),
                    };
                    properties.insert(key.clone(), default_value);
                }
            }
        }

        Ok(properties)
    }

    /// Create widget generation prompt
    fn create_widget_generation_prompt(&self, request: &WidgetGenerationRequest) -> Result<String> {
        let prompt_builder = WidgetPromptBuilder::new()?;
        prompt_builder
            .build_generation_prompt(request, &self.widget_factory.get_available_widgets())
    }

    /// Create widget update prompt
    fn create_widget_update_prompt(
        &self,
        config: &WidgetConfig,
        state: &Value,
        context: &str,
    ) -> Result<String> {
        let prompt_builder = WidgetPromptBuilder::new()?;
        prompt_builder.build_update_prompt(config, state, context)
    }

    /// Hash request for caching
    fn hash_request(&self, request: &WidgetGenerationRequest) -> u64 {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        request.widget_type.hash(&mut hasher);
        request.context.hash(&mut hasher);

        // Hash config as JSON string for consistency
        if let Ok(config_str) = serde_json::to_string(&request.config) {
            config_str.hash(&mut hasher);
        }

        hasher.finish()
    }

    /// Get cached configuration if available and not expired
    fn get_cached_config(&self, widget_type: &str, request_hash: u64) -> Option<WidgetConfig> {
        let cache_key = format!("{}-{}", widget_type, request_hash);

        self.config_cache.get(&cache_key).and_then(|entry| {
            if entry.timestamp.elapsed() < self.cache_ttl {
                Some(entry.config.clone())
            } else {
                // Remove expired entry
                drop(entry);
                self.config_cache.remove(&cache_key);
                None
            }
        })
    }

    /// Cache widget configuration
    fn cache_config(&self, widget_type: &str, request_hash: u64, config: WidgetConfig) {
        let cache_key = format!("{}-{}", widget_type, request_hash);
        let entry = CacheEntry {
            config,
            timestamp: std::time::Instant::now(),
            request_hash,
        };

        self.config_cache.insert(cache_key, entry);

        // Clean up old entries periodically
        if self.config_cache.len() > 100 {
            self.cleanup_cache();
        }
    }

    /// Clean up expired cache entries
    fn cleanup_cache(&self) {
        let now = std::time::Instant::now();
        let expired_keys: Vec<String> = self
            .config_cache
            .iter()
            .filter(|entry| now.duration_since(entry.timestamp) > self.cache_ttl)
            .map(|entry| entry.key().clone())
            .collect();

        let expired_count = expired_keys.len();
        for key in expired_keys {
            self.config_cache.remove(&key);
        }

        debug!("Cleaned up {} expired cache entries", expired_count);
    }

    /// Get debugging information
    pub fn get_debug_info(&self) -> DebugInfo {
        DebugInfo {
            cache_size: self.config_cache.len(),
            cache_ttl_seconds: self.cache_ttl.as_secs(),
            registered_widgets: self.widget_registry.list_widgets(),
            available_widget_types: self
                .widget_factory
                .get_available_widgets()
                .keys()
                .cloned()
                .collect(),
        }
    }
}

/// Debug information for the integration
#[derive(Debug, Clone, Serialize)]
pub struct DebugInfo {
    pub cache_size: usize,
    pub cache_ttl_seconds: u64,
    pub registered_widgets: Vec<String>,
    pub available_widget_types: Vec<String>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_cache_operations() {
        // TODO: Add tests for cache operations
    }

    #[tokio::test]
    async fn test_fallback_config_generation() {
        // TODO: Add tests for fallback configuration
    }
}
