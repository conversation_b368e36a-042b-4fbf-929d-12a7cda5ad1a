// Widget generation prompts and templates - Updated to use new prompt system

use crate::agent::core::WidgetGenerationRequest;
use crate::errors::Result;
use crate::prompts::{ContextBuilder, PromptIntegration};
use crate::ui::widgets::WidgetConfig;
use serde_json::{json, Value};
use std::collections::HashMap;
use std::sync::Arc;

/// Prompt builder for widget generation using the new prompt system
pub struct WidgetPromptBuilder {
    prompt_integration: Arc<PromptIntegration>,
}

impl WidgetPromptBuilder {
    pub fn new() -> Result<Self> {
        let prompt_integration = Arc::new(PromptIntegration::new().map_err(|e| {
            crate::errors::AutorunError::Unknown(format!(
                "Failed to initialize prompt system: {}",
                e
            ))
        })?);

        Ok(Self { prompt_integration })
    }

    pub fn with_prompt_integration(prompt_integration: Arc<PromptIntegration>) -> Self {
        Self { prompt_integration }
    }

    /// Build generation prompt for creating new widgets using prompt templates
    pub fn build_generation_prompt(
        &self,
        request: &WidgetGenerationRequest,
        available_widgets: &HashMap<String, Value>,
    ) -> Result<String> {
        let widget_schema = available_widgets.get(&request.widget_type).ok_or_else(|| {
            crate::errors::AutorunError::NotFound(format!(
                "Unknown widget type: {}",
                request.widget_type
            ))
        })?;

        // Build context for the widget creation prompt
        let context = ContextBuilder::new()
            .variable("widget_type", request.widget_type.clone())
            .variable("context", request.context.clone())
            .variable("additional_config", request.config.clone())
            .variable("widget_schema", widget_schema.clone())
            .variable(
                "example_config",
                self.get_example_config(&request.widget_type),
            )
            .build();

        // Render the widget creation prompt
        let messages = self
            .prompt_integration
            .prompt_manager
            .render_prompt("widget_create", &context)
            .map_err(|e| {
                crate::errors::AutorunError::Unknown(format!(
                    "Failed to render widget creation prompt: {}",
                    e
                ))
            })?;

        // Extract the user message content (should be the last message)
        let prompt = messages
            .last()
            .map(|msg| msg.content.clone())
            .unwrap_or_else(|| "Failed to generate widget creation prompt".to_string());

        Ok(prompt)
    }

    /// Build prompt for incremental widget updates using prompt templates
    pub fn build_update_prompt(
        &self,
        current_config: &WidgetConfig,
        current_state: &Value,
        update_context: &str,
    ) -> Result<String> {
        // Build context for the widget update prompt
        let context = ContextBuilder::new()
            .variable("current_config", serde_json::to_value(current_config)?)
            .variable("current_state", current_state.clone())
            .variable("update_context", update_context)
            .build();

        // Render the widget update prompt
        let messages = self
            .prompt_integration
            .prompt_manager
            .render_prompt("widget_update", &context)
            .map_err(|e| {
                crate::errors::AutorunError::Unknown(format!(
                    "Failed to render widget update prompt: {}",
                    e
                ))
            })?;

        // Extract the user message content
        let prompt = messages
            .last()
            .map(|msg| msg.content.clone())
            .unwrap_or_else(|| "Failed to generate widget update prompt".to_string());

        Ok(prompt)
    }

    /// Get example config for a widget type
    fn get_example_config(&self, widget_type: &str) -> Value {
        match widget_type {
            "checkbox" => WidgetTemplates::checkbox_template(),
            "list" => WidgetTemplates::list_template(),
            "input" => WidgetTemplates::input_template(),
            "progress" => WidgetTemplates::progress_template(),
            "table" => WidgetTemplates::table_template(),
            "enhanced_list" => WidgetTemplates::enhanced_list_template(),
            _ => json!({}),
        }
    }

    /// Build prompt for widget interaction handling using template system
    pub fn build_interaction_prompt(
        &self,
        widget_type: &str,
        interaction_type: &str,
        context: &Value,
    ) -> Result<String> {
        let template = r#"Handle the following widget interaction:

**Widget Type:** {{ widget_type }}
**Interaction Type:** {{ interaction_type }}
**Interaction Context:** {{ context | tojsonpretty }}

Generate an appropriate response or state update for this interaction.
The response should be a JSON object that describes how the widget should react.

**Consider:**
1. The type of widget and its typical behavior
2. The specific interaction (click, select, input, etc.)
3. Any constraints or validation rules
4. User experience best practices for TUI applications"#;

        let prompt_context = ContextBuilder::new()
            .variable("widget_type", widget_type)
            .variable("interaction_type", interaction_type)
            .variable("context", context.clone())
            .build();

        let prompt = self
            .prompt_integration
            .prompt_manager
            .render_string(template, &prompt_context)
            .map_err(|e| {
                crate::errors::AutorunError::Unknown(format!(
                    "Failed to render interaction prompt: {}",
                    e
                ))
            })?;

        Ok(prompt)
    }

    /// Build prompt for widget validation using template system
    pub fn build_validation_prompt(&self, config: &WidgetConfig, schema: &Value) -> Result<String> {
        let template = r#"Validate the following widget configuration against its schema:

**Widget Configuration:**
```json
{{ config | tojsonpretty }}
```

**Expected Schema:**
```json
{{ schema | tojsonpretty }}
```

**Check for:**
1. Required fields presence
2. Type correctness
3. Value constraints (min/max, enum values, etc.)
4. Layout validity for terminal display
5. Any logical inconsistencies

**Respond with a JSON object:**
```json
{
    "valid": true/false,
    "errors": [
        // List of validation errors if any
    ],
    "warnings": [
        // Non-critical issues or suggestions
    ]
}
```"#;

        let prompt_context = ContextBuilder::new()
            .variable("config", serde_json::to_value(config)?)
            .variable("schema", schema.clone())
            .build();

        let prompt = self
            .prompt_integration
            .prompt_manager
            .render_string(template, &prompt_context)
            .map_err(|e| {
                crate::errors::AutorunError::Unknown(format!(
                    "Failed to render validation prompt: {}",
                    e
                ))
            })?;

        Ok(prompt)
    }

    /// Get the system prompt for widget generation
    pub fn get_system_prompt(&self) -> Result<String> {
        let context = self.prompt_integration.build_context().map_err(|e| {
            crate::errors::AutorunError::Unknown(format!("Failed to build context: {}", e))
        })?;

        let messages = self
            .prompt_integration
            .prompt_manager
            .render_prompt("widget_generation_system", &context)
            .map_err(|e| {
                crate::errors::AutorunError::Unknown(format!(
                    "Failed to render system prompt: {}",
                    e
                ))
            })?;

        // Extract system message content
        let system_prompt = messages.iter()
            .find(|msg| matches!(msg.role, crate::prompts::PromptRole::System))
            .map(|msg| msg.content.clone())
            .unwrap_or_else(|| "You are an AI assistant specialized in generating Ratatui-based TUI widget configurations.".to_string());

        Ok(system_prompt)
    }

    /// Build prompt for widget layout optimization using template system
    pub fn build_layout_optimization_prompt(
        &self,
        widgets: &[WidgetConfig],
        container_constraints: &Value,
    ) -> Result<String> {
        let template = r#"Optimize the layout for the following widgets within the given constraints:

**Widgets to Layout:**
```json
{{ widgets | tojsonpretty }}
```

**Container Constraints:**
```json
{{ container_constraints | tojsonpretty }}
```

**Generate an optimized layout configuration that:**
1. Efficiently uses available terminal space
2. Maintains visual hierarchy
3. Ensures all widgets are properly visible
4. Follows TUI best practices
5. Handles different terminal sizes gracefully

**Response format:**
```json
{
    "layout": {
        "direction": "Vertical" | "Horizontal",
        "constraints": [
            // Optimized constraints for each widget
        ]
    },
    "widget_order": [
        // Ordered list of widget IDs
    ],
    "responsive_rules": {
        // Rules for different terminal sizes
    }
}
```"#;

        let prompt_context = ContextBuilder::new()
            .variable("widgets", serde_json::to_value(widgets)?)
            .variable("container_constraints", container_constraints.clone())
            .build();

        let prompt = self
            .prompt_integration
            .prompt_manager
            .render_string(template, &prompt_context)
            .map_err(|e| {
                crate::errors::AutorunError::Unknown(format!(
                    "Failed to render layout optimization prompt: {}",
                    e
                ))
            })?;

        Ok(prompt)
    }

    /// Build prompt for error recovery using template system  
    pub fn build_error_recovery_prompt(
        &self,
        error_context: &str,
        widget_type: &str,
        last_valid_state: &Value,
    ) -> Result<String> {
        let template = r#"Help recover from a widget error:

**Error Context:** {{ error_context }}
**Widget Type:** {{ widget_type }}
**Last Valid State:**
```json
{{ last_valid_state | tojsonpretty }}
```

**Provide a recovery strategy that:**
1. Suggests a valid configuration to restore functionality
2. Identifies potential causes of the error
3. Offers alternative approaches if needed
4. Maintains as much user state as possible

**Response format:**
```json
{
    "recovery_config": {
        // Valid widget configuration
    },
    "recovery_steps": [
        // Steps to apply the recovery
    ],
    "prevention_tips": [
        // How to avoid this error in the future
    ]
}
```"#;

        let prompt_context = ContextBuilder::new()
            .variable("error_context", error_context)
            .variable("widget_type", widget_type)
            .variable("last_valid_state", last_valid_state.clone())
            .build();

        let prompt = self
            .prompt_integration
            .prompt_manager
            .render_string(template, &prompt_context)
            .map_err(|e| {
                crate::errors::AutorunError::Unknown(format!(
                    "Failed to render error recovery prompt: {}",
                    e
                ))
            })?;

        Ok(prompt)
    }
}

/// Template for common widget types
pub struct WidgetTemplates;

impl WidgetTemplates {
    /// Get template for checkbox widget
    pub fn checkbox_template() -> Value {
        json!({
            "widget_type": "checkbox",
            "id": "checkbox-template",
            "title": "Options",
            "properties": {
                "items": [
                    {"label": "Option 1", "value": "opt1", "checked": false, "enabled": true},
                    {"label": "Option 2", "value": "opt2", "checked": true, "enabled": true}
                ],
                "allow_multiple": true
            },
            "layout": {
                "constraints": [{"Min": 3}],
                "direction": "Vertical"
            }
        })
    }

    /// Get template for list widget
    pub fn list_template() -> Value {
        json!({
            "widget_type": "list",
            "id": "list-template",
            "title": "Items",
            "properties": {
                "items": ["Item 1", "Item 2", "Item 3"],
                "selected_index": 0,
                "highlight_style": "bold"
            },
            "layout": {
                "constraints": [{"Percentage": 50}],
                "direction": "Vertical"
            }
        })
    }

    /// Get template for input widget
    pub fn input_template() -> Value {
        json!({
            "widget_type": "input",
            "id": "input-template",
            "title": "User Input",
            "properties": {
                "placeholder": "Enter text...",
                "value": "",
                "max_length": 100,
                "validation_pattern": null
            },
            "layout": {
                "constraints": [{"Length": 3}],
                "direction": "Horizontal"
            }
        })
    }

    /// Get template for progress widget
    pub fn progress_template() -> Value {
        json!({
            "widget_type": "progress",
            "id": "progress-template",
            "title": "Progress",
            "properties": {
                "value": 0,
                "max": 100,
                "label": "Processing...",
                "show_percentage": true
            },
            "layout": {
                "constraints": [{"Length": 3}],
                "direction": "Horizontal"
            }
        })
    }

    /// Get template for table widget
    pub fn table_template() -> Value {
        json!({
            "widget_type": "table",
            "id": "table-template",
            "title": "Data Table",
            "properties": {
                "headers": ["Column 1", "Column 2", "Column 3"],
                "rows": [
                    ["Data 1-1", "Data 1-2", "Data 1-3"],
                    ["Data 2-1", "Data 2-2", "Data 2-3"]
                ],
                "selected_row": null,
                "column_widths": null
            },
            "layout": {
                "constraints": [{"Percentage": 60}],
                "direction": "Vertical"
            }
        })
    }

    /// Get template for enhanced list widget
    pub fn enhanced_list_template() -> Value {
        json!({
            "widget_type": "enhanced_list",
            "id": "enhanced-list-template",
            "title": "Enhanced List",
            "properties": {
                "enhanced_list_config": {
                    "id": "enhanced-list-template",
                    "title": "Enhanced List",
                    "items": [
                        {
                            "id": "item1",
                            "content": "First item",
                            "description": "This is the first item",
                            "metadata": {"category": "example"},
                            "selectable": true,
                            "checkable": true,
                            "checked": false,
                            "style": {
                                "fg_color": "green",
                                "modifiers": ["bold"]
                            }
                        },
                        {
                            "id": "item2",
                            "content": "Second item",
                            "description": "This is the second item",
                            "metadata": {"category": "example"},
                            "selectable": true,
                            "checkable": true,
                            "checked": true
                        }
                    ],
                    "multi_select": true,
                    "show_checkboxes": true,
                    "show_descriptions": true,
                    "filterable": true,
                    "searchable": true,
                    "highlight_symbol": "► ",
                    "pagination": {
                        "enabled": false,
                        "page_size": 10,
                        "show_page_info": true
                    },
                    "sort_options": {
                        "enabled": true,
                        "sort_by": "content",
                        "ascending": true
                    },
                    "hierarchical": false,
                    "lazy_load": false,
                    "auto_update": true,
                    "update_interval_ms": 5000
                }
            },
            "layout": {
                "constraints": [{"Percentage": 70}],
                "direction": "Vertical"
            }
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_prompt_builder_creation() {
        let builder = WidgetPromptBuilder::new();
        // Basic creation test
    }

    #[test]
    fn test_widget_templates() {
        let checkbox = WidgetTemplates::checkbox_template();
        assert_eq!(checkbox["widget_type"], "checkbox");

        let list = WidgetTemplates::list_template();
        assert_eq!(list["widget_type"], "list");

        let input = WidgetTemplates::input_template();
        assert_eq!(input["widget_type"], "input");
    }
}
