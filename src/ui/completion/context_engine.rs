//! Context engine for unified @ command handling
//!
//! Provides a unified interface for resolving @ commands across all context types
//! with intelligent caching, completion suggestions, and integration with context providers.

use crate::context::{
    ContextCompletion, ContextData, ContextProviderRegistry, ContextType, RegistryConfig,
};
use crate::errors::{AutorunError, Result};
use crate::tools::ExecutionContext;
use crate::ui::completion::mention_completion::{MentionSuggestion, MentionType};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use tokio::sync::RwLock;
use uuid::Uuid;

/// Context engine for unified @ command resolution
pub struct ContextEngine {
    /// Context provider registry
    registry: Arc<ContextProviderRegistry>,
    /// Context cache with TTL
    cache: RwLock<HashMap<String, CachedContext>>,
    /// Engine configuration
    config: ContextEngineConfig,
    /// Usage statistics
    stats: RwLock<ContextEngineStats>,
}

/// Context engine configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ContextEngineConfig {
    /// Enable intelligent caching
    pub enable_caching: bool,
    /// Default cache TTL in seconds
    pub default_cache_ttl: u64,
    /// Maximum cache size (number of entries)
    pub max_cache_size: usize,
    /// Enable completion suggestions
    pub enable_completions: bool,
    /// Maximum completion suggestions
    pub max_completions: usize,
    /// Enable fuzzy matching for completions
    pub enable_fuzzy_matching: bool,
    /// Minimum score for fuzzy matches (0.0-1.0)
    pub min_fuzzy_score: f64,
    /// Enable context prefetching
    pub enable_prefetching: bool,
    /// Prefetch threshold (number of recent accesses)
    pub prefetch_threshold: usize,
}

impl Default for ContextEngineConfig {
    fn default() -> Self {
        Self {
            enable_caching: true,
            default_cache_ttl: 300, // 5 minutes
            max_cache_size: 1000,
            enable_completions: true,
            max_completions: 50,
            enable_fuzzy_matching: true,
            min_fuzzy_score: 0.3,
            enable_prefetching: false,
            prefetch_threshold: 3,
        }
    }
}

/// Cached context entry
#[derive(Debug, Clone)]
struct CachedContext {
    /// The cached context data
    data: ContextData,
    /// Cache expiry time
    expires_at: SystemTime,
    /// Number of accesses
    access_count: u32,
    /// Last access time
    last_accessed: SystemTime,
}

/// Context engine statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContextEngineStats {
    /// Total context resolutions
    pub total_resolutions: usize,
    /// Successful resolutions
    pub successful_resolutions: usize,
    /// Failed resolutions
    pub failed_resolutions: usize,
    /// Cache hits
    pub cache_hits: usize,
    /// Cache misses
    pub cache_misses: usize,
    /// Total completion requests
    pub total_completions: usize,
    /// Average resolution time (ms)
    pub average_resolution_time_ms: f64,
    /// Most popular context types
    pub popular_context_types: HashMap<String, usize>,
}

impl Default for ContextEngineStats {
    fn default() -> Self {
        Self {
            total_resolutions: 0,
            successful_resolutions: 0,
            failed_resolutions: 0,
            cache_hits: 0,
            cache_misses: 0,
            total_completions: 0,
            average_resolution_time_ms: 0.0,
            popular_context_types: HashMap::new(),
        }
    }
}

impl ContextEngine {
    /// Create a new context engine
    pub async fn new(config: ContextEngineConfig) -> Result<Self> {
        let registry_config = RegistryConfig {
            enable_caching: config.enable_caching,
            cache_ttl_seconds: config.default_cache_ttl,
            max_cache_size: config.max_cache_size,
            enable_stats: true,
            provider_timeout_seconds: 30,
        };

        let registry = Arc::new(crate::context::initialize_context_system(registry_config).await?);

        Ok(Self {
            registry,
            cache: RwLock::new(HashMap::new()),
            config,
            stats: RwLock::new(ContextEngineStats::default()),
        })
    }

    /// Create with existing registry
    pub fn new_with_registry(
        registry: Arc<ContextProviderRegistry>,
        config: ContextEngineConfig,
    ) -> Self {
        Self {
            registry,
            cache: RwLock::new(HashMap::new()),
            config,
            stats: RwLock::new(ContextEngineStats::default()),
        }
    }

    /// Parse a context reference from @ command input
    pub fn parse_context_reference(&self, input: &str) -> Result<(ContextType, String)> {
        if !input.starts_with('@') {
            return Err(AutorunError::ContextError(
                "Context reference must start with @".to_string(),
            ));
        }

        // Find the end of the context type prefix
        let parts: Vec<&str> = input.splitn(2, ' ').collect();
        if parts.is_empty() {
            return Err(AutorunError::ContextError(
                "Invalid context reference format".to_string(),
            ));
        }

        let prefix = parts[0];
        let reference = if parts.len() > 1 {
            parts[1].to_string()
        } else {
            String::new()
        };

        let context_type = ContextType::from_prefix(prefix).ok_or_else(|| {
            AutorunError::ContextError(format!("Unknown context type: {}", prefix))
        })?;

        Ok((context_type, reference))
    }

    /// Resolve a context reference
    pub async fn resolve_context(
        &self,
        input: &str,
        execution_context: &ExecutionContext,
    ) -> Result<ContextData> {
        let start_time = SystemTime::now();
        let (context_type, reference) = self.parse_context_reference(input)?;

        // Update statistics
        {
            let mut stats = self.stats.write().await;
            stats.total_resolutions += 1;
            *stats
                .popular_context_types
                .entry(context_type.prefix().to_string())
                .or_insert(0) += 1;
        }

        // Check cache first
        if self.config.enable_caching {
            let cache_key = format!("{}:{}", context_type.prefix(), reference);
            let cache = self.cache.read().await;

            if let Some(cached) = cache.get(&cache_key) {
                if SystemTime::now() < cached.expires_at {
                    // Update cache access stats
                    {
                        let mut stats = self.stats.write().await;
                        stats.cache_hits += 1;
                    }

                    // Update access count (we'd need to change this to avoid the cache read/write issue)
                    return Ok(cached.data.clone());
                }
            }

            // Cache miss
            {
                let mut stats = self.stats.write().await;
                stats.cache_misses += 1;
            }
        }

        // Resolve through registry
        let result = self
            .registry
            .resolve_context(&reference, context_type.clone(), execution_context)
            .await;

        match result {
            Ok(context_data) => {
                // Cache the result
                if self.config.enable_caching {
                    let cache_key = format!("{}:{}", context_type.prefix(), reference);
                    let expires_at =
                        SystemTime::now() + Duration::from_secs(self.config.default_cache_ttl);

                    let cached_entry = CachedContext {
                        data: context_data.clone(),
                        expires_at,
                        access_count: 1,
                        last_accessed: SystemTime::now(),
                    };

                    let mut cache = self.cache.write().await;

                    // Implement LRU eviction if cache is full
                    if cache.len() >= self.config.max_cache_size {
                        // Find least recently used entry
                        if let Some(lru_key) = cache
                            .iter()
                            .min_by_key(|(_, entry)| entry.last_accessed)
                            .map(|(key, _)| key.clone())
                        {
                            cache.remove(&lru_key);
                        }
                    }

                    cache.insert(cache_key, cached_entry);
                }

                // Update success statistics
                {
                    let mut stats = self.stats.write().await;
                    stats.successful_resolutions += 1;

                    // Update average resolution time
                    if let Ok(elapsed) = start_time.elapsed() {
                        let elapsed_ms = elapsed.as_millis() as f64;
                        stats.average_resolution_time_ms = (stats.average_resolution_time_ms
                            * (stats.successful_resolutions - 1) as f64
                            + elapsed_ms)
                            / stats.successful_resolutions as f64;
                    }
                }

                Ok(context_data)
            }
            Err(e) => {
                // Update failure statistics
                {
                    let mut stats = self.stats.write().await;
                    stats.failed_resolutions += 1;
                }
                Err(e)
            }
        }
    }

    /// Get completion suggestions for partial context input
    pub async fn get_completions(
        &self,
        partial_input: &str,
        execution_context: &ExecutionContext,
        limit: Option<usize>,
    ) -> Result<Vec<MentionSuggestion>> {
        if !self.config.enable_completions {
            return Ok(Vec::new());
        }

        let limit = limit.unwrap_or(self.config.max_completions);

        // Update completion statistics
        {
            let mut stats = self.stats.write().await;
            stats.total_completions += 1;
        }

        // If input doesn't start with @, suggest all context types
        if !partial_input.starts_with('@') {
            return Ok(self.get_context_type_suggestions(limit));
        }

        // Parse partial input
        let parts: Vec<&str> = partial_input.splitn(2, ' ').collect();
        let prefix_part = parts[0];
        let reference_part = if parts.len() > 1 { parts[1] } else { "" };

        // If we don't have a complete prefix yet, suggest matching prefixes
        if !reference_part.is_empty() || partial_input.ends_with(' ') {
            // We have a complete prefix, get completions for the reference part
            if let Some(context_type) = ContextType::from_prefix(prefix_part) {
                return self
                    .get_reference_completions(
                        context_type,
                        reference_part,
                        execution_context,
                        limit,
                    )
                    .await;
            }
        }

        // Suggest context type prefixes
        Ok(self.get_prefix_suggestions(prefix_part, limit))
    }

    /// Get suggestions for context type prefixes
    fn get_context_type_suggestions(&self, limit: usize) -> Vec<MentionSuggestion> {
        let context_types = [
            ContextType::File,
            ContextType::Folder,
            ContextType::Codebase,
            ContextType::Mcp,
            ContextType::Symbol,
            ContextType::Web,
            ContextType::Session,
            ContextType::Workspace,
            ContextType::Git,
            ContextType::Memory,
        ];

        context_types
            .iter()
            .take(limit)
            .map(|ct| MentionSuggestion {
                mention_type: self.context_type_to_mention_type(ct),
                name: ct.prefix().to_string(),
                display_name: format!("{} {}", ct.icon(), ct.prefix()),
                description: format!("Reference a {} context", ct.prefix()),
                metadata: HashMap::new(),
                score: 1.0,
            })
            .collect()
    }

    /// Get suggestions for partial context type prefixes
    fn get_prefix_suggestions(&self, partial_prefix: &str, limit: usize) -> Vec<MentionSuggestion> {
        let context_types = [
            ContextType::File,
            ContextType::Folder,
            ContextType::Codebase,
            ContextType::Mcp,
            ContextType::Symbol,
            ContextType::Web,
            ContextType::Session,
            ContextType::Workspace,
            ContextType::Git,
            ContextType::Memory,
        ];

        let mut suggestions = Vec::new();

        for context_type in &context_types {
            let prefix = context_type.prefix();
            if prefix.starts_with(partial_prefix) {
                suggestions.push(MentionSuggestion {
                    mention_type: self.context_type_to_mention_type(context_type),
                    name: prefix.to_string(),
                    display_name: format!("{} {}", context_type.icon(), prefix),
                    description: format!("Reference a {} context", prefix),
                    metadata: HashMap::new(),
                    score: if prefix == partial_prefix { 1.0 } else { 0.8 },
                });
            }
        }

        // Sort by score and limit
        suggestions.sort_by(|a, b| {
            b.score
                .partial_cmp(&a.score)
                .unwrap_or(std::cmp::Ordering::Equal)
        });
        suggestions.truncate(limit);
        suggestions
    }

    /// Get completions for context references
    async fn get_reference_completions(
        &self,
        context_type: ContextType,
        partial_reference: &str,
        execution_context: &ExecutionContext,
        limit: usize,
    ) -> Result<Vec<MentionSuggestion>> {
        let completions = self
            .registry
            .get_completions(
                partial_reference,
                context_type.clone(),
                execution_context,
                limit,
            )
            .await?;

        let suggestions = completions
            .into_iter()
            .map(|completion| MentionSuggestion {
                mention_type: self.context_type_to_mention_type(&completion.context_type),
                name: completion.text,
                display_name: completion.display_text,
                description: completion.description,
                metadata: completion.metadata,
                score: completion.score,
            })
            .collect();

        Ok(suggestions)
    }

    /// Convert ContextType to MentionType
    fn context_type_to_mention_type(&self, context_type: &ContextType) -> MentionType {
        match context_type {
            ContextType::File => MentionType::File,
            ContextType::Folder => MentionType::Folder,
            ContextType::Codebase => MentionType::Codebase,
            ContextType::Mcp => MentionType::Mcp,
            ContextType::Symbol => MentionType::Symbol,
            ContextType::Web => MentionType::Web,
            ContextType::Session => MentionType::Session,
            ContextType::Workspace => MentionType::Workspace,
            ContextType::Git => MentionType::Git,
            ContextType::Memory => MentionType::Memory,
        }
    }

    /// Clear the context cache
    pub async fn clear_cache(&self) {
        let mut cache = self.cache.write().await;
        cache.clear();

        // Also clear registry cache
        self.registry.clear_cache().await;
    }

    /// Get engine statistics
    pub async fn get_stats(&self) -> ContextEngineStats {
        let stats = self.stats.read().await;
        stats.clone()
    }

    /// Get cache statistics
    pub async fn get_cache_stats(&self) -> CacheStats {
        let cache = self.cache.read().await;
        let total_entries = cache.len();
        let expired_entries = cache
            .values()
            .filter(|entry| SystemTime::now() >= entry.expires_at)
            .count();

        CacheStats {
            total_entries,
            expired_entries,
            active_entries: total_entries - expired_entries,
            cache_hit_ratio: {
                let stats = self.stats.read().await;
                if stats.cache_hits + stats.cache_misses > 0 {
                    stats.cache_hits as f64 / (stats.cache_hits + stats.cache_misses) as f64
                } else {
                    0.0
                }
            },
        }
    }

    /// Cleanup expired cache entries
    pub async fn cleanup_cache(&self) -> usize {
        let mut cache = self.cache.write().await;
        let now = SystemTime::now();
        let initial_size = cache.len();

        cache.retain(|_key, entry| now < entry.expires_at);

        initial_size - cache.len()
    }

    /// Prefetch popular contexts
    pub async fn prefetch_popular_contexts(
        &self,
        execution_context: &ExecutionContext,
    ) -> Result<usize> {
        if !self.config.enable_prefetching {
            return Ok(0);
        }

        let popular_contexts = {
            let stats = self.stats.read().await;
            stats.popular_context_types.clone()
        };

        let mut prefetched = 0;
        for (context_prefix, usage_count) in popular_contexts {
            if usage_count >= self.config.prefetch_threshold {
                // This is a simplified prefetch - in reality we'd need more context
                // about what specific references to prefetch
                prefetched += 1;
            }
        }

        Ok(prefetched)
    }
}

/// Cache statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheStats {
    pub total_entries: usize,
    pub expired_entries: usize,
    pub active_entries: usize,
    pub cache_hit_ratio: f64,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tools::PermissionLevel;
    use std::collections::HashMap;

    fn create_test_execution_context() -> ExecutionContext {
        ExecutionContext {
            working_directory: std::env::current_dir().unwrap_or_default(),
            permissions: PermissionLevel::Full,
            session_data: HashMap::new(),
            environment: HashMap::new(),
            tool_registry: None,
        }
    }

    #[tokio::test]
    async fn test_context_reference_parsing() {
        let engine = ContextEngine::new(ContextEngineConfig::default())
            .await
            .unwrap();

        let (context_type, reference) =
            engine.parse_context_reference("@file src/main.rs").unwrap();
        assert_eq!(context_type, ContextType::File);
        assert_eq!(reference, "src/main.rs");

        let (context_type, reference) = engine.parse_context_reference("@folder").unwrap();
        assert_eq!(context_type, ContextType::Folder);
        assert_eq!(reference, "");
    }

    #[tokio::test]
    async fn test_context_type_suggestions() {
        let engine = ContextEngine::new(ContextEngineConfig::default())
            .await
            .unwrap();
        let suggestions = engine.get_context_type_suggestions(5);

        assert!(suggestions.len() <= 5);
        assert!(suggestions.iter().any(|s| s.name == "@file"));
        assert!(suggestions.iter().any(|s| s.name == "@folder"));
    }

    #[tokio::test]
    async fn test_prefix_suggestions() {
        let engine = ContextEngine::new(ContextEngineConfig::default())
            .await
            .unwrap();
        let suggestions = engine.get_prefix_suggestions("@f", 10);

        assert!(suggestions.iter().any(|s| s.name == "@file"));
        assert!(suggestions.iter().any(|s| s.name == "@folder"));
        assert!(!suggestions.iter().any(|s| s.name == "@memory"));
    }

    #[tokio::test]
    async fn test_cache_cleanup() {
        let engine = ContextEngine::new(ContextEngineConfig::default())
            .await
            .unwrap();
        let cleaned = engine.cleanup_cache().await;
        assert_eq!(cleaned, 0); // No entries to clean initially
    }
}
