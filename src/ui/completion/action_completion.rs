//! Action completion provider for / commands
//!
//! Provides intelligent completion suggestions for action commands including
//! memory operations, MCP tools, and custom commands.

use crate::commands::parser::{CommandCompletion, CompletionType};
use crate::storage::memory_store::MemoryStore;
use crate::mcp::client::McpClient;
use crate::tools::ExecutionContext;

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

/// Action completion provider
pub struct ActionCompletionProvider {
    memory_store: Arc<RwLock<MemoryStore>>,
    mcp_client: Arc<RwLock<McpClient>>,
    cache: Arc<RwLock<CompletionCache>>,
}

/// Completion cache for performance
#[derive(Default)]
struct CompletionCache {
    memory_completions: HashMap<String, Vec<CommandCompletion>>,
    mcp_completions: HashMap<String, Vec<CommandCompletion>>,
    last_update: Option<std::time::SystemTime>,
}

impl ActionCompletionProvider {
    /// Helper function to create a command completion
    fn create_completion(
        text: &str,
        insert_text: &str,
        description: &str,
        completion_type: CompletionType,
        priority: u8,
    ) -> CommandCompletion {
        CommandCompletion {
            text: text.to_string(),
            insert_text: insert_text.to_string(),
            description: Some(description.to_string()),
            completion_type,
            priority,
            metadata: std::collections::HashMap::new(),
        }
    }

    /// Create a new action completion provider
    pub fn new(
        memory_store: Arc<RwLock<MemoryStore>>,
        mcp_client: Arc<RwLock<McpClient>>,
    ) -> Self {
        Self {
            memory_store,
            mcp_client,
            cache: Arc::new(RwLock::new(CompletionCache::default())),
        }
    }

    /// Get completions for action commands
    pub async fn get_completions(
        &self,
        partial_command: &str,
        _execution_context: &ExecutionContext,
    ) -> Vec<CommandCompletion> {
        let mut completions = Vec::new();

        // Remove leading slash if present
        let command = partial_command.trim_start_matches('/');
        
        if command.is_empty() {
            // Return top-level action commands
            completions.extend(self.get_root_action_completions().await);
        } else if command.starts_with("memory") {
            completions.extend(self.get_memory_completions(command).await);
        } else {
            // Partial command matching
            completions.extend(self.get_partial_completions(command).await);
        }

        completions
    }

    /// Get root-level action command completions
    async fn get_root_action_completions(&self) -> Vec<CommandCompletion> {
        vec![
            Self::create_completion(
                "memory",
                "/memory",
                "Memory management operations",
                CompletionType::Command,
                100,
            ),
            Self::create_completion(
                "mcp",
                "/mcp",
                "MCP tool operations",
                CompletionType::Command,
                90,
            ),
        ]
    }

    /// Get memory command completions
    async fn get_memory_completions(&self, command: &str) -> Vec<CommandCompletion> {
        let mut completions = Vec::new();

        // Extract subcommand if present
        let parts: Vec<&str> = command.split_whitespace().collect();
        
        if parts.len() == 1 {
            // Memory subcommands
            let subcommands = [
                ("create", "Create a new memory"),
                ("search", "Search existing memories"),
                ("list", "List memories by tag or recent"),
                ("delete", "Delete a memory"),
                ("update", "Update an existing memory"),
            ];

            for (cmd, desc) in subcommands {
                completions.push(Self::create_completion(
                    &format!("memory {}", cmd),
                    &format!("/memory {}", cmd),
                    desc,
                    CompletionType::Command,
                    80,
                ));
            }
        }

        completions
    }

    /// Get partial command completions
    async fn get_partial_completions(&self, partial: &str) -> Vec<CommandCompletion> {
        let mut completions = Vec::new();

        // Memory command matching
        if "memory".starts_with(partial) {
            completions.push(Self::create_completion(
                "memory",
                "/memory",
                "Memory management operations",
                CompletionType::Command,
                100,
            ));
        }

        // MCP command matching
        if "mcp".starts_with(partial) {
            completions.push(Self::create_completion(
                "mcp",
                "/mcp",
                "MCP tool operations",
                CompletionType::Command,
                90,
            ));
        }

        completions
    }

    /// Clear completion cache
    pub async fn clear_cache(&self) {
        let mut cache = self.cache.write().await;
        cache.memory_completions.clear();
        cache.mcp_completions.clear();
        cache.last_update = None;
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::storage::StorageConfig;
    use crate::tools::{ExecutionContext, PermissionRule};
    use tempfile::TempDir;
    use std::collections::HashMap;

    async fn create_test_provider() -> ActionCompletionProvider {
        let temp_dir = TempDir::new().unwrap();
        let mut config = StorageConfig::default();
        config.storage_dir = temp_dir.path().to_path_buf();
        
        let memory_store = Arc::new(RwLock::new(MemoryStore::new(config).await.unwrap()));
        let mcp_client = Arc::new(RwLock::new(McpClient::new().unwrap()));
        
        ActionCompletionProvider::new(memory_store, mcp_client)
    }

    #[tokio::test]
    async fn test_root_completions() {
        let provider = create_test_provider().await;
        let context = ExecutionContext {
            working_directory: std::env::current_dir().unwrap(),
            permission_rules: vec![PermissionRule::AllowAll],
            session_id: "test".to_string(),
            capabilities: HashMap::new(),
        };

        let completions = provider.get_completions("", &context).await;
        assert!(!completions.is_empty());
        assert!(completions.iter().any(|c| c.text == "memory"));
    }

    #[tokio::test]
    async fn test_memory_completions() {
        let provider = create_test_provider().await;
        let context = ExecutionContext {
            working_directory: std::env::current_dir().unwrap(),
            permission_rules: vec![PermissionRule::AllowAll],
            session_id: "test".to_string(),
            capabilities: HashMap::new(),
        };

        let completions = provider.get_completions("memory", &context).await;
        assert!(!completions.is_empty());
        assert!(completions.iter().any(|c| c.text.contains("create")));
        assert!(completions.iter().any(|c| c.text.contains("search")));
    }
}