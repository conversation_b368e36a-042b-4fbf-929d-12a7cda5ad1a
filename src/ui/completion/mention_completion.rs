use std::collections::HashMap;
use std::sync::Arc;

use dashmap::DashMap;
use fuzzy_matcher::skim::SkimMatcherV2;
use fuzzy_matcher::FuzzyMatcher;
use serde::{Deserialize, Serialize};

use crate::errors::Result;
use crate::tools::{ExecutionContext, ToolRegistry};

/// Types of entities that can be mentioned
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum MentionType {
    File,
    Folder,
    Codebase,
    Mcp,
    Symbol,
    Web,
    Session,
    Workspace,
    Git,
    Memory,
    Tool,
    Concept,
    Function,
    Variable,
    Type,
}

impl MentionType {
    fn prefix(&self) -> &'static str {
        match self {
            MentionType::File => "@file",
            MentionType::Folder => "@folder",
            MentionType::Codebase => "@codebase",
            MentionType::Mcp => "@mcp",
            MentionType::Symbol => "@symbol",
            MentionType::Web => "@web",
            MentionType::Session => "@session",
            MentionType::Workspace => "@workspace",
            MentionType::Git => "@git",
            MentionType::Memory => "@memory",
            MentionType::Tool => "@tool",
            MentionType::Concept => "@concept",
            MentionType::Function => "@function",
            MentionType::Variable => "@var",
            MentionType::Type => "@type",
        }
    }

    pub fn icon(&self) -> &'static str {
        match self {
            MentionType::File => "📄",
            MentionType::Folder => "📁",
            MentionType::Codebase => "📚",
            MentionType::Mcp => "🔧",
            MentionType::Symbol => "🔍",
            MentionType::Web => "🌐",
            MentionType::Session => "💬",
            MentionType::Workspace => "🏢",
            MentionType::Git => "🌲",
            MentionType::Memory => "🧠",
            MentionType::Tool => "⚙️",
            MentionType::Concept => "💡",
            MentionType::Function => "🔵",
            MentionType::Variable => "📦",
            MentionType::Type => "🏷️",
        }
    }
}

/// A suggestion for mention completion
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MentionSuggestion {
    pub mention_type: MentionType,
    pub name: String,
    pub display_name: String,
    pub description: String,
    pub metadata: HashMap<String, String>,
    pub score: f64,
}

/// Cache for mention suggestions to improve performance
pub struct MentionCache {
    file_cache: DashMap<String, Vec<MentionSuggestion>>,
    tool_cache: Vec<MentionSuggestion>,
    last_file_scan: std::time::Instant,
    cache_duration: std::time::Duration,
}

impl MentionCache {
    fn new() -> Self {
        Self {
            file_cache: DashMap::new(),
            tool_cache: Vec::new(),
            last_file_scan: std::time::Instant::now(),
            cache_duration: std::time::Duration::from_secs(300), // 5 minutes
        }
    }

    fn is_expired(&self) -> bool {
        self.last_file_scan.elapsed() > self.cache_duration
    }

    fn invalidate(&mut self) {
        self.file_cache.clear();
        self.tool_cache.clear();
        self.last_file_scan = std::time::Instant::now();
    }
}

/// The mention completion engine
pub struct MentionCompletionEngine {
    cache: MentionCache,
    matcher: SkimMatcherV2,
    tool_registry: Arc<ToolRegistry>,
}

impl MentionCompletionEngine {
    pub fn new(tool_registry: Arc<ToolRegistry>) -> Self {
        Self {
            cache: MentionCache::new(),
            matcher: SkimMatcherV2::default(),
            tool_registry,
        }
    }

    /// Detect if the current input contains a mention pattern
    pub fn detect_mention(&self, input: &str, cursor_pos: usize) -> Option<(usize, String)> {
        // Find the @ character before the cursor
        let before_cursor = &input[..cursor_pos];

        // Look for @ followed by optional type prefix and partial text
        if let Some(at_pos) = before_cursor.rfind('@') {
            // Check if @ is not escaped and is a word boundary
            if at_pos == 0
                || before_cursor
                    .chars()
                    .nth(at_pos - 1)
                    .map_or(true, |c| c.is_whitespace())
            {
                let mention_text = &input[at_pos + 1..cursor_pos];
                return Some((at_pos, mention_text.to_string()));
            }
        }

        None
    }

    /// Get suggestions based on the mention text
    pub async fn get_suggestions(
        &mut self,
        mention_text: &str,
        context: &ExecutionContext,
        limit: usize,
    ) -> Result<Vec<MentionSuggestion>> {
        let mut all_suggestions = Vec::new();

        // Parse mention type from text (e.g., "file:main" or just "main")
        let (mention_type, query) = self.parse_mention_text(mention_text);

        // Collect suggestions based on type
        match mention_type {
            Some(MentionType::File) => {
                all_suggestions.extend(self.get_file_suggestions(query, context).await?);
            }
            Some(MentionType::Tool) => {
                all_suggestions.extend(self.get_tool_suggestions(query)?);
            }
            Some(MentionType::Concept) => {
                all_suggestions.extend(self.get_concept_suggestions(query, context).await?);
            }
            Some(MentionType::Function) => {
                all_suggestions.extend(self.get_function_suggestions(query, context).await?);
            }
            Some(MentionType::Variable) => {
                all_suggestions.extend(self.get_variable_suggestions(query, context).await?);
            }
            Some(MentionType::Type) => {
                all_suggestions.extend(self.get_type_suggestions(query, context).await?);
            }
            None => {
                // No specific type, search all categories
                all_suggestions.extend(self.get_file_suggestions(query, context).await?);
                all_suggestions.extend(self.get_tool_suggestions(query)?);
                all_suggestions.extend(self.get_concept_suggestions(query, context).await?);
                all_suggestions.extend(self.get_function_suggestions(query, context).await?);
            }
        }

        // Sort by score and limit results
        all_suggestions.sort_by(|a, b| {
            b.score
                .partial_cmp(&a.score)
                .unwrap_or(std::cmp::Ordering::Equal)
        });
        all_suggestions.truncate(limit);

        Ok(all_suggestions)
    }

    /// Parse mention text to extract type and query
    fn parse_mention_text<'a>(&self, text: &'a str) -> (Option<MentionType>, &'a str) {
        if text.starts_with("file:") {
            (Some(MentionType::File), &text[5..])
        } else if text.starts_with("tool:") {
            (Some(MentionType::Tool), &text[5..])
        } else if text.starts_with("concept:") {
            (Some(MentionType::Concept), &text[8..])
        } else if text.starts_with("function:") || text.starts_with("func:") {
            let prefix_len = if text.starts_with("function:") { 9 } else { 5 };
            (Some(MentionType::Function), &text[prefix_len..])
        } else if text.starts_with("var:") {
            (Some(MentionType::Variable), &text[4..])
        } else if text.starts_with("type:") {
            (Some(MentionType::Type), &text[5..])
        } else {
            (None, text)
        }
    }

    /// Get file suggestions
    async fn get_file_suggestions(
        &mut self,
        query: &str,
        context: &ExecutionContext,
    ) -> Result<Vec<MentionSuggestion>> {
        // Check cache
        if self.cache.is_expired() {
            self.cache.invalidate();
        }

        let cache_key = format!("{:?}:{}", context.working_directory, query);

        if let Some(cached) = self.cache.file_cache.get(&cache_key) {
            return Ok(cached.clone());
        }

        let mut suggestions = Vec::new();

        // Use glob to find files
        let patterns = vec![
            "**/*.rs",
            "**/*.toml",
            "**/*.md",
            "**/*.json",
            "**/*.yaml",
            "**/*.yml",
            "**/*.txt",
        ];

        for pattern in patterns {
            if let Ok(entries) = glob::glob(&format!(
                "{}/{}",
                context.working_directory.display(),
                pattern
            )) {
                for entry in entries.flatten() {
                    if let Ok(relative_path) = entry.strip_prefix(&context.working_directory) {
                        let path_str = relative_path.to_string_lossy();

                        // Score based on fuzzy match
                        if let Some(score) = self.matcher.fuzzy_match(&path_str, query) {
                            let file_name = entry.file_name().unwrap_or_default().to_string_lossy();
                            let mut metadata = HashMap::new();
                            metadata.insert("path".to_string(), path_str.to_string());

                            suggestions.push(MentionSuggestion {
                                mention_type: MentionType::File,
                                name: path_str.to_string(),
                                display_name: file_name.to_string(),
                                description: format!("File: {}", path_str),
                                metadata,
                                score: score as f64,
                            });
                        }
                    }
                }
            }
        }

        // Cache results
        self.cache.file_cache.insert(cache_key, suggestions.clone());

        Ok(suggestions)
    }

    /// Get tool suggestions
    fn get_tool_suggestions(&mut self, query: &str) -> Result<Vec<MentionSuggestion>> {
        if self.cache.tool_cache.is_empty() {
            // Build tool cache
            let tool_names = self.tool_registry.list_tools();

            for name in tool_names {
                let mut metadata = HashMap::new();

                // Try to get additional tool info
                if let Some((tool_name, description, schema)) =
                    self.tool_registry.get_tool_info(&name)
                {
                    metadata.insert(
                        "schema".to_string(),
                        serde_json::to_string(&schema).unwrap_or_default(),
                    );

                    self.cache.tool_cache.push(MentionSuggestion {
                        mention_type: MentionType::Tool,
                        name: tool_name.clone(),
                        display_name: tool_name.clone(),
                        description,
                        metadata,
                        score: 0.0,
                    });
                } else {
                    // Fallback with minimal info
                    self.cache.tool_cache.push(MentionSuggestion {
                        mention_type: MentionType::Tool,
                        name: name.clone(),
                        display_name: name.clone(),
                        description: format!("Tool: {}", name),
                        metadata,
                        score: 0.0,
                    });
                }
            }
        }

        // Filter and score based on query
        let mut suggestions = Vec::new();
        for tool in &self.cache.tool_cache {
            if let Some(score) = self.matcher.fuzzy_match(&tool.name, query) {
                let mut suggestion = tool.clone();
                suggestion.score = score as f64;
                suggestions.push(suggestion);
            }
        }

        Ok(suggestions)
    }

    /// Get concept suggestions (placeholder - could be extended with project analysis)
    async fn get_concept_suggestions(
        &self,
        query: &str,
        _context: &ExecutionContext,
    ) -> Result<Vec<MentionSuggestion>> {
        // This could be extended to analyze project documentation, comments, etc.
        let concepts = vec![
            ("architecture", "System architecture and design patterns"),
            ("testing", "Testing strategies and frameworks"),
            (
                "error_handling",
                "Error handling patterns and best practices",
            ),
            ("async", "Asynchronous programming concepts"),
            ("performance", "Performance optimization strategies"),
        ];

        let mut suggestions = Vec::new();
        for (name, description) in concepts {
            if let Some(score) = self.matcher.fuzzy_match(name, query) {
                suggestions.push(MentionSuggestion {
                    mention_type: MentionType::Concept,
                    name: name.to_string(),
                    display_name: name.to_string(),
                    description: description.to_string(),
                    metadata: HashMap::new(),
                    score: score as f64,
                });
            }
        }

        Ok(suggestions)
    }

    /// Get function suggestions (placeholder - could use tree-sitter or LSP)
    async fn get_function_suggestions(
        &self,
        query: &str,
        context: &ExecutionContext,
    ) -> Result<Vec<MentionSuggestion>> {
        // This is a simplified implementation
        // In a real implementation, you'd use tree-sitter or LSP to parse functions
        let mut suggestions = Vec::new();

        // For now, just search for function-like patterns in Rust files
        let pattern = format!("{}/src/**/*.rs", context.working_directory.display());

        if let Ok(entries) = glob::glob(&pattern) {
            for entry in entries.flatten() {
                if let Ok(content) = std::fs::read_to_string(&entry) {
                    // Simple regex to find function definitions
                    let func_regex =
                        regex::Regex::new(r"(?m)^\s*(pub\s+)?(async\s+)?fn\s+(\w+)").unwrap();

                    for cap in func_regex.captures_iter(&content) {
                        if let Some(func_name) = cap.get(3) {
                            let name = func_name.as_str();

                            if let Some(score) = self.matcher.fuzzy_match(name, query) {
                                let mut metadata = HashMap::new();
                                metadata.insert(
                                    "file".to_string(),
                                    entry.to_string_lossy().to_string(),
                                );

                                suggestions.push(MentionSuggestion {
                                    mention_type: MentionType::Function,
                                    name: name.to_string(),
                                    display_name: format!("{}()", name),
                                    description: format!(
                                        "Function in {}",
                                        entry.file_name().unwrap_or_default().to_string_lossy()
                                    ),
                                    metadata,
                                    score: score as f64,
                                });
                            }
                        }
                    }
                }
            }
        }

        Ok(suggestions)
    }

    /// Get variable suggestions (placeholder)
    async fn get_variable_suggestions(
        &self,
        _query: &str,
        _context: &ExecutionContext,
    ) -> Result<Vec<MentionSuggestion>> {
        // This would require more sophisticated parsing
        Ok(Vec::new())
    }

    /// Get type suggestions (placeholder)
    async fn get_type_suggestions(
        &self,
        _query: &str,
        _context: &ExecutionContext,
    ) -> Result<Vec<MentionSuggestion>> {
        // This would require type analysis
        Ok(Vec::new())
    }

    /// Complete a mention with the selected suggestion
    pub fn complete_mention(
        &self,
        input: &str,
        cursor_pos: usize,
        suggestion: &MentionSuggestion,
    ) -> (String, usize) {
        if let Some((at_pos, _)) = self.detect_mention(input, cursor_pos) {
            let before_mention = &input[..at_pos];
            let after_cursor = &input[cursor_pos..];

            // Format the completion based on type
            let completion = match suggestion.mention_type {
                MentionType::File => format!("@file:{}", suggestion.name),
                MentionType::Tool => format!("@tool:{}", suggestion.name),
                MentionType::Function => format!("@function:{}", suggestion.name),
                _ => format!("@{}", suggestion.name),
            };

            let new_input = format!("{}{} {}", before_mention, completion, after_cursor);
            let new_cursor = before_mention.len() + completion.len() + 1;

            (new_input, new_cursor)
        } else {
            (input.to_string(), cursor_pos)
        }
    }
}

/// Widget state for the mention popup
#[derive(Debug, Clone)]
pub struct MentionPopupState {
    pub suggestions: Vec<MentionSuggestion>,
    pub selected_index: usize,
    pub visible: bool,
}

impl MentionPopupState {
    pub fn new() -> Self {
        Self {
            suggestions: Vec::new(),
            selected_index: 0,
            visible: false,
        }
    }

    pub fn show(&mut self, suggestions: Vec<MentionSuggestion>) {
        self.suggestions = suggestions;
        self.selected_index = 0;
        self.visible = true;
    }

    pub fn hide(&mut self) {
        self.visible = false;
        self.suggestions.clear();
    }

    pub fn select_next(&mut self) {
        if !self.suggestions.is_empty() {
            self.selected_index = (self.selected_index + 1) % self.suggestions.len();
        }
    }

    pub fn select_previous(&mut self) {
        if !self.suggestions.is_empty() {
            self.selected_index = if self.selected_index == 0 {
                self.suggestions.len() - 1
            } else {
                self.selected_index - 1
            };
        }
    }

    pub fn get_selected(&self) -> Option<&MentionSuggestion> {
        self.suggestions.get(self.selected_index)
    }
}
