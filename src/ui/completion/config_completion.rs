use crate::errors::{AutorunError, Result};
use crate::ui::enhanced::state::UiMode;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tracing::{debug, warn};

/// Configuration command completion system
/// Provides dynamic completion for setting names, values, themes, providers, and sessions
#[derive(Debug)]
pub struct ConfigCompletionProvider {
    /// Setting definitions for validation and completion
    setting_registry: SettingRegistry,
    
    /// Theme registry for theme completion
    theme_registry: ThemeRegistry,
    
    /// Provider registry for provider completion
    provider_registry: ProviderRegistry,
    
    /// Session registry for session completion
    session_registry: SessionRegistry,
    
    /// Completion cache for performance
    completion_cache: CompletionCache,
    
    /// Completion analytics
    analytics: CompletionAnalytics,
}

/// Setting definition for completion and validation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SettingDefinition {
    /// Setting key
    pub key: String,
    
    /// Setting description
    pub description: String,
    
    /// Setting type
    pub setting_type: SettingType,
    
    /// Possible values (for enum types)
    pub possible_values: Option<Vec<String>>,
    
    /// Value constraints
    pub constraints: Option<ValueConstraints>,
    
    /// Default value
    pub default_value: Option<String>,
    
    /// Whether setting is deprecated
    pub deprecated: bool,
    
    /// Setting category
    pub category: SettingCategory,
    
    /// Examples
    pub examples: Vec<String>,
}

/// Setting type for validation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SettingType {
    String,
    Integer,
    Float,
    Boolean,
    Enum,
    Path,
    Url,
    Duration,
    Size,
}

/// Value constraints
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValueConstraints {
    /// Minimum value (for numeric types)
    pub min: Option<f64>,
    
    /// Maximum value (for numeric types)
    pub max: Option<f64>,
    
    /// Pattern validation (for strings)
    pub pattern: Option<String>,
    
    /// Required format
    pub format: Option<String>,
}

/// Setting category for organization
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SettingCategory {
    Llm,
    Ui,
    Session,
    Tools,
    Performance,
    Security,
    Debug,
    Experimental,
}

/// Completion suggestion
#[derive(Debug, Clone)]
pub struct CompletionSuggestion {
    /// Completion text
    pub text: String,
    
    /// Display text (may include formatting)
    pub display: String,
    
    /// Completion type
    pub completion_type: CompletionType,
    
    /// Relevance score (0.0 - 1.0)
    pub relevance: f32,
    
    /// Additional context information
    pub context: Option<String>,
    
    /// Documentation snippet
    pub documentation: Option<String>,
    
    /// Whether this is a preview suggestion
    pub preview: bool,
}

/// Type of completion
#[derive(Debug, Clone)]
pub enum CompletionType {
    Command,
    SettingKey,
    SettingValue,
    ThemeName,
    ProviderName,
    SessionName,
    FilePath,
    Parameter,
}

/// Completion context for better suggestions
#[derive(Debug, Clone)]
pub struct CompletionContext {
    /// Current input text
    pub input: String,
    
    /// Cursor position
    pub cursor_position: usize,
    
    /// Current UI mode
    pub mode: UiMode,
    
    /// Command being completed
    pub command_context: Option<CommandContext>,
    
    /// Recently used commands
    pub recent_commands: Vec<String>,
    
    /// Available features
    pub available_features: Vec<String>,
}

/// Command-specific completion context
#[derive(Debug, Clone)]
pub struct CommandContext {
    /// Command name
    pub command: String,
    
    /// Parsed arguments so far
    pub arguments: Vec<String>,
    
    /// Current argument being completed
    pub current_arg_index: usize,
    
    /// Expected argument type
    pub expected_type: Option<ArgumentType>,
}

/// Expected argument type
#[derive(Debug, Clone)]
pub enum ArgumentType {
    SettingKey,
    SettingValue(String), // Setting key for value completion
    ThemeName,
    ProviderName,
    SessionName,
    FilePath,
    FreeText,
}

impl ConfigCompletionProvider {
    /// Create a new configuration completion provider
    pub fn new() -> Self {
        Self {
            setting_registry: SettingRegistry::new(),
            theme_registry: ThemeRegistry::new(),
            provider_registry: ProviderRegistry::new(),
            session_registry: SessionRegistry::new(),
            completion_cache: CompletionCache::new(),
            analytics: CompletionAnalytics::new(),
        }
    }
    
    /// Get completions for configuration commands
    pub async fn get_completions(&self, context: &CompletionContext) -> Result<Vec<CompletionSuggestion>> {
        let start_time = std::time::Instant::now();
        
        // Check cache first
        let cache_key = self.generate_cache_key(context);
        if let Some(cached) = self.completion_cache.get(&cache_key).await {
            self.analytics.record_cache_hit(&cache_key).await;
            return Ok(cached);
        }
        
        let completions = self.generate_completions(context).await?;
        
        // Cache the results
        self.completion_cache.store(&cache_key, &completions).await;
        
        let generation_time = start_time.elapsed().as_millis() as u64;
        self.analytics.record_completion_generation(&context.input, completions.len(), generation_time).await;
        
        debug!("Generated {} completions for '{}' in {}ms", 
               completions.len(), context.input, generation_time);
        
        Ok(completions)
    }
    
    /// Generate completions based on context
    async fn generate_completions(&self, context: &CompletionContext) -> Result<Vec<CompletionSuggestion>> {
        let input = &context.input;
        
        // Parse the input to understand what we're completing
        if input.starts_with(':') {
            return self.complete_colon_command(context).await;
        }
        
        // Default to no completions
        Ok(vec![])
    }
    
    /// Complete colon commands (:mode, :set, etc.)
    async fn complete_colon_command(&self, context: &CompletionContext) -> Result<Vec<CompletionSuggestion>> {
        let input = &context.input[1..]; // Remove the ':'
        let parts: Vec<&str> = input.split_whitespace().collect();
        
        if parts.is_empty() || (parts.len() == 1 && !input.ends_with(' ')) {
            // Complete command name
            return self.complete_command_name(parts.get(0).unwrap_or(&"")).await;
        }
        
        let command = parts[0];
        let args = &parts[1..];
        
        match command {
            "set" => self.complete_set_command(args, context).await,
            "mode" => self.complete_mode_command(args).await,
            "theme" => self.complete_theme_command(args).await,
            "provider" => self.complete_provider_command(args).await,
            "session" => self.complete_session_command(args).await,
            "config" => self.complete_config_command(args).await,
            "log" => self.complete_log_command(args).await,
            "help" => self.complete_help_command(args).await,
            "workspace" => self.complete_workspace_command(args).await,
            "save" | "load" => self.complete_session_name_command(args).await,
            "export" => self.complete_export_command(args).await,
            _ => Ok(vec![]),
        }
    }
    
    /// Complete command names
    async fn complete_command_name(&self, partial: &str) -> Result<Vec<CompletionSuggestion>> {
        let commands = vec![
            ("mode", "Switch UI mode (normal|vim|debug)"),
            ("set", "Set configuration value"),
            ("theme", "Switch theme"),
            ("provider", "Switch LLM provider"),
            ("workspace", "Switch workspace"),
            ("session", "Session management"),
            ("config", "Config management"),
            ("log", "Set log level"),
            ("help", "Show help"),
            ("exit", "Exit application"),
            ("clear", "Clear chat history"),
            ("save", "Save context"),
            ("load", "Load context"),
            ("export", "Export data"),
        ];
        
        let mut suggestions = vec![];
        
        for (cmd, desc) in commands {
            if cmd.starts_with(partial) {
                suggestions.push(CompletionSuggestion {
                    text: cmd.to_string(),
                    display: format!("{} - {}", cmd, desc),
                    completion_type: CompletionType::Command,
                    relevance: self.calculate_relevance(cmd, partial),
                    context: Some(desc.to_string()),
                    documentation: None,
                    preview: false,
                });
            }
        }
        
        suggestions.sort_by(|a, b| b.relevance.partial_cmp(&a.relevance).unwrap());
        Ok(suggestions)
    }
    
    /// Complete :set command
    async fn complete_set_command(&self, args: &[&str], context: &CompletionContext) -> Result<Vec<CompletionSuggestion>> {
        if args.is_empty() || (args.len() == 1 && !context.input.contains('=')) {
            // Complete setting key
            let partial = args.get(0).unwrap_or(&"");
            return self.complete_setting_key(partial).await;
        }
        
        if args.len() == 1 && context.input.contains('=') {
            // Complete setting value
            let setting_arg = args[0];
            if let Some(eq_pos) = setting_arg.find('=') {
                let key = &setting_arg[..eq_pos];
                let partial_value = &setting_arg[eq_pos + 1..];
                return self.complete_setting_value(key, partial_value).await;
            }
        }
        
        Ok(vec![])
    }
    
    /// Complete setting keys
    async fn complete_setting_key(&self, partial: &str) -> Result<Vec<CompletionSuggestion>> {
        let settings = self.setting_registry.get_all_settings().await;
        let mut suggestions = vec![];
        
        for setting in settings {
            if setting.key.starts_with(partial) && !setting.deprecated {
                suggestions.push(CompletionSuggestion {
                    text: format!("{}=", setting.key),
                    display: format!("{} - {}", setting.key, setting.description),
                    completion_type: CompletionType::SettingKey,
                    relevance: self.calculate_relevance(&setting.key, partial),
                    context: Some(format!("Type: {:?}", setting.setting_type)),
                    documentation: Some(setting.description.clone()),
                    preview: false,
                });
            }
        }
        
        suggestions.sort_by(|a, b| b.relevance.partial_cmp(&a.relevance).unwrap());
        Ok(suggestions)
    }
    
    /// Complete setting values
    async fn complete_setting_value(&self, key: &str, partial: &str) -> Result<Vec<CompletionSuggestion>> {
        let setting = self.setting_registry.get_setting(key).await;
        let mut suggestions = vec![];
        
        if let Some(setting) = setting {
            match setting.setting_type {
                SettingType::Enum => {
                    if let Some(values) = &setting.possible_values {
                        for value in values {
                            if value.starts_with(partial) {
                                suggestions.push(CompletionSuggestion {
                                    text: value.clone(),
                                    display: value.clone(),
                                    completion_type: CompletionType::SettingValue,
                                    relevance: self.calculate_relevance(value, partial),
                                    context: Some("Enum value".to_string()),
                                    documentation: None,
                                    preview: false,
                                });
                            }
                        }
                    }
                }
                SettingType::Boolean => {
                    for value in &["true", "false"] {
                        if value.starts_with(partial) {
                            suggestions.push(CompletionSuggestion {
                                text: value.to_string(),
                                display: value.to_string(),
                                completion_type: CompletionType::SettingValue,
                                relevance: self.calculate_relevance(value, partial),
                                context: Some("Boolean value".to_string()),
                                documentation: None,
                                preview: false,
                            });
                        }
                    }
                }
                _ => {
                    // For other types, provide examples if available
                    for example in &setting.examples {
                        if example.starts_with(partial) {
                            suggestions.push(CompletionSuggestion {
                                text: example.clone(),
                                display: format!("{} (example)", example),
                                completion_type: CompletionType::SettingValue,
                                relevance: self.calculate_relevance(example, partial) * 0.8,
                                context: Some("Example value".to_string()),
                                documentation: None,
                                preview: true,
                            });
                        }
                    }
                }
            }
        }
        
        suggestions.sort_by(|a, b| b.relevance.partial_cmp(&a.relevance).unwrap());
        Ok(suggestions)
    }
    
    /// Complete :mode command
    async fn complete_mode_command(&self, args: &[&str]) -> Result<Vec<CompletionSuggestion>> {
        if args.is_empty() || args.len() == 1 {
            let partial = args.get(0).unwrap_or(&"");
            let modes = vec![
                ("normal", "Standard interactive mode"),
                ("vim", "Vim-like modal editing"),
                ("debug", "Enhanced debugging mode"),
            ];
            
            let mut suggestions = vec![];
            for (mode, desc) in modes {
                if mode.starts_with(partial) {
                    suggestions.push(CompletionSuggestion {
                        text: mode.to_string(),
                        display: format!("{} - {}", mode, desc),
                        completion_type: CompletionType::Parameter,
                        relevance: self.calculate_relevance(mode, partial),
                        context: Some(desc.to_string()),
                        documentation: None,
                        preview: false,
                    });
                }
            }
            
            return Ok(suggestions);
        }
        
        Ok(vec![])
    }
    
    /// Complete :theme command
    async fn complete_theme_command(&self, args: &[&str]) -> Result<Vec<CompletionSuggestion>> {
        if args.is_empty() || args.len() == 1 {
            let partial = args.get(0).unwrap_or(&"");
            return self.theme_registry.get_theme_completions(partial).await;
        }
        
        Ok(vec![])
    }
    
    /// Complete :provider command
    async fn complete_provider_command(&self, args: &[&str]) -> Result<Vec<CompletionSuggestion>> {
        if args.is_empty() || args.len() == 1 {
            let partial = args.get(0).unwrap_or(&"");
            return self.provider_registry.get_provider_completions(partial).await;
        }
        
        Ok(vec![])
    }
    
    /// Complete :session command
    async fn complete_session_command(&self, args: &[&str]) -> Result<Vec<CompletionSuggestion>> {
        if args.is_empty() || args.len() == 1 {
            let partial = args.get(0).unwrap_or(&"");
            let actions = vec![
                ("save", "Save current session"),
                ("load", "Load a saved session"),
                ("new", "Start a new session"),
                ("list", "List all sessions"),
            ];
            
            let mut suggestions = vec![];
            for (action, desc) in actions {
                if action.starts_with(partial) {
                    suggestions.push(CompletionSuggestion {
                        text: action.to_string(),
                        display: format!("{} - {}", action, desc),
                        completion_type: CompletionType::Parameter,
                        relevance: self.calculate_relevance(action, partial),
                        context: Some(desc.to_string()),
                        documentation: None,
                        preview: false,
                    });
                }
            }
            
            return Ok(suggestions);
        }
        
        if args.len() == 2 && (args[0] == "load") {
            // Complete session names for load command
            let partial = args[1];
            return self.session_registry.get_session_completions(partial).await;
        }
        
        Ok(vec![])
    }
    
    /// Complete other commands (placeholder implementations)
    async fn complete_config_command(&self, _args: &[&str]) -> Result<Vec<CompletionSuggestion>> { Ok(vec![]) }
    async fn complete_log_command(&self, _args: &[&str]) -> Result<Vec<CompletionSuggestion>> { Ok(vec![]) }
    async fn complete_help_command(&self, _args: &[&str]) -> Result<Vec<CompletionSuggestion>> { Ok(vec![]) }
    async fn complete_workspace_command(&self, _args: &[&str]) -> Result<Vec<CompletionSuggestion>> { Ok(vec![]) }
    async fn complete_session_name_command(&self, _args: &[&str]) -> Result<Vec<CompletionSuggestion>> { Ok(vec![]) }
    async fn complete_export_command(&self, _args: &[&str]) -> Result<Vec<CompletionSuggestion>> { Ok(vec![]) }
    
    /// Calculate relevance score for completion matching
    fn calculate_relevance(&self, candidate: &str, partial: &str) -> f32 {
        if partial.is_empty() {
            return 0.5;
        }
        
        if candidate == partial {
            return 1.0;
        }
        
        if candidate.starts_with(partial) {
            return 0.9 - (candidate.len() - partial.len()) as f32 * 0.01;
        }
        
        // Check for substring match
        if candidate.contains(partial) {
            return 0.7;
        }
        
        // Check for fuzzy match (simple implementation)
        let mut score = 0.0;
        let mut last_match = 0;
        
        for ch in partial.chars() {
            if let Some(pos) = candidate[last_match..].find(ch) {
                score += 1.0 / (pos + 1) as f32;
                last_match += pos + 1;
            }
        }
        
        score / partial.len() as f32 * 0.6
    }
    
    /// Generate cache key for completion context
    fn generate_cache_key(&self, context: &CompletionContext) -> String {
        format!("{}:{}:{:?}", 
                context.input, 
                context.cursor_position, 
                context.mode)
    }
}

// Supporting components (placeholder implementations)

#[derive(Debug)]
struct SettingRegistry;

impl SettingRegistry {
    fn new() -> Self { Self }
    
    async fn get_all_settings(&self) -> Vec<SettingDefinition> {
        vec![
            SettingDefinition {
                key: "llm.provider".to_string(),
                description: "LLM provider to use".to_string(),
                setting_type: SettingType::Enum,
                possible_values: Some(vec![
                    "anthropic".to_string(),
                    "openrouter".to_string(), 
                    "openai".to_string(),
                    "ollama".to_string(),
                ]),
                constraints: None,
                default_value: Some("anthropic".to_string()),
                deprecated: false,
                category: SettingCategory::Llm,
                examples: vec!["anthropic".to_string(), "openrouter".to_string()],
            },
            SettingDefinition {
                key: "llm.temperature".to_string(),
                description: "Response randomness (0.0-2.0)".to_string(),
                setting_type: SettingType::Float,
                possible_values: None,
                constraints: Some(ValueConstraints {
                    min: Some(0.0),
                    max: Some(2.0),
                    pattern: None,
                    format: None,
                }),
                default_value: Some("0.7".to_string()),
                deprecated: false,
                category: SettingCategory::Llm,
                examples: vec!["0.7".to_string(), "1.0".to_string(), "0.3".to_string()],
            },
        ]
    }
    
    async fn get_setting(&self, key: &str) -> Option<SettingDefinition> {
        let all = self.get_all_settings().await;
        all.into_iter().find(|s| s.key == key)
    }
}

#[derive(Debug)]
struct ThemeRegistry;

impl ThemeRegistry {
    fn new() -> Self { Self }
    
    async fn get_theme_completions(&self, partial: &str) -> Result<Vec<CompletionSuggestion>> {
        let themes = vec![
            ("default", "Balanced dark theme", true),
            ("dark", "Deep dark theme", true),
            ("light", "Light theme", true),
            ("high-contrast", "High contrast theme", true),
        ];
        
        let mut suggestions = vec![];
        for (name, desc, available) in themes {
            if name.starts_with(partial) {
                suggestions.push(CompletionSuggestion {
                    text: name.to_string(),
                    display: format!("{} - {}{}", name, desc, if available { "" } else { " (unavailable)" }),
                    completion_type: CompletionType::ThemeName,
                    relevance: if available { 1.0 } else { 0.3 },
                    context: Some(desc.to_string()),
                    documentation: None,
                    preview: true,
                });
            }
        }
        
        Ok(suggestions)
    }
}

#[derive(Debug)]
struct ProviderRegistry;

impl ProviderRegistry {
    fn new() -> Self { Self }
    
    async fn get_provider_completions(&self, partial: &str) -> Result<Vec<CompletionSuggestion>> {
        let providers = vec![
            ("anthropic", "Anthropic Claude API", true),
            ("openrouter", "OpenRouter multi-model API", true),
            ("openai", "OpenAI API", true),
            ("ollama", "Local Ollama server", false),
        ];
        
        let mut suggestions = vec![];
        for (name, desc, available) in providers {
            if name.starts_with(partial) {
                suggestions.push(CompletionSuggestion {
                    text: name.to_string(),
                    display: format!("{} - {}{}", name, desc, if available { "" } else { " (not configured)" }),
                    completion_type: CompletionType::ProviderName,
                    relevance: if available { 1.0 } else { 0.5 },
                    context: Some(desc.to_string()),
                    documentation: None,
                    preview: false,
                });
            }
        }
        
        Ok(suggestions)
    }
}

#[derive(Debug)]
struct SessionRegistry;

impl SessionRegistry {
    fn new() -> Self { Self }
    
    async fn get_session_completions(&self, partial: &str) -> Result<Vec<CompletionSuggestion>> {
        let sessions = vec![
            ("default", Some("2024-01-15 14:30".to_string())),
            ("work-project", Some("2024-01-14 09:15".to_string())),
            ("experiment", Some("2024-01-13 16:45".to_string())),
        ];
        
        let mut suggestions = vec![];
        for (name, last_modified) in sessions {
            if name.starts_with(partial) {
                let context = last_modified.map(|lm| format!("Last modified: {}", lm));
                
                suggestions.push(CompletionSuggestion {
                    text: name.to_string(),
                    display: if let Some(ref ctx) = context {
                        format!("{} ({})", name, ctx)
                    } else {
                        name.to_string()
                    },
                    completion_type: CompletionType::SessionName,
                    relevance: 1.0,
                    context,
                    documentation: None,
                    preview: false,
                });
            }
        }
        
        Ok(suggestions)
    }
}

#[derive(Debug)]
struct CompletionCache;

impl CompletionCache {
    fn new() -> Self { Self }
    async fn get(&self, _key: &str) -> Option<Vec<CompletionSuggestion>> { None }
    async fn store(&self, _key: &str, _completions: &[CompletionSuggestion]) {}
}

#[derive(Debug)]
struct CompletionAnalytics;

impl CompletionAnalytics {
    fn new() -> Self { Self }
    async fn record_cache_hit(&self, _key: &str) {}
    async fn record_completion_generation(&self, _input: &str, _count: usize, _time_ms: u64) {}
}