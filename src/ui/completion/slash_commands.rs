use crate::agent::core::Agent<PERSON>ore;
use crate::commands::CommandRegistry;
use ratatui::prelude::*;
use ratatui::widgets::{Block, Borders, List, ListItem, Padding};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, VecDeque};
use std::sync::Arc;
use tokio::sync::RwLock;

/// Represents a single completion suggestion
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompletionSuggestion {
    /// The actual text to insert
    pub text: String,
    /// Display text (may include additional formatting)
    pub display: String,
    /// Description of the suggestion
    pub description: String,
    /// Type of suggestion (command, parameter, etc.)
    pub suggestion_type: SuggestionType,
    /// Relevance score (higher is better)
    pub score: f64,
    /// Icon or symbol to display
    pub icon: String,
    /// Additional metadata
    pub metadata: HashMap<String, String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub enum SuggestionType {
    Command,
    CommandAlias,
    Parameter,
    Value,
    History,
    AiSuggested,
}

/// Manages completion suggestions with AI integration
#[derive(Clone)]
pub struct CompletionEngine {
    /// Command registry reference
    command_registry: Arc<CommandRegistry>,
    /// Command usage history
    usage_history: Arc<RwLock<CommandHistory>>,
    /// Cached AI suggestions
    ai_cache: Arc<RwLock<HashMap<String, Vec<CompletionSuggestion>>>>,
    /// Parameter hints (wrapped in Arc for Clone)
    parameter_hints: Arc<HashMap<String, Vec<ParameterHint>>>,
}

#[derive(Debug, Clone)]
struct CommandHistory {
    /// Recent command uses with timestamps
    recent_uses: VecDeque<(String, chrono::DateTime<chrono::Utc>)>,
    /// Usage count per command
    usage_counts: HashMap<String, usize>,
    /// Recent parameter values per command
    parameter_history: HashMap<String, VecDeque<String>>,
}

#[derive(Debug, Clone)]
struct ParameterHint {
    name: String,
    description: String,
    value_type: String,
    examples: Vec<String>,
}

impl CompletionEngine {
    pub fn new(command_registry: Arc<CommandRegistry>) -> Self {
        let mut parameter_hints = HashMap::new();

        // Initialize parameter hints for built-in commands
        parameter_hints.insert(
            "help".to_string(),
            vec![ParameterHint {
                name: "command".to_string(),
                description: "Command name to get help for".to_string(),
                value_type: "string".to_string(),
                examples: vec!["clear".to_string(), "save".to_string(), "model".to_string()],
            }],
        );

        parameter_hints.insert(
            "save".to_string(),
            vec![ParameterHint {
                name: "filename".to_string(),
                description: "File name to save conversation to".to_string(),
                value_type: "path".to_string(),
                examples: vec!["chat.txt".to_string(), "session_2024.log".to_string()],
            }],
        );

        parameter_hints.insert(
            "model".to_string(),
            vec![ParameterHint {
                name: "model_name".to_string(),
                description: "LLM model to switch to".to_string(),
                value_type: "string".to_string(),
                examples: vec![
                    "claude-3-opus-20240229".to_string(),
                    "gpt-4".to_string(),
                    "claude-3-sonnet".to_string(),
                ],
            }],
        );

        parameter_hints.insert(
            "up".to_string(),
            vec![ParameterHint {
                name: "lines".to_string(),
                description: "Number of lines to scroll up".to_string(),
                value_type: "number".to_string(),
                examples: vec!["5".to_string(), "10".to_string(), "20".to_string()],
            }],
        );

        parameter_hints.insert(
            "down".to_string(),
            vec![ParameterHint {
                name: "lines".to_string(),
                description: "Number of lines to scroll down".to_string(),
                value_type: "number".to_string(),
                examples: vec!["5".to_string(), "10".to_string(), "20".to_string()],
            }],
        );

        Self {
            command_registry,
            usage_history: Arc::new(RwLock::new(CommandHistory {
                recent_uses: VecDeque::with_capacity(100),
                usage_counts: HashMap::new(),
                parameter_history: HashMap::new(),
            })),
            ai_cache: Arc::new(RwLock::new(HashMap::new())),
            parameter_hints: Arc::new(parameter_hints),
        }
    }

    /// Get completion suggestions for the current input
    pub async fn get_suggestions(
        &self,
        input: &str,
        _cursor_position: usize,
        agent_core: Option<&AgentCore>,
    ) -> Vec<CompletionSuggestion> {
        if !input.starts_with('/') {
            return Vec::new();
        }

        let input = input.trim_start_matches('/');
        let parts: Vec<&str> = input.split_whitespace().collect();

        if parts.is_empty() || (parts.len() == 1 && !input.ends_with(' ')) {
            // Command completion
            self.get_command_suggestions(parts.get(0).unwrap_or(&""))
                .await
        } else {
            // Parameter completion
            let command = parts[0];
            let param_input = if input.ends_with(' ') {
                ""
            } else {
                parts.last().unwrap_or(&"")
            };

            self.get_parameter_suggestions(command, param_input, &parts[1..], agent_core)
                .await
        }
    }

    /// Get command suggestions
    async fn get_command_suggestions(&self, partial: &str) -> Vec<CompletionSuggestion> {
        let mut suggestions = Vec::new();
        let commands = self.command_registry.list_commands();
        let history = self.usage_history.read().await;

        // Score and collect command suggestions
        for (name, description) in commands {
            if name.contains(partial) {
                let score = if name.starts_with(partial) {
                    100.0
                } else {
                    50.0
                };
                let usage_boost = history.usage_counts.get(name).unwrap_or(&0) * 10;
                let total_score = score + usage_boost as f64;

                suggestions.push(CompletionSuggestion {
                    text: format!("{} ", name),
                    display: name.to_string(),
                    description: description.to_string(),
                    suggestion_type: SuggestionType::Command,
                    score: total_score,
                    icon: "📌".to_string(),
                    metadata: HashMap::new(),
                });
            }
        }

        // Add recent commands from history
        for (recent_cmd, _) in history.recent_uses.iter().take(5) {
            if recent_cmd.starts_with(partial)
                && !suggestions.iter().any(|s| s.text.trim() == recent_cmd)
            {
                suggestions.push(CompletionSuggestion {
                    text: format!("{} ", recent_cmd),
                    display: recent_cmd.clone(),
                    description: "Recently used".to_string(),
                    suggestion_type: SuggestionType::History,
                    score: 100.0,
                    icon: "🕐".to_string(),
                    metadata: HashMap::new(),
                });
            }
        }

        // Sort by score (descending)
        suggestions.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap());
        suggestions.truncate(10);

        suggestions
    }

    /// Get parameter suggestions for a command
    async fn get_parameter_suggestions(
        &self,
        command: &str,
        partial: &str,
        existing_params: &[&str],
        agent_core: Option<&AgentCore>,
    ) -> Vec<CompletionSuggestion> {
        let mut suggestions = Vec::new();

        // Get parameter hints for the command
        if let Some(hints) = self.parameter_hints.get(command) {
            for hint in hints {
                if existing_params.is_empty() || !existing_params.contains(&hint.name.as_str()) {
                    // Add example values
                    for example in &hint.examples {
                        if example.contains(partial) {
                            let score = if example.starts_with(partial) {
                                100.0
                            } else {
                                50.0
                            };
                            suggestions.push(CompletionSuggestion {
                                text: example.clone(),
                                display: example.clone(),
                                description: hint.description.clone(),
                                suggestion_type: SuggestionType::Value,
                                score: score,
                                icon: "💡".to_string(),
                                metadata: HashMap::from([
                                    ("param_name".to_string(), hint.name.clone()),
                                    ("value_type".to_string(), hint.value_type.clone()),
                                ]),
                            });
                        }
                    }
                }
            }
        }

        // Add suggestions from history
        let history = self.usage_history.read().await;
        if let Some(param_history) = history.parameter_history.get(command) {
            for historical_value in param_history.iter().take(5) {
                if historical_value.starts_with(partial) {
                    suggestions.push(CompletionSuggestion {
                        text: historical_value.clone(),
                        display: historical_value.clone(),
                        description: "Previously used".to_string(),
                        suggestion_type: SuggestionType::History,
                        score: 80.0,
                        icon: "🕐".to_string(),
                        metadata: HashMap::new(),
                    });
                }
            }
        }

        // Request AI suggestions if available
        if let Some(_agent) = agent_core {
            let cached_key = format!("{}:{}", command, partial);
            let cache = self.ai_cache.read().await;

            if let Some(ai_suggestions) = cache.get(&cached_key) {
                suggestions.extend(ai_suggestions.clone());
            } else {
                drop(cache);
                // In a real implementation, we would request suggestions from the agent
                // For now, we'll add some mock AI suggestions
                if command == "save" && partial.is_empty() {
                    let timestamp = chrono::Local::now().format("%Y%m%d_%H%M%S");
                    suggestions.push(CompletionSuggestion {
                        text: format!("conversation_{}.txt", timestamp),
                        display: format!("conversation_{}.txt", timestamp),
                        description: "AI suggested filename with timestamp".to_string(),
                        suggestion_type: SuggestionType::AiSuggested,
                        score: 95.0,
                        icon: "🤖".to_string(),
                        metadata: HashMap::new(),
                    });
                }
            }
        }

        // Sort and limit suggestions
        suggestions.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap());
        suggestions.truncate(8);

        suggestions
    }

    /// Record command usage for learning
    pub async fn record_usage(&self, command: &str, params: Vec<String>) {
        let mut history = self.usage_history.write().await;

        // Update usage count
        *history.usage_counts.entry(command.to_string()).or_insert(0) += 1;

        // Add to recent uses
        history
            .recent_uses
            .push_front((command.to_string(), chrono::Utc::now()));
        if history.recent_uses.len() > 100 {
            history.recent_uses.pop_back();
        }

        // Record parameters
        if !params.is_empty() {
            let param_list = history
                .parameter_history
                .entry(command.to_string())
                .or_insert_with(|| VecDeque::with_capacity(20));

            for param in params {
                if !param_list.contains(&param) {
                    param_list.push_front(param);
                    if param_list.len() > 20 {
                        param_list.pop_back();
                    }
                }
            }
        }
    }

    /// Clear AI cache (useful when context changes significantly)
    pub async fn clear_ai_cache(&self) {
        self.ai_cache.write().await.clear();
    }
}

/// Widget for displaying completion suggestions
#[derive(Clone)]
pub struct CompletionPopup {
    suggestions: Vec<CompletionSuggestion>,
    selected_index: usize,
    visible: bool,
}

impl CompletionPopup {
    pub fn new() -> Self {
        Self {
            suggestions: Vec::new(),
            selected_index: 0,
            visible: false,
        }
    }

    pub fn update_suggestions(&mut self, suggestions: Vec<CompletionSuggestion>) {
        let is_empty = suggestions.is_empty();
        self.suggestions = suggestions;
        self.selected_index = 0;
        self.visible = !is_empty;
    }

    pub fn is_visible(&self) -> bool {
        self.visible
    }

    pub fn hide(&mut self) {
        self.visible = false;
    }

    pub fn move_selection_up(&mut self) {
        if self.selected_index > 0 {
            self.selected_index -= 1;
        }
    }

    pub fn move_selection_down(&mut self) {
        if self.selected_index < self.suggestions.len().saturating_sub(1) {
            self.selected_index += 1;
        }
    }

    pub fn get_selected(&self) -> Option<&CompletionSuggestion> {
        self.suggestions.get(self.selected_index)
    }

    pub fn render(&self, area: Rect, buf: &mut Buffer) {
        if !self.visible || self.suggestions.is_empty() {
            return;
        }

        // Calculate popup dimensions
        let popup_height = (self.suggestions.len() + 2).min(10) as u16;
        let popup_width = 60u16;

        // Position above the input area
        let popup_area = Rect {
            x: area.x + 2,
            y: area.y.saturating_sub(popup_height + 1),
            width: popup_width.min(area.width - 4),
            height: popup_height,
        };

        // Create list items with formatting
        let items: Vec<ListItem> = self
            .suggestions
            .iter()
            .enumerate()
            .map(|(i, suggestion)| {
                let style = if i == self.selected_index {
                    Style::default().bg(Color::DarkGray).fg(Color::White)
                } else {
                    Style::default()
                };

                let icon_style = match suggestion.suggestion_type {
                    SuggestionType::Command => Style::default().fg(Color::Blue),
                    SuggestionType::CommandAlias => Style::default().fg(Color::Cyan),
                    SuggestionType::Parameter => Style::default().fg(Color::Green),
                    SuggestionType::Value => Style::default().fg(Color::Yellow),
                    SuggestionType::History => Style::default().fg(Color::Magenta),
                    SuggestionType::AiSuggested => Style::default().fg(Color::LightBlue),
                };

                let _content = format!(
                    "{} {} - {}",
                    suggestion.icon, suggestion.display, suggestion.description
                );

                ListItem::new(Line::from(vec![
                    Span::styled(&suggestion.icon, icon_style),
                    Span::raw(" "),
                    Span::styled(&suggestion.display, style.add_modifier(Modifier::BOLD)),
                    Span::raw(" - "),
                    Span::styled(&suggestion.description, style.add_modifier(Modifier::DIM)),
                ]))
            })
            .collect();

        // Render the list with a block
        let list = List::new(items).block(
            Block::default()
                .borders(Borders::ALL)
                .title(" Suggestions (Tab/↑↓ to navigate, Enter to select) ")
                .border_style(Style::default().fg(Color::Blue))
                .padding(Padding::horizontal(1)),
        );

        // Clear the area first to avoid artifacts
        for y in popup_area.top()..popup_area.bottom() {
            for x in popup_area.left()..popup_area.right() {
                if let Some(cell) = buf.cell_mut((x, y)) {
                    cell.reset();
                }
            }
        }

        // Render the list
        Widget::render(list, popup_area, buf);
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_command_suggestions() {
        let registry = Arc::new(CommandRegistry::new());
        let engine = CompletionEngine::new(registry);

        // Test command completion
        let suggestions = engine.get_suggestions("/he", 3, None).await;
        assert!(!suggestions.is_empty());
        assert!(suggestions.iter().any(|s| s.text.starts_with("help")));
    }

    #[tokio::test]
    async fn test_parameter_suggestions() {
        let registry = Arc::new(CommandRegistry::new());
        let engine = CompletionEngine::new(registry);

        // Test parameter completion for save command
        let suggestions = engine.get_suggestions("/save ", 6, None).await;
        assert!(!suggestions.is_empty());
    }

    #[tokio::test]
    async fn test_usage_history() {
        let registry = Arc::new(CommandRegistry::new());
        let engine = CompletionEngine::new(registry);

        // Record some usage
        engine.record_usage("help", vec!["clear".to_string()]).await;
        engine.record_usage("help", vec!["save".to_string()]).await;

        // Check that history affects suggestions
        let suggestions = engine.get_suggestions("/h", 2, None).await;
        assert!(!suggestions.is_empty());
    }
}
