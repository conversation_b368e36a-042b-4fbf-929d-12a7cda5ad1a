// Command history management

use std::collections::VecDeque;

/// Command history manager
pub struct CommandHistory {
    entries: VecDeque<String>,
    current_index: Option<usize>,
    max_size: usize,
}

/// History entry
#[derive(Debug, <PERSON>lone)]
pub struct HistoryEntry {
    pub command: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// History manager
pub struct HistoryManager {
    history: CommandHistory,
}

impl CommandHistory {
    pub fn new(max_size: usize) -> Self {
        Self {
            entries: VecDeque::new(),
            current_index: None,
            max_size,
        }
    }

    pub fn add_entry(&mut self, entry: String) {
        if !entry.trim().is_empty() {
            self.entries.retain(|e| e != &entry);
            self.entries.push_front(entry);

            while self.entries.len() > self.max_size {
                self.entries.pop_back();
            }
        }

        self.current_index = None;
    }

    pub fn get_previous(&mut self) -> Option<&String> {
        if self.entries.is_empty() {
            return None;
        }

        let index = match self.current_index {
            None => 0,
            Some(i) => (i + 1).min(self.entries.len() - 1),
        };

        self.current_index = Some(index);
        self.entries.get(index)
    }

    pub fn get_next(&mut self) -> Option<&String> {
        if let Some(index) = self.current_index {
            if index > 0 {
                let new_index = index - 1;
                self.current_index = Some(new_index);
                self.entries.get(new_index)
            } else {
                self.current_index = None;
                None
            }
        } else {
            None
        }
    }
}

impl HistoryManager {
    pub fn new(max_size: usize) -> Self {
        Self {
            history: CommandHistory::new(max_size),
        }
    }

    pub fn add_entry(&mut self, entry: String) {
        self.history.add_entry(entry);
    }

    pub fn get_previous(&mut self) -> Option<&String> {
        self.history.get_previous()
    }

    pub fn get_next(&mut self) -> Option<&String> {
        self.history.get_next()
    }
}
