// Enhanced input system module
// Provides REPL-like interface with command history, auto-completion, and Vim keybindings

pub mod completion;
pub mod enhanced_handler;
pub mod handler;
pub mod history;
pub mod repl;
pub mod vim;

pub use completion::{CompletionContext, CompletionEngine, CompletionItem, CompletionProvider};
pub use enhanced_handler::{EnhancedInputHandler, HandlerConfig};
pub use handler::{CompletionIn<PERSON><PERSON><PERSON><PERSON>, InputHandler, TextInputHandler};
pub use history::{CommandHistory, HistoryEntry, HistoryManager};
pub use repl::{ExecutionStatus, ReplCommand, ReplConfig, ReplResult, ReplState, ValidationState};
pub use vim::{VimCommand, VimKeybindings, VimMode, VimMotion, VimOperator, VimState};

use crate::errors::Result;
use crate::ui::enhanced::{Action, ComponentResult, InputAction};
use crossterm::event::{Event, KeyCode, KeyEvent, KeyModifiers};
use ratatui::prelude::*;
use serde::{Deserialize, Serialize};
use std::collections::VecDeque;
use std::time::{Duration, Instant};
use uuid::Uuid;

/// Input context for handlers
#[derive(Debug, Clone)]
pub struct InputContext {
    /// Current input mode
    pub mode: crate::ui::enhanced::Mode,

    /// Current input text
    pub text: String,

    /// Cursor position
    pub cursor_position: usize,

    /// Selection range (start, end)
    pub selection: Option<(usize, usize)>,

    /// Vim state (if vim mode enabled)
    pub vim_state: Option<VimState>,

    /// Completion state
    pub completion_active: bool,

    /// Multi-line mode
    pub multiline: bool,

    /// Input validation
    pub is_valid: bool,

    /// Error message
    pub error_message: Option<String>,
}

/// Input result from handlers
#[derive(Debug, Clone, PartialEq)]
pub enum InputResult {
    /// Input was handled
    Handled,

    /// Input was not handled
    NotHandled,

    /// Input resulted in an action
    Action(Action),

    /// Mode change requested
    ModeChange(crate::ui::enhanced::Mode),

    /// Text change
    TextChange(String),

    /// Cursor move
    CursorMove(usize),

    /// Submit input
    Submit(String),

    /// Cancel input
    Cancel,

    /// Continue processing
    Continue,
}

// Re-export ReplInterface from repl module
pub use repl::ReplInterface;
