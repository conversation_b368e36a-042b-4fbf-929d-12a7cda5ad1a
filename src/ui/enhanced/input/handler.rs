// Input handler trait and implementations

use super::completion::{
    CommandCompletionProvider, CompletionContext, CompletionEngine, MentionCompletionProvider,
};
use super::{InputContext, InputResult, VimState};
use crate::errors::Result;
use crate::ui::enhanced::Mode;
use crossterm::event::Event;

/// Input handler trait
pub trait InputHandler: Send + Sync {
    fn can_handle(&self, event: &Event, context: &InputContext) -> bool;
    fn handle(&mut self, event: &Event, context: &mut InputContext) -> Result<InputResult>;
}

/// Basic text input handler with completion support
pub struct TextInputHandler {
    completion_engine: Option<CompletionEngine>,
}

/// Completion input handler
pub struct CompletionInputHandler {
    completion_engine: CompletionEngine,
}

impl TextInputHandler {
    pub fn new() -> Self {
        let mut completion_engine = CompletionEngine::new();
        completion_engine.register_provider(Box::new(CommandCompletionProvider::new()));
        completion_engine.register_provider(Box::new(MentionCompletionProvider::new()));

        Self {
            completion_engine: Some(completion_engine),
        }
    }

    pub fn new_without_completion() -> Self {
        Self {
            completion_engine: None,
        }
    }
}

impl InputHandler for TextInputHandler {
    fn can_handle(&self, event: &Event, context: &InputContext) -> bool {
        matches!(context.mode, Mode::Insert) && matches!(event, Event::Key(_))
    }

    fn handle(&mut self, event: &Event, context: &mut InputContext) -> Result<InputResult> {
        use crossterm::event::{KeyCode, KeyEvent, KeyModifiers};

        if let Event::Key(KeyEvent {
            code, modifiers, ..
        }) = event
        {
            match (code, modifiers) {
                (KeyCode::Char(c), &KeyModifiers::NONE)
                | (KeyCode::Char(c), &KeyModifiers::SHIFT) => {
                    context.text.insert(context.cursor_position, *c);
                    context.cursor_position += 1;

                    // Trigger completion for specific characters
                    if let Some(ref mut engine) = self.completion_engine {
                        if *c == '@' || *c == '/' {
                            let completion_context = CompletionContext::from_input(
                                &context.text,
                                context.cursor_position,
                                context.mode.clone(),
                            );

                            // Trigger completion asynchronously (this is a simplified approach)
                            // In a real implementation, this would be handled differently
                            // to avoid blocking the UI thread
                            tokio::spawn(async move {
                                // Note: This is a placeholder - actual completion would be handled
                                // by the UI layer that manages the completion state
                            });
                        }
                    }

                    Ok(InputResult::Handled)
                }
                (KeyCode::Backspace, _) => {
                    if context.cursor_position > 0 {
                        context.cursor_position -= 1;
                        context.text.remove(context.cursor_position);
                    }
                    Ok(InputResult::Handled)
                }
                (KeyCode::Delete, _) => {
                    if context.cursor_position < context.text.len() {
                        context.text.remove(context.cursor_position);
                    }
                    Ok(InputResult::Handled)
                }
                (KeyCode::Left, _) => {
                    if context.cursor_position > 0 {
                        context.cursor_position -= 1;
                    }
                    Ok(InputResult::Handled)
                }
                (KeyCode::Right, _) => {
                    if context.cursor_position < context.text.len() {
                        context.cursor_position += 1;
                    }
                    Ok(InputResult::Handled)
                }
                (KeyCode::Home, _) => {
                    context.cursor_position = 0;
                    Ok(InputResult::Handled)
                }
                (KeyCode::End, _) => {
                    context.cursor_position = context.text.len();
                    Ok(InputResult::Handled)
                }
                _ => Ok(InputResult::NotHandled),
            }
        } else {
            Ok(InputResult::NotHandled)
        }
    }
}

impl CompletionInputHandler {
    pub fn new() -> Self {
        let mut completion_engine = CompletionEngine::new();
        completion_engine.register_provider(Box::new(CommandCompletionProvider::new()));
        completion_engine.register_provider(Box::new(MentionCompletionProvider::new()));

        Self { completion_engine }
    }
}

impl InputHandler for CompletionInputHandler {
    fn can_handle(&self, event: &Event, context: &InputContext) -> bool {
        // Handle completion-related events (Tab, arrow keys when completion is active)
        matches!(event, Event::Key(_)) && context.completion_active
    }

    fn handle(&mut self, event: &Event, context: &mut InputContext) -> Result<InputResult> {
        use crossterm::event::{KeyCode, KeyEvent};

        if let Event::Key(KeyEvent { code, .. }) = event {
            match code {
                KeyCode::Tab => {
                    // Trigger or cycle completion
                    Ok(InputResult::Handled)
                }
                KeyCode::Up => {
                    // Move completion selection up
                    Ok(InputResult::Handled)
                }
                KeyCode::Down => {
                    // Move completion selection down
                    Ok(InputResult::Handled)
                }
                KeyCode::Enter => {
                    // Accept completion
                    Ok(InputResult::Handled)
                }
                KeyCode::Esc => {
                    // Cancel completion
                    Ok(InputResult::Handled)
                }
                _ => Ok(InputResult::NotHandled),
            }
        } else {
            Ok(InputResult::NotHandled)
        }
    }
}
