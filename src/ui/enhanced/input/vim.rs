// Vim keybinding system

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Vim keybinding system
pub struct VimKeybindings {
    state: VimState,
}

/// Vim state
#[derive(Debug, Clone)]
pub struct VimState {
    pub mode: VimMode,
    pub key_sequence: Vec<char>,
    pub pending_operator: Option<VimOperator>,
    pub register: Option<char>,
    pub count: Option<usize>,
    pub last_command: Option<VimCommand>,
    pub registers: HashMap<char, String>,
}

/// Vim modes
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum VimMode {
    Normal,
    Insert,
    Visual,
    VisualLine,
    Command,
    Replace,
}

/// Vim operators
#[derive(Debug, Clone)]
pub enum VimOperator {
    Delete,
    Yank,
    Change,
    Indent,
    Unindent,
}

/// Vim commands
#[derive(Debug, Clone)]
pub struct VimCommand {
    pub operator: Option<VimOperator>,
    pub motion: Option<VimMotion>,
    pub count: Option<usize>,
}

/// Vim motions
#[derive(Debug, <PERSON>lone)]
pub enum VimMotion {
    Left,
    Right,
    Up,
    Down,
    WordForward,
    WordBackward,
    LineStart,
    LineEnd,
    DocumentStart,
    DocumentEnd,
}

impl VimKeybindings {
    pub fn new() -> Self {
        Self {
            state: VimState::new(),
        }
    }

    pub fn get_state(&self) -> &VimState {
        &self.state
    }
}

impl VimState {
    pub fn new() -> Self {
        Self {
            mode: VimMode::Insert,
            key_sequence: Vec::new(),
            pending_operator: None,
            register: None,
            count: None,
            last_command: None,
            registers: HashMap::new(),
        }
    }
}

impl Default for VimState {
    fn default() -> Self {
        Self::new()
    }
}
