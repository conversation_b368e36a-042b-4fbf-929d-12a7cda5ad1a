// Enhanced input handler with unified command detection and completion

use super::{InputContext, InputResult, VimState};
use crate::commands::completion::{CompletionConfig, UnifiedCompletionProvider};
use crate::commands::parser::{CommandType, ParsingContext};
use crate::commands::registry::{CommandRegistry, ExecutionContext};
use crate::errors::Result;
use crate::ui::enhanced::Mode;
use crossterm::event::{Event, KeyCode, KeyEvent, KeyModifiers};
use std::sync::Arc;
use tokio::sync::RwLock;

/// Enhanced input handler with unified command system integration
pub struct EnhancedInputHandler {
    /// Unified completion provider
    completion_provider: Arc<UnifiedCompletionProvider>,
    /// Command registry
    registry: Arc<CommandRegistry>,
    /// Current completion state
    completion_state: RwLock<CompletionState>,
    /// Handler configuration
    config: HandlerConfig,
}

/// Completion state
#[derive(Debug, Clone)]
struct CompletionState {
    /// Whether completion is currently active
    active: bool,
    /// Current completion suggestions
    suggestions: Vec<crate::commands::parser::CommandCompletion>,
    /// Selected suggestion index
    selected_index: usize,
    /// Position where completion was triggered
    trigger_position: usize,
    /// Original text at trigger position
    original_text: String,
}

/// Handler configuration
#[derive(Debug, Clone)]
pub struct HandlerConfig {
    /// Auto-trigger completion on command prefixes
    pub auto_trigger_completion: bool,
    /// Trigger characters for auto-completion
    pub trigger_chars: Vec<char>,
    /// Minimum characters before triggering completion
    pub min_trigger_length: usize,
    /// Enable command validation during typing
    pub enable_validation: bool,
    /// Show command hints in status
    pub show_command_hints: bool,
}

impl Default for HandlerConfig {
    fn default() -> Self {
        Self {
            auto_trigger_completion: true,
            trigger_chars: vec!['@', '/', ':'],
            min_trigger_length: 1,
            enable_validation: true,
            show_command_hints: true,
        }
    }
}

impl EnhancedInputHandler {
    /// Create a new enhanced input handler
    pub fn new(
        registry: Arc<CommandRegistry>,
        completion_config: CompletionConfig,
        handler_config: HandlerConfig,
    ) -> Self {
        let completion_provider = Arc::new(UnifiedCompletionProvider::new(
            registry.clone(),
            completion_config,
        ));

        Self {
            completion_provider,
            registry,
            completion_state: RwLock::new(CompletionState::new()),
            config: handler_config,
        }
    }

    /// Check if character should trigger completion
    fn should_trigger_completion(&self, c: char, context: &InputContext) -> bool {
        if !self.config.auto_trigger_completion {
            return false;
        }

        // Check if character is a trigger character
        if self.config.trigger_chars.contains(&c) {
            return true;
        }

        // Check if we're continuing a command and have enough characters
        if self.detect_active_command(context).is_some() {
            let current_word_len = self.get_current_word_length(context);
            return current_word_len >= self.config.min_trigger_length;
        }

        false
    }

    /// Detect if there's an active command being typed
    fn detect_active_command(&self, context: &InputContext) -> Option<CommandType> {
        if context.cursor_position == 0 {
            return None;
        }

        let text_before = &context.text[..context.cursor_position];

        // Find the last trigger character
        for (i, ch) in text_before.char_indices().rev() {
            if self.config.trigger_chars.contains(&ch) {
                // Check if it's at a word boundary
                if i == 0
                    || text_before
                        .chars()
                        .nth(i - 1)
                        .map_or(true, |c| c.is_whitespace())
                {
                    return match ch {
                        '@' => Some(CommandType::Context),
                        '/' => Some(CommandType::Action),
                        ':' => Some(CommandType::Config),
                        _ => None,
                    };
                }
            } else if ch.is_whitespace() {
                // Stop at whitespace that's not part of the current command
                break;
            }
        }

        None
    }

    /// Get the length of the current word being typed
    fn get_current_word_length(&self, context: &InputContext) -> usize {
        if context.cursor_position == 0 {
            return 0;
        }

        let text_before = &context.text[..context.cursor_position];

        // Find the start of the current word
        let word_start = text_before
            .rfind(|c: char| c.is_whitespace() || self.config.trigger_chars.contains(&c))
            .map(|i| i + 1)
            .unwrap_or(0);

        context.cursor_position - word_start
    }

    /// Trigger completion asynchronously
    async fn trigger_completion(&self, context: &InputContext) -> Result<()> {
        let mut state = self.completion_state.write().await;

        // Get execution context
        let exec_context = ExecutionContext {
            working_directory: std::env::current_dir().unwrap_or_default(),
            permissions: crate::tools::PermissionLevel::Full, // TODO: Get from actual context
            session_data: std::collections::HashMap::new(),
            environment: std::env::vars().collect(),
            tool_registry: None, // TODO: Get from app state
        };

        // Get completions
        match self
            .completion_provider
            .get_completions(&context.text, context.cursor_position, Some(&exec_context))
            .await
        {
            Ok(suggestions) => {
                if !suggestions.is_empty() {
                    state.active = true;
                    state.suggestions = suggestions;
                    state.selected_index = 0;
                    state.trigger_position = context.cursor_position;
                    state.original_text = context.text.clone();
                }
            }
            Err(_) => {
                // Silently handle completion errors
                state.active = false;
            }
        }

        Ok(())
    }

    /// Handle completion navigation
    async fn handle_completion_navigation(
        &self,
        direction: CompletionDirection,
    ) -> Result<InputResult> {
        let mut state = self.completion_state.write().await;

        if !state.active || state.suggestions.is_empty() {
            return Ok(InputResult::NotHandled);
        }

        match direction {
            CompletionDirection::Next => {
                state.selected_index = (state.selected_index + 1) % state.suggestions.len();
            }
            CompletionDirection::Previous => {
                state.selected_index = if state.selected_index == 0 {
                    state.suggestions.len() - 1
                } else {
                    state.selected_index - 1
                };
            }
        }

        Ok(InputResult::Handled)
    }

    /// Accept the current completion
    async fn accept_completion(&self, context: &mut InputContext) -> Result<InputResult> {
        let mut state = self.completion_state.write().await;

        if !state.active || state.suggestions.is_empty() {
            return Ok(InputResult::NotHandled);
        }

        let selected = &state.suggestions[state.selected_index];

        // Record selection for learning
        self.completion_provider
            .record_selection(&context.text, selected, state.selected_index)
            .await;

        // Apply completion
        self.apply_completion(context, selected, &mut state)?;

        // Clear completion state
        state.active = false;
        state.suggestions.clear();
        state.selected_index = 0;

        Ok(InputResult::Handled)
    }

    /// Apply completion to the input context
    fn apply_completion(
        &self,
        context: &mut InputContext,
        completion: &crate::commands::parser::CommandCompletion,
        state: &mut CompletionState,
    ) -> Result<()> {
        // Find the start of the completion area
        let completion_start = self.find_completion_start(context)?;

        // Replace text from completion start to cursor
        let before_completion = &context.text[..completion_start];
        let after_cursor = &context.text[context.cursor_position..];

        // Build new text
        let new_text = format!(
            "{}{}{}",
            before_completion, completion.insert_text, after_cursor
        );
        let new_cursor_pos = before_completion.len() + completion.insert_text.len();

        context.text = new_text;
        context.cursor_position = new_cursor_pos;

        Ok(())
    }

    /// Find the start position for completion replacement
    fn find_completion_start(&self, context: &InputContext) -> Result<usize> {
        if context.cursor_position == 0 {
            return Ok(0);
        }

        let text_before = &context.text[..context.cursor_position];

        // Find the last trigger character or whitespace
        let start = text_before
            .rfind(|c: char| c.is_whitespace() || self.config.trigger_chars.contains(&c))
            .unwrap_or(0);

        Ok(start)
    }

    /// Cancel completion
    async fn cancel_completion(&self) -> Result<InputResult> {
        let mut state = self.completion_state.write().await;
        state.active = false;
        state.suggestions.clear();
        state.selected_index = 0;
        Ok(InputResult::Handled)
    }

    /// Validate current command if enabled
    async fn validate_command(&self, context: &InputContext) -> Result<Vec<String>> {
        if !self.config.enable_validation {
            return Ok(Vec::new());
        }

        // Parse current input
        match self
            .registry
            .parse_command(&context.text, context.cursor_position)
            .await?
        {
            crate::commands::parser::ParseResult::Success(command)
            | crate::commands::parser::ParseResult::Partial(command) => {
                self.registry.validate_command(&command).await
            }
            _ => Ok(Vec::new()),
        }
    }
}

/// Completion navigation direction
#[derive(Debug, Clone, Copy)]
enum CompletionDirection {
    Next,
    Previous,
}

impl CompletionState {
    fn new() -> Self {
        Self {
            active: false,
            suggestions: Vec::new(),
            selected_index: 0,
            trigger_position: 0,
            original_text: String::new(),
        }
    }
}

/// Input handler trait implementation
impl super::InputHandler for EnhancedInputHandler {
    fn can_handle(&self, event: &Event, context: &InputContext) -> bool {
        matches!(context.mode, Mode::Insert) && matches!(event, Event::Key(_))
    }

    fn handle(&mut self, event: &Event, context: &mut InputContext) -> Result<InputResult> {
        if let Event::Key(KeyEvent {
            code, modifiers, ..
        }) = event
        {
            // Handle completion-specific keys first
            if let Ok(completion_state) = self.completion_state.try_read() {
                if completion_state.active {
                    match code {
                        KeyCode::Tab | KeyCode::Down => {
                            return tokio::task::block_in_place(|| {
                                tokio::runtime::Handle::current().block_on(
                                    self.handle_completion_navigation(CompletionDirection::Next),
                                )
                            });
                        }
                        KeyCode::Up => {
                            return tokio::task::block_in_place(|| {
                                tokio::runtime::Handle::current().block_on(
                                    self.handle_completion_navigation(
                                        CompletionDirection::Previous,
                                    ),
                                )
                            });
                        }
                        KeyCode::Enter => {
                            return tokio::task::block_in_place(|| {
                                tokio::runtime::Handle::current()
                                    .block_on(self.accept_completion(context))
                            });
                        }
                        KeyCode::Esc => {
                            return tokio::task::block_in_place(|| {
                                tokio::runtime::Handle::current().block_on(self.cancel_completion())
                            });
                        }
                        _ => {}
                    }
                }
            }

            // Handle regular text input
            match (code, modifiers) {
                (KeyCode::Char(c), &KeyModifiers::NONE)
                | (KeyCode::Char(c), &KeyModifiers::SHIFT) => {
                    // Insert character
                    context.text.insert(context.cursor_position, *c);
                    context.cursor_position += 1;

                    // Check if we should trigger completion
                    if self.should_trigger_completion(*c, context) {
                        tokio::task::block_in_place(|| {
                            tokio::runtime::Handle::current()
                                .block_on(self.trigger_completion(context))
                        })?;
                    }

                    Ok(InputResult::Handled)
                }
                (KeyCode::Backspace, _) => {
                    if context.cursor_position > 0 {
                        context.cursor_position -= 1;
                        context.text.remove(context.cursor_position);

                        // Cancel completion if we backspace too far
                        tokio::task::block_in_place(|| {
                            tokio::runtime::Handle::current().block_on(async {
                                let state = self.completion_state.read().await;
                                if state.active && context.cursor_position < state.trigger_position
                                {
                                    drop(state);
                                    let _ = self.cancel_completion().await;
                                }
                            })
                        });
                    }
                    Ok(InputResult::Handled)
                }
                (KeyCode::Delete, _) => {
                    if context.cursor_position < context.text.len() {
                        context.text.remove(context.cursor_position);
                    }
                    Ok(InputResult::Handled)
                }
                (KeyCode::Left, _) => {
                    if context.cursor_position > 0 {
                        context.cursor_position -= 1;
                    }
                    Ok(InputResult::Handled)
                }
                (KeyCode::Right, _) => {
                    if context.cursor_position < context.text.len() {
                        context.cursor_position += 1;
                    }
                    Ok(InputResult::Handled)
                }
                (KeyCode::Home, _) => {
                    context.cursor_position = 0;
                    Ok(InputResult::Handled)
                }
                (KeyCode::End, _) => {
                    context.cursor_position = context.text.len();
                    Ok(InputResult::Handled)
                }
                _ => Ok(InputResult::NotHandled),
            }
        } else {
            Ok(InputResult::NotHandled)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::commands::registry::RegistryConfig;

    #[tokio::test]
    async fn test_enhanced_handler_creation() {
        let registry = Arc::new(CommandRegistry::new(RegistryConfig::default()));
        let handler = EnhancedInputHandler::new(
            registry,
            CompletionConfig::default(),
            HandlerConfig::default(),
        );

        let state = handler.completion_state.read().await;
        assert!(!state.active);
        assert!(state.suggestions.is_empty());
    }

    #[test]
    fn test_command_detection() {
        let registry = Arc::new(CommandRegistry::new(RegistryConfig::default()));
        let handler = EnhancedInputHandler::new(
            registry,
            CompletionConfig::default(),
            HandlerConfig::default(),
        );

        let mut context = InputContext {
            text: "@file".to_string(),
            cursor_position: 5,
            mode: Mode::Insert,
            completion_active: false,
            vim_state: None,
        };

        assert_eq!(
            handler.detect_active_command(&context),
            Some(CommandType::Context)
        );

        context.text = "/edit".to_string();
        assert_eq!(
            handler.detect_active_command(&context),
            Some(CommandType::Action)
        );

        context.text = ":set".to_string();
        assert_eq!(
            handler.detect_active_command(&context),
            Some(CommandType::Config)
        );
    }

    #[test]
    fn test_trigger_detection() {
        let registry = Arc::new(CommandRegistry::new(RegistryConfig::default()));
        let handler = EnhancedInputHandler::new(
            registry,
            CompletionConfig::default(),
            HandlerConfig::default(),
        );

        let context = InputContext {
            text: "hello".to_string(),
            cursor_position: 5,
            mode: Mode::Insert,
            completion_active: false,
            vim_state: None,
        };

        // Should trigger on command prefixes
        assert!(handler.should_trigger_completion('@', &context));
        assert!(handler.should_trigger_completion('/', &context));
        assert!(handler.should_trigger_completion(':', &context));

        // Should not trigger on regular characters without command context
        assert!(!handler.should_trigger_completion('a', &context));
    }
}
