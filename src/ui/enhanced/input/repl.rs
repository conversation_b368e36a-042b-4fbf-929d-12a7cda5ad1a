// REPL interface implementation

use super::{CommandHistory, Input<PERSON>ontext, Input<PERSON><PERSON>ler, InputResult};
use crate::errors::Result;
use crate::ui::enhanced::Mode;
use chrono::{DateTime, Utc};
use crossterm::event::Event;
use serde::{Deserialize, Serialize};
use std::time::{Duration, Instant};

/// REPL interface
pub struct ReplInterface {
    state: ReplState,
    history: CommandHistory,
    handlers: Vec<Box<dyn InputHandler>>,
    config: ReplConfig,
}

/// REPL state
#[derive(Debug, Clone)]
pub struct ReplState {
    pub input: String,
    pub cursor: usize,
    pub mode: Mode,
    pub prompt: String,
    pub lines: Vec<String>,
    pub current_line: usize,
    pub execution_status: ExecutionStatus,
    pub last_result: Option<ReplResult>,
    pub validation: ValidationState,
}

/// REPL configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReplConfig {
    pub vim_mode: bool,
    pub auto_completion: bool,
    pub syntax_highlighting: bool,
    pub multiline_input: bool,
    pub history_size: usize,
    pub auto_save_history: bool,
    pub prompt_format: String,
    pub tab_size: usize,
    pub auto_indent: bool,
    pub show_line_numbers: bool,
    pub execution_timeout: u64,
}

/// REPL command result
#[derive(Debug, Clone)]
pub enum ReplResult {
    Success {
        output: String,
        duration: Duration,
    },
    Error {
        message: String,
        details: Option<String>,
        duration: Duration,
    },
    Empty {
        duration: Duration,
    },
    Cancelled,
}

/// REPL command
#[derive(Debug, Clone)]
pub struct ReplCommand {
    pub text: String,
    pub timestamp: DateTime<Utc>,
}

/// Execution status
#[derive(Debug, Clone, PartialEq)]
pub enum ExecutionStatus {
    Ready,
    Processing {
        command: String,
        started_at: Instant,
        progress: Option<f32>,
    },
    Completed {
        command: String,
        duration: Duration,
        success: bool,
    },
    Failed {
        command: String,
        error: String,
        duration: Duration,
    },
}

/// Validation state
#[derive(Debug, Clone, Default)]
pub struct ValidationState {
    pub is_valid: bool,
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
    pub suggestions: Vec<String>,
}

impl ReplInterface {
    pub fn new(config: ReplConfig) -> Self {
        let mut handlers: Vec<Box<dyn InputHandler>> = Vec::new();
        handlers.push(Box::new(super::handler::TextInputHandler::new()));

        Self {
            state: ReplState::new(&config.prompt_format),
            history: CommandHistory::new(config.history_size),
            handlers,
            config,
        }
    }

    pub fn handle_event(&mut self, event: &Event) -> Result<InputResult> {
        let mut context = InputContext {
            mode: self.state.mode.clone(),
            text: self.state.input.clone(),
            cursor_position: self.state.cursor,
            selection: None,
            vim_state: None,
            completion_active: false,
            multiline: self.config.multiline_input && self.state.lines.len() > 1,
            is_valid: self.state.validation.is_valid,
            error_message: self.state.validation.errors.first().cloned(),
        };

        for handler in &mut self.handlers {
            if handler.can_handle(event, &context) {
                match handler.handle(event, &mut context)? {
                    InputResult::NotHandled => continue,
                    result => {
                        self.apply_context_changes(&context);
                        return Ok(result);
                    }
                }
            }
        }

        Ok(InputResult::NotHandled)
    }

    fn apply_context_changes(&mut self, context: &InputContext) {
        self.state.mode = context.mode.clone();
        self.state.input = context.text.clone();
        self.state.cursor = context.cursor_position;
        self.state.validation.is_valid = context.is_valid;

        if let Some(error) = &context.error_message {
            self.state.validation.errors = vec![error.clone()];
        } else {
            self.state.validation.errors.clear();
        }
    }

    pub fn submit(&mut self) -> Result<String> {
        let input = if self.config.multiline_input && self.state.lines.len() > 1 {
            self.state.lines.join("\n")
        } else {
            self.state.input.clone()
        };

        if !input.trim().is_empty() {
            self.history.add_entry(input.clone());
        }

        self.clear_input();

        self.state.execution_status = ExecutionStatus::Processing {
            command: input.clone(),
            started_at: Instant::now(),
            progress: None,
        };

        Ok(input)
    }

    pub fn clear_input(&mut self) {
        self.state.input.clear();
        self.state.cursor = 0;
        self.state.lines = vec![String::new()];
        self.state.current_line = 0;
        self.state.validation = ValidationState::default();
    }

    pub fn get_state(&self) -> &ReplState {
        &self.state
    }

    pub fn get_input(&self) -> &str {
        &self.state.input
    }

    pub fn get_cursor(&self) -> usize {
        self.state.cursor
    }

    pub fn get_mode(&self) -> &Mode {
        &self.state.mode
    }

    pub fn set_mode(&mut self, mode: Mode) {
        self.state.mode = mode;
    }

    pub fn get_execution_status(&self) -> &ExecutionStatus {
        &self.state.execution_status
    }

    pub fn is_ready(&self) -> bool {
        matches!(
            self.state.execution_status,
            ExecutionStatus::Ready | ExecutionStatus::Completed { .. }
        )
    }

    pub fn validate_input(&mut self) -> bool {
        let input = &self.state.input;
        let mut validation = ValidationState::default();

        if input.trim().is_empty() {
            validation.warnings.push("Empty input".to_string());
        }

        if input.len() > 10000 {
            validation
                .errors
                .push("Input too long (max 10000 characters)".to_string());
        }

        validation.is_valid = validation.errors.is_empty();
        self.state.validation = validation;

        self.state.validation.is_valid
    }
}

impl ReplState {
    pub fn new(prompt: &str) -> Self {
        Self {
            input: String::new(),
            cursor: 0,
            mode: Mode::Insert,
            prompt: prompt.to_string(),
            lines: vec![String::new()],
            current_line: 0,
            execution_status: ExecutionStatus::Ready,
            last_result: None,
            validation: ValidationState::default(),
        }
    }
}

impl Default for ReplConfig {
    fn default() -> Self {
        Self {
            vim_mode: false,
            auto_completion: true,
            syntax_highlighting: true,
            multiline_input: true,
            history_size: 1000,
            auto_save_history: true,
            prompt_format: "❯ ".to_string(),
            tab_size: 4,
            auto_indent: true,
            show_line_numbers: false,
            execution_timeout: 30,
        }
    }
}
