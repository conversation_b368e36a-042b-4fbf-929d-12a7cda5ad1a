// Enhanced TUI state management
// Manages the complete state of the enhanced TUI interface including navigation,
// input, content, and configuration state

use super::{FocusTarget, Mode, PerformanceMetrics};
use crate::ui::enhanced::config::{KeybindingConfig, Theme};
use crate::ui::enhanced::content_blocks::{ContentBlock, ContentBlockType};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, VecDeque};
use std::time::Duration;
use uuid::Uuid;

/// Main TUI state container
#[derive(Debug, Clone)]
pub struct TuiState {
    /// Current UI mode
    pub mode: Mode,

    /// Current focus target
    pub focus: FocusTarget,

    /// Navigation state
    pub navigation: NavigationState,

    /// Input state
    pub input: InputState,

    /// Content state
    pub content: ContentState,

    /// Execution state
    pub execution: ExecutionStatus,

    /// Configuration state
    pub config: ConfigState,

    /// Performance metrics
    pub metrics: PerformanceMetrics,

    /// Debug information
    pub debug: DebugState,
}

/// Navigation state for content blocks
#[derive(Debug, Clone)]
pub struct NavigationState {
    /// Currently selected content block
    pub selected_block: Option<Uuid>,

    /// Currently focused content block
    pub focused_block: Option<Uuid>,

    /// Selected content blocks (for multi-selection)
    pub selected_blocks: Vec<Uuid>,

    /// Current scroll position
    pub scroll_offset: usize,

    /// Viewport size
    pub viewport_height: u16,

    /// Search state
    pub search: SearchState,

    /// Navigation history
    pub history: VecDeque<Uuid>,

    /// Maximum history size
    pub max_history: usize,
}

/// Search state for content navigation
#[derive(Debug, Clone, Default)]
pub struct SearchState {
    /// Current search query
    pub query: String,

    /// Search results (block IDs)
    pub results: Vec<Uuid>,

    /// Current result index
    pub current_result: Option<usize>,

    /// Search direction (forward/backward)
    pub direction: SearchDirection,

    /// Case sensitive search
    pub case_sensitive: bool,

    /// Regex search
    pub regex: bool,
}

#[derive(Debug, Clone, PartialEq)]
pub enum SearchDirection {
    Forward,
    Backward,
}

impl Default for SearchDirection {
    fn default() -> Self {
        SearchDirection::Forward
    }
}

/// Input state for text input and completion
#[derive(Debug, Clone)]
pub struct InputState {
    /// Current input text
    pub text: String,

    /// Cursor position
    pub cursor_position: usize,

    /// Selection range (start, end)
    pub selection: Option<(usize, usize)>,

    /// Input history
    pub history: InputHistory,

    /// Completion state
    pub completion: Option<CompletionState>,

    /// Multi-line input mode
    pub multiline: bool,

    /// Input validation state
    pub validation: ValidationState,

    /// Vim state (if vim mode is enabled)
    pub vim: Option<VimState>,
}

/// Input history management
#[derive(Debug, Clone)]
pub struct InputHistory {
    /// History entries
    pub entries: VecDeque<String>,

    /// Current position in history
    pub current_index: Option<usize>,

    /// Maximum history size
    pub max_size: usize,

    /// Temporary entry for current input
    pub temp_entry: String,
}

/// Auto-completion state
#[derive(Debug, Clone)]
pub struct CompletionState {
    /// Available completions
    pub items: Vec<CompletionItem>,

    /// Currently selected completion
    pub selected_index: usize,

    /// Completion trigger position
    pub trigger_position: usize,

    /// Completion type
    pub completion_type: CompletionType,

    /// Filter text
    pub filter: String,

    /// Popup visibility
    pub visible: bool,
}

/// Completion item
#[derive(Debug, Clone)]
pub struct CompletionItem {
    /// Display text
    pub text: String,

    /// Insertion text (may differ from display)
    pub insert_text: String,

    /// Item description
    pub description: Option<String>,

    /// Item kind/type
    pub kind: CompletionItemKind,

    /// Priority for sorting
    pub priority: u8,
}

#[derive(Debug, Clone, PartialEq)]
pub enum CompletionItemKind {
    File,
    Tool,
    Command,
    Variable,
    Function,
    Keyword,
    Text,
}

#[derive(Debug, Clone, PartialEq)]
pub enum CompletionType {
    Mention, // @-triggered
    Command, // /-triggered
    Context, // .-triggered
    General, // Tab-triggered
}

/// Input validation state
#[derive(Debug, Clone, Default)]
pub struct ValidationState {
    /// Validation errors
    pub errors: Vec<ValidationError>,

    /// Validation warnings
    pub warnings: Vec<ValidationWarning>,

    /// Is input valid
    pub is_valid: bool,
}

#[derive(Debug, Clone)]
pub struct ValidationError {
    pub message: String,
    pub position: Option<usize>,
}

#[derive(Debug, Clone)]
pub struct ValidationWarning {
    pub message: String,
    pub position: Option<usize>,
}

/// Vim state for vim keybindings
#[derive(Debug, Clone)]
pub struct VimState {
    /// Current vim mode
    pub mode: VimMode,

    /// Key sequence buffer
    pub key_sequence: Vec<char>,

    /// Pending operator
    pub pending_operator: Option<VimOperator>,

    /// Current register
    pub register: Option<char>,

    /// Repeat count
    pub count: Option<usize>,

    /// Last command for repeat
    pub last_command: Option<VimCommand>,

    /// Registers for copy/paste
    pub registers: HashMap<char, String>,
}

#[derive(Debug, Clone, PartialEq)]
pub enum VimMode {
    Normal,
    Insert,
    Visual,
    VisualLine,
    Command,
    Replace,
}

#[derive(Debug, Clone)]
pub enum VimOperator {
    Delete,
    Yank,
    Change,
    Indent,
    Unindent,
}

#[derive(Debug, Clone)]
pub struct VimCommand {
    pub operator: Option<VimOperator>,
    pub motion: Option<VimMotion>,
    pub count: Option<usize>,
}

#[derive(Debug, Clone)]
pub enum VimMotion {
    Left,
    Right,
    Up,
    Down,
    WordForward,
    WordBackward,
    LineStart,
    LineEnd,
    DocumentStart,
    DocumentEnd,
}

/// Content state for managing content blocks
#[derive(Debug, Clone)]
pub struct ContentState {
    /// All content blocks
    pub blocks: Vec<ContentBlock>,

    /// Block index for quick lookup
    pub block_index: HashMap<Uuid, usize>,

    /// Filtered blocks (for search/filter)
    pub filtered_blocks: Option<Vec<Uuid>>,

    /// Content statistics
    pub stats: ContentStats,

    /// Virtual scrolling state
    pub virtual_scroll: VirtualScrollState,
}

/// Content statistics
#[derive(Debug, Clone, Default)]
pub struct ContentStats {
    pub total_blocks: usize,
    pub user_messages: usize,
    pub assistant_responses: usize,
    pub code_blocks: usize,
    pub tool_outputs: usize,
    pub errors: usize,
    pub system_messages: usize,
}

/// Virtual scrolling state for performance
#[derive(Debug, Clone)]
pub struct VirtualScrollState {
    /// First visible block index
    pub first_visible: usize,

    /// Last visible block index
    pub last_visible: usize,

    /// Total content height
    pub total_height: u16,

    /// Viewport height
    pub viewport_height: u16,

    /// Scroll position
    pub scroll_position: u16,
}

/// Execution status for operations
#[derive(Debug, Clone, PartialEq)]
pub enum ExecutionStatus {
    Idle,
    Processing {
        message: String,
        progress: Option<f32>,
        started_at: DateTime<Utc>,
    },
    Completed {
        message: String,
        duration: Duration,
        completed_at: DateTime<Utc>,
    },
    Failed {
        error: String,
        failed_at: DateTime<Utc>,
    },
}

/// Configuration state
#[derive(Debug, Clone)]
pub struct ConfigState {
    /// Current theme
    pub theme: Theme,

    /// Keybinding configuration
    pub keybindings: KeybindingConfig,

    /// UI preferences
    pub preferences: UiPreferences,

    /// Feature flags
    pub features: FeatureFlags,
}

/// UI preferences
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UiPreferences {
    /// Show line numbers
    pub show_line_numbers: bool,

    /// Syntax highlighting
    pub syntax_highlighting: bool,

    /// Auto-completion enabled
    pub auto_completion: bool,

    /// Vim mode enabled
    pub vim_mode: bool,

    /// Screen reader mode
    pub screen_reader: bool,

    /// Animation enabled
    pub animations: bool,

    /// Font size
    pub font_size: u16,

    /// Tab size
    pub tab_size: u8,

    /// Word wrap
    pub word_wrap: bool,

    /// Auto-save interval (seconds)
    pub auto_save_interval: u64,
}

impl Default for UiPreferences {
    fn default() -> Self {
        Self {
            show_line_numbers: true,
            syntax_highlighting: true,
            auto_completion: true,
            vim_mode: false,
            screen_reader: false,
            animations: true,
            font_size: 14,
            tab_size: 4,
            word_wrap: true,
            auto_save_interval: 300, // 5 minutes
        }
    }
}

/// Feature flags for experimental features
#[derive(Debug, Clone, Default)]
pub struct FeatureFlags {
    /// Enhanced content blocks
    pub enhanced_blocks: bool,

    /// Advanced completion
    pub advanced_completion: bool,

    /// Virtual scrolling
    pub virtual_scrolling: bool,

    /// Hot reloading
    pub hot_reload: bool,

    /// Debug mode
    pub debug_mode: bool,

    /// Performance monitoring
    pub performance_monitoring: bool,
}

/// Debug state for development
#[derive(Debug, Clone, Default)]
pub struct DebugState {
    /// Debug overlay visible
    pub overlay_visible: bool,

    /// Debug information
    pub info: HashMap<String, String>,

    /// Performance history
    pub performance_history: VecDeque<PerformanceMetrics>,

    /// Event log
    pub event_log: VecDeque<DebugEvent>,

    /// Maximum log size
    pub max_log_size: usize,
}

#[derive(Debug, Clone)]
pub struct DebugEvent {
    pub timestamp: DateTime<Utc>,
    pub event_type: String,
    pub description: String,
    pub data: Option<String>,
}

impl TuiState {
    pub fn new() -> Self {
        Self {
            mode: Mode::Insert,
            focus: FocusTarget::InputArea,
            navigation: NavigationState::new(),
            input: InputState::new(),
            content: ContentState::new(),
            execution: ExecutionStatus::Idle,
            config: ConfigState::default(),
            metrics: PerformanceMetrics::default(),
            debug: DebugState::default(),
        }
    }

    /// Update content statistics
    pub fn update_content_stats(&mut self) {
        let mut stats = ContentStats::default();
        stats.total_blocks = self.content.blocks.len();

        for block in &self.content.blocks {
            match block.block_type {
                ContentBlockType::UserMessage => stats.user_messages += 1,
                ContentBlockType::AssistantResponse => stats.assistant_responses += 1,
                ContentBlockType::CodeBlock { .. } => stats.code_blocks += 1,
                ContentBlockType::ToolOutput { .. } => stats.tool_outputs += 1,
                ContentBlockType::Error { .. } => stats.errors += 1,
                ContentBlockType::SystemMessage => stats.system_messages += 1,
                ContentBlockType::Widget { .. } => {} // Don't count widgets in stats
            }
        }

        self.content.stats = stats;
    }

    /// Add debug event
    pub fn add_debug_event(
        &mut self,
        event_type: String,
        description: String,
        data: Option<String>,
    ) {
        let event = DebugEvent {
            timestamp: Utc::now(),
            event_type,
            description,
            data,
        };

        self.debug.event_log.push_back(event);

        // Limit log size
        while self.debug.event_log.len() > self.debug.max_log_size {
            self.debug.event_log.pop_front();
        }
    }
}

impl NavigationState {
    pub fn new() -> Self {
        Self {
            selected_block: None,
            focused_block: None,
            selected_blocks: Vec::new(),
            scroll_offset: 0,
            viewport_height: 0,
            search: SearchState::default(),
            history: VecDeque::new(),
            max_history: 100,
        }
    }

    /// Add block to navigation history
    pub fn add_to_history(&mut self, block_id: Uuid) {
        // Remove if already in history
        self.history.retain(|&id| id != block_id);

        // Add to front
        self.history.push_front(block_id);

        // Limit history size
        while self.history.len() > self.max_history {
            self.history.pop_back();
        }
    }
}

impl InputState {
    pub fn new() -> Self {
        Self {
            text: String::new(),
            cursor_position: 0,
            selection: None,
            history: InputHistory::new(),
            completion: None,
            multiline: false,
            validation: ValidationState::default(),
            vim: None,
        }
    }
}

impl InputHistory {
    pub fn new() -> Self {
        Self {
            entries: VecDeque::new(),
            current_index: None,
            max_size: 1000,
            temp_entry: String::new(),
        }
    }

    /// Add entry to history
    pub fn add_entry(&mut self, entry: String) {
        if !entry.trim().is_empty() {
            // Remove duplicate if exists
            self.entries.retain(|e| e != &entry);

            // Add to front
            self.entries.push_front(entry);

            // Limit size
            while self.entries.len() > self.max_size {
                self.entries.pop_back();
            }
        }

        // Reset index
        self.current_index = None;
    }
}

impl ContentState {
    pub fn new() -> Self {
        Self {
            blocks: Vec::new(),
            block_index: HashMap::new(),
            filtered_blocks: None,
            stats: ContentStats::default(),
            virtual_scroll: VirtualScrollState::default(),
        }
    }

    /// Add content block
    pub fn add_block(&mut self, block: ContentBlock) {
        let id = block.id;
        let index = self.blocks.len();

        self.blocks.push(block);
        self.block_index.insert(id, index);
    }

    /// Remove content block
    pub fn remove_block(&mut self, id: Uuid) -> Option<ContentBlock> {
        if let Some(&index) = self.block_index.get(&id) {
            let block = self.blocks.remove(index);
            self.block_index.remove(&id);

            // Update indices
            for (_, idx) in self.block_index.iter_mut() {
                if *idx > index {
                    *idx -= 1;
                }
            }

            Some(block)
        } else {
            None
        }
    }

    /// Get block by ID
    pub fn get_block(&self, id: Uuid) -> Option<&ContentBlock> {
        self.block_index
            .get(&id)
            .and_then(|&index| self.blocks.get(index))
    }

    /// Get mutable block by ID
    pub fn get_block_mut(&mut self, id: Uuid) -> Option<&mut ContentBlock> {
        if let Some(&index) = self.block_index.get(&id) {
            self.blocks.get_mut(index)
        } else {
            None
        }
    }
}

impl Default for ConfigState {
    fn default() -> Self {
        Self {
            theme: Theme::default(),
            keybindings: KeybindingConfig::default(),
            preferences: UiPreferences::default(),
            features: FeatureFlags::default(),
        }
    }
}

impl Default for VirtualScrollState {
    fn default() -> Self {
        Self {
            first_visible: 0,
            last_visible: 0,
            total_height: 0,
            viewport_height: 0,
            scroll_position: 0,
        }
    }
}

impl Default for TuiState {
    fn default() -> Self {
        Self::new()
    }
}
