// Layout utilities

use ratatui::prelude::*;

/// Layout utilities
pub struct LayoutUtils;

/// Constraint builder for easier layout creation
pub struct ConstraintBuilder {
    constraints: Vec<Constraint>,
}

impl LayoutUtils {
    /// Create a responsive layout that adapts to screen size
    pub fn responsive_layout(area: Rect, min_width: u16, max_width: u16) -> Layout {
        let width = area.width.clamp(min_width, max_width);
        let margin = (area.width.saturating_sub(width)) / 2;

        Layout::default()
            .direction(Direction::Horizontal)
            .constraints([
                Constraint::Length(margin),
                Constraint::Length(width),
                Constraint::Length(margin),
            ])
    }

    /// Create a three-column layout
    pub fn three_column_layout(left_width: u16, right_width: u16) -> Layout {
        Layout::default()
            .direction(Direction::Horizontal)
            .constraints([
                Constraint::Length(left_width),
                Constraint::Min(1),
                Constraint::Length(right_width),
            ])
    }

    /// Create a header-content-footer layout
    pub fn header_content_footer_layout(header_height: u16, footer_height: u16) -> Layout {
        Layout::default()
            .direction(Direction::Vertical)
            .constraints([
                Constraint::Length(header_height),
                Constraint::Min(1),
                Constraint::Length(footer_height),
            ])
    }

    /// Create a sidebar layout
    pub fn sidebar_layout(sidebar_width: u16, left_side: bool) -> Layout {
        if left_side {
            Layout::default()
                .direction(Direction::Horizontal)
                .constraints([Constraint::Length(sidebar_width), Constraint::Min(1)])
        } else {
            Layout::default()
                .direction(Direction::Horizontal)
                .constraints([Constraint::Min(1), Constraint::Length(sidebar_width)])
        }
    }

    /// Create a grid layout
    pub fn grid_layout(rows: u16, cols: u16) -> (Layout, Layout) {
        let row_layout = Layout::default()
            .direction(Direction::Vertical)
            .constraints(vec![Constraint::Percentage(100 / rows); rows as usize]);

        let col_layout = Layout::default()
            .direction(Direction::Horizontal)
            .constraints(vec![Constraint::Percentage(100 / cols); cols as usize]);

        (row_layout, col_layout)
    }

    /// Calculate optimal number of columns for given width and item width
    pub fn calculate_columns(total_width: u16, item_width: u16, min_spacing: u16) -> u16 {
        if item_width == 0 {
            return 1;
        }

        let available_width = total_width.saturating_sub(min_spacing);
        let cols = available_width / (item_width + min_spacing);
        cols.max(1)
    }

    /// Create a flexible layout that distributes space evenly
    pub fn flexible_layout(count: usize, direction: Direction) -> Layout {
        let constraints = vec![Constraint::Percentage(100 / count as u16); count];

        Layout::default()
            .direction(direction)
            .constraints(constraints)
    }

    /// Create a layout with fixed and flexible sections
    pub fn mixed_layout(fixed_sizes: Vec<u16>, direction: Direction) -> Layout {
        let mut constraints = Vec::new();

        for size in fixed_sizes {
            constraints.push(Constraint::Length(size));
        }
        constraints.push(Constraint::Min(1)); // Flexible section

        Layout::default()
            .direction(direction)
            .constraints(constraints)
    }
}

impl ConstraintBuilder {
    /// Create a new constraint builder
    pub fn new() -> Self {
        Self {
            constraints: Vec::new(),
        }
    }

    /// Add a fixed length constraint
    pub fn length(mut self, length: u16) -> Self {
        self.constraints.push(Constraint::Length(length));
        self
    }

    /// Add a minimum length constraint
    pub fn min(mut self, min: u16) -> Self {
        self.constraints.push(Constraint::Min(min));
        self
    }

    /// Add a maximum length constraint
    pub fn max(mut self, max: u16) -> Self {
        self.constraints.push(Constraint::Max(max));
        self
    }

    /// Add a percentage constraint
    pub fn percentage(mut self, percentage: u16) -> Self {
        self.constraints.push(Constraint::Percentage(percentage));
        self
    }

    /// Add a ratio constraint
    pub fn ratio(mut self, numerator: u32, denominator: u32) -> Self {
        self.constraints
            .push(Constraint::Ratio(numerator, denominator));
        self
    }

    /// Add a flexible constraint (minimum 1)
    pub fn flexible(mut self) -> Self {
        self.constraints.push(Constraint::Min(1));
        self
    }

    /// Build the constraints vector
    pub fn build(self) -> Vec<Constraint> {
        self.constraints
    }

    /// Build a layout with the constraints
    pub fn build_layout(self, direction: Direction) -> Layout {
        Layout::default()
            .direction(direction)
            .constraints(self.constraints)
    }
}

impl Default for ConstraintBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// Helper functions for common layout patterns
pub mod patterns {
    use super::*;

    /// Create a dialog layout (centered with padding)
    pub fn dialog_layout(area: Rect, width_percent: u16, height_percent: u16) -> Rect {
        let popup_layout = Layout::default()
            .direction(Direction::Vertical)
            .constraints([
                Constraint::Percentage((100 - height_percent) / 2),
                Constraint::Percentage(height_percent),
                Constraint::Percentage((100 - height_percent) / 2),
            ])
            .split(area);

        Layout::default()
            .direction(Direction::Horizontal)
            .constraints([
                Constraint::Percentage((100 - width_percent) / 2),
                Constraint::Percentage(width_percent),
                Constraint::Percentage((100 - width_percent) / 2),
            ])
            .split(popup_layout[1])[1]
    }

    /// Create a popup layout (smaller than dialog)
    pub fn popup_layout(area: Rect) -> Rect {
        dialog_layout(area, 60, 40)
    }

    /// Create a full-screen overlay layout
    pub fn overlay_layout(area: Rect) -> Rect {
        dialog_layout(area, 90, 80)
    }

    /// Create a notification layout (top-right corner)
    pub fn notification_layout(area: Rect, width: u16, height: u16) -> Rect {
        let x = area.width.saturating_sub(width);
        let y = 0;

        Rect {
            x,
            y,
            width: width.min(area.width),
            height: height.min(area.height),
        }
    }

    /// Create a tooltip layout (near cursor position)
    pub fn tooltip_layout(
        area: Rect,
        cursor_x: u16,
        cursor_y: u16,
        width: u16,
        height: u16,
    ) -> Rect {
        let x = if cursor_x + width <= area.width {
            cursor_x
        } else {
            cursor_x.saturating_sub(width)
        };

        let y = if cursor_y + height <= area.height {
            cursor_y + 1
        } else {
            cursor_y.saturating_sub(height)
        };

        Rect {
            x,
            y,
            width: width.min(area.width),
            height: height.min(area.height),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_constraint_builder() {
        let constraints = ConstraintBuilder::new()
            .length(10)
            .percentage(50)
            .flexible()
            .build();

        assert_eq!(constraints.len(), 3);
        assert!(matches!(constraints[0], Constraint::Length(10)));
        assert!(matches!(constraints[1], Constraint::Percentage(50)));
        assert!(matches!(constraints[2], Constraint::Min(1)));
    }

    #[test]
    fn test_calculate_columns() {
        assert_eq!(LayoutUtils::calculate_columns(100, 20, 5), 4);
        assert_eq!(LayoutUtils::calculate_columns(50, 20, 5), 2);
        assert_eq!(LayoutUtils::calculate_columns(10, 20, 5), 1);
    }

    #[test]
    fn test_dialog_layout() {
        let area = Rect::new(0, 0, 100, 50);
        let dialog = patterns::dialog_layout(area, 60, 40);

        assert_eq!(dialog.width, 60);
        assert_eq!(dialog.height, 20);
        assert_eq!(dialog.x, 20);
        assert_eq!(dialog.y, 15);
    }
}
