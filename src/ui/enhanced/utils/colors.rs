// Color utilities

use ratatui::prelude::*;

/// Color utilities
pub struct ColorUtils;

/// Color palette for theming
#[derive(Debug, Clone)]
pub struct ColorPalette {
    pub primary: Color,
    pub secondary: Color,
    pub accent: Color,
    pub background: Color,
    pub surface: Color,
    pub text: Color,
    pub text_secondary: Color,
    pub border: Color,
    pub success: Color,
    pub warning: Color,
    pub error: Color,
    pub info: Color,
}

impl ColorUtils {
    /// Convert hex string to Color
    pub fn from_hex(hex: &str) -> Option<Color> {
        let hex = hex.trim_start_matches('#');

        if hex.len() != 6 {
            return None;
        }

        let r = u8::from_str_radix(&hex[0..2], 16).ok()?;
        let g = u8::from_str_radix(&hex[2..4], 16).ok()?;
        let b = u8::from_str_radix(&hex[4..6], 16).ok()?;

        Some(Color::Rgb(r, g, b))
    }

    /// Convert Color to hex string
    pub fn to_hex(color: Color) -> String {
        match color {
            Color::Rgb(r, g, b) => format!("#{:02x}{:02x}{:02x}", r, g, b),
            Color::Black => "#000000".to_string(),
            Color::Red => "#ff0000".to_string(),
            Color::Green => "#00ff00".to_string(),
            Color::Yellow => "#ffff00".to_string(),
            Color::Blue => "#0000ff".to_string(),
            Color::Magenta => "#ff00ff".to_string(),
            Color::Cyan => "#00ffff".to_string(),
            Color::Gray => "#808080".to_string(),
            Color::DarkGray => "#404040".to_string(),
            Color::LightRed => "#ff8080".to_string(),
            Color::LightGreen => "#80ff80".to_string(),
            Color::LightYellow => "#ffff80".to_string(),
            Color::LightBlue => "#8080ff".to_string(),
            Color::LightMagenta => "#ff80ff".to_string(),
            Color::LightCyan => "#80ffff".to_string(),
            Color::White => "#ffffff".to_string(),
            _ => "#000000".to_string(), // Default for indexed colors
        }
    }

    /// Lighten a color by a percentage
    pub fn lighten(color: Color, percent: f32) -> Color {
        match color {
            Color::Rgb(r, g, b) => {
                let factor = 1.0 + percent / 100.0;
                let new_r = ((r as f32 * factor).min(255.0)) as u8;
                let new_g = ((g as f32 * factor).min(255.0)) as u8;
                let new_b = ((b as f32 * factor).min(255.0)) as u8;
                Color::Rgb(new_r, new_g, new_b)
            }
            _ => color, // Can't lighten non-RGB colors
        }
    }

    /// Darken a color by a percentage
    pub fn darken(color: Color, percent: f32) -> Color {
        match color {
            Color::Rgb(r, g, b) => {
                let factor = 1.0 - percent / 100.0;
                let new_r = ((r as f32 * factor).max(0.0)) as u8;
                let new_g = ((g as f32 * factor).max(0.0)) as u8;
                let new_b = ((b as f32 * factor).max(0.0)) as u8;
                Color::Rgb(new_r, new_g, new_b)
            }
            _ => color, // Can't darken non-RGB colors
        }
    }

    /// Calculate contrast ratio between two colors
    pub fn contrast_ratio(color1: Color, color2: Color) -> f32 {
        let lum1 = Self::luminance(color1);
        let lum2 = Self::luminance(color2);

        let lighter = lum1.max(lum2);
        let darker = lum1.min(lum2);

        (lighter + 0.05) / (darker + 0.05)
    }

    /// Calculate relative luminance of a color
    fn luminance(color: Color) -> f32 {
        let (r, g, b) = match color {
            Color::Rgb(r, g, b) => (r as f32 / 255.0, g as f32 / 255.0, b as f32 / 255.0),
            Color::Black => (0.0, 0.0, 0.0),
            Color::White => (1.0, 1.0, 1.0),
            Color::Red => (1.0, 0.0, 0.0),
            Color::Green => (0.0, 1.0, 0.0),
            Color::Blue => (0.0, 0.0, 1.0),
            Color::Yellow => (1.0, 1.0, 0.0),
            Color::Magenta => (1.0, 0.0, 1.0),
            Color::Cyan => (0.0, 1.0, 1.0),
            Color::Gray => (0.5, 0.5, 0.5),
            Color::DarkGray => (0.25, 0.25, 0.25),
            _ => (0.5, 0.5, 0.5), // Default for other colors
        };

        let r_lin = if r <= 0.03928 {
            r / 12.92
        } else {
            ((r + 0.055) / 1.055).powf(2.4)
        };
        let g_lin = if g <= 0.03928 {
            g / 12.92
        } else {
            ((g + 0.055) / 1.055).powf(2.4)
        };
        let b_lin = if b <= 0.03928 {
            b / 12.92
        } else {
            ((b + 0.055) / 1.055).powf(2.4)
        };

        0.2126 * r_lin + 0.7152 * g_lin + 0.0722 * b_lin
    }

    /// Check if color combination meets WCAG accessibility standards
    pub fn is_accessible(foreground: Color, background: Color, level: AccessibilityLevel) -> bool {
        let ratio = Self::contrast_ratio(foreground, background);

        match level {
            AccessibilityLevel::AA => ratio >= 4.5,
            AccessibilityLevel::AAA => ratio >= 7.0,
            AccessibilityLevel::AALarge => ratio >= 3.0,
            AccessibilityLevel::AAALarge => ratio >= 4.5,
        }
    }

    /// Generate a complementary color
    pub fn complementary(color: Color) -> Color {
        match color {
            Color::Rgb(r, g, b) => Color::Rgb(255 - r, 255 - g, 255 - b),
            Color::Black => Color::White,
            Color::White => Color::Black,
            Color::Red => Color::Cyan,
            Color::Green => Color::Magenta,
            Color::Blue => Color::Yellow,
            Color::Yellow => Color::Blue,
            Color::Magenta => Color::Green,
            Color::Cyan => Color::Red,
            _ => color,
        }
    }

    /// Create a color gradient between two colors
    pub fn gradient(start: Color, end: Color, steps: usize) -> Vec<Color> {
        if steps == 0 {
            return vec![];
        }

        if steps == 1 {
            return vec![start];
        }

        let (start_r, start_g, start_b) = Self::color_to_rgb(start);
        let (end_r, end_g, end_b) = Self::color_to_rgb(end);

        let mut gradient = Vec::new();

        for i in 0..steps {
            let t = i as f32 / (steps - 1) as f32;

            let r = (start_r as f32 + t * (end_r as f32 - start_r as f32)) as u8;
            let g = (start_g as f32 + t * (end_g as f32 - start_g as f32)) as u8;
            let b = (start_b as f32 + t * (end_b as f32 - start_b as f32)) as u8;

            gradient.push(Color::Rgb(r, g, b));
        }

        gradient
    }

    /// Convert Color to RGB values
    fn color_to_rgb(color: Color) -> (u8, u8, u8) {
        match color {
            Color::Rgb(r, g, b) => (r, g, b),
            Color::Black => (0, 0, 0),
            Color::Red => (255, 0, 0),
            Color::Green => (0, 255, 0),
            Color::Yellow => (255, 255, 0),
            Color::Blue => (0, 0, 255),
            Color::Magenta => (255, 0, 255),
            Color::Cyan => (0, 255, 255),
            Color::Gray => (128, 128, 128),
            Color::DarkGray => (64, 64, 64),
            Color::LightRed => (255, 128, 128),
            Color::LightGreen => (128, 255, 128),
            Color::LightYellow => (255, 255, 128),
            Color::LightBlue => (128, 128, 255),
            Color::LightMagenta => (255, 128, 255),
            Color::LightCyan => (128, 255, 255),
            Color::White => (255, 255, 255),
            _ => (128, 128, 128), // Default for indexed colors
        }
    }
}

/// WCAG accessibility levels
#[derive(Debug, Clone, Copy)]
pub enum AccessibilityLevel {
    AA,       // 4.5:1 contrast ratio
    AAA,      // 7:1 contrast ratio
    AALarge,  // 3:1 contrast ratio for large text
    AAALarge, // 4.5:1 contrast ratio for large text
}

impl ColorPalette {
    /// Create a dark theme palette
    pub fn dark() -> Self {
        Self {
            primary: Color::Rgb(100, 149, 237),        // Cornflower blue
            secondary: Color::Rgb(147, 112, 219),      // Medium slate blue
            accent: Color::Rgb(255, 165, 0),           // Orange
            background: Color::Rgb(16, 16, 16),        // Very dark gray
            surface: Color::Rgb(32, 32, 32),           // Dark gray
            text: Color::Rgb(255, 255, 255),           // White
            text_secondary: Color::Rgb(170, 170, 170), // Light gray
            border: Color::Rgb(64, 64, 64),            // Medium gray
            success: Color::Rgb(34, 197, 94),          // Green
            warning: Color::Rgb(251, 191, 36),         // Yellow
            error: Color::Rgb(239, 68, 68),            // Red
            info: Color::Rgb(59, 130, 246),            // Blue
        }
    }

    /// Create a light theme palette
    pub fn light() -> Self {
        Self {
            primary: Color::Rgb(59, 130, 246),         // Blue
            secondary: Color::Rgb(139, 92, 246),       // Purple
            accent: Color::Rgb(245, 101, 101),         // Red
            background: Color::Rgb(255, 255, 255),     // White
            surface: Color::Rgb(248, 250, 252),        // Very light gray
            text: Color::Rgb(15, 23, 42),              // Very dark blue
            text_secondary: Color::Rgb(100, 116, 139), // Slate gray
            border: Color::Rgb(203, 213, 225),         // Light gray
            success: Color::Rgb(34, 197, 94),          // Green
            warning: Color::Rgb(251, 191, 36),         // Yellow
            error: Color::Rgb(239, 68, 68),            // Red
            info: Color::Rgb(59, 130, 246),            // Blue
        }
    }

    /// Create a high contrast palette for accessibility
    pub fn high_contrast() -> Self {
        Self {
            primary: Color::White,
            secondary: Color::Yellow,
            accent: Color::Cyan,
            background: Color::Black,
            surface: Color::Black,
            text: Color::White,
            text_secondary: Color::White,
            border: Color::White,
            success: Color::Green,
            warning: Color::Yellow,
            error: Color::Red,
            info: Color::Cyan,
        }
    }
}

impl Default for ColorPalette {
    fn default() -> Self {
        Self::dark()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_from_hex() {
        assert_eq!(ColorUtils::from_hex("#ff0000"), Some(Color::Rgb(255, 0, 0)));
        assert_eq!(ColorUtils::from_hex("00ff00"), Some(Color::Rgb(0, 255, 0)));
        assert_eq!(ColorUtils::from_hex("#invalid"), None);
    }

    #[test]
    fn test_to_hex() {
        assert_eq!(ColorUtils::to_hex(Color::Rgb(255, 0, 0)), "#ff0000");
        assert_eq!(ColorUtils::to_hex(Color::Black), "#000000");
        assert_eq!(ColorUtils::to_hex(Color::White), "#ffffff");
    }

    #[test]
    fn test_contrast_ratio() {
        let ratio = ColorUtils::contrast_ratio(Color::Black, Color::White);
        assert!(ratio > 20.0); // Should be 21:1 for perfect black/white

        let ratio = ColorUtils::contrast_ratio(Color::Black, Color::Black);
        assert_eq!(ratio, 1.0); // Same colors have 1:1 ratio
    }

    #[test]
    fn test_accessibility() {
        assert!(ColorUtils::is_accessible(
            Color::Black,
            Color::White,
            AccessibilityLevel::AAA
        ));
        assert!(!ColorUtils::is_accessible(
            Color::Gray,
            Color::DarkGray,
            AccessibilityLevel::AA
        ));
    }

    #[test]
    fn test_gradient() {
        let gradient = ColorUtils::gradient(Color::Black, Color::White, 3);
        assert_eq!(gradient.len(), 3);
        assert_eq!(gradient[0], Color::Black);
        assert_eq!(gradient[2], Color::White);
    }
}
