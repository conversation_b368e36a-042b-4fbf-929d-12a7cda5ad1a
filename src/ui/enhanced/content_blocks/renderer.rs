// Content block renderer
// Handles rendering of different content block types

use super::{ContentBlock, ContentBlockType};
use crate::errors::Result;
use ratatui::prelude::*;
use ratatui::widgets::{Block, Borders, Paragraph, Wrap};
use std::collections::HashMap;

/// Content block renderer trait
pub trait ContentBlockRenderer: Send + Sync {
    fn render(&self, block: &ContentBlock, area: Rect, frame: &mut Frame<'_>);
    fn get_height(&self, block: &ContentBlock, width: u16) -> u16;
    fn handle_interaction(
        &self,
        block: &ContentBlock,
        event: &crossterm::event::Event,
    ) -> Option<crate::ui::enhanced::Action>;
}

/// Registry for content block renderers
pub struct BlockRendererRegistry {
    renderers: HashMap<String, Box<dyn ContentBlockRenderer>>,
}

/// Render context for content blocks
pub struct RenderContext {
    pub theme: crate::ui::enhanced::config::Theme,
    pub viewport_width: u16,
    pub viewport_height: u16,
    pub is_focused: bool,
}

impl BlockRendererRegistry {
    pub fn new() -> Self {
        Self {
            renderers: HashMap::new(),
        }
    }

    pub fn register_renderer(
        &mut self,
        block_type: String,
        renderer: Box<dyn ContentBlockRenderer>,
    ) {
        self.renderers.insert(block_type, renderer);
    }

    pub fn get_renderer(&self, block_type: &str) -> Option<&dyn ContentBlockRenderer> {
        self.renderers.get(block_type).map(|r| r.as_ref())
    }
}

/// Default content block renderer
pub struct DefaultContentBlockRenderer;

impl ContentBlockRenderer for DefaultContentBlockRenderer {
    fn render(&self, block: &ContentBlock, area: Rect, frame: &mut Frame<'_>) {
        let style = block.get_border_style();
        let title = format!(
            "{} - {}",
            block.get_type_name(),
            block.timestamp.format("%H:%M:%S")
        );

        let content = Paragraph::new(block.content.as_str())
            .style(Style::default())
            .wrap(Wrap { trim: true })
            .block(
                Block::default()
                    .borders(Borders::ALL)
                    .title(title)
                    .border_style(style),
            );

        frame.render_widget(content, area);
    }

    fn get_height(&self, block: &ContentBlock, width: u16) -> u16 {
        let lines = block.content.lines().count() as u16;
        let wrapped_lines = if width > 4 {
            block
                .content
                .lines()
                .map(|line| ((line.len() as u16).saturating_sub(1) / (width - 4)) + 1)
                .sum::<u16>()
        } else {
            lines
        };

        wrapped_lines + 2 // +2 for borders
    }

    fn handle_interaction(
        &self,
        _block: &ContentBlock,
        _event: &crossterm::event::Event,
    ) -> Option<crate::ui::enhanced::Action> {
        None
    }
}

impl Default for BlockRendererRegistry {
    fn default() -> Self {
        Self::new()
    }
}
