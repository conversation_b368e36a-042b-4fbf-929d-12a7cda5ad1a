// Content block implementation
// Basic implementation for content blocks

use super::{ContentBlockType, ContentMetadata, RenderState};
use chrono::{DateTime, Utc};
use ratatui::prelude::*;
use uuid::Uuid;

/// Content block implementation
#[derive(Debug, <PERSON>lone, PartialEq)]
pub struct ContentBlock {
    pub id: Uuid,
    pub block_type: ContentBlockType,
    pub content: String,
    pub metadata: ContentMetadata,
    pub timestamp: DateTime<Utc>,
    pub render_state: RenderState,
    pub is_selected: bool,
    pub is_focused: bool,
    pub is_highlighted: bool,
    pub is_visible: bool,
}

impl ContentBlock {
    pub fn new(block_type: ContentBlockType, content: String) -> Self {
        let metadata = ContentMetadata {
            content_length: content.len(),
            line_count: content.lines().count(),
            word_count: content.split_whitespace().count(),
            ..Default::default()
        };

        Self {
            id: Uuid::new_v4(),
            block_type,
            content,
            metadata,
            timestamp: Utc::now(),
            render_state: RenderState::default(),
            is_selected: false,
            is_focused: false,
            is_highlighted: false,
            is_visible: true,
        }
    }

    pub fn user_message(content: String) -> Self {
        Self::new(ContentBlockType::UserMessage, content)
    }

    pub fn assistant_response(content: String) -> Self {
        Self::new(ContentBlockType::AssistantResponse, content)
    }

    pub fn code_block(content: String, language: Option<String>) -> Self {
        Self::new(
            ContentBlockType::CodeBlock {
                language,
                syntax_highlighted: false,
            },
            content,
        )
    }

    pub fn tool_output(content: String, tool_name: String, success: bool) -> Self {
        Self::new(
            ContentBlockType::ToolOutput {
                tool_name,
                success,
                execution_time: None,
            },
            content,
        )
    }

    pub fn error(
        content: String,
        error_type: super::ErrorType,
        severity: super::ErrorSeverity,
    ) -> Self {
        Self::new(
            ContentBlockType::Error {
                error_type,
                severity,
            },
            content,
        )
    }

    pub fn system_message(content: String) -> Self {
        Self::new(ContentBlockType::SystemMessage, content)
    }

    pub fn get_type_name(&self) -> &'static str {
        match self.block_type {
            ContentBlockType::UserMessage => "User",
            ContentBlockType::AssistantResponse => "Assistant",
            ContentBlockType::CodeBlock { .. } => "Code",
            ContentBlockType::ToolOutput { .. } => "Tool",
            ContentBlockType::Error { .. } => "Error",
            ContentBlockType::SystemMessage => "System",
            ContentBlockType::Widget { .. } => "Widget",
        }
    }

    pub fn get_color(&self) -> Color {
        match self.block_type {
            ContentBlockType::UserMessage => Color::Blue,
            ContentBlockType::AssistantResponse => Color::Green,
            ContentBlockType::CodeBlock { .. } => Color::Magenta,
            ContentBlockType::ToolOutput { success: true, .. } => Color::Yellow,
            ContentBlockType::ToolOutput { success: false, .. } => Color::Red,
            ContentBlockType::Error {
                severity: super::ErrorSeverity::Critical,
                ..
            } => Color::Red,
            ContentBlockType::Error {
                severity: super::ErrorSeverity::Error,
                ..
            } => Color::LightRed,
            ContentBlockType::Error {
                severity: super::ErrorSeverity::Warning,
                ..
            } => Color::Yellow,
            ContentBlockType::Error {
                severity: super::ErrorSeverity::Info,
                ..
            } => Color::Cyan,
            ContentBlockType::SystemMessage => Color::Gray,
            ContentBlockType::Widget { .. } => Color::White,
        }
    }

    pub fn get_border_style(&self) -> Style {
        let color = self.get_color();
        let mut style = Style::default().fg(color);

        if self.is_focused {
            style = style.add_modifier(Modifier::BOLD);
        }

        if self.is_selected {
            style = style.bg(color.into()).fg(Color::Black);
        }

        style
    }
}
