// Content blocks module for enhanced TUI
// Provides navigable content blocks with different types and visual distinctions

pub mod block;
pub mod navigator;
pub mod renderer;
pub mod types;

pub use navigator::{NavigationCommand, NavigationResult};
pub use renderer::{BlockRendererRegistry, ContentBlockRenderer, RenderContext};
pub use types::{ContentMetadata, RenderState};

use crate::errors::Result;
use crate::ui::enhanced::{Action, NavigationAction};
use chrono::{DateTime, Utc};
use ratatui::prelude::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;

// Re-export types from submodules
pub use block::ContentBlock;
pub use types::{ContentBlockType, ErrorSeverity, ErrorType};

// Re-export ContentNavigator from navigator module
pub use navigator::ContentNavigator;

/// Search state for content navigation
#[derive(Debug, <PERSON><PERSON>, Default)]
pub struct SearchState {
    /// Search query
    pub query: String,

    /// Search results
    pub results: Vec<Uuid>,

    /// Current result index
    pub current_result: Option<usize>,

    /// Case sensitive search
    pub case_sensitive: bool,

    /// Regex search
    pub regex: bool,

    /// Search in content only or include metadata
    pub content_only: bool,
}

/// Filter state for content display
#[derive(Debug, Clone, Default, PartialEq)]
pub struct FilterState {
    /// Block type filter
    pub block_types: Option<Vec<ContentBlockType>>,

    /// Tag filter
    pub tags: Option<Vec<String>>,

    /// Author filter
    pub author: Option<String>,

    /// Date range filter
    pub date_range: Option<(DateTime<Utc>, DateTime<Utc>)>,

    /// Custom filter function
    pub custom_filter: Option<String>, // Serialized filter expression
}
