// Content block types and definitions

use serde::{Deserialize, Serialize};

/// Content block types with specific properties
#[derive(Debug, <PERSON><PERSON>, PartialEq, Serialize, Deserialize)]
pub enum ContentBlockType {
    UserMessage,
    AssistantResponse,
    CodeBlock {
        language: Option<String>,
        syntax_highlighted: bool,
    },
    ToolOutput {
        tool_name: String,
        success: bool,
        execution_time: Option<u64>,
    },
    Error {
        error_type: ErrorType,
        severity: ErrorSeverity,
    },
    SystemMessage,
    Widget {
        widget_id: String,
        widget_type: String,
    },
}

/// Error types for error blocks
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ErrorType {
    Validation,
    Execution,
    Network,
    Permission,
    Timeout,
    Unknown,
}

/// Error severity levels
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ErrorSeverity {
    Info,
    Warning,
    Error,
    Critical,
}

/// Content metadata structure
#[derive(Debug, <PERSON>lone, Default, Part<PERSON>Eq, Serialize, Deserialize)]
pub struct ContentMetadata {
    /// Author/source of the content
    pub author: Option<String>,

    /// Tags for categorization
    pub tags: Vec<String>,

    /// Custom properties
    pub properties: std::collections::HashMap<String, String>,

    /// Content statistics
    pub content_length: usize,
    pub line_count: usize,
    pub word_count: usize,
}

/// Render state for content blocks
#[derive(Debug, Clone, Default, PartialEq)]
pub struct RenderState {
    /// Calculated height
    pub height: u16,

    /// Calculated width
    pub width: u16,

    /// Scroll position within block
    pub scroll_offset: u16,

    /// Whether content is wrapped
    pub is_wrapped: bool,

    /// Whether content is truncated
    pub is_truncated: bool,

    /// Last render area
    pub last_area: Option<ratatui::prelude::Rect>,

    /// Dirty flag for re-rendering
    pub is_dirty: bool,
}
