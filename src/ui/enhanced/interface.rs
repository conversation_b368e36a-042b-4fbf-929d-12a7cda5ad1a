// Enhanced TUI interface implementation
// Main interface that integrates all enhanced TUI components

use super::config::ConfigManager;
use super::input::completion::{
    CommandCompletionProvider, CompletionContext, CompletionEngine, MentionCompletionProvider,
};
use super::input::repl::ReplInterface;
use super::{
    Action, ComponentRegistry, ContentNavigator, FocusTarget, Mode, PerformanceMetrics, TuiState,
};
use crate::core::AppCore;
use crate::errors::Result;
use crossterm::event::{Event, KeyCode, KeyEvent, KeyModifiers, MouseEvent};
use ratatui::prelude::*;
use ratatui::widgets::{Block, Borders, Clear, List, ListItem, ListState, Paragraph, Wrap};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{mpsc, Mutex};
use tui_textarea::TextArea;
use uuid::Uuid;

/// Enhanced TUI interface that provides modern coding-focused experience
pub struct EnhancedTuiInterface {
    /// Current TUI state
    state: TuiState,

    /// Content navigator for managing content blocks
    content_navigator: ContentNavigator,

    /// REPL interface for command input
    repl_interface: ReplInterface,

    /// Component registry for UI components
    component_registry: ComponentRegistry,

    /// Configuration manager
    config_manager: Arc<Mutex<ConfigManager>>,

    /// Connection to app core
    app_core: Arc<Mutex<AppCore>>,

    /// Message channels
    message_tx: mpsc::Sender<crate::core::AppCoreMessage>,
    message_rx: mpsc::Receiver<crate::core::AppCoreMessage>,

    /// Completion engine for auto-completion
    completion_engine: CompletionEngine,

    /// Text area widget for input
    text_area: tui_textarea::TextArea<'static>,

    /// Performance monitoring
    performance_metrics: PerformanceMetrics,

    /// Last render time
    last_render: Instant,

    /// Frame counter
    frame_count: u64,

    /// Event processing times
    event_times: Vec<Duration>,

    /// Whether to show log panel
    show_logs: bool,
}

impl EnhancedTuiInterface {
    /// Create a new enhanced TUI interface
    pub async fn new(
        config: super::config::TuiConfig,
        vim_mode: bool,
        accessibility: bool,
        debug_mode: bool,
        show_logs: bool,
    ) -> Result<Self> {
        Self::new_with_channels(config, vim_mode, accessibility, debug_mode, show_logs, None).await
    }

    /// Create a new enhanced TUI interface with external message channels
    pub async fn new_with_channels(
        config: super::config::TuiConfig,
        vim_mode: bool,
        accessibility: bool,
        debug_mode: bool,
        show_logs: bool,
        message_channels: Option<(
            mpsc::Sender<crate::core::AppCoreMessage>,
            mpsc::Receiver<crate::core::AppCoreMessage>,
        )>,
    ) -> Result<Self> {
        // Initialize configuration manager
        let config_dir = super::config::utils::get_default_config_dir()?;
        super::config::utils::ensure_config_dir(&config_dir).await?;
        let config_manager = Arc::new(Mutex::new(ConfigManager::new(config_dir).await?));

        // Initialize content navigator
        let content_navigator = ContentNavigator::new();

        // Initialize REPL interface
        let repl_config = super::input::repl::ReplConfig {
            vim_mode,
            auto_completion: config.ui.auto_completion,
            syntax_highlighting: config.ui.syntax_highlighting,
            multiline_input: true,
            history_size: config.performance.max_history_entries,
            auto_save_history: true,
            prompt_format: "❯ ".to_string(),
            tab_size: config.ui.tab_size as usize,
            auto_indent: true,
            show_line_numbers: config.ui.show_line_numbers,
            execution_timeout: 30,
        };
        let repl_interface = ReplInterface::new(repl_config);

        // Initialize component registry
        let component_factory = Arc::new(super::components::ComponentFactory::new());
        let component_registry = ComponentRegistry::new(component_factory);

        // Create or use provided message channels
        let (message_tx, message_rx) = if let Some((tx, rx)) = message_channels {
            (tx, rx)
        } else {
            mpsc::channel(1000)
        };

        // Initialize state
        let mut state = TuiState::new();
        state.config.theme = config.theme;
        state.config.keybindings = config.keybindings;
        state.config.preferences.vim_mode = vim_mode;
        state.config.preferences.screen_reader = accessibility;
        state.debug.overlay_visible = debug_mode;

        // Create completion engine
        let mut completion_engine = CompletionEngine::new();
        completion_engine.register_provider(Box::new(CommandCompletionProvider::new()));
        completion_engine.register_provider(Box::new(MentionCompletionProvider::new()));

        // Create placeholder app core (will be set later)
        let app_core = Arc::new(Mutex::new(
            AppCore::new(
                crate::config::Config::default(),
                None,
                Arc::new(crate::tools::ToolRegistry::new()),
                Arc::new(tokio::sync::RwLock::new(
                    crate::mcp::client::McpClientManager::new(),
                )),
            )
            .await?,
        ));

        Ok(Self {
            state,
            content_navigator,
            repl_interface,
            component_registry,
            config_manager,
            app_core,
            message_tx,
            message_rx,
            completion_engine,
            text_area: {
                let mut textarea = TextArea::default();
                textarea.set_cursor_line_style(Style::default());
                textarea
                    .set_placeholder_text("Type your message here... (Shift+Enter for new line)");
                textarea
            },
            performance_metrics: PerformanceMetrics::default(),
            last_render: Instant::now(),
            frame_count: 0,
            event_times: Vec::new(),
            show_logs,
        })
    }

    /// Set the app core reference
    pub fn set_app_core(&mut self, app_core: Arc<Mutex<AppCore>>) {
        self.app_core = app_core;
    }

    /// Get a clone of the message sender for external use
    pub fn get_message_sender(&self) -> mpsc::Sender<crate::core::AppCoreMessage> {
        self.message_tx.clone()
    }

    /// Handle input events
    pub async fn handle_event(&mut self, event: Event) -> Result<Option<Action>> {
        let start_time = Instant::now();

        // Update performance metrics
        self.performance_metrics.event_processing_time = start_time.elapsed();

        let result = match event {
            Event::Key(key_event) => self.handle_key_event(key_event).await,
            Event::Mouse(mouse_event) => self.handle_mouse_event(mouse_event).await,
            Event::Resize(width, height) => self.handle_resize(width, height).await,
            _ => Ok(None),
        };

        // Record event processing time
        let processing_time = start_time.elapsed();
        self.event_times.push(processing_time);

        // Keep only recent event times for averaging
        if self.event_times.len() > 100 {
            self.event_times.remove(0);
        }

        // Update debug information
        if self.state.debug.overlay_visible {
            self.state.add_debug_event(
                "event_processed".to_string(),
                format!("Event processed in {:?}", processing_time),
                Some(format!("{:?}", event)),
            );
        }

        result
    }

    /// Handle keyboard events
    async fn handle_key_event(&mut self, key: KeyEvent) -> Result<Option<Action>> {
        // Handle global keybindings first
        if let Some(action) = self.handle_global_keybindings(&key).await? {
            return Ok(Some(action));
        }

        // Handle mode-specific input
        // Only use VIM modes if VIM mode is enabled
        if self.state.config.preferences.vim_mode {
            match self.state.mode {
                Mode::Normal => self.handle_normal_mode_key(key).await,
                Mode::Insert => self.handle_insert_mode_key(key).await,
                Mode::Visual => self.handle_visual_mode_key(key).await,
                Mode::Command => self.handle_command_mode_key(key).await,
                Mode::Widget => self.handle_widget_mode_key(key).await,
            }
        } else {
            // When VIM mode is disabled, only handle Insert mode
            match self.state.mode {
                Mode::Insert => self.handle_insert_mode_key(key).await,
                Mode::Command => self.handle_command_mode_key(key).await,
                _ => {
                    // Force back to insert mode if somehow in a VIM-only mode
                    self.state.mode = Mode::Insert;
                    self.state.focus = FocusTarget::InputArea;
                    self.handle_insert_mode_key(key).await
                }
            }
        }
    }

    /// Handle global keybindings
    async fn handle_global_keybindings(&mut self, key: &KeyEvent) -> Result<Option<Action>> {
        use crossterm::event::{KeyCode, KeyModifiers};

        match (key.code, key.modifiers) {
            // Exit application
            (KeyCode::Char('c'), KeyModifiers::CONTROL) => {
                Ok(Some(Action::System(super::SystemAction::Exit)))
            }

            // Toggle debug overlay
            (KeyCode::F(12), _) => {
                self.state.debug.overlay_visible = !self.state.debug.overlay_visible;
                Ok(None)
            }

            // Reload configuration
            (KeyCode::F(5), _) => {
                let mut config_manager = self.config_manager.lock().await;
                config_manager.check_for_changes().await?;
                Ok(Some(Action::System(super::SystemAction::ReloadConfig)))
            }

            _ => Ok(None),
        }
    }

    /// Handle normal mode keys
    async fn handle_normal_mode_key(&mut self, key: KeyEvent) -> Result<Option<Action>> {
        use crossterm::event::KeyCode;

        match key.code {
            KeyCode::Char('i') => {
                self.state.mode = Mode::Insert;
                self.state.focus = FocusTarget::InputArea;
                Ok(None)
            }
            KeyCode::Char('v') => {
                self.state.mode = Mode::Visual;
                Ok(None)
            }
            KeyCode::Char(':') => {
                self.state.mode = Mode::Command;
                self.state.focus = FocusTarget::InputArea;
                // Clear input when entering command mode
                self.repl_interface.clear_input();
                Ok(None)
            }
            KeyCode::Char('j') | KeyCode::Down => {
                let result = self
                    .content_navigator
                    .execute_command(super::content_blocks::NavigationCommand::NextBlock);
                self.handle_navigation_result(result).await
            }
            KeyCode::Char('k') | KeyCode::Up => {
                let result = self
                    .content_navigator
                    .execute_command(super::content_blocks::NavigationCommand::PreviousBlock);
                self.handle_navigation_result(result).await
            }
            KeyCode::Char('g') => {
                let result = self
                    .content_navigator
                    .execute_command(super::content_blocks::NavigationCommand::FirstBlock);
                self.handle_navigation_result(result).await
            }
            KeyCode::Char('G') => {
                let result = self
                    .content_navigator
                    .execute_command(super::content_blocks::NavigationCommand::LastBlock);
                self.handle_navigation_result(result).await
            }
            KeyCode::Char(' ') => {
                let result = self
                    .content_navigator
                    .execute_command(super::content_blocks::NavigationCommand::ToggleSelection);
                self.handle_navigation_result(result).await
            }
            KeyCode::Char('/') => {
                self.state.mode = Mode::Command;
                self.state.focus = FocusTarget::InputArea;
                // TODO: Start search mode
                Ok(None)
            }
            _ => Ok(None),
        }
    }

    /// Handle insert mode keys
    async fn handle_insert_mode_key(&mut self, key: KeyEvent) -> Result<Option<Action>> {
        use crossterm::event::{KeyCode, KeyModifiers};

        // Handle completion navigation first if active
        if self.completion_engine.is_active() {
            match (key.code, key.modifiers) {
                (KeyCode::Up, _) => {
                    self.completion_engine.move_selection_up();
                    return Ok(None);
                }
                (KeyCode::Down, _) => {
                    self.completion_engine.move_selection_down();
                    return Ok(None);
                }
                (KeyCode::Tab, _) => {
                    if let Some(item) = self.completion_engine.accept_selection() {
                        self.apply_completion(item).await?;
                    }
                    return Ok(None);
                }
                (KeyCode::Esc, _) => {
                    self.completion_engine.cancel();
                    return Ok(None);
                }
                _ => {} // Continue to normal handling
            }
        }

        match (key.code, key.modifiers) {
            (KeyCode::Esc, _) => {
                if self.state.config.preferences.vim_mode {
                    self.state.mode = Mode::Normal;
                    self.state.focus = FocusTarget::ContentArea;
                }
                Ok(None)
            }
            (KeyCode::Enter, KeyModifiers::NONE) => {
                // Submit input if not empty
                let input_text = self
                    .text_area
                    .lines()
                    .join(
                        "
",
                    )
                    .trim()
                    .to_string();

                if !input_text.is_empty() {
                    tracing::info!("Enhanced TUI: Enter key pressed - submitting user input");
                    tracing::debug!(
                        "Enhanced TUI: Input mode: {:?}, raw input length: {}",
                        self.state.mode,
                        input_text.len()
                    );

                    // Remove command prefix if in command mode
                    let final_input =
                        if self.state.mode == Mode::Command && input_text.starts_with(':') {
                            tracing::debug!("Enhanced TUI: Removing command prefix from input");
                            input_text[1..].to_string()
                        } else {
                            input_text
                        };

                    self.process_user_input(final_input).await?;

                    // Clear the text area
                    self.text_area.select_all();
                    self.text_area.cut();

                    // Return to insert mode if was in command mode
                    if self.state.mode == Mode::Command {
                        self.state.mode = Mode::Insert;
                    }
                }
                Ok(None)
            }
            (KeyCode::Enter, KeyModifiers::SHIFT) => {
                // Insert newline for multi-line input
                self.text_area.insert_newline();
                Ok(None)
            }
            (KeyCode::Char(':'), _) if self.state.config.preferences.vim_mode => {
                // Enter command mode in VIM
                self.state.mode = Mode::Command;
                self.text_area.insert_char(':');
                Ok(None)
            }
            (KeyCode::Char(c), _) if c == '@' || c == '/' => {
                // Insert character and trigger completion
                self.text_area.insert_char(c);
                self.trigger_completion().await?;
                Ok(None)
            }
            _ => {
                // Pass other keys to text area
                self.text_area.input(key);

                // Update completion filter if active
                if self.completion_engine.is_active() {
                    self.update_completion_filter().await?;
                }

                Ok(None)
            }
        }
    }

    /// Handle visual mode keys
    async fn handle_visual_mode_key(&mut self, _key: KeyEvent) -> Result<Option<Action>> {
        // TODO: Implement visual mode
        Ok(None)
    }

    /// Handle command mode keys
    async fn handle_command_mode_key(&mut self, key: KeyEvent) -> Result<Option<Action>> {
        use crossterm::event::{KeyCode, KeyModifiers};

        match (key.code, key.modifiers) {
            (KeyCode::Esc, _) => {
                // Exit command mode and return to insert mode
                self.state.mode = Mode::Insert;
                self.state.focus = FocusTarget::InputArea;
                // Clear any command input
                self.repl_interface.clear_input();
                Ok(None)
            }
            (KeyCode::Enter, KeyModifiers::NONE) => {
                // Execute command
                if let Ok(command) = self.repl_interface.submit() {
                    if !command.trim().is_empty() {
                        // Execute the command
                        self.execute_command(command).await?;
                    }
                }
                // Return to insert mode
                self.state.mode = Mode::Insert;
                self.state.focus = FocusTarget::InputArea;
                Ok(None)
            }
            _ => {
                // Pass to REPL interface for character input
                let event = crossterm::event::Event::Key(key);
                match self.repl_interface.handle_event(&event)? {
                    super::input::InputResult::Handled => Ok(None),
                    _ => Ok(None),
                }
            }
        }
    }

    /// Handle widget mode keys
    async fn handle_widget_mode_key(&mut self, key: KeyEvent) -> Result<Option<Action>> {
        use crossterm::event::KeyCode;

        match key.code {
            KeyCode::Esc => {
                self.state.mode = Mode::Normal;
                self.state.focus = FocusTarget::ContentArea;
                Ok(None)
            }
            KeyCode::Tab => {
                // Cycle through widgets
                // TODO: Implement widget cycling
                Ok(None)
            }
            _ => {
                // Pass to component registry
                let event = Event::Key(key);
                match self.component_registry.handle_event_for_focused(&event) {
                    super::ComponentResult::Action(action) => Ok(action),
                    _ => Ok(None),
                }
            }
        }
    }

    /// Handle mouse events
    async fn handle_mouse_event(&mut self, _mouse: MouseEvent) -> Result<Option<Action>> {
        // TODO: Implement mouse handling
        Ok(None)
    }

    /// Handle resize events
    async fn handle_resize(&mut self, width: u16, height: u16) -> Result<Option<Action>> {
        self.state.navigation.viewport_height = height.saturating_sub(6); // Account for UI elements
        self.content_navigator
            .set_viewport_height(self.state.navigation.viewport_height);
        Ok(None)
    }

    /// Handle navigation result
    async fn handle_navigation_result(
        &mut self,
        result: super::content_blocks::NavigationResult,
    ) -> Result<Option<Action>> {
        match result {
            super::content_blocks::NavigationResult::Success => Ok(None),
            super::content_blocks::NavigationResult::SuccessWithAction(action) => Ok(Some(action)),
            super::content_blocks::NavigationResult::SearchCompleted { results, current } => {
                self.state.navigation.search.results = vec![]; // TODO: Update with actual results
                Ok(None)
            }
            _ => Ok(None),
        }
    }

    /// Execute a command
    async fn execute_command(&mut self, command: String) -> Result<()> {
        // Add command to content as a system message
        let command_block =
            super::content_blocks::ContentBlock::system_message(format!("Command: {}", command));
        self.content_navigator.add_block(command_block);

        // TODO: Implement actual command execution
        // For now, just acknowledge the command
        let response_block = super::content_blocks::ContentBlock::system_message(format!(
            "Command '{}' executed",
            command
        ));
        self.content_navigator.add_block(response_block);

        Ok(())
    }

    /// Process user input
    async fn process_user_input(&mut self, input: String) -> Result<()> {
        tracing::info!(
            "Enhanced TUI: Processing user input - length: {} chars",
            input.len()
        );
        tracing::debug!(
            "Enhanced TUI: User input content: {}",
            if input.len() > 100 {
                format!("{}...", &input[..100])
            } else {
                input.clone()
            }
        );

        // Add user message to content
        tracing::debug!("Enhanced TUI: Adding user message block to content navigator");
        let user_block = super::content_blocks::ContentBlock::user_message(input.clone());
        self.content_navigator.add_block(user_block);
        tracing::info!(
            "Enhanced TUI: User message block added, total blocks: {}",
            self.content_navigator.block_count()
        );

        // Send to app core
        tracing::debug!("Enhanced TUI: Sending UserInput message to agent core");
        let message = crate::core::AppCoreMessage::UserInput(input.clone());
        if let Err(e) = self.message_tx.send(message).await {
            tracing::error!("Enhanced TUI: Failed to send user input to agent: {}", e);
            return Err(crate::errors::AutorunError::Io(std::io::Error::new(
                std::io::ErrorKind::BrokenPipe,
                format!("Failed to send user input: {}", e),
            )));
        }
        tracing::info!("Enhanced TUI: User input sent to agent successfully");

        // Update execution status
        tracing::debug!("Enhanced TUI: Setting execution status to Processing");
        self.state.execution = super::state::ExecutionStatus::Processing {
            message: "Processing user input...".to_string(),
            progress: None,
            started_at: chrono::Utc::now(),
        };

        Ok(())
    }

    /// Render the interface
    pub async fn render(&mut self, frame: &mut Frame<'_>) -> Result<()> {
        let start_time = Instant::now();

        // Update performance metrics
        self.performance_metrics.frame_time = self.last_render.elapsed();
        self.last_render = Instant::now();
        self.frame_count += 1;

        // Main layout - conditional based on show_logs flag
        let (main_chunks, log_area) = if self.show_logs {
            // Horizontal split with log panel
            let horizontal_chunks = Layout::default()
                .direction(Direction::Horizontal)
                .constraints([
                    Constraint::Percentage(70), // Main content area (left)
                    Constraint::Percentage(30), // Log area (right)
                ])
                .split(frame.area());

            // Left side layout (main content)
            let main_chunks = Layout::default()
                .direction(Direction::Vertical)
                .constraints([
                    Constraint::Min(10),   // Content area
                    Constraint::Length(5), // Input area
                    Constraint::Length(1), // Status bar
                ])
                .split(horizontal_chunks[0]);

            (main_chunks, Some(horizontal_chunks[1]))
        } else {
            // Full width layout without log panel
            let main_chunks = Layout::default()
                .direction(Direction::Vertical)
                .constraints([
                    Constraint::Min(10),   // Content area
                    Constraint::Length(5), // Input area
                    Constraint::Length(1), // Status bar
                ])
                .split(frame.area());

            (main_chunks, None)
        };

        // Render content area
        self.render_content_area(frame, main_chunks[0]).await?;

        // Render input area
        self.render_input_area(frame, main_chunks[1]).await?;

        // Render status bar
        self.render_status_bar(frame, main_chunks[2]).await?;

        // Render log area if enabled
        if let Some(log_area) = log_area {
            self.render_log_area(frame, log_area).await?;
        }

        // Render debug overlay if enabled
        if self.state.debug.overlay_visible {
            self.render_debug_overlay(frame).await?;
        }

        // Update render time
        self.performance_metrics.render_time = start_time.elapsed();

        Ok(())
    }

    /// Render content area
    async fn render_content_area(&mut self, frame: &mut Frame<'_>, area: Rect) -> Result<()> {
        let visible_blocks = self.content_navigator.get_visible_blocks();

        if visible_blocks.is_empty() {
            // Show welcome message
            let welcome = Paragraph::new(
                "Welcome to AutoRun Enhanced TUI\n\nStart typing to chat with the AI assistant!",
            )
            .style(Style::default().fg(Color::Gray))
            .alignment(Alignment::Center)
            .block(
                Block::default()
                    .borders(Borders::ALL)
                    .title("Content")
                    .border_style(Style::default().fg(Color::Blue)),
            );

            frame.render_widget(welcome, area);
            return Ok(());
        }

        // Render content blocks
        let block_height = 3; // Minimum height per block
        let visible_count = (area.height.saturating_sub(2) / block_height) as usize;
        let start_index = self.state.navigation.scroll_offset;
        let end_index = (start_index + visible_count).min(visible_blocks.len());

        let block_areas = Layout::default()
            .direction(Direction::Vertical)
            .constraints(
                (start_index..end_index)
                    .map(|_| Constraint::Length(block_height))
                    .collect::<Vec<_>>(),
            )
            .split(area);

        for (i, block) in visible_blocks[start_index..end_index].iter().enumerate() {
            if i < block_areas.len() {
                self.render_content_block(frame, block_areas[i], block)
                    .await?;
            }
        }

        Ok(())
    }

    /// Render individual content block
    async fn render_content_block(
        &self,
        frame: &mut Frame<'_>,
        area: Rect,
        block: &super::content_blocks::ContentBlock,
    ) -> Result<()> {
        // Determine colors based on block type
        let (border_color, header_style) = match block.get_type_name() {
            "User" => (Color::Cyan, Style::default().fg(Color::Cyan).bold()),
            "Assistant" => (Color::Green, Style::default().fg(Color::Green).bold()),
            "Tool" => (Color::Yellow, Style::default().fg(Color::Yellow).bold()),
            "Error" => (Color::Red, Style::default().fg(Color::Red).bold()),
            _ => (Color::Gray, Style::default().fg(Color::Gray)),
        };

        // Create header with metadata
        let header = format!(
            "{} • {} • {} chars",
            block.get_type_name(),
            block.timestamp.format("%H:%M:%S"),
            block.content.len()
        );

        // Render left border
        let border_area = Rect {
            x: area.x,
            y: area.y,
            width: 1,
            height: area.height,
        };

        let border_block = Block::default().style(Style::default().bg(border_color));
        frame.render_widget(border_block, border_area);

        // Content area (excluding the border)
        let content_area = Rect {
            x: area.x + 4,
            y: area.y,
            width: area.width.saturating_sub(4),
            height: area.height,
        };

        // Create layout for header and content
        let layout = Layout::default()
            .direction(Direction::Vertical)
            .constraints([
                Constraint::Length(1), // Header
                Constraint::Min(1),    // Content
            ])
            .split(content_area);

        // Render header
        let header_widget = Paragraph::new(header).style(header_style);
        frame.render_widget(header_widget, layout[0]);

        // Render content (truncated if too long - collapsible behavior)
        let display_content = if block.content.lines().count() > 3 {
            let preview: Vec<&str> = block.content.lines().take(2).collect();
            format!(
                "{}
... [+{} more lines] (click to expand)",
                preview.join(
                    "
"
                ),
                block.content.lines().count() - 2
            )
        } else {
            block.content.clone()
        };

        let content_widget = Paragraph::new(display_content)
            .style(Style::default())
            .wrap(Wrap { trim: true });
        frame.render_widget(content_widget, layout[1]);

        Ok(())
    }

    /// Render input area
    async fn render_input_area(&mut self, frame: &mut Frame<'_>, area: Rect) -> Result<()> {
        let border_style = match self.state.focus {
            FocusTarget::InputArea => Style::default().fg(Color::Cyan),
            _ => Style::default().fg(Color::Gray),
        };

        // Set up text area styling
        let title = if self.state.config.preferences.vim_mode {
            format!("Chat [{}]", self.state.mode.to_string())
        } else {
            "Chat".to_string()
        };

        // Configure text area block
        self.text_area.set_block(
            Block::default()
                .borders(Borders::ALL)
                .title(title)
                .border_style(border_style),
        );

        // Handle command mode prefix
        if self.state.mode == Mode::Command && !self.text_area.lines()[0].starts_with(':') {
            let current_text = self.text_area.lines().join(
                "
",
            );
            self.text_area.delete_line_by_head();
            self.text_area.insert_str(format!(":{}", current_text));
        }

        frame.render_widget(&self.text_area, area);

        // Render completion popup if active
        if self.completion_engine.is_active() {
            self.render_completion_popup(frame, area).await?;
        }

        Ok(())
    }

    /// Render completion popup
    async fn render_completion_popup(
        &mut self,
        frame: &mut Frame<'_>,
        input_area: Rect,
    ) -> Result<()> {
        if let Some(completion_state) = self.completion_engine.get_state() {
            if completion_state.visible && !completion_state.filtered_items.is_empty() {
                // Calculate popup position (below input area)
                let popup_height = (completion_state.filtered_items.len() as u16)
                    .min(10)
                    .max(3);
                let popup_width = input_area.width.saturating_sub(4);

                let popup_area = Rect {
                    x: input_area.x + 2,
                    y: input_area.bottom(),
                    width: popup_width,
                    height: popup_height + 2, // +2 for borders
                };

                // Don't render if popup would be off-screen
                if popup_area.bottom() > frame.area().height {
                    return Ok(());
                }

                // Prepare completion items for display
                let items: Vec<ListItem> = completion_state
                    .filtered_items
                    .iter()
                    .enumerate()
                    .take(10) // Limit to 10 visible items
                    .filter_map(|(i, &item_index)| {
                        completion_state.items.get(item_index).map(|item| {
                            let style = if i == completion_state.selected_index {
                                Style::default().bg(Color::Blue).fg(Color::White)
                            } else {
                                Style::default()
                            };

                            let text = if let Some(ref desc) = item.description {
                                format!("{} - {}", item.text, desc)
                            } else {
                                item.text.clone()
                            };

                            ListItem::new(text).style(style)
                        })
                    })
                    .collect();

                let completion_list = List::new(items).block(
                    Block::default()
                        .borders(Borders::ALL)
                        .title("Completions (Tab to accept, Esc to cancel)")
                        .border_style(Style::default().fg(Color::Green)),
                );

                frame.render_widget(Clear, popup_area);
                frame.render_widget(completion_list, popup_area);
            }
        }

        Ok(())
    }

    /// Render log area with tui-logger widget
    async fn render_log_area(&mut self, frame: &mut Frame<'_>, area: Rect) -> Result<()> {
        // Use tui-logger widget to display live logs
        let tui_widget = tui_logger::TuiLoggerWidget::default()
            .style_error(Style::default().fg(Color::Red))
            .style_debug(Style::default().fg(Color::Green))
            .style_warn(Style::default().fg(Color::Yellow))
            .style_trace(Style::default().fg(Color::Gray))
            .style_info(Style::default().fg(Color::Blue))
            .block(
                Block::default()
                    .title("Live Logs")
                    .borders(Borders::ALL)
                    .border_style(Style::default().fg(Color::Green)),
            )
            .output_separator('|')
            .output_timestamp(Some("%H:%M:%S%.3f".to_string()))
            .output_level(Some(tui_logger::TuiLoggerLevelOutput::Abbreviated))
            .output_target(true)
            .output_file(false)
            .output_line(false);

        frame.render_widget(tui_widget, area);
        Ok(())
    }

    /// Render status bar
    async fn render_status_bar(&mut self, frame: &mut Frame<'_>, area: Rect) -> Result<()> {
        let status_text = match &self.state.execution {
            super::state::ExecutionStatus::Idle => "Ready".to_string(),
            super::state::ExecutionStatus::Processing { message, .. } => message.clone(),
            super::state::ExecutionStatus::Completed { message, .. } => message.clone(),
            super::state::ExecutionStatus::Failed { error, .. } => format!("Error: {}", error),
        };

        let help_text = if self.state.config.preferences.vim_mode {
            match self.state.mode {
                Mode::Normal => " j/k:navigate i:insert v:visual /:search ",
                Mode::Insert => " ESC:normal Enter:send Shift+Enter:newline ",
                Mode::Visual => " ESC:normal y:copy d:delete ",
                Mode::Command => " ESC:cancel Enter:execute ",
                Mode::Widget => " ESC:normal Tab:next-widget ",
            }
        } else {
            match self.state.mode {
                Mode::Insert => " Enter:send Shift+Enter:newline ",
                Mode::Command => " ESC:cancel Enter:execute ",
                _ => " Enter:send Shift+Enter:newline ", // Force back to insert mode help
            }
        };

        let status_bar = Paragraph::new(Line::from(vec![
            Span::styled(
                format!(" {} ", status_text),
                Style::default().bg(Color::DarkGray),
            ),
            Span::raw(" "),
            Span::styled(help_text, Style::default().fg(Color::DarkGray)),
        ]))
        .alignment(Alignment::Left);

        frame.render_widget(status_bar, area);
        Ok(())
    }

    /// Render debug overlay
    async fn render_debug_overlay(&mut self, frame: &mut Frame<'_>) -> Result<()> {
        let area = super::utils_ext::centered_rect(60, 40, frame.area());

        let avg_frame_time = if !self.event_times.is_empty() {
            self.event_times.iter().sum::<Duration>() / self.event_times.len() as u32
        } else {
            Duration::ZERO
        };

        let debug_text = format!(
            "Debug Information\n\n\
            Frame: {} | FPS: {:.1}\n\
            Frame Time: {:.2}ms\n\
            Render Time: {:.2}ms\n\
            Event Time: {:.2}ms\n\
            Content Blocks: {}\n\
            Memory Usage: {}KB\n\
            Mode: {:?}\n\
            Focus: {:?}",
            self.frame_count,
            1000.0 / self.performance_metrics.frame_time.as_millis().max(1) as f64,
            self.performance_metrics.frame_time.as_secs_f64() * 1000.0,
            self.performance_metrics.render_time.as_secs_f64() * 1000.0,
            avg_frame_time.as_secs_f64() * 1000.0,
            self.content_navigator.get_visible_blocks().len(),
            self.performance_metrics.memory_usage / 1024,
            self.state.mode,
            self.state.focus,
        );

        let debug_widget = Paragraph::new(debug_text)
            .style(Style::default().fg(Color::Yellow))
            .block(
                Block::default()
                    .borders(Borders::ALL)
                    .title("Debug Overlay (F12 to toggle)")
                    .border_style(Style::default().fg(Color::Yellow)),
            );

        frame.render_widget(Clear, area);
        frame.render_widget(debug_widget, area);

        Ok(())
    }

    /// Get current state
    pub fn get_state(&self) -> &TuiState {
        &self.state
    }

    /// Check if interface is ready for input
    pub fn is_ready(&self) -> bool {
        self.repl_interface.is_ready()
    }

    /// Process messages from app core
    pub async fn process_messages(&mut self) -> Result<()> {
        let mut message_count = 0;
        while let Ok(message) = self.message_rx.try_recv() {
            message_count += 1;
            tracing::debug!(
                "Enhanced TUI: Processing message #{} from agent",
                message_count
            );

            match message {
                crate::core::AppCoreMessage::AgentResponse(response) => {
                    tracing::info!(
                        "Enhanced TUI: Received agent response - length: {} chars",
                        response.len()
                    );
                    tracing::debug!(
                        "Enhanced TUI: Response content: {}",
                        if response.len() > 150 {
                            format!("{}...", &response[..150])
                        } else {
                            response.clone()
                        }
                    );

                    let response_block =
                        super::content_blocks::ContentBlock::assistant_response(response);

                    tracing::debug!("Enhanced TUI: Adding response block to content navigator");
                    self.content_navigator.add_block(response_block);
                    tracing::info!(
                        "Enhanced TUI: Content block added successfully, total blocks: {}",
                        self.content_navigator.block_count()
                    );

                    self.state.execution = super::state::ExecutionStatus::Completed {
                        message: "Response received".to_string(),
                        duration: Duration::from_millis(100), // TODO: Calculate actual duration
                        completed_at: chrono::Utc::now(),
                    };
                    tracing::debug!("Enhanced TUI: Set execution status to Completed");
                }
                crate::core::AppCoreMessage::Error(error) => {
                    tracing::error!(
                        "Enhanced TUI: Received error message - length: {} chars",
                        error.len()
                    );
                    tracing::error!("Enhanced TUI: Error content: {}", error);

                    let error_block = super::content_blocks::ContentBlock::error(
                        error.clone(),
                        super::content_blocks::ErrorType::Execution,
                        super::content_blocks::ErrorSeverity::Error,
                    );

                    tracing::debug!("Enhanced TUI: Adding error block to content navigator");
                    self.content_navigator.add_block(error_block);
                    tracing::info!(
                        "Enhanced TUI: Error block added, total blocks: {}",
                        self.content_navigator.block_count()
                    );

                    self.state.execution = super::state::ExecutionStatus::Failed {
                        error: "Processing failed".to_string(),
                        failed_at: chrono::Utc::now(),
                    };
                    tracing::debug!("Enhanced TUI: Set execution status to Failed");
                }
                crate::core::AppCoreMessage::ProcessingStatus(status) => {
                    tracing::info!("Enhanced TUI: Processing status update: {}", status);
                    self.state.execution = super::state::ExecutionStatus::Processing {
                        message: status.clone(),
                        progress: None,
                        started_at: chrono::Utc::now(),
                    };
                    tracing::debug!(
                        "Enhanced TUI: Set execution status to Processing: {}",
                        status
                    );
                }
                crate::core::AppCoreMessage::UserInput(_) => {
                    tracing::debug!(
                        "Enhanced TUI: Received UserInput message (handled separately)"
                    );
                }
                _ => {
                    tracing::warn!("Enhanced TUI: Received unhandled message type");
                }
            }
        }

        if message_count > 0 {
            tracing::info!(
                "Enhanced TUI: Processed {} messages in this cycle",
                message_count
            );
        }

        Ok(())
    }

    /// Trigger completion based on current input
    async fn trigger_completion(&mut self) -> Result<()> {
        let input_text = self.text_area.lines().join(
            "
",
        );
        let cursor_position = self.text_area.cursor().1; // Column position
        let context =
            CompletionContext::from_input(&input_text, cursor_position, self.state.mode.clone());

        self.completion_engine.trigger_completion(context).await?;
        Ok(())
    }

    /// Update completion filter based on current input
    async fn update_completion_filter(&mut self) -> Result<()> {
        if self.completion_engine.is_active() {
            let input_text = self.text_area.lines().join(
                "
",
            );
            let cursor_position = self.text_area.cursor().1; // Column position
            let context = CompletionContext::from_input(
                &input_text,
                cursor_position,
                self.state.mode.clone(),
            );
            self.completion_engine.update_filter(&context.query);
        }
        Ok(())
    }

    /// Apply a selected completion item
    async fn apply_completion(
        &mut self,
        item: super::input::completion::CompletionItem,
    ) -> Result<()> {
        // Get current line and cursor position
        let (row, col) = self.text_area.cursor();
        let current_line = &self.text_area.lines()[row];

        // Find the trigger character position (@ or /) before cursor
        let mut trigger_pos = None;
        let chars: Vec<char> = current_line.chars().collect();
        for i in (0..col.min(chars.len())).rev() {
            let c = chars[i];
            if c == '@' || c == '/' {
                trigger_pos = Some(i);
                break;
            }
            if c == ' ' {
                break; // Stop at word boundary
            }
        }

        if let Some(start_pos) = trigger_pos {
            // Delete from trigger position to cursor
            let chars_to_delete = col - start_pos;
            for _ in 0..chars_to_delete {
                self.text_area.delete_char();
            }

            // Insert the completion text
            self.text_area.insert_str(&item.insert_text);
        }

        tracing::debug!("Applied completion: {} -> {}", item.text, item.insert_text);
        Ok(())
    }
}

impl Mode {
    fn to_string(&self) -> &'static str {
        match self {
            Mode::Normal => "NORMAL",
            Mode::Insert => "INSERT",
            Mode::Visual => "VISUAL",
            Mode::Command => "COMMAND",
            Mode::Widget => "WIDGET",
        }
    }
}
