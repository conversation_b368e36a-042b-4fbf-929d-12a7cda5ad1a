// Hot-reloading system for configuration

use crate::errors::Result;
use notify::{Event as NotifyEvent, RecursiveMode, Watcher};
use std::path::PathBuf;
use tokio::sync::mpsc;

/// Configuration event types
#[derive(Debug, Clone)]
pub enum ConfigEvent {
    ConfigChanged,
    ThemeChanged(String),
    KeybindingsChanged,
}

/// Configuration watcher
pub struct ConfigWatcher {
    _watcher: notify::RecommendedWatcher,
    receiver: mpsc::Receiver<ConfigEvent>,
}

/// Hot reload manager
pub struct HotReloadManager {
    config_dir: PathBuf,
    watchers: Vec<ConfigWatcher>,
    event_receiver: Option<mpsc::Receiver<ConfigEvent>>,
}

impl ConfigWatcher {
    pub async fn new(config_dir: PathBuf) -> Result<Self> {
        let (tx, rx) = mpsc::channel(100);

        let mut watcher = notify::recommended_watcher(move |res: notify::Result<NotifyEvent>| {
            if let Ok(event) = res {
                let config_event = match event.kind {
                    notify::EventKind::Modify(_) => {
                        if let Some(path) = event.paths.first() {
                            if let Some(name) = path.file_name().and_then(|n| n.to_str()) {
                                if name == "config.toml" {
                                    ConfigEvent::ConfigChanged
                                } else if name.ends_with(".toml")
                                    && path
                                        .parent()
                                        .and_then(|p| p.file_name())
                                        .and_then(|n| n.to_str())
                                        == Some("themes")
                                {
                                    let theme_name = name.trim_end_matches(".toml");
                                    ConfigEvent::ThemeChanged(theme_name.to_string())
                                } else if name.ends_with(".toml")
                                    && path
                                        .parent()
                                        .and_then(|p| p.file_name())
                                        .and_then(|n| n.to_str())
                                        == Some("keybindings")
                                {
                                    ConfigEvent::KeybindingsChanged
                                } else {
                                    return;
                                }
                            } else {
                                return;
                            }
                        } else {
                            return;
                        }
                    }
                    _ => return,
                };

                let _ = tx.try_send(config_event);
            }
        })?;

        watcher.watch(&config_dir, RecursiveMode::Recursive)?;

        Ok(Self {
            _watcher: watcher,
            receiver: rx,
        })
    }

    pub async fn next_event(&mut self) -> Option<ConfigEvent> {
        self.receiver.recv().await
    }
}

impl HotReloadManager {
    pub async fn new(config_dir: PathBuf) -> Result<Self> {
        Ok(Self {
            config_dir,
            watchers: Vec::new(),
            event_receiver: None,
        })
    }

    pub async fn start(&mut self) -> Result<()> {
        let watcher = ConfigWatcher::new(self.config_dir.clone()).await?;
        self.watchers.push(watcher);
        Ok(())
    }

    pub async fn stop(&mut self) -> Result<()> {
        self.watchers.clear();
        Ok(())
    }

    pub async fn get_events(&mut self) -> Result<Vec<ConfigEvent>> {
        let mut events = Vec::new();

        for watcher in &mut self.watchers {
            while let Ok(event) = watcher.receiver.try_recv() {
                events.push(event);
            }
        }

        Ok(events)
    }
}
