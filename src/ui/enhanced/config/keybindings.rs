// Keybinding configuration system

use crate::errors::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;

/// Keybinding configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct KeybindingConfig {
    pub global: HashMap<String, ActionMapping>,
    pub mode_specific: HashMap<String, HashMap<String, ActionMapping>>,
    pub vim_enabled: bool,
}

/// Key binding definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KeyBinding {
    pub key: String,
    pub action: ActionMapping,
    pub description: String,
}

/// Action mapping for keybindings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ActionMapping {
    Navigate(String),
    Input(String),
    Content(String),
    UI(String),
    System(String),
    Custom(String),
}

/// Keybinding manager
pub struct KeybindingManager {
    keybindings_dir: PathBuf,
    current_config: KeybindingConfig,
}

impl KeybindingManager {
    pub async fn new(keybindings_dir: PathBuf) -> Result<Self> {
        if !keybindings_dir.exists() {
            tokio::fs::create_dir_all(&keybindings_dir).await?;
        }

        let mut manager = Self {
            keybindings_dir,
            current_config: KeybindingConfig::default(),
        };

        manager.create_default_keybindings().await?;

        Ok(manager)
    }

    pub async fn load_default_keybindings(&self) -> Result<KeybindingConfig> {
        let default_path = self.keybindings_dir.join("default.toml");
        if default_path.exists() {
            let content = tokio::fs::read_to_string(&default_path).await?;
            let config: KeybindingConfig = toml::from_str(&content)?;
            Ok(config)
        } else {
            Ok(KeybindingConfig::default())
        }
    }

    pub async fn save_keybindings(&self, config: &KeybindingConfig, name: &str) -> Result<()> {
        let path = self.keybindings_dir.join(format!("{}.toml", name));
        let content = toml::to_string_pretty(config)?;
        tokio::fs::write(path, content).await?;
        Ok(())
    }

    pub async fn validate_keybindings(&self, _config: &KeybindingConfig) -> Result<()> {
        // Basic validation - can be extended
        Ok(())
    }

    async fn create_default_keybindings(&self) -> Result<()> {
        let default_config = KeybindingConfig::default();
        let default_path = self.keybindings_dir.join("default.toml");

        if !default_path.exists() {
            self.save_keybindings(&default_config, "default").await?;
        }

        Ok(())
    }
}

impl Default for KeybindingConfig {
    fn default() -> Self {
        let mut global = HashMap::new();
        global.insert("q".to_string(), ActionMapping::System("quit".to_string()));
        global.insert("?".to_string(), ActionMapping::UI("help".to_string()));

        let mut normal_mode = HashMap::new();
        normal_mode.insert(
            "i".to_string(),
            ActionMapping::UI("enter_insert".to_string()),
        );
        normal_mode.insert(
            "j".to_string(),
            ActionMapping::Navigate("next_block".to_string()),
        );
        normal_mode.insert(
            "k".to_string(),
            ActionMapping::Navigate("previous_block".to_string()),
        );
        normal_mode.insert("/".to_string(), ActionMapping::UI("search".to_string()));

        let mut insert_mode = HashMap::new();
        insert_mode.insert(
            "Escape".to_string(),
            ActionMapping::UI("exit_insert".to_string()),
        );
        insert_mode.insert(
            "Tab".to_string(),
            ActionMapping::Input("complete".to_string()),
        );
        insert_mode.insert(
            "Enter".to_string(),
            ActionMapping::Input("submit".to_string()),
        );

        let mut mode_specific = HashMap::new();
        mode_specific.insert("normal".to_string(), normal_mode);
        mode_specific.insert("insert".to_string(), insert_mode);

        Self {
            global,
            mode_specific,
            vim_enabled: false,
        }
    }
}
