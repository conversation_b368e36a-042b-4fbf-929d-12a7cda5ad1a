// Enhanced TUI module - Modern coding-focused interface
// Provides navigable content blocks, REPL-like functionality, advanced input system,
// and configurable architecture while maintaining backward compatibility

pub mod accessibility;
pub mod components;
pub mod config;
pub mod content_blocks;
pub mod input;
pub mod interface;
pub mod state;
pub mod utils;

pub use components::{Component, ComponentRegistry, ComponentState};
pub use config::{ConfigManager, KeybindingConfig, Theme, TuiConfig};
pub use content_blocks::{ContentBlock, ContentBlockType, ContentNavigator};
pub use input::{CompletionEngine, InputHandler, VimKeybindings};
pub use interface::EnhancedTuiInterface;
pub use state::{ExecutionStatus, InputState, NavigationState, TuiState};

use crate::errors::Result;
use crossterm::event::{Event, KeyEvent};
use ratatui::prelude::*;
use std::time::Duration;
use uuid::Uuid;

/// Main action types for the enhanced TUI
#[derive(Debug, Clone, PartialEq)]
pub enum Action {
    /// Navigation actions
    Navigate(NavigationAction),
    /// Input actions
    Input(InputAction),
    /// Content actions
    Content(ContentAction),
    /// UI actions
    UI(UIAction),
    /// System actions
    System(SystemAction),
}

#[derive(Debug, Clone, PartialEq)]
pub enum NavigationAction {
    NextBlock,
    PreviousBlock,
    FirstBlock,
    LastBlock,
    SelectBlock(Uuid),
    ToggleSelection(Uuid),
    ScrollUp(usize),
    ScrollDown(usize),
}

#[derive(Debug, Clone, PartialEq)]
pub enum InputAction {
    InsertChar(char),
    DeleteChar,
    DeleteWord,
    NewLine,
    Submit,
    Complete,
    CancelCompletion,
    HistoryPrevious,
    HistoryNext,
}

#[derive(Debug, Clone, PartialEq)]
pub enum ContentAction {
    AddBlock(ContentBlock),
    RemoveBlock(Uuid),
    UpdateBlock(Uuid, String),
    CopyBlock(Uuid),
    SearchContent(String),
    FilterContent(String),
}

#[derive(Debug, Clone, PartialEq)]
pub enum UIAction {
    ChangeMode(Mode),
    ChangeFocus(FocusTarget),
    ToggleWidget(String),
    ShowDialog(DialogType),
    HideDialog,
    Refresh,
}

#[derive(Debug, Clone, PartialEq)]
pub enum SystemAction {
    Exit,
    SaveSession,
    LoadSession(String),
    ReloadConfig,
    ToggleDebug,
}

/// UI modes for the enhanced TUI
#[derive(Debug, Clone, PartialEq)]
pub enum Mode {
    Normal,
    Insert,
    Visual,
    Command,
    Widget,
}

/// Focus targets for navigation
#[derive(Debug, Clone, PartialEq)]
pub enum FocusTarget {
    ContentArea,
    InputArea,
    StatusBar,
    Widget(String),
    Dialog,
}

/// Dialog types
#[derive(Debug, Clone, PartialEq)]
pub enum DialogType {
    Help,
    Settings,
    ThemeSelector,
    KeybindingEditor,
    About,
}

/// Result type for component operations
#[derive(Debug, Clone, PartialEq)]
pub enum ComponentResult {
    Handled,
    NotHandled,
    Action(Option<Action>),
    ModeChange(Mode),
    FocusChange(FocusTarget),
}

/// Input result type
#[derive(Debug, Clone, PartialEq)]
pub enum InputResult {
    Handled,
    NotHandled,
    Action(Action),
    ModeChange(Mode),
    Continue,
}

/// Performance metrics for monitoring
#[derive(Debug, Clone, Default)]
pub struct PerformanceMetrics {
    pub frame_time: Duration,
    pub render_time: Duration,
    pub event_processing_time: Duration,
    pub memory_usage: usize,
    pub active_components: usize,
    pub content_blocks: usize,
}

/// Enhanced TUI error types
#[derive(Debug, thiserror::Error)]
pub enum EnhancedTuiError {
    #[error("Component not found: {0}")]
    ComponentNotFound(String),

    #[error("Invalid configuration: {0}")]
    InvalidConfig(String),

    #[error("Theme error: {0}")]
    ThemeError(String),

    #[error("Input error: {0}")]
    InputError(String),

    #[error("Accessibility error: {0}")]
    AccessibilityError(String),

    #[error("Rendering error: {0}")]
    RenderError(String),
}

/// Trait for objects that can be rendered in the enhanced TUI
pub trait Renderable {
    fn render(&self, area: Rect, frame: &mut Frame);
    fn get_constraints(&self) -> Vec<Constraint>;
    fn is_visible(&self) -> bool {
        true
    }
}

/// Trait for objects that can handle events
pub trait EventHandler {
    fn handle_event(&mut self, event: &Event) -> Result<ComponentResult>;
    fn can_handle(&self, event: &Event) -> bool;
    fn get_priority(&self) -> u8 {
        0
    }
}

/// Trait for stateful components
pub trait Stateful {
    type State: Clone + Send + Sync;

    fn get_state(&self) -> &Self::State;
    fn set_state(&mut self, state: Self::State);
    fn reset_state(&mut self);
}

/// Trait for configurable components
pub trait Configurable {
    type Config: Clone + Send + Sync;

    fn configure(&mut self, config: Self::Config) -> Result<()>;
    fn get_config(&self) -> Self::Config;
}

/// Trait for accessible components
pub trait Accessible {
    fn get_aria_label(&self) -> Option<String>;
    fn get_aria_role(&self) -> Option<String>;
    fn get_description(&self) -> Option<String>;
    fn is_focusable(&self) -> bool;
    fn announce_change(&self, change: &str);
}

/// Enhanced TUI builder for configuration
pub struct EnhancedTuiBuilder {
    config: TuiConfig,
    vim_mode: bool,
    accessibility: bool,
    debug_mode: bool,
}

impl EnhancedTuiBuilder {
    pub fn new() -> Self {
        Self {
            config: TuiConfig::default(),
            vim_mode: false,
            accessibility: false,
            debug_mode: false,
        }
    }

    pub fn with_config(mut self, config: TuiConfig) -> Self {
        self.config = config;
        self
    }

    pub fn with_vim_mode(mut self, enabled: bool) -> Self {
        self.vim_mode = enabled;
        self
    }

    pub fn with_accessibility(mut self, enabled: bool) -> Self {
        self.accessibility = enabled;
        self
    }

    pub fn with_debug_mode(mut self, enabled: bool) -> Self {
        self.debug_mode = enabled;
        self
    }

    pub async fn build(self) -> Result<EnhancedTuiInterface> {
        EnhancedTuiInterface::new(
            self.config,
            self.vim_mode,
            self.accessibility,
            self.debug_mode,
            false,
        )
        .await
    }
}

impl Default for EnhancedTuiBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// Utility functions for the enhanced TUI
pub mod utils_ext {
    use super::*;

    /// Create a centered rect within the given area
    pub fn centered_rect(percent_x: u16, percent_y: u16, area: Rect) -> Rect {
        let popup_layout = Layout::default()
            .direction(Direction::Vertical)
            .constraints([
                Constraint::Percentage((100 - percent_y) / 2),
                Constraint::Percentage(percent_y),
                Constraint::Percentage((100 - percent_y) / 2),
            ])
            .split(area);

        Layout::default()
            .direction(Direction::Horizontal)
            .constraints([
                Constraint::Percentage((100 - percent_x) / 2),
                Constraint::Percentage(percent_x),
                Constraint::Percentage((100 - percent_x) / 2),
            ])
            .split(popup_layout[1])[1]
    }

    /// Calculate text height for wrapping
    pub fn calculate_text_height(text: &str, width: u16) -> u16 {
        if width == 0 {
            return 0;
        }

        text.lines()
            .map(|line| {
                if line.is_empty() {
                    1
                } else {
                    ((line.len() as u16).saturating_sub(1) / width) + 1
                }
            })
            .sum()
    }

    /// Truncate text to fit within width
    pub fn truncate_text(text: &str, width: usize) -> String {
        if text.len() <= width {
            text.to_string()
        } else if width <= 3 {
            "...".to_string()
        } else {
            format!("{}...", &text[..width.saturating_sub(3)])
        }
    }

    /// Convert key event to string representation
    pub fn key_event_to_string(key: &KeyEvent) -> String {
        use crossterm::event::{KeyCode, KeyModifiers};

        let mut result = String::new();

        if key.modifiers.contains(KeyModifiers::CONTROL) {
            result.push_str("Ctrl+");
        }
        if key.modifiers.contains(KeyModifiers::ALT) {
            result.push_str("Alt+");
        }
        if key.modifiers.contains(KeyModifiers::SHIFT) {
            result.push_str("Shift+");
        }

        match key.code {
            KeyCode::Char(c) => result.push(c),
            KeyCode::Enter => result.push_str("Enter"),
            KeyCode::Tab => result.push_str("Tab"),
            KeyCode::Backspace => result.push_str("Backspace"),
            KeyCode::Delete => result.push_str("Delete"),
            KeyCode::Esc => result.push_str("Escape"),
            KeyCode::Up => result.push_str("Up"),
            KeyCode::Down => result.push_str("Down"),
            KeyCode::Left => result.push_str("Left"),
            KeyCode::Right => result.push_str("Right"),
            KeyCode::Home => result.push_str("Home"),
            KeyCode::End => result.push_str("End"),
            KeyCode::PageUp => result.push_str("PageUp"),
            KeyCode::PageDown => result.push_str("PageDown"),
            KeyCode::F(n) => result.push_str(&format!("F{}", n)),
            _ => result.push_str("Unknown"),
        }

        result
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_centered_rect() {
        let area = Rect::new(0, 0, 100, 50);
        let centered = utils_ext::centered_rect(50, 50, area);

        assert_eq!(centered.width, 50);
        assert_eq!(centered.height, 25);
        assert_eq!(centered.x, 25);
        assert_eq!(centered.y, 12);
    }

    #[test]
    fn test_calculate_text_height() {
        assert_eq!(utils_ext::calculate_text_height("hello", 10), 1);
        assert_eq!(utils_ext::calculate_text_height("hello\nworld", 10), 2);
        assert_eq!(
            utils_ext::calculate_text_height("very long line that wraps", 10),
            3
        );
    }

    #[test]
    fn test_truncate_text() {
        assert_eq!(utils_ext::truncate_text("hello", 10), "hello");
        assert_eq!(utils_ext::truncate_text("hello world", 8), "hello...");
        assert_eq!(utils_ext::truncate_text("hi", 2), "hi");
    }

    #[tokio::test]
    async fn test_enhanced_tui_builder() {
        let builder = EnhancedTuiBuilder::new()
            .with_vim_mode(true)
            .with_accessibility(true)
            .with_debug_mode(false);

        // Note: This would require proper initialization in a real test
        // let tui = builder.build().await.unwrap();
        // assert!(tui.vim_mode);
    }
}
