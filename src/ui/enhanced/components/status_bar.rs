// Status bar component

use super::{Component, ComponentResult, ComponentState};
use crossterm::event::Event;
use ratatui::prelude::*;
use ratatui::widgets::{Block, Borders, Clear, Gauge, List, ListItem, ListState, Paragraph, Wrap};

/// Status bar component
pub struct StatusBar {
    id: String,
    sections: Vec<StatusSection>,
}

/// Status bar state
#[derive(Debug, <PERSON>lone)]
pub struct StatusBarState {
    pub is_focused: bool,
    pub is_dirty: bool,
    pub current_status: String,
    pub mode: String,
    pub help_text: String,
}

/// Status section
#[derive(Debug, Clone)]
pub struct StatusSection {
    pub content: String,
    pub style: Style,
    pub alignment: Alignment,
    pub width: StatusSectionWidth,
}

/// Status section width
#[derive(Debug, Clone)]
pub enum StatusSectionWidth {
    Fixed(u16),
    Percentage(u16),
    Flexible,
}

impl StatusBar {
    pub fn new(id: String) -> Self {
        Self {
            id,
            sections: Vec::new(),
        }
    }

    pub fn with_sections(mut self, sections: Vec<StatusSection>) -> Self {
        self.sections = sections;
        self
    }

    pub fn add_section(&mut self, section: StatusSection) {
        self.sections.push(section);
    }

    pub fn update_section(&mut self, index: usize, content: String) {
        if let Some(section) = self.sections.get_mut(index) {
            section.content = content;
        }
    }
}

impl Component for StatusBar {
    type State = StatusBarState;

    fn render(&self, state: &Self::State, area: Rect, frame: &mut Frame<'_>) {
        if self.sections.is_empty() {
            // Default status bar layout
            let status_text = format!(" {} ", state.current_status);
            let mode_text = format!(" {} ", state.mode);
            let help_text = format!(" {} ", state.help_text);

            let status_bar = Paragraph::new(Line::from(vec![
                Span::styled(
                    status_text,
                    Style::default().bg(Color::DarkGray).fg(Color::White),
                ),
                Span::raw(" "),
                Span::styled(mode_text, Style::default().bg(Color::Blue).fg(Color::White)),
                Span::raw(" "),
                Span::styled(help_text, Style::default().fg(Color::DarkGray)),
            ]))
            .alignment(Alignment::Left);

            frame.render_widget(status_bar, area);
        } else {
            // Custom sections layout
            let constraints: Vec<Constraint> = self
                .sections
                .iter()
                .map(|section| match section.width {
                    StatusSectionWidth::Fixed(width) => Constraint::Length(width),
                    StatusSectionWidth::Percentage(percent) => Constraint::Percentage(percent),
                    StatusSectionWidth::Flexible => Constraint::Min(1),
                })
                .collect();

            let chunks = Layout::default()
                .direction(Direction::Horizontal)
                .constraints(constraints)
                .split(area);

            for (section, chunk) in self.sections.iter().zip(chunks.iter()) {
                let widget = Paragraph::new(section.content.as_str())
                    .style(section.style)
                    .alignment(section.alignment);

                frame.render_widget(widget, *chunk);
            }
        }
    }

    fn handle_event(&mut self, _state: &mut Self::State, _event: &Event) -> ComponentResult {
        ComponentResult::NotHandled
    }

    fn get_constraints(&self, _state: &Self::State) -> Vec<Constraint> {
        vec![Constraint::Length(1)]
    }

    fn is_focusable(&self) -> bool {
        false
    }

    fn is_focused(&self, state: &Self::State) -> bool {
        state.is_focused
    }

    fn set_focus(&self, state: &mut Self::State, focused: bool) {
        state.is_focused = focused;
        state.mark_dirty();
    }

    fn get_id(&self) -> &str {
        &self.id
    }

    fn get_type(&self) -> super::ComponentType {
        super::ComponentType::StatusBar
    }
}

impl ComponentState for StatusBarState {
    fn reset(&mut self) {
        self.current_status = "Ready".to_string();
        self.mode = "NORMAL".to_string();
        self.help_text = "Press ? for help".to_string();
        self.mark_dirty();
    }

    fn is_dirty(&self) -> bool {
        self.is_dirty
    }

    fn mark_clean(&mut self) {
        self.is_dirty = false;
    }

    fn mark_dirty(&mut self) {
        self.is_dirty = true;
    }
}

impl Default for StatusBarState {
    fn default() -> Self {
        Self {
            is_focused: false,
            is_dirty: true,
            current_status: "Ready".to_string(),
            mode: "NORMAL".to_string(),
            help_text: "Press ? for help".to_string(),
        }
    }
}

impl StatusSection {
    pub fn new(content: String) -> Self {
        Self {
            content,
            style: Style::default(),
            alignment: Alignment::Left,
            width: StatusSectionWidth::Flexible,
        }
    }

    pub fn with_style(mut self, style: Style) -> Self {
        self.style = style;
        self
    }

    pub fn with_alignment(mut self, alignment: Alignment) -> Self {
        self.alignment = alignment;
        self
    }

    pub fn with_width(mut self, width: StatusSectionWidth) -> Self {
        self.width = width;
        self
    }
}
