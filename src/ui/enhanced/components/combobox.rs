// Combobox/dropdown component for enhanced TUI
// Provides auto-completion with fuzzy search, keyboard and mouse navigation

use super::{Component, ComponentResult, ComponentState, ComponentType};
// use crate::errors::Result; // Unused for now
// use crate::ui::enhanced::{Action, Accessible, Configurable}; // Unused for now
use crossterm::event::{Event, KeyCode, KeyEvent, KeyModifiers, MouseEvent, MouseEventKind};
use ratatui::prelude::*;
use ratatui::widgets::{Block, Borders, List, ListItem, Paragraph};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Combobox component for dropdown selection with auto-completion
pub struct Combobox {
    /// Component ID
    id: String,

    /// Available items
    items: Vec<ComboboxItem>,

    /// Filtered items (after search)
    filtered_items: Vec<usize>, // Indices into items

    /// Configuration
    config: ComboboxConfig,

    /// Fuzzy search engine
    fuzzy_search: FuzzySearch,
}

/// Combobox state
#[derive(Debug, <PERSON><PERSON>)]
pub struct ComboboxState {
    /// Currently selected item index (in filtered_items)
    pub selected_index: Option<usize>,

    /// Currently highlighted item index (for keyboard navigation)
    pub highlighted_index: Option<usize>,

    /// Current search/filter text
    pub filter_text: String,

    /// Whether dropdown is open
    pub is_open: bool,

    /// Whether component has focus
    pub is_focused: bool,

    /// Scroll offset for long lists
    pub scroll_offset: usize,

    /// Maximum visible items
    pub max_visible: usize,

    /// Whether state has changed
    pub is_dirty: bool,

    /// Last interaction timestamp
    pub last_interaction: std::time::Instant,
}

/// Combobox item
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComboboxItem {
    /// Display text
    pub text: String,

    /// Value (may differ from display text)
    pub value: String,

    /// Optional description
    pub description: Option<String>,

    /// Optional icon/symbol
    pub icon: Option<String>,

    /// Item category
    pub category: Option<String>,

    /// Custom data
    pub data: HashMap<String, serde_json::Value>,

    /// Whether item is enabled
    pub enabled: bool,

    /// Search keywords for fuzzy matching
    pub keywords: Vec<String>,
}

/// Combobox configuration
#[derive(Debug, Clone)]
pub struct ComboboxConfig {
    /// Enable fuzzy search
    pub fuzzy_search: bool,

    /// Case sensitive search
    pub case_sensitive: bool,

    /// Maximum visible items in dropdown
    pub max_visible_items: usize,

    /// Enable mouse interaction
    pub mouse_enabled: bool,

    /// Enable keyboard navigation
    pub keyboard_enabled: bool,

    /// Auto-close on selection
    pub auto_close: bool,

    /// Show descriptions
    pub show_descriptions: bool,

    /// Show icons
    pub show_icons: bool,

    /// Show categories
    pub show_categories: bool,

    /// Placeholder text
    pub placeholder: String,

    /// Dropdown position
    pub dropdown_position: DropdownPosition,

    /// Styling options
    pub style: ComboboxStyle,
}

/// Dropdown position relative to input
#[derive(Debug, Clone)]
pub enum DropdownPosition {
    Below,
    Above,
    Auto, // Choose based on available space
}

/// Combobox styling
#[derive(Debug, Clone)]
pub struct ComboboxStyle {
    /// Normal item style
    pub normal_style: Style,

    /// Highlighted item style
    pub highlighted_style: Style,

    /// Selected item style
    pub selected_style: Style,

    /// Disabled item style
    pub disabled_style: Style,

    /// Border style
    pub border_style: Style,

    /// Category header style
    pub category_style: Style,
}

/// Fuzzy search engine for filtering items
struct FuzzySearch {
    case_sensitive: bool,
}

impl Combobox {
    /// Create a new combobox
    pub fn new(id: String, items: Vec<ComboboxItem>, config: ComboboxConfig) -> Self {
        let filtered_items: Vec<usize> = (0..items.len()).collect();

        Self {
            id,
            items,
            filtered_items,
            config: config.clone(),
            fuzzy_search: FuzzySearch::new(config.case_sensitive),
        }
    }

    /// Add item to combobox
    pub fn add_item(&mut self, item: ComboboxItem) {
        self.items.push(item);
        self.update_filter("");
    }

    /// Remove item by index
    pub fn remove_item(&mut self, index: usize) -> Option<ComboboxItem> {
        if index < self.items.len() {
            let item = self.items.remove(index);
            self.update_filter("");
            Some(item)
        } else {
            None
        }
    }

    /// Clear all items
    pub fn clear_items(&mut self) {
        self.items.clear();
        self.filtered_items.clear();
    }

    /// Update filter and refresh filtered items
    pub fn update_filter(&mut self, filter: &str) {
        if filter.is_empty() {
            self.filtered_items = (0..self.items.len()).collect();
        } else if self.config.fuzzy_search {
            self.filtered_items = self.fuzzy_search.search(&self.items, filter);
        } else {
            self.filtered_items = self.simple_search(&self.items, filter);
        }
    }

    /// Simple text search (non-fuzzy)
    fn simple_search(&self, items: &[ComboboxItem], query: &str) -> Vec<usize> {
        let query = if self.config.case_sensitive {
            query.to_string()
        } else {
            query.to_lowercase()
        };

        items
            .iter()
            .enumerate()
            .filter(|(_, item)| {
                let text = if self.config.case_sensitive {
                    item.text.clone()
                } else {
                    item.text.to_lowercase()
                };

                text.contains(&query)
                    || item.keywords.iter().any(|keyword| {
                        let keyword = if self.config.case_sensitive {
                            keyword.clone()
                        } else {
                            keyword.to_lowercase()
                        };
                        keyword.contains(&query)
                    })
            })
            .map(|(index, _)| index)
            .collect()
    }

    /// Get currently selected item
    pub fn get_selected_item(&self, state: &ComboboxState) -> Option<&ComboboxItem> {
        state
            .selected_index
            .and_then(|idx| self.filtered_items.get(idx))
            .and_then(|&item_idx| self.items.get(item_idx))
    }

    /// Get currently highlighted item
    pub fn get_highlighted_item(&self, state: &ComboboxState) -> Option<&ComboboxItem> {
        state
            .highlighted_index
            .and_then(|idx| self.filtered_items.get(idx))
            .and_then(|&item_idx| self.items.get(item_idx))
    }

    /// Get visible items for rendering
    pub fn get_visible_items(&self, state: &ComboboxState) -> Vec<(usize, &ComboboxItem)> {
        let start = state.scroll_offset;
        let end = (start + state.max_visible).min(self.filtered_items.len());

        self.filtered_items[start..end]
            .iter()
            .enumerate()
            .filter_map(|(display_idx, &item_idx)| {
                self.items
                    .get(item_idx)
                    .map(|item| (start + display_idx, item))
            })
            .collect()
    }
}

impl Component for Combobox {
    type State = ComboboxState;

    fn render(&self, state: &Self::State, area: Rect, frame: &mut Frame) {
        if !state.is_open {
            self.render_closed(state, area, frame);
        } else {
            self.render_open(state, area, frame);
        }
    }

    fn handle_event(&mut self, state: &mut Self::State, event: &Event) -> ComponentResult {
        if !state.is_focused {
            return ComponentResult::NotHandled;
        }

        match event {
            Event::Key(key) => self.handle_key_event(state, key),
            Event::Mouse(mouse) => self.handle_mouse_event(state, mouse),
            _ => ComponentResult::NotHandled,
        }
    }

    fn get_constraints(&self, state: &Self::State) -> Vec<Constraint> {
        if state.is_open {
            vec![
                Constraint::Length(1),                            // Input line
                Constraint::Length(state.max_visible as u16 + 2), // Dropdown + borders
            ]
        } else {
            vec![Constraint::Length(1)] // Just input line
        }
    }

    fn is_focusable(&self) -> bool {
        true
    }

    fn is_focused(&self, state: &Self::State) -> bool {
        state.is_focused
    }

    fn set_focus(&self, state: &mut Self::State, focused: bool) {
        state.is_focused = focused;
        if !focused {
            state.is_open = false;
        }
        state.mark_dirty();
    }

    fn get_id(&self) -> &str {
        &self.id
    }

    fn get_type(&self) -> ComponentType {
        ComponentType::Combobox
    }
}

impl Combobox {
    /// Render combobox in closed state
    fn render_closed(&self, state: &ComboboxState, area: Rect, frame: &mut Frame) {
        let text = if state.filter_text.is_empty() {
            &self.config.placeholder
        } else {
            &state.filter_text
        };

        let style = if state.is_focused {
            self.config.style.highlighted_style
        } else {
            self.config.style.normal_style
        };

        let input = Paragraph::new(text.as_str()).style(style).block(
            Block::default()
                .borders(Borders::ALL)
                .border_style(self.config.style.border_style),
        );

        frame.render_widget(input, area);
    }

    /// Render combobox in open state
    fn render_open(&self, state: &ComboboxState, area: Rect, frame: &mut Frame) {
        // Split area for input and dropdown
        let chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([Constraint::Length(1), Constraint::Min(1)])
            .split(area);

        // Render input
        self.render_closed(state, chunks[0], frame);

        // Render dropdown
        self.render_dropdown(state, chunks[1], frame);
    }

    /// Render dropdown list
    fn render_dropdown(&self, state: &ComboboxState, area: Rect, frame: &mut Frame) {
        let visible_items = self.get_visible_items(state);

        let items: Vec<ListItem> = visible_items
            .iter()
            .map(|(display_idx, item)| {
                let is_highlighted = state.highlighted_index == Some(*display_idx);
                let is_selected = state.selected_index == Some(*display_idx);

                let style = if !item.enabled {
                    self.config.style.disabled_style
                } else if is_selected {
                    self.config.style.selected_style
                } else if is_highlighted {
                    self.config.style.highlighted_style
                } else {
                    self.config.style.normal_style
                };

                let mut text = String::new();

                // Add icon if enabled
                if self.config.show_icons {
                    if let Some(icon) = &item.icon {
                        text.push_str(icon);
                        text.push(' ');
                    }
                }

                // Add main text
                text.push_str(&item.text);

                // Add description if enabled
                if self.config.show_descriptions {
                    if let Some(description) = &item.description {
                        text.push_str(" - ");
                        text.push_str(description);
                    }
                }

                ListItem::new(text).style(style)
            })
            .collect();

        let list = List::new(items).block(
            Block::default()
                .borders(Borders::ALL)
                .border_style(self.config.style.border_style)
                .title("Options"),
        );

        frame.render_widget(list, area);
    }

    /// Handle keyboard events
    fn handle_key_event(&mut self, state: &mut ComboboxState, key: &KeyEvent) -> ComponentResult {
        match (key.code, key.modifiers) {
            (KeyCode::Esc, _) => {
                state.is_open = false;
                state.mark_dirty();
                ComponentResult::Handled
            }
            (KeyCode::Enter, _) => {
                if state.is_open {
                    if let Some(item) = self.get_highlighted_item(state) {
                        state.selected_index = state.highlighted_index;
                        if self.config.auto_close {
                            state.is_open = false;
                        }
                        state.mark_dirty();
                        return ComponentResult::Action(Some(crate::ui::enhanced::Action::Input(
                            crate::ui::enhanced::InputAction::Submit,
                        )));
                    }
                } else {
                    state.is_open = true;
                    state.mark_dirty();
                }
                ComponentResult::Handled
            }
            (KeyCode::Up, _) => {
                if state.is_open {
                    self.move_highlight_up(state);
                    ComponentResult::Handled
                } else {
                    ComponentResult::NotHandled
                }
            }
            (KeyCode::Down, _) => {
                if state.is_open {
                    self.move_highlight_down(state);
                } else {
                    state.is_open = true;
                    state.mark_dirty();
                }
                ComponentResult::Handled
            }
            (KeyCode::Tab, _) => {
                if state.is_open && state.highlighted_index.is_some() {
                    state.selected_index = state.highlighted_index;
                    if self.config.auto_close {
                        state.is_open = false;
                    }
                    state.mark_dirty();
                    ComponentResult::Action(Some(crate::ui::enhanced::Action::Input(
                        crate::ui::enhanced::InputAction::Complete,
                    )))
                } else {
                    ComponentResult::NotHandled
                }
            }
            (KeyCode::Char(c), KeyModifiers::NONE) => {
                state.filter_text.push(c);
                self.update_filter(&state.filter_text);
                state.highlighted_index = if self.filtered_items.is_empty() {
                    None
                } else {
                    Some(0)
                };
                state.is_open = true;
                state.mark_dirty();
                ComponentResult::Handled
            }
            (KeyCode::Backspace, _) => {
                if !state.filter_text.is_empty() {
                    state.filter_text.pop();
                    self.update_filter(&state.filter_text);
                    state.highlighted_index = if self.filtered_items.is_empty() {
                        None
                    } else {
                        Some(0)
                    };
                    state.mark_dirty();
                }
                ComponentResult::Handled
            }
            _ => ComponentResult::NotHandled,
        }
    }

    /// Handle mouse events
    fn handle_mouse_event(
        &mut self,
        state: &mut ComboboxState,
        mouse: &MouseEvent,
    ) -> ComponentResult {
        if !self.config.mouse_enabled {
            return ComponentResult::NotHandled;
        }

        match mouse.kind {
            MouseEventKind::Down(_) => {
                // TODO: Implement mouse click handling
                ComponentResult::Handled
            }
            MouseEventKind::ScrollUp => {
                if state.scroll_offset > 0 {
                    state.scroll_offset -= 1;
                    state.mark_dirty();
                }
                ComponentResult::Handled
            }
            MouseEventKind::ScrollDown => {
                let max_scroll = self.filtered_items.len().saturating_sub(state.max_visible);
                if state.scroll_offset < max_scroll {
                    state.scroll_offset += 1;
                    state.mark_dirty();
                }
                ComponentResult::Handled
            }
            _ => ComponentResult::NotHandled,
        }
    }

    /// Move highlight up
    fn move_highlight_up(&self, state: &mut ComboboxState) {
        if let Some(current) = state.highlighted_index {
            if current > 0 {
                state.highlighted_index = Some(current - 1);

                // Adjust scroll if needed
                if current - 1 < state.scroll_offset {
                    state.scroll_offset = current - 1;
                }

                state.mark_dirty();
            }
        } else if !self.filtered_items.is_empty() {
            state.highlighted_index = Some(self.filtered_items.len() - 1);
            state.scroll_offset = self.filtered_items.len().saturating_sub(state.max_visible);
            state.mark_dirty();
        }
    }

    /// Move highlight down
    fn move_highlight_down(&self, state: &mut ComboboxState) {
        if let Some(current) = state.highlighted_index {
            if current + 1 < self.filtered_items.len() {
                state.highlighted_index = Some(current + 1);

                // Adjust scroll if needed
                if current + 1 >= state.scroll_offset + state.max_visible {
                    state.scroll_offset = (current + 1).saturating_sub(state.max_visible - 1);
                }

                state.mark_dirty();
            }
        } else if !self.filtered_items.is_empty() {
            state.highlighted_index = Some(0);
            state.scroll_offset = 0;
            state.mark_dirty();
        }
    }
}

impl ComponentState for ComboboxState {
    fn reset(&mut self) {
        self.selected_index = None;
        self.highlighted_index = None;
        self.filter_text.clear();
        self.is_open = false;
        self.scroll_offset = 0;
        self.mark_dirty();
    }

    fn is_dirty(&self) -> bool {
        self.is_dirty
    }

    fn mark_clean(&mut self) {
        self.is_dirty = false;
    }

    fn mark_dirty(&mut self) {
        self.is_dirty = true;
        self.last_interaction = std::time::Instant::now();
    }
}

impl Default for ComboboxState {
    fn default() -> Self {
        Self {
            selected_index: None,
            highlighted_index: None,
            filter_text: String::new(),
            is_open: false,
            is_focused: false,
            scroll_offset: 0,
            max_visible: 10,
            is_dirty: true,
            last_interaction: std::time::Instant::now(),
        }
    }
}

impl Default for ComboboxConfig {
    fn default() -> Self {
        Self {
            fuzzy_search: true,
            case_sensitive: false,
            max_visible_items: 10,
            mouse_enabled: true,
            keyboard_enabled: true,
            auto_close: true,
            show_descriptions: true,
            show_icons: true,
            show_categories: false,
            placeholder: "Type to search...".to_string(),
            dropdown_position: DropdownPosition::Below,
            style: ComboboxStyle::default(),
        }
    }
}

impl Default for ComboboxStyle {
    fn default() -> Self {
        Self {
            normal_style: Style::default(),
            highlighted_style: Style::default().bg(Color::Blue).fg(Color::White),
            selected_style: Style::default().bg(Color::Green).fg(Color::White),
            disabled_style: Style::default().fg(Color::DarkGray),
            border_style: Style::default().fg(Color::Gray),
            category_style: Style::default()
                .fg(Color::Yellow)
                .add_modifier(Modifier::BOLD),
        }
    }
}

impl FuzzySearch {
    fn new(case_sensitive: bool) -> Self {
        Self { case_sensitive }
    }

    fn search(&self, items: &[ComboboxItem], query: &str) -> Vec<usize> {
        // Simple fuzzy search implementation
        // In a real implementation, you might use a more sophisticated algorithm
        let query = if self.case_sensitive {
            query.to_string()
        } else {
            query.to_lowercase()
        };

        let mut results: Vec<(usize, i32)> = items
            .iter()
            .enumerate()
            .filter_map(|(index, item)| {
                let text = if self.case_sensitive {
                    item.text.clone()
                } else {
                    item.text.to_lowercase()
                };

                let score = self.calculate_score(&text, &query);
                if score > 0 {
                    Some((index, score))
                } else {
                    None
                }
            })
            .collect();

        // Sort by score (higher is better)
        results.sort_by(|a, b| b.1.cmp(&a.1));

        results.into_iter().map(|(index, _)| index).collect()
    }

    fn calculate_score(&self, text: &str, query: &str) -> i32 {
        if query.is_empty() {
            return 100;
        }

        if text.contains(query) {
            // Exact substring match gets high score
            let position = text.find(query).unwrap();
            return 100 - position as i32; // Earlier matches score higher
        }

        // Check for character sequence match
        let mut score = 0;
        let mut text_chars = text.chars().peekable();
        let mut query_chars = query.chars().peekable();

        while let (Some(&text_char), Some(&query_char)) = (text_chars.peek(), query_chars.peek()) {
            if text_char == query_char {
                score += 1;
                query_chars.next();
            }
            text_chars.next();
        }

        // If we matched all query characters, return the score
        if query_chars.peek().is_none() {
            score
        } else {
            0
        }
    }
}
