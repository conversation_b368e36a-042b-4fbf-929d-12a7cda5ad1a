// Dialog component

use super::{Component, ComponentResult, ComponentState};
use crossterm::event::{Event, KeyCode, KeyEvent};
use ratatui::prelude::*;
use ratatui::widgets::{Block, Borders, Clear, Gauge, List, ListItem, ListState, Paragraph, Wrap};

/// Dialog component
pub struct Dialog {
    id: String,
    title: String,
    message: String,
    buttons: Vec<DialogButton>,
}

/// Dialog state
#[derive(Debug, Clone)]
pub struct DialogState {
    pub is_open: bool,
    pub selected_button: usize,
    pub is_focused: bool,
    pub is_dirty: bool,
}

/// Dialog button
#[derive(Debug, Clone)]
pub struct DialogButton {
    pub label: String,
    pub action: DialogAction,
}

/// Dialog actions
#[derive(Debug, Clone)]
pub enum DialogAction {
    Accept,
    Cancel,
    Custom(String),
}

/// Dialog result
#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum DialogResult {
    Accepted,
    Cancelled,
    Custom(String),
}

impl Dialog {
    pub fn new(id: String, title: String, message: String) -> Self {
        Self {
            id,
            title,
            message,
            buttons: vec![
                DialogButton {
                    label: "OK".to_string(),
                    action: DialogAction::Accept,
                },
                DialogButton {
                    label: "Cancel".to_string(),
                    action: DialogAction::Cancel,
                },
            ],
        }
    }

    pub fn with_buttons(mut self, buttons: Vec<DialogButton>) -> Self {
        self.buttons = buttons;
        self
    }
}

impl Component for Dialog {
    type State = DialogState;

    fn render(&self, state: &Self::State, area: Rect, frame: &mut Frame<'_>) {
        if !state.is_open {
            return;
        }

        // Create centered dialog area
        let dialog_area = centered_rect(60, 40, area);

        // Clear background
        frame.render_widget(Clear, dialog_area);

        // Split dialog into title, message, and buttons
        let chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([
                Constraint::Length(3), // Title
                Constraint::Min(3),    // Message
                Constraint::Length(3), // Buttons
            ])
            .split(dialog_area);

        // Render title
        let title = Paragraph::new(self.title.as_str())
            .style(Style::default().add_modifier(Modifier::BOLD))
            .alignment(Alignment::Center)
            .block(Block::default().borders(Borders::TOP | Borders::LEFT | Borders::RIGHT));
        frame.render_widget(title, chunks[0]);

        // Render message
        let message = Paragraph::new(self.message.as_str())
            .wrap(Wrap { trim: true })
            .alignment(Alignment::Left)
            .block(Block::default().borders(Borders::LEFT | Borders::RIGHT));
        frame.render_widget(message, chunks[1]);

        // Render buttons
        let button_constraints: Vec<Constraint> = self
            .buttons
            .iter()
            .map(|_| Constraint::Percentage(100 / self.buttons.len() as u16))
            .collect();

        let button_chunks = Layout::default()
            .direction(Direction::Horizontal)
            .constraints(button_constraints)
            .split(chunks[2]);

        for (i, (button, chunk)) in self.buttons.iter().zip(button_chunks.iter()).enumerate() {
            let style = if i == state.selected_button {
                Style::default().bg(Color::Blue).fg(Color::White)
            } else {
                Style::default()
            };

            let button_widget = Paragraph::new(button.label.as_str())
                .style(style)
                .alignment(Alignment::Center)
                .block(Block::default().borders(Borders::ALL));

            frame.render_widget(button_widget, *chunk);
        }
    }

    fn handle_event(&mut self, state: &mut Self::State, event: &Event) -> ComponentResult {
        if !state.is_open || !state.is_focused {
            return ComponentResult::NotHandled;
        }

        match event {
            Event::Key(KeyEvent {
                code: KeyCode::Esc, ..
            }) => {
                state.is_open = false;
                state.mark_dirty();
                ComponentResult::Action(Some(crate::ui::enhanced::Action::UI(
                    crate::ui::enhanced::UIAction::HideDialog,
                )))
            }
            Event::Key(KeyEvent {
                code: KeyCode::Enter,
                ..
            }) => {
                let action = &self.buttons[state.selected_button].action;
                state.is_open = false;
                state.mark_dirty();

                match action {
                    DialogAction::Accept => ComponentResult::Action(Some(
                        crate::ui::enhanced::Action::UI(crate::ui::enhanced::UIAction::HideDialog),
                    )),
                    DialogAction::Cancel => ComponentResult::Action(Some(
                        crate::ui::enhanced::Action::UI(crate::ui::enhanced::UIAction::HideDialog),
                    )),
                    DialogAction::Custom(_action) => ComponentResult::Action(Some(
                        crate::ui::enhanced::Action::UI(crate::ui::enhanced::UIAction::HideDialog),
                    )),
                }
            }
            Event::Key(KeyEvent {
                code: KeyCode::Left,
                ..
            }) => {
                if state.selected_button > 0 {
                    state.selected_button -= 1;
                    state.mark_dirty();
                }
                ComponentResult::Handled
            }
            Event::Key(KeyEvent {
                code: KeyCode::Right,
                ..
            }) => {
                if state.selected_button + 1 < self.buttons.len() {
                    state.selected_button += 1;
                    state.mark_dirty();
                }
                ComponentResult::Handled
            }
            Event::Key(KeyEvent {
                code: KeyCode::Tab, ..
            }) => {
                state.selected_button = (state.selected_button + 1) % self.buttons.len();
                state.mark_dirty();
                ComponentResult::Handled
            }
            _ => ComponentResult::NotHandled,
        }
    }

    fn get_constraints(&self, state: &Self::State) -> Vec<Constraint> {
        if state.is_open {
            vec![Constraint::Min(10)]
        } else {
            vec![Constraint::Length(0)]
        }
    }

    fn is_focusable(&self) -> bool {
        true
    }

    fn is_focused(&self, state: &Self::State) -> bool {
        state.is_focused
    }

    fn set_focus(&self, state: &mut Self::State, focused: bool) {
        state.is_focused = focused;
        state.mark_dirty();
    }

    fn get_id(&self) -> &str {
        &self.id
    }

    fn get_type(&self) -> super::ComponentType {
        super::ComponentType::Dialog
    }
}

impl ComponentState for DialogState {
    fn reset(&mut self) {
        self.is_open = false;
        self.selected_button = 0;
        self.mark_dirty();
    }

    fn is_dirty(&self) -> bool {
        self.is_dirty
    }

    fn mark_clean(&mut self) {
        self.is_dirty = false;
    }

    fn mark_dirty(&mut self) {
        self.is_dirty = true;
    }
}

impl Default for DialogState {
    fn default() -> Self {
        Self {
            is_open: false,
            selected_button: 0,
            is_focused: false,
            is_dirty: false,
        }
    }
}

/// Helper function to create centered rectangle
fn centered_rect(percent_x: u16, percent_y: u16, r: Rect) -> Rect {
    let popup_layout = Layout::default()
        .direction(Direction::Vertical)
        .constraints([
            Constraint::Percentage((100 - percent_y) / 2),
            Constraint::Percentage(percent_y),
            Constraint::Percentage((100 - percent_y) / 2),
        ])
        .split(r);

    Layout::default()
        .direction(Direction::Horizontal)
        .constraints([
            Constraint::Percentage((100 - percent_x) / 2),
            Constraint::Percentage(percent_x),
            Constraint::Percentage((100 - percent_x) / 2),
        ])
        .split(popup_layout[1])[1]
}
