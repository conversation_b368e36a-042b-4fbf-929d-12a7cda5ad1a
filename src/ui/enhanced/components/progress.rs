// Progress bar component

use super::{Component, ComponentResult, ComponentState};
use crossterm::event::Event;
use ratatui::prelude::*;
use ratatui::widgets::{Block, Borders, Clear, Gauge, List, ListItem, ListState, Paragraph, Wrap};

/// Progress bar component
pub struct ProgressBar {
    id: String,
    label: String,
}

/// Progress bar state
#[derive(Debug, Clone)]
pub struct ProgressBarState {
    pub progress: f32,
    pub is_focused: bool,
    pub is_dirty: bool,
}

/// Progress indicator
pub struct ProgressIndicator {
    style: ProgressStyle,
}

/// Progress style
#[derive(Debug, Clone)]
pub struct ProgressStyle {
    pub filled_char: char,
    pub empty_char: char,
    pub filled_style: Style,
    pub empty_style: Style,
}

impl ProgressBar {
    pub fn new(id: String, label: String) -> Self {
        Self { id, label }
    }
}

impl Component for ProgressBar {
    type State = ProgressBarState;

    fn render(&self, state: &Self::State, area: Rect, frame: &mut Frame<'_>) {
        let progress = (state.progress * area.width as f32) as u16;
        let filled = "█".repeat(progress as usize);
        let empty = "░".repeat((area.width - progress) as usize);

        let text = format!("{}{} {:.1}%", filled, empty, state.progress * 100.0);
        let widget = Paragraph::new(text).block(
            Block::default()
                .borders(Borders::ALL)
                .title(self.label.as_str()),
        );

        frame.render_widget(widget, area);
    }

    fn handle_event(&mut self, _state: &mut Self::State, _event: &Event) -> ComponentResult {
        ComponentResult::NotHandled
    }

    fn get_constraints(&self, _state: &Self::State) -> Vec<Constraint> {
        vec![Constraint::Length(3)]
    }

    fn is_focusable(&self) -> bool {
        false
    }

    fn is_focused(&self, state: &Self::State) -> bool {
        state.is_focused
    }

    fn set_focus(&self, state: &mut Self::State, focused: bool) {
        state.is_focused = focused;
        state.mark_dirty();
    }

    fn get_id(&self) -> &str {
        &self.id
    }

    fn get_type(&self) -> super::ComponentType {
        super::ComponentType::ProgressBar
    }
}

impl ComponentState for ProgressBarState {
    fn reset(&mut self) {
        self.progress = 0.0;
        self.mark_dirty();
    }

    fn is_dirty(&self) -> bool {
        self.is_dirty
    }

    fn mark_clean(&mut self) {
        self.is_dirty = false;
    }

    fn mark_dirty(&mut self) {
        self.is_dirty = true;
    }
}

impl Default for ProgressBarState {
    fn default() -> Self {
        Self {
            progress: 0.0,
            is_focused: false,
            is_dirty: true,
        }
    }
}

impl ProgressIndicator {
    pub fn new() -> Self {
        Self {
            style: ProgressStyle::default(),
        }
    }
}

impl Default for ProgressStyle {
    fn default() -> Self {
        Self {
            filled_char: '█',
            empty_char: '░',
            filled_style: Style::default().fg(Color::Green),
            empty_style: Style::default().fg(Color::DarkGray),
        }
    }
}
