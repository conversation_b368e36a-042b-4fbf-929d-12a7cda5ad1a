// Keyboard navigation support

use crate::errors::Result;
use std::collections::HashMap;

/// Keyboard navigation manager
pub struct KeyboardNavigation {
    tab_order: Vec<String>,
    skip_links: HashMap<String, String>,
}

/// Focus manager for accessibility
pub struct FocusManager {
    focus_chain: Vec<String>,
    current_focus: Option<String>,
    focus_history: Vec<String>,
}

impl KeyboardNavigation {
    pub fn new() -> Self {
        Self {
            tab_order: Vec::new(),
            skip_links: HashMap::new(),
        }
    }

    pub fn set_tab_order(&mut self, order: Vec<String>) {
        self.tab_order = order;
    }

    pub fn add_skip_link(&mut self, key: String, target: String) {
        self.skip_links.insert(key, target);
    }

    pub fn get_skip_target(&self, key: &str) -> Option<&String> {
        self.skip_links.get(key)
    }

    pub fn get_tab_order(&self) -> &[String] {
        &self.tab_order
    }
}

impl FocusManager {
    pub fn new() -> Self {
        Self {
            focus_chain: Vec::new(),
            current_focus: None,
            focus_history: Vec::new(),
        }
    }

    pub fn set_focus_chain(&mut self, chain: Vec<String>) {
        self.focus_chain = chain;
    }

    pub fn set_focus(&mut self, element_id: &str) -> Result<()> {
        if self.focus_chain.contains(&element_id.to_string()) {
            if let Some(current) = &self.current_focus {
                self.focus_history.push(current.clone());
            }
            self.current_focus = Some(element_id.to_string());
            Ok(())
        } else {
            Err(crate::errors::AutorunError::NotFound(format!(
                "Element {} not in focus chain",
                element_id
            ))
            .into())
        }
    }

    pub fn get_current_focus(&self) -> Option<&String> {
        self.current_focus.as_ref()
    }

    pub fn get_next_focusable(&self, current: Option<&str>) -> Option<String> {
        if self.focus_chain.is_empty() {
            return None;
        }

        if let Some(current_id) = current {
            if let Some(pos) = self.focus_chain.iter().position(|id| id == current_id) {
                let next_pos = (pos + 1) % self.focus_chain.len();
                return Some(self.focus_chain[next_pos].clone());
            }
        }

        Some(self.focus_chain[0].clone())
    }

    pub fn get_previous_focusable(&self, current: Option<&str>) -> Option<String> {
        if self.focus_chain.is_empty() {
            return None;
        }

        if let Some(current_id) = current {
            if let Some(pos) = self.focus_chain.iter().position(|id| id == current_id) {
                let prev_pos = if pos == 0 {
                    self.focus_chain.len() - 1
                } else {
                    pos - 1
                };
                return Some(self.focus_chain[prev_pos].clone());
            }
        }

        Some(self.focus_chain[self.focus_chain.len() - 1].clone())
    }

    pub fn go_back(&mut self) -> Option<String> {
        if let Some(previous) = self.focus_history.pop() {
            self.current_focus = Some(previous.clone());
            Some(previous)
        } else {
            None
        }
    }

    pub fn clear_history(&mut self) {
        self.focus_history.clear();
    }
}

impl Default for KeyboardNavigation {
    fn default() -> Self {
        Self::new()
    }
}

impl Default for FocusManager {
    fn default() -> Self {
        Self::new()
    }
}
