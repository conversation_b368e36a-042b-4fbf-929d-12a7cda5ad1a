// Accessibility module for enhanced TUI

pub mod keyboard;
pub mod screen_reader;

pub use keyboard::{FocusManager, KeyboardNavigation};
pub use screen_reader::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ScreenReaderSupport};

use crate::errors::Result;

/// Accessibility manager
pub struct AccessibilityManager {
    screen_reader: ScreenReaderSupport,
    keyboard_nav: KeyboardNavigation,
    focus_manager: FocusManager,
    enabled: bool,
}

/// Accessibility configuration
#[derive(Debug, Clone)]
pub struct AccessibilityConfig {
    pub screen_reader_enabled: bool,
    pub high_contrast: bool,
    pub reduce_motion: bool,
    pub large_text: bool,
    pub sound_feedback: bool,
}

impl AccessibilityManager {
    pub fn new(config: AccessibilityConfig) -> Self {
        Self {
            screen_reader: ScreenReaderSupport::new(config.screen_reader_enabled),
            keyboard_nav: KeyboardNavigation::new(),
            focus_manager: FocusManager::new(),
            enabled: config.screen_reader_enabled,
        }
    }

    pub fn announce(&self, message: &str) {
        if self.enabled {
            self.screen_reader.announce(message);
        }
    }

    pub fn set_focus(&mut self, element_id: &str) -> Result<()> {
        self.focus_manager.set_focus(element_id)?;

        if self.enabled {
            self.screen_reader.announce_focus_change(element_id);
        }

        Ok(())
    }

    pub fn get_next_focusable(&self, current: Option<&str>) -> Option<String> {
        self.focus_manager.get_next_focusable(current)
    }

    pub fn get_previous_focusable(&self, current: Option<&str>) -> Option<String> {
        self.focus_manager.get_previous_focusable(current)
    }
}

impl Default for AccessibilityConfig {
    fn default() -> Self {
        Self {
            screen_reader_enabled: false,
            high_contrast: false,
            reduce_motion: false,
            large_text: false,
            sound_feedback: false,
        }
    }
}
