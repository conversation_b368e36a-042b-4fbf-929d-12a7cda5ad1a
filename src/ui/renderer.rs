use crate::core::AppCore;
use crate::ui::app_interface::{FocusState, Mode, TuiInterface};
use crate::ui::completion::MentionType;
use ratatui::{
    layout::{Alignment, Constraint, Direction, Layout, Rect},
    style::{Color, Modifier, Style},
    text::{Line, Span, Text},
    widgets::{Block, Borders, Clear, List, ListItem, Paragraph, Wrap},
    Frame,
};
use std::sync::Arc;
use tokio::sync::Mutex;
use tui_textarea::TextArea;

/// Renderer handles all TUI rendering logic
pub struct Renderer;

impl Renderer {
    /// Main render function
    pub async fn render(frame: &mut Frame<'_>, tui: &TuiInterface) {
        let chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([
                Constraint::Min(10),   // Messages area
                Constraint::Length(5), // Input area
                Constraint::Length(1), // Status bar
            ])
            .split(frame.area());

        // Render messages
        Self::render_messages(frame, tui, chunks[0]).await;

        // Render input area
        Self::render_input(frame, tui, chunks[1]);

        // Render status bar
        Self::render_status_bar(frame, tui, chunks[2]).await;

        // Render popups (if any)
        if tui.mention_popup_state.visible {
            Self::render_mention_popup(frame, tui, chunks[1]);
        }
    }

    /// Advanced UI with widget support
    pub async fn render_advanced(frame: &mut Frame<'_>, tui: &TuiInterface) {
        let has_widgets = !tui.active_widgets.is_empty();

        let main_chunks = if has_widgets {
            Layout::default()
                .direction(Direction::Horizontal)
                .constraints([
                    Constraint::Percentage(60), // Main chat area
                    Constraint::Percentage(40), // Widget area
                ])
                .split(frame.area())
        } else {
            Layout::default()
                .direction(Direction::Horizontal)
                .constraints([Constraint::Percentage(100)])
                .split(frame.area())
        };

        // Left side - chat interface
        let chat_chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([
                Constraint::Min(10),   // Messages
                Constraint::Length(5), // Input
                Constraint::Length(1), // Status bar
            ])
            .split(main_chunks[0]);

        // Render chat components
        Self::render_messages(frame, tui, chat_chunks[0]).await;
        Self::render_input(frame, tui, chat_chunks[1]);
        Self::render_status_bar(frame, tui, chat_chunks[2]).await;

        // Right side - widgets (if any)
        if has_widgets && main_chunks.len() > 1 {
            Self::render_widgets(frame, tui, main_chunks[1]).await;
        }

        // Render popups
        if tui.mention_popup_state.visible {
            Self::render_mention_popup(frame, tui, chat_chunks[1]);
        }
    }

    async fn render_messages(frame: &mut Frame<'_>, tui: &TuiInterface, area: Rect) {
        let core = tui.app_core.lock().await;
        let messages = core.get_messages();

        let messages_widget = List::new(
            messages
                .iter()
                .skip(tui.scroll_offset)
                .map(|msg| {
                    let style = if msg.starts_with("You:") {
                        Style::default().fg(Color::Blue)
                    } else if msg.starts_with("Assistant:") {
                        Style::default().fg(Color::Green)
                    } else if msg.starts_with("Error:") {
                        Style::default().fg(Color::Red)
                    } else {
                        Style::default()
                    };
                    ListItem::new(msg.as_str()).style(style)
                })
                .collect::<Vec<_>>(),
        )
        .block(
            Block::default()
                .borders(Borders::ALL)
                .title("Chat")
                .border_style(match tui.focus_state {
                    FocusState::Chat => Style::default().fg(Color::Cyan),
                    _ => Style::default(),
                }),
        );

        frame.render_widget(messages_widget, area);
    }

    fn render_input(frame: &mut Frame<'_>, tui: &TuiInterface, area: Rect) {
        let mut textarea = tui.input_textarea.clone();

        textarea.set_block(
            Block::default()
                .borders(Borders::ALL)
                .title("Input")
                .border_style(match tui.focus_state {
                    FocusState::Input => Style::default().fg(Color::Cyan),
                    _ => Style::default(),
                }),
        );

        frame.render_widget(textarea.widget(), area);
    }

    async fn render_status_bar(frame: &mut Frame<'_>, tui: &TuiInterface, area: Rect) {
        let core = tui.app_core.lock().await;

        let mode_str = match tui.mode {
            Mode::Normal => "NORMAL",
            Mode::Input => "INPUT",
            Mode::Widget => "WIDGET",
        };

        let status = if core.is_processing() {
            format!(" {} | {} ", mode_str, core.get_processing_status())
        } else {
            format!(" {} | Ready ", mode_str)
        };

        let help = match tui.mode {
            Mode::Normal => " q:quit i:input j/k:scroll w:widgets ",
            Mode::Input => " ESC:normal Enter:send Tab:complete ",
            Mode::Widget => " ESC:normal Tab:next-widget ",
        };

        let status_bar = Paragraph::new(Line::from(vec![
            Span::styled(status, Style::default().bg(Color::DarkGray)),
            Span::raw(" "),
            Span::styled(help, Style::default().fg(Color::DarkGray)),
        ]))
        .alignment(Alignment::Left);

        frame.render_widget(status_bar, area);
    }

    fn render_mention_popup(frame: &mut Frame<'_>, tui: &TuiInterface, input_area: Rect) {
        let suggestions = &tui.mention_popup_state.suggestions;
        if suggestions.is_empty() {
            return;
        }

        // Calculate popup position
        let popup_height = (suggestions.len() as u16 + 2).min(10);
        let popup_width = 40;
        let popup_y = input_area.y.saturating_sub(popup_height);
        let popup_x = input_area.x + 5; // Fixed position for now

        let popup_area = Rect::new(popup_x, popup_y, popup_width, popup_height);

        // Create list items with icons
        let items: Vec<ListItem> = suggestions
            .iter()
            .enumerate()
            .map(|(i, suggestion)| {
                let icon = match suggestion.mention_type {
                    MentionType::File => "📄",
                    _ => "🔍", // Fallback for other types
                };

                let style = if i == tui.mention_popup_state.selected_index {
                    Style::default()
                        .bg(Color::DarkGray)
                        .add_modifier(Modifier::BOLD)
                } else {
                    Style::default()
                };

                ListItem::new(format!("{} {}", icon, suggestion.display_name)).style(style)
            })
            .collect();

        let popup = List::new(items).block(
            Block::default()
                .borders(Borders::ALL)
                .title("Suggestions")
                .border_style(Style::default().fg(Color::Yellow)),
        );

        // Clear the area first
        frame.render_widget(Clear, popup_area);
        frame.render_widget(popup, popup_area);
    }

    async fn render_widgets(frame: &mut Frame<'_>, tui: &TuiInterface, area: Rect) {
        if tui.active_widgets.is_empty() {
            return;
        }

        let widget_count = tui.active_widgets.len();
        let constraints: Vec<Constraint> = (0..widget_count)
            .map(|_| Constraint::Ratio(1, widget_count as u32))
            .collect();

        let widget_areas = Layout::default()
            .direction(Direction::Vertical)
            .constraints(constraints)
            .split(area);

        for (i, widget_id) in tui.active_widgets.iter().enumerate() {
            if i < widget_areas.len() {
                Self::render_single_widget(frame, tui, widget_areas[i], widget_id).await;
            }
        }
    }

    async fn render_single_widget(
        frame: &mut Frame<'_>,
        tui: &TuiInterface,
        area: Rect,
        widget_id: &str,
    ) {
        // For now, just render a placeholder
        let is_focused = matches!(&tui.focus_state, FocusState::Widget(id) if id == widget_id);
        let block = Block::default()
            .borders(Borders::ALL)
            .title(format!("Widget: {}", widget_id))
            .border_style(if is_focused {
                Style::default().fg(Color::Cyan)
            } else {
                Style::default()
            });

        frame.render_widget(block, area);
    }
}
