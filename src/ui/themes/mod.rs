//! Theme management system
//! 
//! This module provides theme loading, hot-swapping, and validation
//! with support for built-in and custom themes.

pub mod theme_manager;
pub mod theme_validator;
pub mod color_scheme;
pub mod theme_presets;

// Re-export main types
pub use theme_manager::{
    ThemeManager, Theme, ThemeMetadata, ThemeCategory, ColorScheme, 
    StyleDefinitions, ComponentStyles, AccessibilityConfig,
};
pub use theme_validator::{ThemeValidator, ThemeValidationResult};
pub use color_scheme::{ColorScheme as ColorPalette, ColorUtils};
pub use theme_presets::ThemePresets;

use crate::errors::Result;
use ratatui::style::{Color, Style};

/// Initialize the theme system
pub async fn initialize_theme_system() -> Result<ThemeManager> {
    let manager = ThemeManager::new();
    
    // Load any custom themes from user directory
    // TODO: Scan theme directories and load custom themes
    
    Ok(manager)
}

/// Theme change event
#[derive(Debug, Clone)]
pub struct ThemeChangeEvent {
    /// Previous theme name
    pub from_theme: String,
    
    /// New theme name
    pub to_theme: String,
    
    /// Change timestamp
    pub timestamp: chrono::DateTime<chrono::Utc>,
    
    /// Whether change was successful
    pub successful: bool,
    
    /// Components that need refresh
    pub affected_components: Vec<String>,
}

/// Theme compatibility information
#[derive(Debug, Clone)]
pub struct ThemeCompatibility {
    /// Minimum terminal color support required
    pub min_colors: u16,
    
    /// Whether theme supports true color
    pub true_color: bool,
    
    /// Whether theme works in monochrome
    pub monochrome_fallback: bool,
    
    /// Supported UI modes
    pub supported_modes: Vec<String>,
    
    /// Platform compatibility
    pub platforms: Vec<Platform>,
}

/// Platform support
#[derive(Debug, Clone)]
pub enum Platform {
    Linux,
    MacOS,
    Windows,
    Web,
    Mobile,
}

/// Theme performance metrics
#[derive(Debug, Clone)]
pub struct ThemePerformance {
    /// Load time in milliseconds
    pub load_time_ms: u64,
    
    /// Memory usage in bytes
    pub memory_usage: u64,
    
    /// Render performance impact
    pub render_impact: f32,
    
    /// Cache hit ratio
    pub cache_hit_ratio: f32,
}