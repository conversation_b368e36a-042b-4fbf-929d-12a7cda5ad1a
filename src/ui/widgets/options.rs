// Options widget system for dynamic TUI components
// Supports radio buttons, dropdowns, and button groups with AI integration

use async_trait::async_trait;
use crossterm::event::{KeyCode, KeyEvent};
use ratatui::prelude::*;
use ratatui::widgets::{Block, Borders, Widget};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::collections::HashMap;
// use tracing::debug; // Currently unused

use crate::agent::core::{WidgetGenerationRequest, WidgetUpdateEvent};
use crate::errors::{AutorunError, Result};

use super::{AiWidget, WidgetBuilder, WidgetConfig, WidgetState};

/// Unified state for all option widgets
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OptionsState {
    pub selected_index: usize,
    pub selected_values: Vec<usize>, // For multi-select
    pub expanded: bool,              // For dropdown
    pub focused: bool,
    pub dirty: bool,
}

impl Default for OptionsState {
    fn default() -> Self {
        Self {
            selected_index: 0,
            selected_values: Vec::new(),
            expanded: false,
            focused: false,
            dirty: false,
        }
    }
}

impl WidgetState for OptionsState {
    fn reset(&mut self) {
        self.selected_index = 0;
        self.selected_values.clear();
        self.expanded = false;
        self.focused = false;
        self.dirty = false;
    }

    fn is_dirty(&self) -> bool {
        self.dirty
    }

    fn mark_clean(&mut self) {
        self.dirty = false;
    }
}

/// Configuration for an individual option
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OptionItem {
    pub label: String,
    pub value: Value,
    pub description: Option<String>,
    pub enabled: bool,
    pub icon: Option<String>,
}

/// Display mode for options
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum OptionsDisplayMode {
    Radio,
    Dropdown,
    ButtonGroup,
}

/// Layout mode for button groups
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum ButtonGroupLayout {
    Horizontal,
    Vertical,
    Grid { columns: u16 },
}

/// Configuration for options widget
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OptionsConfig {
    pub display_mode: OptionsDisplayMode,
    pub options: Vec<OptionItem>,
    pub multi_select: bool,
    pub required: bool,
    pub button_layout: Option<ButtonGroupLayout>,
    pub show_descriptions: bool,
    pub max_visible_items: Option<usize>, // For dropdowns
    pub validation_rules: Option<HashMap<String, Value>>,
}

/// Radio button widget implementation
pub struct RadioButtonWidget {
    id: String,
    title: Option<String>,
    config: OptionsConfig,
    state: OptionsState,
}

impl RadioButtonWidget {
    pub fn new(id: String, title: Option<String>, config: OptionsConfig) -> Self {
        Self {
            id,
            title,
            config,
            state: OptionsState::default(),
        }
    }

    fn handle_key_event(&mut self, key: KeyEvent) -> Result<()> {
        match key.code {
            KeyCode::Up | KeyCode::Char('k') => {
                if self.state.selected_index > 0 {
                    self.state.selected_index -= 1;
                    self.state.dirty = true;
                }
            }
            KeyCode::Down | KeyCode::Char('j') => {
                if self.state.selected_index < self.config.options.len() - 1 {
                    self.state.selected_index += 1;
                    self.state.dirty = true;
                }
            }
            KeyCode::Enter | KeyCode::Char(' ') => {
                if self.config.multi_select {
                    if self
                        .state
                        .selected_values
                        .contains(&self.state.selected_index)
                    {
                        self.state
                            .selected_values
                            .retain(|&x| x != self.state.selected_index);
                    } else {
                        self.state.selected_values.push(self.state.selected_index);
                    }
                } else {
                    self.state.selected_values = vec![self.state.selected_index];
                }
                self.state.dirty = true;
            }
            _ => {}
        }
        Ok(())
    }

    pub fn render(&self, area: Rect, buf: &mut Buffer) {
        let block = Block::default()
            .borders(Borders::ALL)
            .border_style(if self.state.focused {
                Style::default().fg(Color::Yellow)
            } else {
                Style::default().fg(Color::DarkGray)
            })
            .title(self.title.as_deref().unwrap_or("Options"));

        let inner_area = block.inner(area);
        block.render(area, buf);

        for (idx, option) in self.config.options.iter().enumerate() {
            if idx >= inner_area.height as usize {
                break;
            }

            let y = inner_area.y + idx as u16;
            let is_selected = if self.config.multi_select {
                self.state.selected_values.contains(&idx)
            } else {
                self.state.selected_values.get(0) == Some(&idx)
            };

            let is_focused = idx == self.state.selected_index;

            // Render radio button symbol
            let symbol = if is_selected { "●" } else { "○" };
            let symbol_style = if !option.enabled {
                Style::default().fg(Color::DarkGray)
            } else if is_selected {
                Style::default().fg(Color::Green)
            } else {
                Style::default().fg(Color::White)
            };

            buf.set_string(inner_area.x, y, symbol, symbol_style);

            // Render label
            let label_style = if !option.enabled {
                Style::default().fg(Color::DarkGray)
            } else if is_focused {
                Style::default()
                    .fg(Color::Yellow)
                    .add_modifier(Modifier::BOLD)
            } else {
                Style::default()
            };

            let label_x = inner_area.x + 3;
            let max_label_width = inner_area.width.saturating_sub(3);
            let label = if option.label.len() > max_label_width as usize {
                format!("{}...", &option.label[..max_label_width as usize - 3])
            } else {
                option.label.clone()
            };

            buf.set_string(label_x, y, &label, label_style);

            // Render description if enabled and there's space
            if self.config.show_descriptions && option.description.is_some() {
                if let Some(desc) = &option.description {
                    let desc_y = y + 1;
                    if desc_y < inner_area.y + inner_area.height {
                        let desc_style = Style::default().fg(Color::Gray);
                        let desc_x = inner_area.x + 5;
                        let max_desc_width = inner_area.width.saturating_sub(5);
                        let desc_text = if desc.len() > max_desc_width as usize {
                            format!("{}...", &desc[..max_desc_width as usize - 3])
                        } else {
                            desc.clone()
                        };
                        buf.set_string(desc_x, desc_y, &desc_text, desc_style);
                    }
                }
            }
        }
    }
}

/// Dropdown widget implementation
pub struct DropdownWidget {
    id: String,
    title: Option<String>,
    config: OptionsConfig,
    state: OptionsState,
}

impl DropdownWidget {
    pub fn new(id: String, title: Option<String>, config: OptionsConfig) -> Self {
        Self {
            id,
            title,
            config,
            state: OptionsState::default(),
        }
    }

    fn handle_key_event(&mut self, key: KeyEvent) -> Result<()> {
        if self.state.expanded {
            match key.code {
                KeyCode::Up | KeyCode::Char('k') => {
                    if self.state.selected_index > 0 {
                        self.state.selected_index -= 1;
                        self.state.dirty = true;
                    }
                }
                KeyCode::Down | KeyCode::Char('j') => {
                    if self.state.selected_index < self.config.options.len() - 1 {
                        self.state.selected_index += 1;
                        self.state.dirty = true;
                    }
                }
                KeyCode::Enter => {
                    self.state.selected_values = vec![self.state.selected_index];
                    self.state.expanded = false;
                    self.state.dirty = true;
                }
                KeyCode::Esc => {
                    self.state.expanded = false;
                    self.state.dirty = true;
                }
                _ => {}
            }
        } else {
            match key.code {
                KeyCode::Enter | KeyCode::Char(' ') | KeyCode::Down => {
                    self.state.expanded = true;
                    self.state.dirty = true;
                }
                _ => {}
            }
        }
        Ok(())
    }

    pub fn render(&self, area: Rect, buf: &mut Buffer) {
        let block = Block::default()
            .borders(Borders::ALL)
            .border_style(if self.state.focused {
                Style::default().fg(Color::Yellow)
            } else {
                Style::default().fg(Color::DarkGray)
            })
            .title(self.title.as_deref().unwrap_or("Dropdown"));

        let inner_area = block.inner(area);
        block.render(area, buf);

        // Render current selection
        let selected_text = if let Some(&idx) = self.state.selected_values.get(0) {
            self.config
                .options
                .get(idx)
                .map(|o| o.label.as_str())
                .unwrap_or("None")
        } else {
            "Select an option..."
        };

        let dropdown_symbol = if self.state.expanded { "▼" } else { "▶" };

        buf.set_string(inner_area.x, inner_area.y, selected_text, Style::default());

        buf.set_string(
            inner_area.x + inner_area.width.saturating_sub(2),
            inner_area.y,
            dropdown_symbol,
            Style::default().fg(Color::Gray),
        );

        // Render expanded dropdown
        if self.state.expanded && inner_area.height > 1 {
            let dropdown_y = inner_area.y + 1;
            let max_items = self
                .config
                .max_visible_items
                .unwrap_or(5)
                .min((inner_area.height - 1) as usize);

            for (idx, option) in self.config.options.iter().enumerate().take(max_items) {
                let y = dropdown_y + idx as u16;
                if y >= inner_area.y + inner_area.height {
                    break;
                }

                let is_selected = idx == self.state.selected_index;
                let style = if !option.enabled {
                    Style::default().fg(Color::DarkGray)
                } else if is_selected {
                    Style::default()
                        .bg(Color::DarkGray)
                        .fg(Color::Yellow)
                        .add_modifier(Modifier::BOLD)
                } else {
                    Style::default().bg(Color::Black)
                };

                let prefix = if is_selected { "► " } else { "  " };
                let text = format!("{}{}", prefix, option.label);

                buf.set_string(inner_area.x, y, &text, style);
            }
        }
    }
}

/// Button group widget implementation
pub struct ButtonGroupWidget {
    id: String,
    title: Option<String>,
    config: OptionsConfig,
    state: OptionsState,
}

impl ButtonGroupWidget {
    pub fn new(id: String, title: Option<String>, config: OptionsConfig) -> Self {
        Self {
            id,
            title,
            config,
            state: OptionsState::default(),
        }
    }

    fn handle_key_event(&mut self, key: KeyEvent) -> Result<()> {
        let layout = self
            .config
            .button_layout
            .as_ref()
            .unwrap_or(&ButtonGroupLayout::Horizontal);

        match layout {
            ButtonGroupLayout::Horizontal => match key.code {
                KeyCode::Left | KeyCode::Char('h') => {
                    if self.state.selected_index > 0 {
                        self.state.selected_index -= 1;
                        self.state.dirty = true;
                    }
                }
                KeyCode::Right | KeyCode::Char('l') => {
                    if self.state.selected_index < self.config.options.len() - 1 {
                        self.state.selected_index += 1;
                        self.state.dirty = true;
                    }
                }
                KeyCode::Enter | KeyCode::Char(' ') => {
                    self.state.selected_values = vec![self.state.selected_index];
                    self.state.dirty = true;
                }
                _ => {}
            },
            ButtonGroupLayout::Vertical => match key.code {
                KeyCode::Up | KeyCode::Char('k') => {
                    if self.state.selected_index > 0 {
                        self.state.selected_index -= 1;
                        self.state.dirty = true;
                    }
                }
                KeyCode::Down | KeyCode::Char('j') => {
                    if self.state.selected_index < self.config.options.len() - 1 {
                        self.state.selected_index += 1;
                        self.state.dirty = true;
                    }
                }
                KeyCode::Enter | KeyCode::Char(' ') => {
                    self.state.selected_values = vec![self.state.selected_index];
                    self.state.dirty = true;
                }
                _ => {}
            },
            ButtonGroupLayout::Grid { columns } => {
                // Handle grid navigation
                match key.code {
                    KeyCode::Left | KeyCode::Char('h') => {
                        if self.state.selected_index > 0 {
                            self.state.selected_index -= 1;
                            self.state.dirty = true;
                        }
                    }
                    KeyCode::Right | KeyCode::Char('l') => {
                        if self.state.selected_index < self.config.options.len() - 1 {
                            self.state.selected_index += 1;
                            self.state.dirty = true;
                        }
                    }
                    KeyCode::Up | KeyCode::Char('k') => {
                        if self.state.selected_index >= *columns as usize {
                            self.state.selected_index -= *columns as usize;
                            self.state.dirty = true;
                        }
                    }
                    KeyCode::Down | KeyCode::Char('j') => {
                        let new_index = self.state.selected_index + *columns as usize;
                        if new_index < self.config.options.len() {
                            self.state.selected_index = new_index;
                            self.state.dirty = true;
                        }
                    }
                    KeyCode::Enter | KeyCode::Char(' ') => {
                        self.state.selected_values = vec![self.state.selected_index];
                        self.state.dirty = true;
                    }
                    _ => {}
                }
            }
        }
        Ok(())
    }

    pub fn render(&self, area: Rect, buf: &mut Buffer) {
        let block = Block::default()
            .borders(Borders::ALL)
            .border_style(if self.state.focused {
                Style::default().fg(Color::Yellow)
            } else {
                Style::default().fg(Color::DarkGray)
            })
            .title(self.title.as_deref().unwrap_or("Actions"));

        let inner_area = block.inner(area);
        block.render(area, buf);

        let layout = self
            .config
            .button_layout
            .as_ref()
            .unwrap_or(&ButtonGroupLayout::Horizontal);

        match layout {
            ButtonGroupLayout::Horizontal => {
                let button_width = inner_area.width / self.config.options.len() as u16;

                for (idx, option) in self.config.options.iter().enumerate() {
                    let x = inner_area.x + (idx as u16 * button_width);
                    let is_selected = self.state.selected_values.get(0) == Some(&idx);
                    let is_focused = idx == self.state.selected_index;

                    let style = if !option.enabled {
                        Style::default().fg(Color::DarkGray)
                    } else if is_selected {
                        Style::default()
                            .bg(Color::Green)
                            .fg(Color::Black)
                            .add_modifier(Modifier::BOLD)
                    } else if is_focused {
                        Style::default()
                            .bg(Color::DarkGray)
                            .fg(Color::Yellow)
                            .add_modifier(Modifier::BOLD)
                    } else {
                        Style::default()
                    };

                    let button_text = format!("[ {} ]", option.label);
                    let text_len = button_text.len().min(button_width as usize);

                    buf.set_string(x, inner_area.y, &button_text[..text_len], style);
                }
            }
            ButtonGroupLayout::Vertical => {
                for (idx, option) in self.config.options.iter().enumerate() {
                    if idx >= inner_area.height as usize {
                        break;
                    }

                    let y = inner_area.y + idx as u16;
                    let is_selected = self.state.selected_values.get(0) == Some(&idx);
                    let is_focused = idx == self.state.selected_index;

                    let style = if !option.enabled {
                        Style::default().fg(Color::DarkGray)
                    } else if is_selected {
                        Style::default()
                            .bg(Color::Green)
                            .fg(Color::Black)
                            .add_modifier(Modifier::BOLD)
                    } else if is_focused {
                        Style::default()
                            .bg(Color::DarkGray)
                            .fg(Color::Yellow)
                            .add_modifier(Modifier::BOLD)
                    } else {
                        Style::default()
                    };

                    let button_text = format!("[ {} ]", option.label);
                    buf.set_string(inner_area.x, y, &button_text, style);
                }
            }
            ButtonGroupLayout::Grid { columns } => {
                let button_width = inner_area.width / columns;

                for (idx, option) in self.config.options.iter().enumerate() {
                    let row = idx as u16 / columns;
                    let col = idx as u16 % columns;

                    if row >= inner_area.height {
                        break;
                    }

                    let x = inner_area.x + (col * button_width);
                    let y = inner_area.y + row;

                    let is_selected = self.state.selected_values.get(0) == Some(&idx);
                    let is_focused = idx == self.state.selected_index;

                    let style = if !option.enabled {
                        Style::default().fg(Color::DarkGray)
                    } else if is_selected {
                        Style::default()
                            .bg(Color::Green)
                            .fg(Color::Black)
                            .add_modifier(Modifier::BOLD)
                    } else if is_focused {
                        Style::default()
                            .bg(Color::DarkGray)
                            .fg(Color::Yellow)
                            .add_modifier(Modifier::BOLD)
                    } else {
                        Style::default()
                    };

                    let button_text = format!("[ {} ]", option.label);
                    let text_len = button_text.len().min(button_width as usize);

                    buf.set_string(x, y, &button_text[..text_len], style);
                }
            }
        }
    }
}

/// Unified options widget that delegates to specific implementations
pub struct OptionsWidget {
    inner: Box<dyn OptionsWidgetImpl>,
}

trait OptionsWidgetImpl: Send + Sync {
    fn id(&self) -> &str;
    fn widget_type(&self) -> &str;
    fn description(&self) -> &str;
    fn update_from_config(&mut self, config: &WidgetConfig) -> Result<()>;
    fn handle_event(&mut self, event: &WidgetUpdateEvent) -> Result<()>;
    fn get_state(&self) -> Value;
    fn get_config(&self) -> WidgetConfig;
    fn render(&self, area: Rect, buf: &mut Buffer);
    fn handle_key_event(&mut self, key: KeyEvent) -> Result<()>;
}

impl OptionsWidgetImpl for RadioButtonWidget {
    fn id(&self) -> &str {
        &self.id
    }

    fn widget_type(&self) -> &str {
        "radio"
    }

    fn description(&self) -> &str {
        "Radio button selection widget"
    }

    fn update_from_config(&mut self, config: &WidgetConfig) -> Result<()> {
        if let Ok(options_config) =
            serde_json::from_value::<OptionsConfig>(config.properties["config"].clone())
        {
            self.config = options_config;
            self.title = config.title.clone();
            Ok(())
        } else {
            Err(AutorunError::Config(
                "Invalid options configuration".to_string(),
            ))
        }
    }

    fn handle_event(&mut self, event: &WidgetUpdateEvent) -> Result<()> {
        match event.event_type.as_str() {
            "key_press" => {
                // For now, skip key event handling through this interface
                // Key events should be handled directly through the UI loop
                Ok(())
            }
            "focus" => {
                self.state.focused = event.data.as_bool().unwrap_or(false);
                Ok(())
            }
            "select" => {
                if let Some(index) = event.data.as_u64() {
                    self.state.selected_index = index as usize;
                    self.state.selected_values = vec![index as usize];
                    self.state.dirty = true;
                }
                Ok(())
            }
            _ => Ok(()),
        }
    }

    fn get_state(&self) -> Value {
        json!({
            "selected_index": self.state.selected_index,
            "selected_values": self.state.selected_values,
            "selected_options": self.state.selected_values.iter()
                .filter_map(|&idx| self.config.options.get(idx))
                .map(|opt| &opt.value)
                .collect::<Vec<_>>(),
            "focused": self.state.focused,
            "dirty": self.state.dirty,
        })
    }

    fn get_config(&self) -> WidgetConfig {
        WidgetConfig {
            widget_type: "radio".to_string(),
            id: self.id.clone(),
            title: self.title.clone(),
            properties: HashMap::from([(
                "config".to_string(),
                serde_json::to_value(&self.config).unwrap(),
            )]),
            layout: super::WidgetLayout {
                constraints: vec![super::LayoutConstraint::Percentage(100)],
                direction: super::LayoutDirection::Vertical,
            },
        }
    }

    fn render(&self, area: Rect, buf: &mut Buffer) {
        self.render(area, buf);
    }

    fn handle_key_event(&mut self, key: KeyEvent) -> Result<()> {
        self.handle_key_event(key)
    }
}

impl OptionsWidgetImpl for DropdownWidget {
    fn id(&self) -> &str {
        &self.id
    }

    fn widget_type(&self) -> &str {
        "dropdown"
    }

    fn description(&self) -> &str {
        "Dropdown selection widget"
    }

    fn update_from_config(&mut self, config: &WidgetConfig) -> Result<()> {
        if let Ok(options_config) =
            serde_json::from_value::<OptionsConfig>(config.properties["config"].clone())
        {
            self.config = options_config;
            self.title = config.title.clone();
            Ok(())
        } else {
            Err(AutorunError::Config(
                "Invalid options configuration".to_string(),
            ))
        }
    }

    fn handle_event(&mut self, event: &WidgetUpdateEvent) -> Result<()> {
        match event.event_type.as_str() {
            "key_press" => {
                // For now, skip key event handling through this interface
                // Key events should be handled directly through the UI loop
                Ok(())
            }
            "focus" => {
                self.state.focused = event.data.as_bool().unwrap_or(false);
                Ok(())
            }
            "toggle" => {
                self.state.expanded = !self.state.expanded;
                self.state.dirty = true;
                Ok(())
            }
            _ => Ok(()),
        }
    }

    fn get_state(&self) -> Value {
        json!({
            "selected_index": self.state.selected_index,
            "selected_values": self.state.selected_values,
            "selected_options": self.state.selected_values.iter()
                .filter_map(|&idx| self.config.options.get(idx))
                .map(|opt| &opt.value)
                .collect::<Vec<_>>(),
            "expanded": self.state.expanded,
            "focused": self.state.focused,
            "dirty": self.state.dirty,
        })
    }

    fn get_config(&self) -> WidgetConfig {
        WidgetConfig {
            widget_type: "dropdown".to_string(),
            id: self.id.clone(),
            title: self.title.clone(),
            properties: HashMap::from([(
                "config".to_string(),
                serde_json::to_value(&self.config).unwrap(),
            )]),
            layout: super::WidgetLayout {
                constraints: vec![super::LayoutConstraint::Length(3)],
                direction: super::LayoutDirection::Vertical,
            },
        }
    }

    fn render(&self, area: Rect, buf: &mut Buffer) {
        self.render(area, buf);
    }

    fn handle_key_event(&mut self, key: KeyEvent) -> Result<()> {
        self.handle_key_event(key)
    }
}

impl OptionsWidgetImpl for ButtonGroupWidget {
    fn id(&self) -> &str {
        &self.id
    }

    fn widget_type(&self) -> &str {
        "button_group"
    }

    fn description(&self) -> &str {
        "Button group selection widget"
    }

    fn update_from_config(&mut self, config: &WidgetConfig) -> Result<()> {
        if let Ok(options_config) =
            serde_json::from_value::<OptionsConfig>(config.properties["config"].clone())
        {
            self.config = options_config;
            self.title = config.title.clone();
            Ok(())
        } else {
            Err(AutorunError::Config(
                "Invalid options configuration".to_string(),
            ))
        }
    }

    fn handle_event(&mut self, event: &WidgetUpdateEvent) -> Result<()> {
        match event.event_type.as_str() {
            "key_press" => {
                // For now, skip key event handling through this interface
                // Key events should be handled directly through the UI loop
                Ok(())
            }
            "focus" => {
                self.state.focused = event.data.as_bool().unwrap_or(false);
                Ok(())
            }
            "click" => {
                if let Some(index) = event.data.as_u64() {
                    self.state.selected_index = index as usize;
                    self.state.selected_values = vec![index as usize];
                    self.state.dirty = true;
                }
                Ok(())
            }
            _ => Ok(()),
        }
    }

    fn get_state(&self) -> Value {
        json!({
            "selected_index": self.state.selected_index,
            "selected_values": self.state.selected_values,
            "selected_options": self.state.selected_values.iter()
                .filter_map(|&idx| self.config.options.get(idx))
                .map(|opt| &opt.value)
                .collect::<Vec<_>>(),
            "focused": self.state.focused,
            "dirty": self.state.dirty,
        })
    }

    fn get_config(&self) -> WidgetConfig {
        WidgetConfig {
            widget_type: "button_group".to_string(),
            id: self.id.clone(),
            title: self.title.clone(),
            properties: HashMap::from([
                (
                    "config".to_string(),
                    serde_json::to_value(&self.config).unwrap(),
                ),
                (
                    "button_layout".to_string(),
                    serde_json::to_value(&self.config.button_layout).unwrap(),
                ),
            ]),
            layout: super::WidgetLayout {
                constraints: vec![super::LayoutConstraint::Length(3)],
                direction: super::LayoutDirection::Horizontal,
            },
        }
    }

    fn render(&self, area: Rect, buf: &mut Buffer) {
        self.render(area, buf);
    }

    fn handle_key_event(&mut self, key: KeyEvent) -> Result<()> {
        self.handle_key_event(key)
    }
}

#[async_trait]
impl AiWidget for OptionsWidget {
    fn id(&self) -> &str {
        self.inner.id()
    }

    fn widget_type(&self) -> &str {
        self.inner.widget_type()
    }

    fn description(&self) -> &str {
        self.inner.description()
    }

    async fn update_from_config(&mut self, config: &WidgetConfig) -> Result<()> {
        self.inner.update_from_config(config)
    }

    async fn handle_event(&mut self, event: &WidgetUpdateEvent) -> Result<()> {
        self.inner.handle_event(event)
    }

    fn get_state(&self) -> Value {
        self.inner.get_state()
    }

    fn validate_layout(&self, area: Rect) -> Result<()> {
        if area.width < 10 || area.height < 3 {
            Err(AutorunError::Config(
                "Options widget requires at least 10x3 characters".to_string(),
            ))
        } else {
            Ok(())
        }
    }

    fn get_config(&self) -> WidgetConfig {
        self.inner.get_config()
    }
}

impl Widget for OptionsWidget {
    fn render(self, area: Rect, buf: &mut Buffer) {
        self.inner.render(area, buf);
    }
}

/// Builder for options widgets
pub struct OptionsWidgetBuilder;

#[async_trait]
impl WidgetBuilder for OptionsWidgetBuilder {
    async fn build(&self, request: &WidgetGenerationRequest) -> Result<Box<dyn AiWidget>> {
        let widget_type = request.config["display_mode"].as_str().unwrap_or("radio");

        let id = request.config["id"]
            .as_str()
            .unwrap_or("options-widget")
            .to_string();

        let title = request.config["title"].as_str().map(|s| s.to_string());

        let config: OptionsConfig = serde_json::from_value(request.config["config"].clone())
            .map_err(|e| AutorunError::Config(format!("Failed to parse options config: {}", e)))?;

        let inner: Box<dyn OptionsWidgetImpl> = match widget_type {
            "dropdown" => Box::new(DropdownWidget::new(id, title, config)),
            "button_group" => Box::new(ButtonGroupWidget::new(id, title, config)),
            _ => Box::new(RadioButtonWidget::new(id, title, config)),
        };

        Ok(Box::new(OptionsWidget { inner }))
    }

    fn get_schema(&self) -> Value {
        json!({
            "$schema": "http://json-schema.org/draft-07/schema#",
            "type": "object",
            "properties": {
                "id": {
                    "type": "string",
                    "description": "Unique identifier for the widget"
                },
                "title": {
                    "type": "string",
                    "description": "Title displayed in the widget border"
                },
                "display_mode": {
                    "type": "string",
                    "enum": ["radio", "dropdown", "button_group"],
                    "description": "Display mode for the options"
                },
                "config": {
                    "type": "object",
                    "properties": {
                        "options": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "label": { "type": "string" },
                                    "value": {},
                                    "description": { "type": "string" },
                                    "enabled": { "type": "boolean", "default": true },
                                    "icon": { "type": "string" }
                                },
                                "required": ["label", "value"]
                            }
                        },
                        "multi_select": {
                            "type": "boolean",
                            "default": false,
                            "description": "Allow multiple selections"
                        },
                        "required": {
                            "type": "boolean",
                            "default": false,
                            "description": "Whether a selection is required"
                        },
                        "button_layout": {
                            "type": "object",
                            "properties": {
                                "type": {
                                    "type": "string",
                                    "enum": ["horizontal", "vertical", "grid"]
                                },
                                "columns": {
                                    "type": "number",
                                    "description": "Number of columns for grid layout"
                                }
                            }
                        },
                        "show_descriptions": {
                            "type": "boolean",
                            "default": false,
                            "description": "Show option descriptions"
                        },
                        "max_visible_items": {
                            "type": "number",
                            "description": "Maximum visible items in dropdown"
                        },
                        "validation_rules": {
                            "type": "object",
                            "description": "Custom validation rules"
                        }
                    },
                    "required": ["options"]
                }
            },
            "required": ["id", "config"]
        })
    }

    fn validate_config(&self, config: &WidgetConfig) -> Result<()> {
        if config.widget_type != "options" {
            return Err(AutorunError::Config(
                "Invalid widget type for OptionsWidgetBuilder".to_string(),
            ));
        }

        // Validate that we have required fields
        if !config.properties.contains_key("config") {
            return Err(AutorunError::Config(
                "Missing 'config' property".to_string(),
            ));
        }

        // Try to parse the config to validate structure
        let _: OptionsConfig = serde_json::from_value(config.properties["config"].clone())
            .map_err(|e| AutorunError::Config(format!("Invalid options configuration: {}", e)))?;

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_options_state_default() {
        let state = OptionsState::default();
        assert_eq!(state.selected_index, 0);
        assert!(state.selected_values.is_empty());
        assert!(!state.expanded);
        assert!(!state.focused);
        assert!(!state.dirty);
    }

    #[test]
    fn test_option_item_serialization() {
        let item = OptionItem {
            label: "Option 1".to_string(),
            value: json!("value1"),
            description: Some("Description".to_string()),
            enabled: true,
            icon: Some("📝".to_string()),
        };

        let serialized = serde_json::to_string(&item).unwrap();
        let deserialized: OptionItem = serde_json::from_str(&serialized).unwrap();

        assert_eq!(item.label, deserialized.label);
        assert_eq!(item.value, deserialized.value);
        assert_eq!(item.description, deserialized.description);
    }

    #[tokio::test]
    async fn test_options_widget_builder() {
        let builder = OptionsWidgetBuilder;
        let request = WidgetGenerationRequest {
            widget_type: "options".to_string(),
            context: "Test context".to_string(),
            config: json!({
                "id": "test-options",
                "title": "Test Options",
                "display_mode": "radio",
                "config": {
                    "options": [
                        {
                            "label": "Option 1",
                            "value": "opt1"
                        },
                        {
                            "label": "Option 2",
                            "value": "opt2"
                        }
                    ],
                    "multi_select": false,
                    "required": true
                }
            }),
        };

        let widget = builder.build(&request).await.unwrap();
        assert_eq!(widget.id(), "test-options");
        assert_eq!(widget.widget_type(), "radio");
    }

    #[test]
    fn test_radio_button_key_navigation() {
        let config = OptionsConfig {
            display_mode: OptionsDisplayMode::Radio,
            options: vec![
                OptionItem {
                    label: "Option 1".to_string(),
                    value: json!("opt1"),
                    description: None,
                    enabled: true,
                    icon: None,
                },
                OptionItem {
                    label: "Option 2".to_string(),
                    value: json!("opt2"),
                    description: None,
                    enabled: true,
                    icon: None,
                },
            ],
            multi_select: false,
            required: true,
            button_layout: None,
            show_descriptions: false,
            max_visible_items: None,
            validation_rules: None,
        };

        let mut widget =
            RadioButtonWidget::new("test".to_string(), Some("Test Radio".to_string()), config);

        assert_eq!(widget.state.selected_index, 0);

        // Test down navigation
        widget
            .handle_key_event(KeyEvent::new(
                KeyCode::Down,
                crossterm::event::KeyModifiers::NONE,
            ))
            .unwrap();
        assert_eq!(widget.state.selected_index, 1);

        // Test up navigation
        widget
            .handle_key_event(KeyEvent::new(
                KeyCode::Up,
                crossterm::event::KeyModifiers::NONE,
            ))
            .unwrap();
        assert_eq!(widget.state.selected_index, 0);

        // Test selection
        widget
            .handle_key_event(KeyEvent::new(
                KeyCode::Enter,
                crossterm::event::KeyModifiers::NONE,
            ))
            .unwrap();
        assert_eq!(widget.state.selected_values, vec![0]);
    }
}
