// AI-powered widget system for dynamic TUI components
// Built on Ratatui's StatefulWidget trait with AI integration

pub mod checkbox;
pub mod enhanced_list;
pub mod mention_popup;
pub mod options;

use async_trait::async_trait;
use dashmap::DashMap;
use ratatui::prelude::*;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::{collections::HashMap, sync::Arc};
use tracing::debug;

use crate::agent::core::{WidgetGenerationRequest, WidgetUpdateEvent};
use crate::errors::Result;

/// Configuration for widget instantiation from AI/JSON
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WidgetConfig {
    pub widget_type: String,
    pub id: String,
    pub title: Option<String>,
    pub properties: HashMap<String, Value>,
    pub layout: WidgetLayout,
}

/// Widget layout configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct WidgetLayout {
    pub constraints: Vec<LayoutConstraint>,
    pub direction: LayoutDirection,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LayoutConstraint {
    Length(u16),
    Min(u16),
    Max(u16),
    Percentage(u16),
    Ratio(u32, u32),
    Fill(u16),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LayoutDirection {
    Horizontal,
    Vertical,
}

/// Base trait for AI-powered widgets
#[async_trait]
pub trait AiWidget: Send + Sync {
    /// Unique identifier for this widget instance
    fn id(&self) -> &str;

    /// Widget type identifier (e.g., "checkbox", "list", "options")
    fn widget_type(&self) -> &str;

    /// Human-readable description of the widget
    fn description(&self) -> &str;

    /// Update widget state from AI/JSON configuration
    async fn update_from_config(&mut self, config: &WidgetConfig) -> Result<()>;

    /// Handle widget-specific events
    async fn handle_event(&mut self, event: &WidgetUpdateEvent) -> Result<()>;

    /// Get current widget state as JSON for AI interaction
    fn get_state(&self) -> Value;

    /// Validate if widget can be rendered in given area
    fn validate_layout(&self, area: Rect) -> Result<()>;

    /// Get the current widget configuration
    fn get_config(&self) -> WidgetConfig;
}

/// Widget state management for stateful widgets
pub trait WidgetState {
    fn reset(&mut self);
    fn is_dirty(&self) -> bool;
    fn mark_clean(&mut self);
}

/// Factory for creating widgets from AI requests
pub struct WidgetFactory {
    widget_types: DashMap<String, Arc<dyn WidgetBuilder>>,
}

#[async_trait]
pub trait WidgetBuilder: Send + Sync {
    /// Build a widget from AI generation request
    async fn build(&self, request: &WidgetGenerationRequest) -> Result<Box<dyn AiWidget>>;

    /// Get JSON schema for this widget type's configuration
    fn get_schema(&self) -> Value;

    /// Validate configuration before building
    fn validate_config(&self, config: &WidgetConfig) -> Result<()>;
}

impl WidgetFactory {
    pub fn new() -> Self {
        Self {
            widget_types: DashMap::new(),
        }
    }

    /// Register a new widget builder
    pub fn register_builder<T: WidgetBuilder + 'static>(&self, widget_type: String, builder: T) {
        let builder_arc = Arc::new(builder);
        self.widget_types.insert(widget_type.clone(), builder_arc);
        debug!("Registered widget builder for type: {}", widget_type);
    }

    /// Create widget from AI generation request
    pub async fn create_widget(
        &self,
        request: &WidgetGenerationRequest,
    ) -> Result<Box<dyn AiWidget>> {
        let builder = self.widget_types.get(&request.widget_type).ok_or_else(|| {
            crate::errors::AutorunError::NotFound(format!(
                "Unknown widget type: {}",
                request.widget_type
            ))
        })?;

        debug!("Creating widget of type: {}", request.widget_type);
        builder.build(request).await
    }

    /// Get all available widget types and their schemas
    pub fn get_available_widgets(&self) -> HashMap<String, Value> {
        self.widget_types
            .iter()
            .map(|entry| {
                let widget_type = entry.key().clone();
                let schema = entry.value().get_schema();
                (widget_type, schema)
            })
            .collect()
    }

    /// Validate widget configuration
    pub fn validate_widget_config(&self, config: &WidgetConfig) -> Result<()> {
        let builder = self.widget_types.get(&config.widget_type).ok_or_else(|| {
            crate::errors::AutorunError::NotFound(format!(
                "Unknown widget type: {}",
                config.widget_type
            ))
        })?;

        builder.validate_config(config)
    }
}

impl Default for WidgetFactory {
    fn default() -> Self {
        Self::new()
    }
}

/// Registry for managing active widget instances
pub struct WidgetRegistry {
    widgets: DashMap<String, Box<dyn AiWidget>>,
    factory: Arc<WidgetFactory>,
}

impl WidgetRegistry {
    pub fn new(factory: Arc<WidgetFactory>) -> Self {
        Self {
            widgets: DashMap::new(),
            factory,
        }
    }

    /// Create and register a new widget
    pub async fn create_widget(&self, request: &WidgetGenerationRequest) -> Result<String> {
        let widget = self.factory.create_widget(request).await?;
        let widget_id = widget.id().to_string();

        // Store widget in registry
        self.widgets.insert(widget_id.clone(), widget);
        debug!("Registered widget with ID: {}", widget_id);

        Ok(widget_id)
    }

    /// Get widget by ID
    pub fn get_widget(
        &self,
        id: &str,
    ) -> Option<dashmap::mapref::one::Ref<String, Box<dyn AiWidget>>> {
        self.widgets.get(id)
    }

    /// Update widget with event
    pub async fn update_widget(&self, event: &WidgetUpdateEvent) -> Result<()> {
        if let Some(mut widget) = self.widgets.get_mut(&event.widget_id) {
            widget.handle_event(event).await?;
            debug!(
                "Updated widget {} with event type: {}",
                event.widget_id, event.event_type
            );
            Ok(())
        } else {
            Err(crate::errors::AutorunError::NotFound(format!(
                "Widget not found: {}",
                event.widget_id
            )))
        }
    }

    /// Remove widget from registry
    pub fn remove_widget(&self, id: &str) -> Result<()> {
        if self.widgets.remove(id).is_some() {
            debug!("Removed widget: {}", id);
            Ok(())
        } else {
            Err(crate::errors::AutorunError::NotFound(format!(
                "Widget not found: {}",
                id
            )))
        }
    }

    /// List all widget IDs
    pub fn list_widgets(&self) -> Vec<String> {
        self.widgets
            .iter()
            .map(|entry| entry.key().clone())
            .collect()
    }

    /// Get widget configurations for serialization
    pub fn get_all_configs(&self) -> HashMap<String, WidgetConfig> {
        self.widgets
            .iter()
            .map(|entry| {
                let id = entry.key().clone();
                let config = entry.value().get_config();
                (id, config)
            })
            .collect()
    }
}

/// Utility functions for converting between layout types
impl From<LayoutConstraint> for ratatui::layout::Constraint {
    fn from(constraint: LayoutConstraint) -> Self {
        match constraint {
            LayoutConstraint::Length(n) => ratatui::layout::Constraint::Length(n),
            LayoutConstraint::Min(n) => ratatui::layout::Constraint::Min(n),
            LayoutConstraint::Max(n) => ratatui::layout::Constraint::Max(n),
            LayoutConstraint::Percentage(n) => ratatui::layout::Constraint::Percentage(n),
            LayoutConstraint::Ratio(a, b) => ratatui::layout::Constraint::Ratio(a, b),
            LayoutConstraint::Fill(n) => ratatui::layout::Constraint::Fill(n),
        }
    }
}

impl From<LayoutDirection> for ratatui::layout::Direction {
    fn from(direction: LayoutDirection) -> Self {
        match direction {
            LayoutDirection::Horizontal => ratatui::layout::Direction::Horizontal,
            LayoutDirection::Vertical => ratatui::layout::Direction::Vertical,
        }
    }
}

/// Helper function to create layout from widget configuration
pub fn create_layout(layout_config: &WidgetLayout, area: Rect) -> Vec<Rect> {
    let constraints: Vec<ratatui::layout::Constraint> = layout_config
        .constraints
        .iter()
        .map(|c| c.clone().into())
        .collect();

    ratatui::layout::Layout::default()
        .direction(layout_config.direction.clone().into())
        .constraints(constraints)
        .split(area)
        .to_vec()
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;

    #[tokio::test]
    async fn test_widget_factory_creation() {
        let factory = WidgetFactory::new();
        assert!(factory.get_available_widgets().is_empty());
    }

    #[test]
    fn test_layout_constraint_conversion() {
        let constraint = LayoutConstraint::Percentage(50);
        let ratatui_constraint: ratatui::layout::Constraint = constraint.into();

        match ratatui_constraint {
            ratatui::layout::Constraint::Percentage(50) => {}
            _ => panic!("Constraint conversion failed"),
        }
    }

    #[test]
    fn test_widget_config_serialization() {
        let config = WidgetConfig {
            widget_type: "checkbox".to_string(),
            id: "test-checkbox".to_string(),
            title: Some("Test Checkbox".to_string()),
            properties: HashMap::from([
                ("checked".to_string(), json!(false)),
                ("label".to_string(), json!("Enable feature")),
            ]),
            layout: WidgetLayout {
                constraints: vec![LayoutConstraint::Percentage(100)],
                direction: LayoutDirection::Horizontal,
            },
        };

        let serialized = serde_json::to_string(&config).unwrap();
        let deserialized: WidgetConfig = serde_json::from_str(&serialized).unwrap();

        assert_eq!(config.widget_type, deserialized.widget_type);
        assert_eq!(config.id, deserialized.id);
    }
}

// Export widget modules
pub use checkbox::{
    CheckboxConfig, CheckboxDisplayMode, CheckboxItem, CheckboxState, CheckboxWidget,
    CheckboxWidgetBuilder,
};
pub use options::{
    ButtonGroupLayout, OptionItem, OptionsConfig, OptionsDisplayMode, OptionsState, OptionsWidget,
    OptionsWidgetBuilder,
};
