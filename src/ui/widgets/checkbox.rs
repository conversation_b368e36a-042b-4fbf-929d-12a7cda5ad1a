// Checkbox widget system for TUI with Unicode symbols
// Supports single checkbox and checkbox groups with AI integration

use async_trait::async_trait;
use crossterm::event::{KeyC<PERSON>, KeyEvent};
use ratatui::prelude::*;
use ratatui::widgets::{Block, Borders, Widget};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::collections::HashMap;

use crate::agent::core::{WidgetGenerationRequest, WidgetUpdateEvent};
use crate::errors::{AutorunError, Result};

use super::{AiWidget, WidgetBuilder, WidgetConfig, WidgetState};

/// Unicode symbols for checkbox states
const CHECKBOX_UNCHECKED: &str = "□"; // U+25A1
const CHECKBOX_CHECKED: &str = "■"; // U+25A0
const CHECKBOX_PARTIAL: &str = "▣"; // U+25A3 (for tri-state checkboxes)

/// State for checkbox widget
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CheckboxState {
    pub selected_index: usize,
    pub checked_items: Vec<usize>,
    pub focused: bool,
    pub dirty: bool,
}

impl Default for CheckboxState {
    fn default() -> Self {
        Self {
            selected_index: 0,
            checked_items: Vec::new(),
            focused: false,
            dirty: false,
        }
    }
}

impl WidgetState for CheckboxState {
    fn reset(&mut self) {
        self.selected_index = 0;
        self.checked_items.clear();
        self.focused = false;
        self.dirty = false;
    }

    fn is_dirty(&self) -> bool {
        self.dirty
    }

    fn mark_clean(&mut self) {
        self.dirty = false;
    }
}

/// Configuration for an individual checkbox item
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CheckboxItem {
    pub label: String,
    pub value: Value,
    pub checked: bool,
    pub enabled: bool,
    pub description: Option<String>,
}

/// Display mode for checkboxes
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum CheckboxDisplayMode {
    Single,   // Single checkbox
    Group,    // Multiple checkboxes in a group
    TriState, // Tri-state checkbox (checked, unchecked, partial)
}

/// Configuration for checkbox widget
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CheckboxConfig {
    pub display_mode: CheckboxDisplayMode,
    pub items: Vec<CheckboxItem>,
    pub allow_multiple: bool,
    pub required: bool,
    pub show_descriptions: bool,
    pub validation_rules: Option<HashMap<String, Value>>,
}

/// Checkbox widget implementation
pub struct CheckboxWidget {
    id: String,
    title: Option<String>,
    config: CheckboxConfig,
    state: CheckboxState,
}

impl CheckboxWidget {
    pub fn new(id: String, title: Option<String>, config: CheckboxConfig) -> Self {
        // Initialize checked items from config
        let mut state = CheckboxState::default();
        for (idx, item) in config.items.iter().enumerate() {
            if item.checked {
                state.checked_items.push(idx);
            }
        }

        Self {
            id,
            title,
            config,
            state,
        }
    }

    fn handle_key_event(&mut self, key: KeyEvent) -> Result<()> {
        match key.code {
            KeyCode::Up | KeyCode::Char('k') => {
                if self.state.selected_index > 0 {
                    self.state.selected_index -= 1;
                    self.state.dirty = true;
                }
            }
            KeyCode::Down | KeyCode::Char('j') => {
                if self.state.selected_index < self.config.items.len() - 1 {
                    self.state.selected_index += 1;
                    self.state.dirty = true;
                }
            }
            KeyCode::Enter | KeyCode::Char(' ') => {
                self.toggle_current_item();
            }
            KeyCode::Char('a') => {
                // Select all (if multiple allowed)
                if self.config.allow_multiple {
                    self.state.checked_items.clear();
                    for idx in 0..self.config.items.len() {
                        if self.config.items[idx].enabled {
                            self.state.checked_items.push(idx);
                        }
                    }
                    self.state.dirty = true;
                }
            }
            KeyCode::Char('n') => {
                // Select none
                self.state.checked_items.clear();
                self.state.dirty = true;
            }
            _ => {}
        }
        Ok(())
    }

    fn toggle_current_item(&mut self) {
        let idx = self.state.selected_index;
        if !self.config.items[idx].enabled {
            return;
        }

        if self.config.allow_multiple {
            if self.state.checked_items.contains(&idx) {
                self.state.checked_items.retain(|&x| x != idx);
            } else {
                self.state.checked_items.push(idx);
            }
        } else {
            // Single selection mode
            if self.state.checked_items.contains(&idx) {
                self.state.checked_items.clear();
            } else {
                self.state.checked_items = vec![idx];
            }
        }
        self.state.dirty = true;
    }

    pub fn render(&self, area: Rect, buf: &mut Buffer) {
        let block = Block::default()
            .borders(Borders::ALL)
            .border_style(if self.state.focused {
                Style::default().fg(Color::Yellow)
            } else {
                Style::default().fg(Color::DarkGray)
            })
            .title(self.title.as_deref().unwrap_or("Checkbox"));

        let inner_area = block.inner(area);
        block.render(area, buf);

        let items_with_descriptions = self.config.show_descriptions
            && self
                .config
                .items
                .iter()
                .any(|item| item.description.is_some());

        let _item_height = if items_with_descriptions { 2 } else { 1 };
        let mut current_y = inner_area.y;

        for (idx, item) in self.config.items.iter().enumerate() {
            if current_y >= inner_area.y + inner_area.height {
                break;
            }

            let is_checked = self.state.checked_items.contains(&idx);
            let is_focused = idx == self.state.selected_index;

            // Determine checkbox symbol
            let symbol = match self.config.display_mode {
                CheckboxDisplayMode::TriState => {
                    // For tri-state, we'd need additional logic here
                    if is_checked {
                        CHECKBOX_CHECKED
                    } else {
                        CHECKBOX_UNCHECKED
                    }
                }
                _ => {
                    if is_checked {
                        CHECKBOX_CHECKED
                    } else {
                        CHECKBOX_UNCHECKED
                    }
                }
            };

            // Checkbox symbol style
            let symbol_style = if !item.enabled {
                Style::default().fg(Color::DarkGray)
            } else if is_checked {
                Style::default().fg(Color::Green)
            } else {
                Style::default().fg(Color::White)
            };

            // Render checkbox symbol
            buf.set_string(inner_area.x, current_y, symbol, symbol_style);

            // Label style
            let label_style = if !item.enabled {
                Style::default().fg(Color::DarkGray)
            } else if is_focused {
                Style::default()
                    .fg(Color::Yellow)
                    .add_modifier(Modifier::BOLD)
            } else {
                Style::default()
            };

            // Render label
            let label_x = inner_area.x + 3;
            let max_label_width = inner_area.width.saturating_sub(3);
            let label = if item.label.len() > max_label_width as usize {
                format!("{}...", &item.label[..max_label_width as usize - 3])
            } else {
                item.label.clone()
            };

            buf.set_string(label_x, current_y, &label, label_style);

            // Render description if enabled
            if self.config.show_descriptions && item.description.is_some() {
                if let Some(desc) = &item.description {
                    current_y += 1;
                    if current_y < inner_area.y + inner_area.height {
                        let desc_style = Style::default().fg(Color::Gray);
                        let desc_x = inner_area.x + 5;
                        let max_desc_width = inner_area.width.saturating_sub(5);
                        let desc_text = if desc.len() > max_desc_width as usize {
                            format!("{}...", &desc[..max_desc_width as usize - 3])
                        } else {
                            desc.clone()
                        };
                        buf.set_string(desc_x, current_y, &desc_text, desc_style);
                    }
                }
            }

            current_y += 1;
        }

        // Render help text at bottom if there's space
        if self.state.focused && current_y + 1 < inner_area.y + inner_area.height {
            let help_text = if self.config.allow_multiple {
                "Space: Toggle | a: All | n: None"
            } else {
                "Space: Toggle"
            };
            let help_style = Style::default().fg(Color::Gray);
            buf.set_string(
                inner_area.x,
                inner_area.y + inner_area.height - 1,
                help_text,
                help_style,
            );
        }
    }
}

#[async_trait]
impl AiWidget for CheckboxWidget {
    fn id(&self) -> &str {
        &self.id
    }

    fn widget_type(&self) -> &str {
        "checkbox"
    }

    fn description(&self) -> &str {
        "Checkbox selection widget with Unicode symbols"
    }

    async fn update_from_config(&mut self, config: &WidgetConfig) -> Result<()> {
        if let Ok(checkbox_config) =
            serde_json::from_value::<CheckboxConfig>(config.properties["config"].clone())
        {
            self.config = checkbox_config;
            self.title = config.title.clone();

            // Update state based on new config
            self.state.checked_items.clear();
            for (idx, item) in self.config.items.iter().enumerate() {
                if item.checked {
                    self.state.checked_items.push(idx);
                }
            }

            Ok(())
        } else {
            Err(AutorunError::Config(
                "Invalid checkbox configuration".to_string(),
            ))
        }
    }

    async fn handle_event(&mut self, event: &WidgetUpdateEvent) -> Result<()> {
        match event.event_type.as_str() {
            "key_press" => {
                // For now, skip key event handling through this interface
                // Key events should be handled directly through the UI loop
                Ok(())
            }
            "focus" => {
                self.state.focused = event.data.as_bool().unwrap_or(false);
                Ok(())
            }
            "check" => {
                if let Some(index) = event.data.as_u64() {
                    let idx = index as usize;
                    if idx < self.config.items.len() && self.config.items[idx].enabled {
                        if !self.state.checked_items.contains(&idx) {
                            self.state.checked_items.push(idx);
                            self.state.dirty = true;
                        }
                    }
                }
                Ok(())
            }
            "uncheck" => {
                if let Some(index) = event.data.as_u64() {
                    let idx = index as usize;
                    self.state.checked_items.retain(|&x| x != idx);
                    self.state.dirty = true;
                }
                Ok(())
            }
            "toggle" => {
                if let Some(index) = event.data.as_u64() {
                    self.state.selected_index = index as usize;
                    self.toggle_current_item();
                }
                Ok(())
            }
            _ => Ok(()),
        }
    }

    fn get_state(&self) -> Value {
        let checked_values: Vec<&Value> = self
            .state
            .checked_items
            .iter()
            .filter_map(|&idx| self.config.items.get(idx))
            .map(|item| &item.value)
            .collect();

        json!({
            "selected_index": self.state.selected_index,
            "checked_indices": self.state.checked_items,
            "checked_values": checked_values,
            "checked_labels": self.state.checked_items.iter()
                .filter_map(|&idx| self.config.items.get(idx))
                .map(|item| &item.label)
                .collect::<Vec<_>>(),
            "focused": self.state.focused,
            "dirty": self.state.dirty,
        })
    }

    fn validate_layout(&self, area: Rect) -> Result<()> {
        let min_height = if self.config.show_descriptions {
            self.config.items.len() * 2 + 2
        } else {
            self.config.items.len() + 2
        };

        if area.width < 10 || area.height < min_height.min(5) as u16 {
            Err(AutorunError::Config(format!(
                "Checkbox widget requires at least 10x{} characters",
                min_height.min(5)
            )))
        } else {
            Ok(())
        }
    }

    fn get_config(&self) -> WidgetConfig {
        WidgetConfig {
            widget_type: "checkbox".to_string(),
            id: self.id.clone(),
            title: self.title.clone(),
            properties: HashMap::from([(
                "config".to_string(),
                serde_json::to_value(&self.config).unwrap(),
            )]),
            layout: super::WidgetLayout {
                constraints: vec![super::LayoutConstraint::Min(3)],
                direction: super::LayoutDirection::Vertical,
            },
        }
    }
}

impl Widget for CheckboxWidget {
    fn render(self, area: Rect, buf: &mut Buffer) {
        CheckboxWidget::render(&self, area, buf);
    }
}

/// Builder for checkbox widgets
pub struct CheckboxWidgetBuilder;

#[async_trait]
impl WidgetBuilder for CheckboxWidgetBuilder {
    async fn build(&self, request: &WidgetGenerationRequest) -> Result<Box<dyn AiWidget>> {
        let id = request.config["id"]
            .as_str()
            .unwrap_or("checkbox-widget")
            .to_string();

        let title = request.config["title"].as_str().map(|s| s.to_string());

        let config: CheckboxConfig = serde_json::from_value(request.config["config"].clone())
            .map_err(|e| AutorunError::Config(format!("Failed to parse checkbox config: {}", e)))?;

        Ok(Box::new(CheckboxWidget::new(id, title, config)))
    }

    fn get_schema(&self) -> Value {
        json!({
            "$schema": "http://json-schema.org/draft-07/schema#",
            "type": "object",
            "properties": {
                "id": {
                    "type": "string",
                    "description": "Unique identifier for the widget"
                },
                "title": {
                    "type": "string",
                    "description": "Title displayed in the widget border"
                },
                "config": {
                    "type": "object",
                    "properties": {
                        "display_mode": {
                            "type": "string",
                            "enum": ["single", "group", "tristate"],
                            "default": "group",
                            "description": "Display mode for checkboxes"
                        },
                        "items": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "label": {
                                        "type": "string",
                                        "description": "Display label for the checkbox"
                                    },
                                    "value": {
                                        "description": "Value associated with the checkbox"
                                    },
                                    "checked": {
                                        "type": "boolean",
                                        "default": false,
                                        "description": "Initial checked state"
                                    },
                                    "enabled": {
                                        "type": "boolean",
                                        "default": true,
                                        "description": "Whether the checkbox is enabled"
                                    },
                                    "description": {
                                        "type": "string",
                                        "description": "Optional description shown below the label"
                                    }
                                },
                                "required": ["label", "value"]
                            },
                            "description": "List of checkbox items"
                        },
                        "allow_multiple": {
                            "type": "boolean",
                            "default": true,
                            "description": "Allow multiple selections (false for single selection)"
                        },
                        "required": {
                            "type": "boolean",
                            "default": false,
                            "description": "Whether at least one selection is required"
                        },
                        "show_descriptions": {
                            "type": "boolean",
                            "default": false,
                            "description": "Show item descriptions below labels"
                        },
                        "validation_rules": {
                            "type": "object",
                            "description": "Custom validation rules"
                        }
                    },
                    "required": ["items"]
                }
            },
            "required": ["id", "config"]
        })
    }

    fn validate_config(&self, config: &WidgetConfig) -> Result<()> {
        if config.widget_type != "checkbox" {
            return Err(AutorunError::Config(
                "Invalid widget type for CheckboxWidgetBuilder".to_string(),
            ));
        }

        // Validate that we have required fields
        if !config.properties.contains_key("config") {
            return Err(AutorunError::Config(
                "Missing 'config' property".to_string(),
            ));
        }

        // Try to parse the config to validate structure
        let checkbox_config: CheckboxConfig =
            serde_json::from_value(config.properties["config"].clone()).map_err(|e| {
                AutorunError::Config(format!("Invalid checkbox configuration: {}", e))
            })?;

        // Validate that we have at least one item
        if checkbox_config.items.is_empty() {
            return Err(AutorunError::Config(
                "Checkbox widget must have at least one item".to_string(),
            ));
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crossterm::event::KeyModifiers;

    #[test]
    fn test_checkbox_state_default() {
        let state = CheckboxState::default();
        assert_eq!(state.selected_index, 0);
        assert!(state.checked_items.is_empty());
        assert!(!state.focused);
        assert!(!state.dirty);
    }

    #[test]
    fn test_checkbox_item_serialization() {
        let item = CheckboxItem {
            label: "Enable feature".to_string(),
            value: json!(true),
            checked: false,
            enabled: true,
            description: Some("Enable this awesome feature".to_string()),
        };

        let serialized = serde_json::to_string(&item).unwrap();
        let deserialized: CheckboxItem = serde_json::from_str(&serialized).unwrap();

        assert_eq!(item.label, deserialized.label);
        assert_eq!(item.value, deserialized.value);
        assert_eq!(item.checked, deserialized.checked);
        assert_eq!(item.description, deserialized.description);
    }

    #[tokio::test]
    async fn test_checkbox_widget_builder() {
        let builder = CheckboxWidgetBuilder;
        let request = WidgetGenerationRequest {
            widget_type: "checkbox".to_string(),
            context: "Test context".to_string(),
            config: json!({
                "id": "test-checkbox",
                "title": "Settings",
                "config": {
                    "display_mode": "group",
                    "items": [
                        {
                            "label": "Option 1",
                            "value": "opt1",
                            "checked": true,
                            "enabled": true
                        },
                        {
                            "label": "Option 2",
                            "value": "opt2",
                            "checked": false,
                            "enabled": true
                        }
                    ],
                    "allow_multiple": true,
                    "required": false,
                    "show_descriptions": false
                }
            }),
        };

        let widget = builder.build(&request).await.unwrap();
        assert_eq!(widget.id(), "test-checkbox");
        assert_eq!(widget.widget_type(), "checkbox");
    }

    #[test]
    fn test_checkbox_key_navigation() {
        let config = CheckboxConfig {
            display_mode: CheckboxDisplayMode::Group,
            items: vec![
                CheckboxItem {
                    label: "Option 1".to_string(),
                    value: json!("opt1"),
                    checked: false,
                    enabled: true,
                    description: None,
                },
                CheckboxItem {
                    label: "Option 2".to_string(),
                    value: json!("opt2"),
                    checked: true,
                    enabled: true,
                    description: None,
                },
                CheckboxItem {
                    label: "Option 3".to_string(),
                    value: json!("opt3"),
                    checked: false,
                    enabled: false, // disabled
                    description: None,
                },
            ],
            allow_multiple: true,
            required: false,
            show_descriptions: false,
            validation_rules: None,
        };

        let mut widget = CheckboxWidget::new(
            "test".to_string(),
            Some("Test Checkbox".to_string()),
            config,
        );

        // Initial state should have Option 2 checked
        assert_eq!(widget.state.checked_items, vec![1]);
        assert_eq!(widget.state.selected_index, 0);

        // Test down navigation
        widget
            .handle_key_event(KeyEvent::new(KeyCode::Down, KeyModifiers::NONE))
            .unwrap();
        assert_eq!(widget.state.selected_index, 1);

        // Test up navigation
        widget
            .handle_key_event(KeyEvent::new(KeyCode::Up, KeyModifiers::NONE))
            .unwrap();
        assert_eq!(widget.state.selected_index, 0);

        // Test toggle
        widget
            .handle_key_event(KeyEvent::new(KeyCode::Enter, KeyModifiers::NONE))
            .unwrap();
        assert!(widget.state.checked_items.contains(&0));
        assert!(widget.state.checked_items.contains(&1));

        // Test select all
        widget
            .handle_key_event(KeyEvent::new(KeyCode::Char('a'), KeyModifiers::NONE))
            .unwrap();
        assert!(widget.state.checked_items.contains(&0));
        assert!(widget.state.checked_items.contains(&1));
        assert!(!widget.state.checked_items.contains(&2)); // Disabled item should not be selected

        // Test select none
        widget
            .handle_key_event(KeyEvent::new(KeyCode::Char('n'), KeyModifiers::NONE))
            .unwrap();
        assert!(widget.state.checked_items.is_empty());
    }

    #[test]
    fn test_single_selection_mode() {
        let config = CheckboxConfig {
            display_mode: CheckboxDisplayMode::Single,
            items: vec![
                CheckboxItem {
                    label: "Option 1".to_string(),
                    value: json!("opt1"),
                    checked: false,
                    enabled: true,
                    description: None,
                },
                CheckboxItem {
                    label: "Option 2".to_string(),
                    value: json!("opt2"),
                    checked: false,
                    enabled: true,
                    description: None,
                },
            ],
            allow_multiple: false,
            required: true,
            show_descriptions: false,
            validation_rules: None,
        };

        let mut widget = CheckboxWidget::new(
            "test".to_string(),
            Some("Single Select".to_string()),
            config,
        );

        // Select first item
        widget
            .handle_key_event(KeyEvent::new(KeyCode::Enter, KeyModifiers::NONE))
            .unwrap();
        assert_eq!(widget.state.checked_items, vec![0]);

        // Move to second item and select
        widget
            .handle_key_event(KeyEvent::new(KeyCode::Down, KeyModifiers::NONE))
            .unwrap();
        widget
            .handle_key_event(KeyEvent::new(KeyCode::Enter, KeyModifiers::NONE))
            .unwrap();
        assert_eq!(widget.state.checked_items, vec![1]); // Only second item should be selected
    }
}
