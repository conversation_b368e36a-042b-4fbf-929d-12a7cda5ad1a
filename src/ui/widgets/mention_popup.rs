use ratatui::{
    buffer::<PERSON><PERSON>er,
    layout::{Alignment, Rect},
    style::{Color, Modifier, Style},
    text::{Line, Span},
    widgets::{Block, Borders, Clear, List, ListItem, Paragraph, Widget},
};

use crate::ui::completion::{MentionPopupState, MentionType};

/// A popup widget for displaying mention suggestions
pub struct MentionPopup<'a> {
    state: &'a MentionPopupState,
    max_height: u16,
    max_width: u16,
}

impl<'a> MentionPopup<'a> {
    pub fn new(state: &'a MentionPopupState) -> Self {
        Self {
            state,
            max_height: 10,
            max_width: 60,
        }
    }

    pub fn max_height(mut self, height: u16) -> Self {
        self.max_height = height;
        self
    }

    pub fn max_width(mut self, width: u16) -> Self {
        self.max_width = width;
        self
    }

    /// Calculate the popup area based on cursor position and available space
    pub fn calculate_area(&self, cursor_area: Rect, container_area: Rect) -> Rect {
        let suggestions_count = self.state.suggestions.len() as u16;
        let height = (suggestions_count + 2).min(self.max_height); // +2 for borders
        let width = self.calculate_width().min(self.max_width);

        // Try to position below cursor
        let mut y = cursor_area.bottom();

        // Check if there's enough space below
        if y + height > container_area.bottom() {
            // Try above
            if cursor_area.y >= height {
                y = cursor_area.y.saturating_sub(height);
            } else {
                // Not enough space above either, show at bottom of container
                y = container_area.bottom().saturating_sub(height);
            }
        }

        // Calculate x position
        let x = if cursor_area.x + width > container_area.right() {
            container_area.right().saturating_sub(width)
        } else {
            cursor_area.x
        };

        Rect::new(x, y, width, height)
    }

    fn calculate_width(&self) -> u16 {
        let max_content_width = self
            .state
            .suggestions
            .iter()
            .map(|s| {
                let type_icon_len = 2; // emoji + space
                let name_len = s.display_name.len();
                let desc_len = s.description.len();
                type_icon_len + name_len + 3 + desc_len // +3 for " - "
            })
            .max()
            .unwrap_or(20);

        (max_content_width as u16 + 4).min(self.max_width) // +4 for borders and padding
    }
}

impl<'a> Widget for MentionPopup<'a> {
    fn render(self, area: Rect, buf: &mut Buffer) {
        if !self.state.visible || self.state.suggestions.is_empty() {
            return;
        }

        // Clear the area first
        Clear.render(area, buf);

        // Create the list items
        let items: Vec<ListItem> = self
            .state
            .suggestions
            .iter()
            .enumerate()
            .map(|(idx, suggestion)| {
                let is_selected = idx == self.state.selected_index;

                // Build the content line
                let mut spans = vec![
                    Span::raw(format!("{} ", suggestion.mention_type.icon())),
                    Span::styled(
                        &suggestion.display_name,
                        Style::default()
                            .fg(get_type_color(&suggestion.mention_type))
                            .add_modifier(if is_selected {
                                Modifier::BOLD
                            } else {
                                Modifier::empty()
                            }),
                    ),
                ];

                // Add description if not too long
                let remaining_width = area.width.saturating_sub(4) as usize; // -4 for borders
                let current_len = suggestion.display_name.len() + 2; // +2 for icon

                if current_len + 3 + suggestion.description.len() < remaining_width {
                    spans.push(Span::raw(" - "));
                    spans.push(Span::styled(
                        &suggestion.description,
                        Style::default().fg(Color::Gray),
                    ));
                } else if current_len + 3 < remaining_width {
                    // Truncate description
                    let desc_len = remaining_width - current_len - 6; // -6 for " - ..."
                    if desc_len > 0 {
                        spans.push(Span::raw(" - "));
                        spans.push(Span::styled(
                            format!(
                                "{}...",
                                &suggestion.description
                                    [..desc_len.min(suggestion.description.len())]
                            ),
                            Style::default().fg(Color::Gray),
                        ));
                    }
                }

                let style = if is_selected {
                    Style::default().bg(Color::DarkGray)
                } else {
                    Style::default()
                };

                ListItem::new(Line::from(spans)).style(style)
            })
            .collect();

        // Create the list widget
        let list = List::new(items)
            .block(
                Block::default()
                    .title(" Suggestions ")
                    .title_alignment(Alignment::Center)
                    .borders(Borders::ALL)
                    .border_style(Style::default().fg(Color::Blue)),
            )
            .style(Style::default().bg(Color::Black));

        list.render(area, buf);

        // Add keybind hints at the bottom
        if area.height > 3 {
            let hint_area = Rect::new(area.x + 1, area.bottom() - 2, area.width - 2, 1);
            let hints = Line::from(vec![
                Span::raw("↑↓"),
                Span::styled(" Navigate ", Style::default().fg(Color::DarkGray)),
                Span::raw("Tab"),
                Span::styled(" Select ", Style::default().fg(Color::DarkGray)),
                Span::raw("Esc"),
                Span::styled(" Cancel", Style::default().fg(Color::DarkGray)),
            ]);

            let hint_paragraph = Paragraph::new(hints)
                .style(Style::default().fg(Color::DarkGray))
                .alignment(Alignment::Center);

            hint_paragraph.render(hint_area, buf);
        }
    }
}

fn get_type_color(mention_type: &MentionType) -> Color {
    match mention_type {
        MentionType::File => Color::Yellow,
        MentionType::Tool => Color::Cyan,
        MentionType::Concept => Color::Magenta,
        MentionType::Function => Color::Blue,
        MentionType::Variable => Color::Green,
        MentionType::Type => Color::LightBlue,
    }
}
