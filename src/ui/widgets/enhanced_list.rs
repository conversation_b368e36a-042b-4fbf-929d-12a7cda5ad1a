// Enhanced List Widget with AI Features
// Extends Ratatui's List widget with dynamic content, filtering, and multi-selection

use async_trait::async_trait;
use crossterm::event::KeyCode;
use ratatui::prelude::*;
use ratatui::widgets::{Block, Borders, List, ListItem, ListState, StatefulWidget, Widget};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::collections::HashMap;
use tracing::debug;

use crate::agent::core::{WidgetGenerationRequest, WidgetUpdateEvent};
use crate::errors::{AutorunError, Result};

use super::checkbox::{CheckboxState, CheckboxWidget};
use super::{
    AiWidget, LayoutConstraint, LayoutDirection, WidgetBuilder, WidgetConfig, WidgetLayout,
    WidgetState,
};

/// Configuration for list item with metadata and content
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnhancedListItem {
    pub id: String,
    pub content: String,
    pub description: Option<String>,
    pub metadata: HashMap<String, Value>,
    pub selectable: bool,
    pub checkable: bool,
    pub checked: bool,
    pub style: Option<ListItemStyle>,
    pub children: Option<Vec<EnhancedListItem>>, // For hierarchical lists
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ListItemStyle {
    pub fg_color: Option<String>,
    pub bg_color: Option<String>,
    pub modifiers: Vec<String>, // "bold", "italic", "underlined", etc.
}

/// Configuration for enhanced list widget
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnhancedListConfig {
    pub id: String,
    pub title: Option<String>,
    pub items: Vec<EnhancedListItem>,
    pub multi_select: bool,
    pub show_checkboxes: bool,
    pub show_descriptions: bool,
    pub filterable: bool,
    pub searchable: bool,
    pub highlight_symbol: String,
    pub pagination: PaginationConfig,
    pub sort_options: SortConfig,
    pub hierarchical: bool,
    pub lazy_load: bool,
    pub auto_update: bool, // Update from LLM responses
    pub update_interval_ms: Option<u64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginationConfig {
    pub enabled: bool,
    pub page_size: usize,
    pub show_page_info: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SortConfig {
    pub enabled: bool,
    pub sort_by: String, // "content", "description", "metadata.field"
    pub ascending: bool,
}

impl Default for EnhancedListConfig {
    fn default() -> Self {
        Self {
            id: "enhanced_list".to_string(),
            title: None,
            items: Vec::new(),
            multi_select: false,
            show_checkboxes: false,
            show_descriptions: false,
            filterable: false,
            searchable: false,
            highlight_symbol: "► ".to_string(),
            pagination: PaginationConfig {
                enabled: false,
                page_size: 10,
                show_page_info: true,
            },
            sort_options: SortConfig {
                enabled: false,
                sort_by: "content".to_string(),
                ascending: true,
            },
            hierarchical: false,
            lazy_load: false,
            auto_update: false,
            update_interval_ms: None,
        }
    }
}

/// State management for enhanced list widget
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnhancedListState {
    pub selected_index: usize,
    pub selected_items: Vec<usize>, // For multi-select
    pub checked_items: Vec<String>, // Item IDs that are checked
    pub current_page: usize,
    pub scroll_offset: usize,
    pub filter_text: String,
    pub search_text: String,
    pub expanded_items: Vec<String>, // For hierarchical lists
    pub focused: bool,
    pub dirty: bool,
    pub visible_items: Vec<usize>, // Filtered/searched item indices
    pub checkbox_state: Option<CheckboxState>, // For checkbox integration
}

impl Default for EnhancedListState {
    fn default() -> Self {
        Self {
            selected_index: 0,
            selected_items: Vec::new(),
            checked_items: Vec::new(),
            current_page: 0,
            scroll_offset: 0,
            filter_text: String::new(),
            search_text: String::new(),
            expanded_items: Vec::new(),
            focused: false,
            dirty: false,
            visible_items: Vec::new(),
            checkbox_state: None,
        }
    }
}

impl WidgetState for EnhancedListState {
    fn reset(&mut self) {
        self.selected_index = 0;
        self.selected_items.clear();
        self.checked_items.clear();
        self.current_page = 0;
        self.scroll_offset = 0;
        self.filter_text.clear();
        self.search_text.clear();
        self.expanded_items.clear();
        self.focused = false;
        self.dirty = false;
        self.visible_items.clear();
        if let Some(ref mut checkbox_state) = self.checkbox_state {
            checkbox_state.reset();
        }
    }

    fn is_dirty(&self) -> bool {
        self.dirty
            || self
                .checkbox_state
                .as_ref()
                .map_or(false, |cs| cs.is_dirty())
    }

    fn mark_clean(&mut self) {
        self.dirty = false;
        if let Some(ref mut checkbox_state) = self.checkbox_state {
            checkbox_state.mark_clean();
        }
    }
}

/// Enhanced List Widget with AI capabilities
pub struct EnhancedListWidget {
    config: EnhancedListConfig,
    state: EnhancedListState,
    checkbox_widget: Option<CheckboxWidget>,
    last_update: std::time::Instant,
}

impl EnhancedListWidget {
    pub fn new(config: EnhancedListConfig) -> Self {
        let mut widget = Self {
            config: config.clone(),
            state: EnhancedListState::default(),
            checkbox_widget: None,
            last_update: std::time::Instant::now(),
        };

        // Initialize checkbox widget if checkboxes are enabled
        if config.show_checkboxes {
            widget.init_checkbox_integration();
        }

        widget.update_visible_items();
        widget
    }

    fn init_checkbox_integration(&mut self) {
        if self.config.show_checkboxes {
            // Create checkbox configuration from list items
            let checkbox_items: Vec<super::checkbox::CheckboxItem> = self
                .config
                .items
                .iter()
                .filter(|item| item.checkable)
                .map(|item| super::checkbox::CheckboxItem {
                    label: item.content.clone(),
                    value: serde_json::to_value(&item.id).unwrap_or_else(|_| json!(item.id)),
                    checked: item.checked,
                    enabled: item.selectable,
                    description: item.description.clone(),
                })
                .collect();

            let checkbox_config = super::checkbox::CheckboxConfig {
                display_mode: if self.config.multi_select {
                    super::checkbox::CheckboxDisplayMode::Group
                } else {
                    super::checkbox::CheckboxDisplayMode::Single
                },
                items: checkbox_items,
                allow_multiple: self.config.multi_select,
                required: false,
                show_descriptions: self.config.show_descriptions,
                validation_rules: None,
            };

            self.checkbox_widget = Some(CheckboxWidget::new(
                format!("{}_checkboxes", self.config.id),
                self.config.title.clone(),
                checkbox_config,
            ));
            self.state.checkbox_state = Some(CheckboxState::default());
        }
    }

    fn update_visible_items(&mut self) {
        let mut visible_indices = Vec::new();

        for (index, item) in self.config.items.iter().enumerate() {
            let mut visible = true;

            // Apply filter
            if !self.state.filter_text.is_empty() {
                visible = item
                    .content
                    .to_lowercase()
                    .contains(&self.state.filter_text.to_lowercase())
                    || item.description.as_ref().map_or(false, |desc| {
                        desc.to_lowercase()
                            .contains(&self.state.filter_text.to_lowercase())
                    });
            }

            // Apply search
            if !self.state.search_text.is_empty() && visible {
                visible = item
                    .content
                    .to_lowercase()
                    .contains(&self.state.search_text.to_lowercase())
                    || item.description.as_ref().map_or(false, |desc| {
                        desc.to_lowercase()
                            .contains(&self.state.search_text.to_lowercase())
                    });
            }

            if visible {
                visible_indices.push(index);
            }
        }

        self.state.visible_items = visible_indices;

        // Adjust selected index if necessary
        if self.state.selected_index >= self.state.visible_items.len()
            && !self.state.visible_items.is_empty()
        {
            self.state.selected_index = self.state.visible_items.len() - 1;
        }

        self.state.dirty = true;
    }

    fn apply_sort(&mut self) {
        if !self.config.sort_options.enabled {
            return;
        }

        let sort_field = &self.config.sort_options.sort_by;
        let ascending = self.config.sort_options.ascending;

        // Sort visible items based on the sort configuration
        self.state.visible_items.sort_by(|&a, &b| {
            let item_a = &self.config.items[a];
            let item_b = &self.config.items[b];

            let comparison = match sort_field.as_str() {
                "content" => item_a.content.cmp(&item_b.content),
                "description" => {
                    let desc_a = item_a.description.as_deref().unwrap_or("");
                    let desc_b = item_b.description.as_deref().unwrap_or("");
                    desc_a.cmp(desc_b)
                }
                field if field.starts_with("metadata.") => {
                    let metadata_field = &field[9..]; // Remove "metadata." prefix
                    let val_a = item_a.metadata.get(metadata_field);
                    let val_b = item_b.metadata.get(metadata_field);

                    match (val_a, val_b) {
                        (Some(a), Some(b)) => {
                            let a_string = a.to_string();
                            let b_string = b.to_string();
                            let str_a = a.as_str().unwrap_or(&a_string);
                            let str_b = b.as_str().unwrap_or(&b_string);
                            str_a.cmp(str_b)
                        }
                        (Some(_), None) => std::cmp::Ordering::Less,
                        (None, Some(_)) => std::cmp::Ordering::Greater,
                        (None, None) => std::cmp::Ordering::Equal,
                    }
                }
                _ => std::cmp::Ordering::Equal,
            };

            if ascending {
                comparison
            } else {
                comparison.reverse()
            }
        });

        self.state.dirty = true;
    }

    fn get_page_items(&self) -> Vec<usize> {
        if !self.config.pagination.enabled {
            return self.state.visible_items.clone();
        }

        let page_size = self.config.pagination.page_size;
        let start_index = self.state.current_page * page_size;
        let end_index = (start_index + page_size).min(self.state.visible_items.len());

        self.state.visible_items[start_index..end_index].to_vec()
    }

    fn handle_navigation(&mut self, key: KeyCode) -> bool {
        let page_items = self.get_page_items();

        match key {
            KeyCode::Up | KeyCode::Char('k') => {
                if self.state.selected_index > 0 {
                    self.state.selected_index -= 1;
                    self.state.dirty = true;
                }
            }
            KeyCode::Down | KeyCode::Char('j') => {
                if self.state.selected_index < page_items.len().saturating_sub(1) {
                    self.state.selected_index += 1;
                    self.state.dirty = true;
                }
            }
            KeyCode::PageUp => {
                if self.config.pagination.enabled && self.state.current_page > 0 {
                    self.state.current_page -= 1;
                    self.state.selected_index = 0;
                    self.state.dirty = true;
                }
            }
            KeyCode::PageDown => {
                if self.config.pagination.enabled {
                    let max_page =
                        (self.state.visible_items.len() + self.config.pagination.page_size - 1)
                            / self.config.pagination.page_size;
                    if self.state.current_page < max_page.saturating_sub(1) {
                        self.state.current_page += 1;
                        self.state.selected_index = 0;
                        self.state.dirty = true;
                    }
                }
            }
            KeyCode::Home => {
                self.state.selected_index = 0;
                self.state.dirty = true;
            }
            KeyCode::End => {
                self.state.selected_index = page_items.len().saturating_sub(1);
                self.state.dirty = true;
            }
            _ => return false,
        }
        true
    }

    fn handle_selection(&mut self, key: KeyCode) -> bool {
        let page_items = self.get_page_items();

        if page_items.is_empty() {
            return false;
        }

        let actual_index = page_items[self.state.selected_index];

        match key {
            KeyCode::Enter | KeyCode::Char(' ') => {
                if self.config.multi_select {
                    if self.state.selected_items.contains(&actual_index) {
                        self.state.selected_items.retain(|&x| x != actual_index);
                    } else {
                        self.state.selected_items.push(actual_index);
                    }
                } else {
                    self.state.selected_items = vec![actual_index];
                }

                // Update checkbox state if applicable
                if self.config.show_checkboxes {
                    let item_id = &self.config.items[actual_index].id;
                    if self.state.checked_items.contains(item_id) {
                        self.state.checked_items.retain(|id| id != item_id);
                    } else {
                        self.state.checked_items.push(item_id.clone());
                    }
                }

                self.state.dirty = true;
                return true;
            }
            KeyCode::Char('a') if self.config.multi_select => {
                // Select all visible items
                self.state.selected_items = self.state.visible_items.clone();
                if self.config.show_checkboxes {
                    self.state.checked_items = self
                        .config
                        .items
                        .iter()
                        .filter(|item| item.checkable)
                        .map(|item| item.id.clone())
                        .collect();
                }
                self.state.dirty = true;
                return true;
            }
            KeyCode::Char('n') if self.config.multi_select => {
                // Select none
                self.state.selected_items.clear();
                self.state.checked_items.clear();
                self.state.dirty = true;
                return true;
            }
            _ => return false,
        }
    }

    fn create_list_items(&self) -> Vec<ListItem> {
        let page_items = self.get_page_items();

        page_items
            .iter()
            .map(|&index| {
                let item = &self.config.items[index];
                let mut spans = Vec::new();

                // Add checkbox if enabled
                if self.config.show_checkboxes && item.checkable {
                    let checkbox_symbol = if self.state.checked_items.contains(&item.id) {
                        "■ " // Checked
                    } else {
                        "□ " // Unchecked
                    };

                    spans.push(Span::styled(
                        checkbox_symbol,
                        Style::default().fg(Color::Yellow),
                    ));
                }

                // Add hierarchical indicator if needed
                if self.config.hierarchical && item.children.is_some() {
                    let expand_symbol = if self.state.expanded_items.contains(&item.id) {
                        "▼ " // Expanded
                    } else {
                        "▶ " // Collapsed
                    };

                    spans.push(Span::styled(
                        expand_symbol,
                        Style::default().fg(Color::Cyan),
                    ));
                }

                // Add content
                let mut content_style = Style::default();
                if let Some(style_config) = &item.style {
                    if let Some(fg) = &style_config.fg_color {
                        if let Some(color) = parse_color(fg) {
                            content_style = content_style.fg(color);
                        }
                    }

                    for modifier in &style_config.modifiers {
                        content_style = match modifier.as_str() {
                            "bold" => content_style.add_modifier(Modifier::BOLD),
                            "italic" => content_style.add_modifier(Modifier::ITALIC),
                            "underlined" => content_style.add_modifier(Modifier::UNDERLINED),
                            _ => content_style,
                        };
                    }
                }

                spans.push(Span::styled(item.content.clone(), content_style));

                // Add description if enabled and available
                if self.config.show_descriptions {
                    if let Some(description) = &item.description {
                        spans.push(Span::styled(
                            format!(" - {}", description),
                            Style::default().fg(Color::Gray),
                        ));
                    }
                }

                ListItem::new(Line::from(spans))
            })
            .collect()
    }
}

#[async_trait]
impl AiWidget for EnhancedListWidget {
    fn id(&self) -> &str {
        &self.config.id
    }

    fn widget_type(&self) -> &str {
        "enhanced_list"
    }

    fn description(&self) -> &str {
        "Enhanced list widget with AI features, filtering, multi-selection, and dynamic content"
    }

    async fn update_from_config(&mut self, widget_config: &WidgetConfig) -> Result<()> {
        if let Some(config_obj) = widget_config.properties.get("enhanced_list_config") {
            let new_config: EnhancedListConfig = serde_json::from_value(config_obj.clone())
                .map_err(|e| {
                    AutorunError::InvalidInput(format!("Invalid enhanced list config: {}", e))
                })?;

            self.config = new_config;

            // Reinitialize checkbox integration if needed
            if self.config.show_checkboxes {
                self.init_checkbox_integration();
            }

            self.update_visible_items();
            self.apply_sort();
            self.state.dirty = true;
        }

        Ok(())
    }

    async fn handle_event(&mut self, event: &WidgetUpdateEvent) -> Result<()> {
        match event.event_type.as_str() {
            "content_update" => {
                // Update items from LLM response
                if let Some(items_data) = event.data.get("items") {
                    let new_items: Vec<EnhancedListItem> =
                        serde_json::from_value(items_data.clone()).map_err(|e| {
                            AutorunError::InvalidInput(format!("Invalid items data: {}", e))
                        })?;

                    self.config.items = new_items;
                    self.update_visible_items();
                    self.state.dirty = true;
                }
            }
            "filter_update" => {
                if let Some(filter_text) = event.data.get("filter").and_then(|v| v.as_str()) {
                    self.state.filter_text = filter_text.to_string();
                    self.update_visible_items();
                }
            }
            "search_update" => {
                if let Some(search_text) = event.data.get("search").and_then(|v| v.as_str()) {
                    self.state.search_text = search_text.to_string();
                    self.update_visible_items();
                }
            }
            "sort_update" => {
                if let Some(sort_config) = event.data.get("sort_config") {
                    let new_sort: SortConfig = serde_json::from_value(sort_config.clone())
                        .map_err(|e| {
                            AutorunError::InvalidInput(format!("Invalid sort config: {}", e))
                        })?;

                    self.config.sort_options = new_sort;
                    self.apply_sort();
                }
            }
            "key_event" => {
                if let Some(key_data) = event.data.get("key") {
                    // Handle keyboard events
                    if let Ok(key_str) = serde_json::from_value::<String>(key_data.clone()) {
                        match key_str.as_str() {
                            "up" => self.handle_navigation(KeyCode::Up),
                            "down" => self.handle_navigation(KeyCode::Down),
                            "enter" => self.handle_selection(KeyCode::Enter),
                            "space" => self.handle_selection(KeyCode::Char(' ')),
                            _ => false,
                        };
                    }
                }
            }
            _ => {}
        }

        Ok(())
    }

    fn get_state(&self) -> Value {
        json!({
            "selected_index": self.state.selected_index,
            "selected_items": self.state.selected_items,
            "checked_items": self.state.checked_items,
            "current_page": self.state.current_page,
            "filter_text": self.state.filter_text,
            "search_text": self.state.search_text,
            "visible_item_count": self.state.visible_items.len(),
            "total_item_count": self.config.items.len(),
            "pagination_enabled": self.config.pagination.enabled,
            "multi_select": self.config.multi_select
        })
    }

    fn validate_layout(&self, area: Rect) -> Result<()> {
        if area.width < 10 || area.height < 3 {
            return Err(AutorunError::InvalidInput(
                "Enhanced list widget requires minimum 10x3 area".to_string(),
            ));
        }
        Ok(())
    }

    fn get_config(&self) -> WidgetConfig {
        WidgetConfig {
            widget_type: "enhanced_list".to_string(),
            id: self.config.id.clone(),
            title: self.config.title.clone(),
            properties: HashMap::from([(
                "enhanced_list_config".to_string(),
                serde_json::to_value(&self.config).unwrap(),
            )]),
            layout: WidgetLayout {
                constraints: vec![LayoutConstraint::Percentage(100)],
                direction: LayoutDirection::Vertical,
            },
        }
    }
}

impl StatefulWidget for EnhancedListWidget {
    type State = ListState;

    fn render(self, area: Rect, buf: &mut Buffer, state: &mut Self::State) {
        // Create list items
        let list_items = self.create_list_items();

        // Create the list widget
        let mut list = List::new(list_items)
            .highlight_style(
                Style::default()
                    .bg(Color::DarkGray)
                    .add_modifier(Modifier::BOLD),
            )
            .highlight_symbol(&self.config.highlight_symbol);

        // Add title if specified
        if let Some(title) = &self.config.title {
            let block = Block::default()
                .title(title.as_str())
                .borders(Borders::ALL)
                .border_style(Style::default().fg(Color::Blue));
            list = list.block(block);
        }

        // Set the selected item
        state.select(Some(self.state.selected_index));

        // Render the list
        StatefulWidget::render(list, area, buf, state);

        // Render pagination info if enabled
        if self.config.pagination.enabled && self.config.pagination.show_page_info {
            let total_pages = (self.state.visible_items.len() + self.config.pagination.page_size
                - 1)
                / self.config.pagination.page_size;
            let page_info = format!(
                "Page {} of {}",
                self.state.current_page + 1,
                total_pages.max(1)
            );

            let info_area = Rect {
                x: area.x + area.width.saturating_sub(page_info.len() as u16 + 2),
                y: area.y + area.height.saturating_sub(1),
                width: page_info.len() as u16 + 2,
                height: 1,
            };

            let page_paragraph =
                ratatui::widgets::Paragraph::new(page_info).style(Style::default().fg(Color::Gray));
            Widget::render(page_paragraph, info_area, buf);
        }
    }
}

/// Builder for creating enhanced list widgets from AI requests
pub struct EnhancedListWidgetBuilder;

#[async_trait]
impl WidgetBuilder for EnhancedListWidgetBuilder {
    async fn build(&self, request: &WidgetGenerationRequest) -> Result<Box<dyn AiWidget>> {
        let config: EnhancedListConfig = serde_json::from_value(request.config.clone())
            .unwrap_or_else(|_| {
                debug!("Failed to parse enhanced list config, using default");
                EnhancedListConfig::default()
            });

        Ok(Box::new(EnhancedListWidget::new(config)))
    }

    fn get_schema(&self) -> Value {
        json!({
            "type": "object",
            "properties": {
                "id": {"type": "string"},
                "title": {"type": "string", "optional": true},
                "items": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "id": {"type": "string"},
                            "content": {"type": "string"},
                            "description": {"type": "string", "optional": true},
                            "metadata": {"type": "object"},
                            "selectable": {"type": "boolean"},
                            "checkable": {"type": "boolean"},
                            "checked": {"type": "boolean"},
                            "style": {
                                "type": "object",
                                "optional": true,
                                "properties": {
                                    "fg_color": {"type": "string", "optional": true},
                                    "bg_color": {"type": "string", "optional": true},
                                    "modifiers": {"type": "array", "items": {"type": "string"}}
                                }
                            },
                            "children": {
                                "type": "array",
                                "optional": true,
                                "items": {"$ref": "#"}
                            }
                        },
                        "required": ["id", "content", "selectable", "checkable", "checked"]
                    }
                },
                "multi_select": {"type": "boolean"},
                "show_checkboxes": {"type": "boolean"},
                "show_descriptions": {"type": "boolean"},
                "filterable": {"type": "boolean"},
                "searchable": {"type": "boolean"},
                "highlight_symbol": {"type": "string"},
                "pagination": {
                    "type": "object",
                    "properties": {
                        "enabled": {"type": "boolean"},
                        "page_size": {"type": "integer", "minimum": 1},
                        "show_page_info": {"type": "boolean"}
                    }
                },
                "sort_options": {
                    "type": "object",
                    "properties": {
                        "enabled": {"type": "boolean"},
                        "sort_by": {"type": "string"},
                        "ascending": {"type": "boolean"}
                    }
                },
                "hierarchical": {"type": "boolean"},
                "lazy_load": {"type": "boolean"},
                "auto_update": {"type": "boolean"},
                "update_interval_ms": {"type": "integer", "optional": true}
            },
            "required": ["id", "items"]
        })
    }

    fn validate_config(&self, config: &WidgetConfig) -> Result<()> {
        if config.widget_type != "enhanced_list" {
            return Err(AutorunError::InvalidInput(
                "Config is not for enhanced_list widget".to_string(),
            ));
        }

        if config.id.is_empty() {
            return Err(AutorunError::InvalidInput(
                "Enhanced list widget ID cannot be empty".to_string(),
            ));
        }

        Ok(())
    }
}

/// Helper function to parse color from string
fn parse_color(color_str: &str) -> Option<Color> {
    match color_str.to_lowercase().as_str() {
        "black" => Some(Color::Black),
        "red" => Some(Color::Red),
        "green" => Some(Color::Green),
        "yellow" => Some(Color::Yellow),
        "blue" => Some(Color::Blue),
        "magenta" => Some(Color::Magenta),
        "cyan" => Some(Color::Cyan),
        "gray" | "grey" => Some(Color::Gray),
        "darkgray" | "darkgrey" => Some(Color::DarkGray),
        "lightred" => Some(Color::LightRed),
        "lightgreen" => Some(Color::LightGreen),
        "lightyellow" => Some(Color::LightYellow),
        "lightblue" => Some(Color::LightBlue),
        "lightmagenta" => Some(Color::LightMagenta),
        "lightcyan" => Some(Color::LightCyan),
        "white" => Some(Color::White),
        _ => {
            // Try parsing as RGB hex
            if color_str.starts_with('#') && color_str.len() == 7 {
                if let Ok(r) = u8::from_str_radix(&color_str[1..3], 16) {
                    if let Ok(g) = u8::from_str_radix(&color_str[3..5], 16) {
                        if let Ok(b) = u8::from_str_radix(&color_str[5..7], 16) {
                            return Some(Color::Rgb(r, g, b));
                        }
                    }
                }
            }
            None
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_enhanced_list_config_creation() {
        let config = EnhancedListConfig::default();
        assert_eq!(config.id, "enhanced_list");
        assert!(!config.multi_select);
        assert!(!config.show_checkboxes);
    }

    #[test]
    fn test_enhanced_list_widget_creation() {
        let config = EnhancedListConfig {
            id: "test_list".to_string(),
            items: vec![EnhancedListItem {
                id: "item1".to_string(),
                content: "First item".to_string(),
                description: None,
                metadata: HashMap::new(),
                selectable: true,
                checkable: false,
                checked: false,
                style: None,
                children: None,
            }],
            ..Default::default()
        };

        let widget = EnhancedListWidget::new(config);
        assert_eq!(widget.id(), "test_list");
        assert_eq!(widget.widget_type(), "enhanced_list");
    }

    #[test]
    fn test_enhanced_list_state_management() {
        let mut state = EnhancedListState::default();
        assert!(!state.is_dirty());

        state.selected_index = 1;
        state.dirty = true;
        assert!(state.is_dirty());

        state.mark_clean();
        assert!(!state.is_dirty());

        state.reset();
        assert_eq!(state.selected_index, 0);
        assert!(state.selected_items.is_empty());
    }

    #[test]
    fn test_builder_schema_validation() {
        let builder = EnhancedListWidgetBuilder;
        let schema = builder.get_schema();

        assert!(schema.get("type").is_some());
        assert!(schema.get("properties").is_some());

        let config = WidgetConfig {
            widget_type: "enhanced_list".to_string(),
            id: "test".to_string(),
            title: None,
            properties: HashMap::new(),
            layout: WidgetLayout {
                constraints: vec![LayoutConstraint::Percentage(100)],
                direction: LayoutDirection::Vertical,
            },
        };

        assert!(builder.validate_config(&config).is_ok());
    }

    #[test]
    fn test_color_parsing() {
        assert_eq!(parse_color("red"), Some(Color::Red));
        assert_eq!(parse_color("blue"), Some(Color::Blue));
        assert_eq!(parse_color("#FF0000"), Some(Color::Rgb(255, 0, 0)));
        assert_eq!(parse_color("invalid"), None);
    }
}
