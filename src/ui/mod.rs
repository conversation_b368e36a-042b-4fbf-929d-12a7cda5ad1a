// UI module - handles terminal user interface with ratatui

pub mod app_interface;
pub mod completion;
pub mod dialogs;
pub mod enhanced;
pub mod llm_integration;
pub mod renderer;
pub mod widgets;

use crate::errors::Result;
use crossterm::terminal::{disable_raw_mode, enable_raw_mode};
use is_terminal::IsTerminal;
use ratatui::prelude::*;
use std::io;

pub type AppTerminal = Terminal<CrosstermBackend<io::Stdout>>;

pub fn setup_terminal() -> Result<AppTerminal> {
    // Check if we have a TTY before trying to enable raw mode
    if !std::io::stdout().is_terminal() {
        return Err(crate::errors::AutorunError::Io(
            std::io::Error::new(
                std::io::ErrorKind::Unsupported,
                "No TTY available - cannot run in interactive mode. Use --print for non-interactive mode."
            )
        ));
    }

    enable_raw_mode().map_err(|e| {
        crate::errors::AutorunError::Io(std::io::Error::new(
            std::io::ErrorKind::Other,
            format!("Failed to enable raw mode: {}", e),
        ))
    })?;

    let stdout = io::stdout();
    // Note: Removed EnterAlternateScreen for better terminal compatibility
    // The TUI will render in the main terminal buffer

    let backend = CrosstermBackend::new(stdout);
    let terminal = Terminal::new(backend).map_err(|e| {
        // Clean up on terminal creation failure
        let _ = disable_raw_mode();
        // Note: Removed LeaveAlternateScreen cleanup since we're not using alternate screen
        crate::errors::AutorunError::Io(std::io::Error::new(
            std::io::ErrorKind::Other,
            format!("Failed to create terminal: {}", e),
        ))
    })?;

    Ok(terminal)
}

pub fn restore_terminal<B: Backend + std::io::Write>(terminal: &mut Terminal<B>) -> Result<()> {
    disable_raw_mode()?;
    // Note: Removed LeaveAlternateScreen since we're not using alternate screen mode
    terminal.show_cursor()?;
    Ok(())
}
