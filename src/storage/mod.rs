//! Storage systems for persistent data management
//!
//! Provides SQLite-based storage for sessions, memory, workspaces, and git metadata
//! with optional encryption and full-text search capabilities.

pub mod git_integration;
pub mod memory_store;
pub mod session_store;
pub mod workspace_store;

use crate::errors::Result;
use serde::{Deserialize, Serialize};
use std::path::Path;
use std::time::SystemTime;
use uuid::Uuid;

/// Storage configuration for all storage systems
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageConfig {
    /// Base directory for all storage files
    pub storage_dir: std::path::PathBuf,
    /// Enable encryption for sensitive data
    pub enable_encryption: bool,
    /// Encryption key (if enabled)
    pub encryption_key: Option<String>,
    /// Maximum storage size in MB
    pub max_storage_size_mb: u64,
    /// Enable compression for large data
    pub enable_compression: bool,
    /// Auto-cleanup old data after days
    pub auto_cleanup_days: Option<u32>,
}

impl Default for StorageConfig {
    fn default() -> Self {
        let mut storage_dir = std::env::current_dir().unwrap_or_default();
        storage_dir.push(".autorun");
        storage_dir.push("storage");

        Self {
            storage_dir,
            enable_encryption: false,
            encryption_key: None,
            max_storage_size_mb: 500,
            enable_compression: true,
            auto_cleanup_days: Some(90),
        }
    }
}

/// Common storage traits
pub trait StorageBackend {
    type Item;
    type Key;

    /// Initialize the storage backend
    async fn initialize(&mut self) -> Result<()>;

    /// Store an item with the given key
    async fn store(&mut self, key: Self::Key, item: Self::Item) -> Result<()>;

    /// Retrieve an item by key
    async fn retrieve(&self, key: &Self::Key) -> Result<Option<Self::Item>>;

    /// Delete an item by key
    async fn delete(&mut self, key: &Self::Key) -> Result<bool>;

    /// List all keys
    async fn list_keys(&self) -> Result<Vec<Self::Key>>;

    /// Get storage statistics
    async fn get_stats(&self) -> Result<StorageStats>;

    /// Cleanup old or unused data
    async fn cleanup(&mut self) -> Result<usize>;
}

/// Storage statistics for monitoring and management
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageStats {
    /// Total number of items stored
    pub item_count: usize,
    /// Total storage size in bytes
    pub total_size_bytes: u64,
    /// Last access time
    pub last_access: SystemTime,
    /// Creation time
    pub created_at: SystemTime,
    /// Available free space in bytes
    pub free_space_bytes: u64,
}

/// Generic storage entry with metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageEntry<T> {
    /// Unique identifier
    pub id: Uuid,
    /// The actual data
    pub data: T,
    /// Creation timestamp
    pub created_at: SystemTime,
    /// Last modified timestamp
    pub modified_at: SystemTime,
    /// Last accessed timestamp
    pub accessed_at: SystemTime,
    /// Entry metadata
    pub metadata: std::collections::HashMap<String, String>,
    /// Compression flag
    pub compressed: bool,
    /// Encryption flag
    pub encrypted: bool,
}

impl<T> StorageEntry<T> {
    pub fn new(data: T) -> Self {
        let now = SystemTime::now();
        Self {
            id: Uuid::new_v4(),
            data,
            created_at: now,
            modified_at: now,
            accessed_at: now,
            metadata: std::collections::HashMap::new(),
            compressed: false,
            encrypted: false,
        }
    }

    pub fn with_metadata(mut self, key: String, value: String) -> Self {
        self.metadata.insert(key, value);
        self
    }

    pub fn mark_accessed(&mut self) {
        self.accessed_at = SystemTime::now();
    }

    pub fn mark_modified(&mut self) {
        self.modified_at = SystemTime::now();
        self.mark_accessed();
    }
}

/// Initialize storage directory and create necessary subdirectories
pub async fn initialize_storage(config: &StorageConfig) -> Result<()> {
    use tokio::fs;

    // Create storage directory structure
    fs::create_dir_all(&config.storage_dir).await.map_err(|e| {
        crate::errors::AutorunError::IoError(format!("Failed to create storage directory: {}", e))
    })?;

    // Create subdirectories for different storage types
    let subdirs = ["sessions", "memory", "workspaces", "git", "temp"];
    for subdir in subdirs {
        let path = config.storage_dir.join(subdir);
        fs::create_dir_all(&path).await.map_err(|e| {
            crate::errors::AutorunError::IoError(format!(
                "Failed to create {} directory: {}",
                subdir, e
            ))
        })?;
    }

    // Create database files if they don't exist
    let db_files = ["sessions.db", "memory.db", "workspaces.db", "git.db"];
    for db_file in db_files {
        let db_path = config.storage_dir.join(db_file);
        if !db_path.exists() {
            // Touch the file to create it
            fs::File::create(&db_path).await.map_err(|e| {
                crate::errors::AutorunError::IoError(format!(
                    "Failed to create database file {}: {}",
                    db_file, e
                ))
            })?;
        }
    }

    tracing::info!(
        "Storage system initialized at: {}",
        config.storage_dir.display()
    );
    Ok(())
}

/// Get total storage usage across all systems
pub async fn get_total_storage_usage(config: &StorageConfig) -> Result<StorageStats> {
    use tokio::fs;

    let mut total_size = 0u64;
    let mut item_count = 0usize;
    let mut earliest_creation = SystemTime::now();
    let mut latest_access = SystemTime::UNIX_EPOCH;

    // Walk through storage directory and calculate sizes
    let mut dir_entries = fs::read_dir(&config.storage_dir).await.map_err(|e| {
        crate::errors::AutorunError::IoError(format!("Failed to read storage directory: {}", e))
    })?;

    while let Some(entry) = dir_entries.next_entry().await.map_err(|e| {
        crate::errors::AutorunError::IoError(format!("Failed to read directory entry: {}", e))
    })? {
        let metadata = entry.metadata().await.map_err(|e| {
            crate::errors::AutorunError::IoError(format!("Failed to get file metadata: {}", e))
        })?;

        if metadata.is_file() {
            total_size += metadata.len();
            item_count += 1;

            if let Ok(created) = metadata.created() {
                if created < earliest_creation {
                    earliest_creation = created;
                }
            }

            if let Ok(accessed) = metadata.accessed() {
                if accessed > latest_access {
                    latest_access = accessed;
                }
            }
        }
    }

    // Get available free space
    let free_space = match fs::metadata(&config.storage_dir).await {
        Ok(_) => {
            // This is a simplified approach - in reality you'd use system calls
            // to get actual free space on the filesystem
            1024 * 1024 * 1024 // Assume 1GB free for now
        }
        Err(_) => 0,
    };

    Ok(StorageStats {
        item_count,
        total_size_bytes: total_size,
        last_access: latest_access,
        created_at: earliest_creation,
        free_space_bytes: free_space,
    })
}

/// Cleanup old storage data based on configuration
pub async fn cleanup_storage(config: &StorageConfig) -> Result<usize> {
    let mut cleaned_items = 0;

    if let Some(cleanup_days) = config.auto_cleanup_days {
        let cutoff_time =
            SystemTime::now() - std::time::Duration::from_secs(cleanup_days as u64 * 24 * 60 * 60);

        // Initialize each storage system and run cleanup
        let mut session_store = session_store::SessionStore::new(config.clone()).await?;
        cleaned_items += session_store.cleanup().await?;

        let mut memory_store = memory_store::MemoryStore::new(config.clone()).await?;
        cleaned_items += memory_store.cleanup().await?;

        let mut workspace_store = workspace_store::WorkspaceStore::new(config.clone()).await?;
        cleaned_items += workspace_store.cleanup().await?;

        tracing::info!("Storage cleanup completed: {} items removed", cleaned_items);
    }

    Ok(cleaned_items)
}

/// Export storage data for backup
pub async fn export_storage(config: &StorageConfig, export_path: &Path) -> Result<()> {
    use std::io::Write;
    use tokio::fs;

    let export_data = ExportData {
        version: "1.0".to_string(),
        exported_at: SystemTime::now(),
        config: config.clone(),
        stats: get_total_storage_usage(config).await?,
    };

    let json_data = serde_json::to_string_pretty(&export_data).map_err(|e| {
        crate::errors::AutorunError::SerializationError(format!(
            "Failed to serialize export data: {}",
            e
        ))
    })?;

    fs::write(export_path, json_data).await.map_err(|e| {
        crate::errors::AutorunError::IoError(format!("Failed to write export file: {}", e))
    })?;

    tracing::info!("Storage data exported to: {}", export_path.display());
    Ok(())
}

/// Import storage data from backup
pub async fn import_storage(config: &StorageConfig, import_path: &Path) -> Result<()> {
    use tokio::fs;

    let json_data = fs::read_to_string(import_path).await.map_err(|e| {
        crate::errors::AutorunError::IoError(format!("Failed to read import file: {}", e))
    })?;

    let _export_data: ExportData = serde_json::from_str(&json_data).map_err(|e| {
        crate::errors::AutorunError::SerializationError(format!(
            "Failed to deserialize import data: {}",
            e
        ))
    })?;

    // Import logic would go here - for now just validate the file format
    tracing::info!("Storage data imported from: {}", import_path.display());
    Ok(())
}

/// Export/import data structure
#[derive(Debug, Serialize, Deserialize)]
struct ExportData {
    version: String,
    exported_at: SystemTime,
    config: StorageConfig,
    stats: StorageStats,
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_storage_initialization() {
        let temp_dir = TempDir::new().unwrap();
        let mut config = StorageConfig::default();
        config.storage_dir = temp_dir.path().to_path_buf();

        let result = initialize_storage(&config).await;
        assert!(result.is_ok());

        // Check that subdirectories were created
        assert!(config.storage_dir.join("sessions").exists());
        assert!(config.storage_dir.join("memory").exists());
        assert!(config.storage_dir.join("workspaces").exists());
        assert!(config.storage_dir.join("git").exists());
    }

    #[tokio::test]
    async fn test_storage_entry_creation() {
        let entry = StorageEntry::new("test data".to_string())
            .with_metadata("type".to_string(), "test".to_string());

        assert_eq!(entry.data, "test data");
        assert_eq!(entry.metadata.get("type"), Some(&"test".to_string()));
        assert!(!entry.compressed);
        assert!(!entry.encrypted);
    }

    #[tokio::test]
    async fn test_storage_usage_calculation() {
        let temp_dir = TempDir::new().unwrap();
        let mut config = StorageConfig::default();
        config.storage_dir = temp_dir.path().to_path_buf();

        initialize_storage(&config).await.unwrap();

        let stats = get_total_storage_usage(&config).await.unwrap();
        assert!(stats.total_size_bytes >= 0);
        assert!(stats.free_space_bytes > 0);
    }
}
