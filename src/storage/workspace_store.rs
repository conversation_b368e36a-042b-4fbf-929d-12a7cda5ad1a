//! Workspace configuration management system
//!
//! Provides persistent storage for workspace configurations, project settings,
//! and development environment state.

use super::{StorageBackend, StorageConfig, StorageEntry, StorageStats};
use crate::errors::{AutorunError, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use std::time::SystemTime;
use tokio_rusqlite::{params, Connection};
use uuid::Uuid;

/// Workspace configuration data structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Workspace {
    /// Unique workspace identifier
    pub id: Uuid,
    /// Workspace name
    pub name: String,
    /// Workspace description
    pub description: Option<String>,
    /// Root directory path
    pub root_path: PathBuf,
    /// Creation timestamp
    pub created_at: SystemTime,
    /// Last modified timestamp
    pub modified_at: SystemTime,
    /// Last accessed timestamp
    pub accessed_at: SystemTime,
    /// Workspace settings
    pub settings: WorkspaceSettings,
    /// Environment variables
    pub environment: HashMap<String, String>,
    /// Project metadata
    pub metadata: HashMap<String, String>,
    /// Workspace status
    pub status: WorkspaceStatus,
    /// Associated sessions
    pub sessions: Vec<Uuid>,
    /// Favorite files and directories
    pub favorites: Vec<FavoriteItem>,
    /// Recent files
    pub recent_files: Vec<RecentFile>,
    /// Custom commands
    pub custom_commands: Vec<CustomCommand>,
    /// Build configurations
    pub build_configs: Vec<BuildConfig>,
}

/// Workspace settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkspaceSettings {
    /// Default shell command
    pub default_shell: Option<String>,
    /// Working directory preferences
    pub working_directory: Option<PathBuf>,
    /// Editor preferences
    pub editor: EditorSettings,
    /// Git configuration
    pub git: GitSettings,
    /// Language-specific settings
    pub languages: HashMap<String, LanguageSettings>,
    /// Tool configurations
    pub tools: HashMap<String, serde_json::Value>,
    /// Theme and UI preferences
    pub ui: UiSettings,
    /// Search and indexing preferences
    pub indexing: IndexingSettings,
}

/// Editor preferences
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EditorSettings {
    /// Default file encoding
    pub encoding: String,
    /// Line ending preference
    pub line_endings: LineEndings,
    /// Tab size
    pub tab_size: u8,
    /// Use spaces instead of tabs
    pub use_spaces: bool,
    /// Auto-save settings
    pub auto_save: bool,
    /// Auto-format on save
    pub format_on_save: bool,
}

/// Line ending types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LineEndings {
    Unix,    // LF
    Windows, // CRLF
    Classic, // CR
    Auto,    // Detect from file
}

/// Git configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitSettings {
    /// Auto-fetch enabled
    pub auto_fetch: bool,
    /// Default branch name
    pub default_branch: String,
    /// Ignore patterns
    pub ignore_patterns: Vec<String>,
    /// Hook configurations
    pub hooks: HashMap<String, String>,
}

/// Language-specific settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LanguageSettings {
    /// File extensions
    pub extensions: Vec<String>,
    /// Formatter command
    pub formatter: Option<String>,
    /// Linter command
    pub linter: Option<String>,
    /// Build command
    pub build_command: Option<String>,
    /// Test command
    pub test_command: Option<String>,
    /// Language server configuration
    pub lsp: Option<LspSettings>,
}

/// Language Server Protocol settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LspSettings {
    /// LSP server command
    pub command: Vec<String>,
    /// Initialization options
    pub init_options: serde_json::Value,
    /// Server capabilities
    pub capabilities: Vec<String>,
}

/// UI preferences
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UiSettings {
    /// Theme name
    pub theme: String,
    /// Color scheme
    pub color_scheme: String,
    /// Font family
    pub font_family: String,
    /// Font size
    pub font_size: u8,
    /// Show line numbers
    pub show_line_numbers: bool,
    /// Show minimap
    pub show_minimap: bool,
}

/// Indexing and search settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IndexingSettings {
    /// Enable automatic indexing
    pub auto_index: bool,
    /// Index hidden files
    pub index_hidden: bool,
    /// Exclude patterns
    pub exclude_patterns: Vec<String>,
    /// Maximum file size to index (MB)
    pub max_file_size_mb: u64,
    /// Update frequency in seconds
    pub update_frequency: u64,
}

/// Workspace status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum WorkspaceStatus {
    Active,
    Inactive,
    Archived,
}

/// Favorite item (file or directory)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FavoriteItem {
    /// Item path relative to workspace root
    pub path: PathBuf,
    /// Display name
    pub name: String,
    /// Item type
    pub item_type: FavoriteType,
    /// Added timestamp
    pub added_at: SystemTime,
}

/// Favorite item type
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FavoriteType {
    File,
    Directory,
    Bookmark,
}

/// Recent file entry
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecentFile {
    /// File path relative to workspace root
    pub path: PathBuf,
    /// Last accessed timestamp
    pub accessed_at: SystemTime,
    /// Access count
    pub access_count: u32,
}

/// Custom command definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CustomCommand {
    /// Command name
    pub name: String,
    /// Command description
    pub description: String,
    /// Command to execute
    pub command: String,
    /// Working directory (relative to workspace)
    pub working_dir: Option<PathBuf>,
    /// Environment variables
    pub env: HashMap<String, String>,
    /// Command category
    pub category: String,
    /// Keyboard shortcut
    pub shortcut: Option<String>,
}

/// Build configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildConfig {
    /// Configuration name
    pub name: String,
    /// Build command
    pub command: String,
    /// Build directory
    pub build_dir: Option<PathBuf>,
    /// Environment variables
    pub env: HashMap<String, String>,
    /// Pre-build commands
    pub pre_build: Vec<String>,
    /// Post-build commands
    pub post_build: Vec<String>,
    /// Target platform
    pub target: Option<String>,
}

/// Workspace storage implementation
pub struct WorkspaceStore {
    config: StorageConfig,
    connection: Connection,
}

impl WorkspaceStore {
    /// Create a new workspace store
    pub async fn new(config: StorageConfig) -> Result<Self> {
        let db_path = config.storage_dir.join("workspaces.db");
        let connection = Connection::open(&db_path).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to open workspace database: {}", e))
        })?;

        let mut store = Self { config, connection };
        store.initialize().await?;
        Ok(store)
    }

    /// Create a new workspace
    pub async fn create_workspace(
        &mut self,
        name: String,
        description: Option<String>,
        root_path: PathBuf,
    ) -> Result<Workspace> {
        let workspace = Workspace {
            id: Uuid::new_v4(),
            name,
            description,
            root_path,
            created_at: SystemTime::now(),
            modified_at: SystemTime::now(),
            accessed_at: SystemTime::now(),
            settings: WorkspaceSettings::default(),
            environment: HashMap::new(),
            metadata: HashMap::new(),
            status: WorkspaceStatus::Active,
            sessions: Vec::new(),
            favorites: Vec::new(),
            recent_files: Vec::new(),
            custom_commands: Vec::new(),
            build_configs: Vec::new(),
        };

        self.store(workspace.id, workspace.clone()).await?;
        Ok(workspace)
    }

    /// Update workspace settings
    pub async fn update_settings(
        &mut self,
        workspace_id: Uuid,
        settings: WorkspaceSettings,
    ) -> Result<bool> {
        if let Some(mut workspace) = self.retrieve(&workspace_id).await? {
            workspace.settings = settings;
            workspace.modified_at = SystemTime::now();
            self.store(workspace_id, workspace).await?;
            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// Add a favorite item
    pub async fn add_favorite(
        &mut self,
        workspace_id: Uuid,
        path: PathBuf,
        name: String,
        item_type: FavoriteType,
    ) -> Result<bool> {
        if let Some(mut workspace) = self.retrieve(&workspace_id).await? {
            let favorite = FavoriteItem {
                path,
                name,
                item_type,
                added_at: SystemTime::now(),
            };

            workspace.favorites.push(favorite);
            workspace.modified_at = SystemTime::now();
            self.store(workspace_id, workspace).await?;
            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// Record file access
    pub async fn record_file_access(
        &mut self,
        workspace_id: Uuid,
        file_path: PathBuf,
    ) -> Result<bool> {
        if let Some(mut workspace) = self.retrieve(&workspace_id).await? {
            // Find existing entry or create new one
            if let Some(entry) = workspace
                .recent_files
                .iter_mut()
                .find(|r| r.path == file_path)
            {
                entry.accessed_at = SystemTime::now();
                entry.access_count += 1;
            } else {
                workspace.recent_files.push(RecentFile {
                    path: file_path,
                    accessed_at: SystemTime::now(),
                    access_count: 1,
                });
            }

            // Keep only the most recent 100 files
            workspace
                .recent_files
                .sort_by(|a, b| b.accessed_at.cmp(&a.accessed_at));
            workspace.recent_files.truncate(100);

            workspace.accessed_at = SystemTime::now();
            self.store(workspace_id, workspace).await?;
            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// Add custom command
    pub async fn add_custom_command(
        &mut self,
        workspace_id: Uuid,
        command: CustomCommand,
    ) -> Result<bool> {
        if let Some(mut workspace) = self.retrieve(&workspace_id).await? {
            workspace.custom_commands.push(command);
            workspace.modified_at = SystemTime::now();
            self.store(workspace_id, workspace).await?;
            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// Get workspaces by status
    pub async fn get_workspaces_by_status(
        &self,
        status: WorkspaceStatus,
        limit: usize,
    ) -> Result<Vec<Workspace>> {
        let status_str = serde_json::to_string(&status).map_err(|e| {
            AutorunError::SerializationError(format!("Failed to serialize status: {}", e))
        })?;

        let sql = "
            SELECT data FROM workspaces 
            WHERE json_extract(data, '$.status') = ?
            ORDER BY json_extract(data, '$.accessed_at') DESC 
            LIMIT ?
        ";

        let mut stmt = self.connection.prepare(sql).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to prepare status query: {}", e))
        })?;

        let rows = stmt
            .query_map(params![status_str, limit], |row| {
                let data: String = row.get(0)?;
                Ok(data)
            })
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to execute status query: {}", e))
            })?;

        let mut workspaces = Vec::new();
        for row_result in rows {
            let data = row_result.map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to read status row: {}", e))
            })?;

            let workspace: Workspace = serde_json::from_str(&data).map_err(|e| {
                AutorunError::SerializationError(format!("Failed to deserialize workspace: {}", e))
            })?;

            workspaces.push(workspace);
        }

        Ok(workspaces)
    }

    /// Search workspaces by name or path
    pub async fn search_workspaces(&self, query: &str, limit: usize) -> Result<Vec<Workspace>> {
        let search_pattern = format!("%{}%", query);
        let sql = "
            SELECT data FROM workspaces 
            WHERE (
                json_extract(data, '$.name') LIKE ? OR
                json_extract(data, '$.root_path') LIKE ?
            )
            ORDER BY json_extract(data, '$.accessed_at') DESC 
            LIMIT ?
        ";

        let mut stmt = self.connection.prepare(sql).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to prepare search query: {}", e))
        })?;

        let rows = stmt
            .query_map(params![search_pattern, search_pattern, limit], |row| {
                let data: String = row.get(0)?;
                Ok(data)
            })
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to execute search query: {}", e))
            })?;

        let mut workspaces = Vec::new();
        for row_result in rows {
            let data = row_result.map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to read search row: {}", e))
            })?;

            let workspace: Workspace = serde_json::from_str(&data).map_err(|e| {
                AutorunError::SerializationError(format!(
                    "Failed to deserialize searched workspace: {}",
                    e
                ))
            })?;

            workspaces.push(workspace);
        }

        Ok(workspaces)
    }

    /// Get workspace statistics
    pub async fn get_workspace_stats(&self) -> Result<WorkspaceStats> {
        let sql = "
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN json_extract(data, '$.status') = '\"Active\"' THEN 1 END) as active,
                COUNT(CASE WHEN json_extract(data, '$.status') = '\"Archived\"' THEN 1 END) as archived,
                AVG(json_array_length(json_extract(data, '$.recent_files'))) as avg_recent_files
            FROM workspaces
        ";

        let mut stmt = self.connection.prepare(sql).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to prepare workspace stats query: {}", e))
        })?;

        let row = stmt
            .query_row(params![], |row| {
                Ok(WorkspaceStats {
                    total_workspaces: row.get::<_, i64>(0)? as usize,
                    active_workspaces: row.get::<_, i64>(1)? as usize,
                    archived_workspaces: row.get::<_, i64>(2)? as usize,
                    average_recent_files: row.get::<_, Option<f64>>(3)?.unwrap_or(0.0),
                })
            })
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to get workspace stats: {}", e))
            })?;

        Ok(row)
    }

    /// Export workspace configuration
    pub async fn export_workspace(&self, workspace_id: Uuid) -> Result<Option<String>> {
        if let Some(workspace) = self.retrieve(&workspace_id).await? {
            let exported = serde_json::to_string_pretty(&workspace).map_err(|e| {
                AutorunError::SerializationError(format!("Failed to export workspace: {}", e))
            })?;
            Ok(Some(exported))
        } else {
            Ok(None)
        }
    }

    /// Import workspace configuration
    pub async fn import_workspace(&mut self, workspace_data: &str) -> Result<Uuid> {
        let workspace: Workspace = serde_json::from_str(workspace_data).map_err(|e| {
            AutorunError::SerializationError(format!("Failed to import workspace: {}", e))
        })?;

        let workspace_id = workspace.id;
        self.store(workspace_id, workspace).await?;
        Ok(workspace_id)
    }
}

impl Default for WorkspaceSettings {
    fn default() -> Self {
        Self {
            default_shell: None,
            working_directory: None,
            editor: EditorSettings::default(),
            git: GitSettings::default(),
            languages: HashMap::new(),
            tools: HashMap::new(),
            ui: UiSettings::default(),
            indexing: IndexingSettings::default(),
        }
    }
}

impl Default for EditorSettings {
    fn default() -> Self {
        Self {
            encoding: "UTF-8".to_string(),
            line_endings: LineEndings::Auto,
            tab_size: 4,
            use_spaces: true,
            auto_save: true,
            format_on_save: false,
        }
    }
}

impl Default for GitSettings {
    fn default() -> Self {
        Self {
            auto_fetch: false,
            default_branch: "main".to_string(),
            ignore_patterns: vec![
                "*.log".to_string(),
                "target/".to_string(),
                "node_modules/".to_string(),
                ".DS_Store".to_string(),
            ],
            hooks: HashMap::new(),
        }
    }
}

impl Default for UiSettings {
    fn default() -> Self {
        Self {
            theme: "default".to_string(),
            color_scheme: "dark".to_string(),
            font_family: "monospace".to_string(),
            font_size: 12,
            show_line_numbers: true,
            show_minimap: false,
        }
    }
}

impl Default for IndexingSettings {
    fn default() -> Self {
        Self {
            auto_index: true,
            index_hidden: false,
            exclude_patterns: vec![
                "target/".to_string(),
                "node_modules/".to_string(),
                ".git/".to_string(),
                "*.log".to_string(),
            ],
            max_file_size_mb: 10,
            update_frequency: 300, // 5 minutes
        }
    }
}

impl StorageBackend for WorkspaceStore {
    type Item = Workspace;
    type Key = Uuid;

    async fn initialize(&mut self) -> Result<()> {
        let create_table_sql = "
            CREATE TABLE IF NOT EXISTS workspaces (
                id TEXT PRIMARY KEY,
                data TEXT NOT NULL,
                created_at INTEGER NOT NULL,
                accessed_at INTEGER NOT NULL
            )
        ";

        self.connection
            .execute(create_table_sql, params![])
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to create workspaces table: {}", e))
            })?;

        // Create indexes for performance
        let indexes = [
            "CREATE INDEX IF NOT EXISTS idx_workspaces_created ON workspaces(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_workspaces_accessed ON workspaces(accessed_at)",
            "CREATE INDEX IF NOT EXISTS idx_workspaces_status ON workspaces(json_extract(data, '$.status'))",
            "CREATE INDEX IF NOT EXISTS idx_workspaces_name ON workspaces(json_extract(data, '$.name'))",
        ];

        for index_sql in indexes {
            self.connection
                .execute(index_sql, params![])
                .await
                .map_err(|e| {
                    AutorunError::DatabaseError(format!("Failed to create workspace index: {}", e))
                })?;
        }

        Ok(())
    }

    async fn store(&mut self, key: Self::Key, item: Self::Item) -> Result<()> {
        let data = serde_json::to_string(&item).map_err(|e| {
            AutorunError::SerializationError(format!("Failed to serialize workspace: {}", e))
        })?;

        let created_at = item
            .created_at
            .duration_since(SystemTime::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs() as i64;

        let accessed_at = item
            .accessed_at
            .duration_since(SystemTime::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs() as i64;

        let sql = "
            INSERT OR REPLACE INTO workspaces (id, data, created_at, accessed_at)
            VALUES (?, ?, ?, ?)
        ";

        self.connection
            .execute(sql, params![key.to_string(), data, created_at, accessed_at])
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to store workspace: {}", e))
            })?;

        Ok(())
    }

    async fn retrieve(&self, key: &Self::Key) -> Result<Option<Self::Item>> {
        let sql = "SELECT data FROM workspaces WHERE id = ?";

        let mut stmt = self.connection.prepare(sql).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to prepare retrieve query: {}", e))
        })?;

        let result = stmt
            .query_row(params![key.to_string()], |row| {
                let data: String = row.get(0)?;
                Ok(data)
            })
            .await;

        match result {
            Ok(data) => {
                let workspace: Workspace = serde_json::from_str(&data).map_err(|e| {
                    AutorunError::SerializationError(format!(
                        "Failed to deserialize workspace: {}",
                        e
                    ))
                })?;
                Ok(Some(workspace))
            }
            Err(tokio_rusqlite::Error::Rusqlite(rusqlite::Error::QueryReturnedNoRows)) => Ok(None),
            Err(e) => Err(AutorunError::DatabaseError(format!(
                "Failed to retrieve workspace: {}",
                e
            ))),
        }
    }

    async fn delete(&mut self, key: &Self::Key) -> Result<bool> {
        let sql = "DELETE FROM workspaces WHERE id = ?";

        let changes = self
            .connection
            .execute(sql, params![key.to_string()])
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to delete workspace: {}", e))
            })?;

        Ok(changes > 0)
    }

    async fn list_keys(&self) -> Result<Vec<Self::Key>> {
        let sql = "SELECT id FROM workspaces";

        let mut stmt = self.connection.prepare(sql).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to prepare list query: {}", e))
        })?;

        let rows = stmt
            .query_map(params![], |row| {
                let id_str: String = row.get(0)?;
                Ok(id_str)
            })
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to execute list query: {}", e))
            })?;

        let mut keys = Vec::new();
        for row_result in rows {
            let id_str = row_result.map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to read list row: {}", e))
            })?;

            let uuid = Uuid::parse_str(&id_str).map_err(|e| {
                AutorunError::SerializationError(format!("Invalid UUID in database: {}", e))
            })?;

            keys.push(uuid);
        }

        Ok(keys)
    }

    async fn get_stats(&self) -> Result<StorageStats> {
        let sql = "
            SELECT 
                COUNT(*) as count,
                SUM(LENGTH(data)) as total_size,
                MIN(created_at) as earliest,
                MAX(accessed_at) as latest
            FROM workspaces
        ";

        let mut stmt = self.connection.prepare(sql).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to prepare stats query: {}", e))
        })?;

        let row = stmt
            .query_row(params![], |row| {
                Ok((
                    row.get::<_, i64>(0)? as usize,
                    row.get::<_, i64>(1)? as u64,
                    row.get::<_, i64>(2)?,
                    row.get::<_, i64>(3)?,
                ))
            })
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to get storage stats: {}", e))
            })?;

        let (count, size, earliest, latest) = row;

        Ok(StorageStats {
            item_count: count,
            total_size_bytes: size,
            created_at: SystemTime::UNIX_EPOCH + std::time::Duration::from_secs(earliest as u64),
            last_access: SystemTime::UNIX_EPOCH + std::time::Duration::from_secs(latest as u64),
            free_space_bytes: 1024 * 1024 * 1024, // Simplified - 1GB
        })
    }

    async fn cleanup(&mut self) -> Result<usize> {
        if let Some(cleanup_days) = self.config.auto_cleanup_days {
            let cutoff_time = SystemTime::now()
                .duration_since(SystemTime::UNIX_EPOCH)
                .unwrap()
                .as_secs() as i64
                - (cleanup_days as i64 * 24 * 60 * 60);

            let sql = "
                DELETE FROM workspaces 
                WHERE json_extract(data, '$.status') = '\"Archived\"' 
                AND accessed_at < ?
            ";

            let changes = self
                .connection
                .execute(sql, params![cutoff_time])
                .await
                .map_err(|e| {
                    AutorunError::DatabaseError(format!("Failed to cleanup workspaces: {}", e))
                })?;

            Ok(changes)
        } else {
            Ok(0)
        }
    }
}

/// Workspace statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkspaceStats {
    pub total_workspaces: usize,
    pub active_workspaces: usize,
    pub archived_workspaces: usize,
    pub average_recent_files: f64,
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    async fn create_test_store() -> (WorkspaceStore, TempDir) {
        let temp_dir = TempDir::new().unwrap();
        let mut config = StorageConfig::default();
        config.storage_dir = temp_dir.path().to_path_buf();

        let store = WorkspaceStore::new(config).await.unwrap();
        (store, temp_dir)
    }

    #[tokio::test]
    async fn test_workspace_creation_and_retrieval() {
        let (mut store, temp_dir) = create_test_store().await;

        let workspace = store
            .create_workspace(
                "Test Workspace".to_string(),
                Some("A test workspace".to_string()),
                temp_dir.path().to_path_buf(),
            )
            .await
            .unwrap();

        let retrieved = store.retrieve(&workspace.id).await.unwrap();
        assert!(retrieved.is_some());
        assert_eq!(retrieved.unwrap().name, "Test Workspace");
    }

    #[tokio::test]
    async fn test_workspace_favorites() {
        let (mut store, temp_dir) = create_test_store().await;

        let workspace = store
            .create_workspace(
                "Test Workspace".to_string(),
                None,
                temp_dir.path().to_path_buf(),
            )
            .await
            .unwrap();

        let success = store
            .add_favorite(
                workspace.id,
                PathBuf::from("src/main.rs"),
                "Main Source".to_string(),
                FavoriteType::File,
            )
            .await
            .unwrap();

        assert!(success);

        let updated_workspace = store.retrieve(&workspace.id).await.unwrap().unwrap();
        assert_eq!(updated_workspace.favorites.len(), 1);
        assert_eq!(updated_workspace.favorites[0].name, "Main Source");
    }

    #[tokio::test]
    async fn test_workspace_search() {
        let (mut store, temp_dir) = create_test_store().await;

        let _workspace1 = store
            .create_workspace(
                "Rust Project".to_string(),
                None,
                temp_dir.path().join("rust"),
            )
            .await
            .unwrap();

        let _workspace2 = store
            .create_workspace(
                "Python Project".to_string(),
                None,
                temp_dir.path().join("python"),
            )
            .await
            .unwrap();

        let results = store.search_workspaces("Rust", 10).await.unwrap();
        assert_eq!(results.len(), 1);
        assert_eq!(results[0].name, "Rust Project");
    }
}
