//! Session persistence system using SQLite backend
//!
//! Provides persistent storage for conversation sessions with support for
//! encryption, compression, and efficient querying.

use super::{StorageBackend, StorageConfig, StorageEntry, StorageStats};
use crate::errors::{AutorunError, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use std::time::SystemTime;
use tokio_rusqlite::{params, Connection};
use uuid::Uuid;

/// Session data structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Session {
    /// Unique session identifier
    pub id: Uuid,
    /// Human-readable session name
    pub name: String,
    /// Session description
    pub description: Option<String>,
    /// Creation timestamp
    pub created_at: SystemTime,
    /// Last access timestamp
    pub last_accessed: SystemTime,
    /// Session messages
    pub messages: Vec<SessionMessage>,
    /// Session metadata
    pub metadata: HashMap<String, String>,
    /// Working directory when session was created
    pub working_directory: PathBuf,
    /// Git branch/commit when session was created
    pub git_context: Option<GitContext>,
    /// Session tags for organization
    pub tags: Vec<String>,
    /// Session status
    pub status: SessionStatus,
}

/// Individual message within a session
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionMessage {
    /// Message ID
    pub id: Uuid,
    /// Message timestamp
    pub timestamp: SystemTime,
    /// Message role (user, assistant, system)
    pub role: MessageRole,
    /// Message content
    pub content: String,
    /// Associated tool calls
    pub tool_calls: Vec<ToolCall>,
    /// Message metadata
    pub metadata: HashMap<String, String>,
}

/// Message role enumeration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MessageRole {
    User,
    Assistant,
    System,
    Tool,
}

/// Tool call information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolCall {
    /// Tool name
    pub tool_name: String,
    /// Tool arguments
    pub arguments: serde_json::Value,
    /// Tool result
    pub result: Option<String>,
    /// Execution time
    pub execution_time: std::time::Duration,
}

/// Git context information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitContext {
    /// Current branch
    pub branch: String,
    /// Current commit hash
    pub commit_hash: String,
    /// Repository URL
    pub repository_url: Option<String>,
    /// Uncommitted changes
    pub has_uncommitted_changes: bool,
}

/// Session status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SessionStatus {
    Active,
    Archived,
    Deleted,
}

/// Session storage implementation
pub struct SessionStore {
    config: StorageConfig,
    connection: Connection,
}

impl SessionStore {
    /// Create a new session store
    pub async fn new(config: StorageConfig) -> Result<Self> {
        let db_path = config.storage_dir.join("sessions.db");
        let connection = Connection::open(&db_path).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to open session database: {}", e))
        })?;

        let mut store = Self { config, connection };
        store.initialize().await?;
        Ok(store)
    }

    /// Create a new session
    pub async fn create_session(
        &mut self,
        name: String,
        description: Option<String>,
        working_directory: PathBuf,
        git_context: Option<GitContext>,
    ) -> Result<Session> {
        let session = Session {
            id: Uuid::new_v4(),
            name,
            description,
            created_at: SystemTime::now(),
            last_accessed: SystemTime::now(),
            messages: Vec::new(),
            metadata: HashMap::new(),
            working_directory,
            git_context,
            tags: Vec::new(),
            status: SessionStatus::Active,
        };

        self.store(session.id, session.clone()).await?;
        Ok(session)
    }

    /// Add a message to a session
    pub async fn add_message(
        &mut self,
        session_id: Uuid,
        role: MessageRole,
        content: String,
        tool_calls: Vec<ToolCall>,
    ) -> Result<SessionMessage> {
        let message = SessionMessage {
            id: Uuid::new_v4(),
            timestamp: SystemTime::now(),
            role,
            content,
            tool_calls,
            metadata: HashMap::new(),
        };

        // Update session with new message
        if let Some(mut session) = self.retrieve(&session_id).await? {
            session.messages.push(message.clone());
            session.last_accessed = SystemTime::now();
            self.store(session_id, session).await?;
        }

        Ok(message)
    }

    /// Get recent sessions
    pub async fn get_recent_sessions(&self, limit: usize) -> Result<Vec<Session>> {
        let query = "
            SELECT data FROM sessions 
            WHERE json_extract(data, '$.status') = 'Active'
            ORDER BY json_extract(data, '$.last_accessed') DESC 
            LIMIT ?
        ";

        let mut stmt =
            self.connection.prepare(query).await.map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to prepare query: {}", e))
            })?;

        let rows = stmt
            .query_map(params![limit], |row| {
                let data: String = row.get(0)?;
                Ok(data)
            })
            .await
            .map_err(|e| AutorunError::DatabaseError(format!("Failed to execute query: {}", e)))?;

        let mut sessions = Vec::new();
        for row_result in rows {
            let data = row_result
                .map_err(|e| AutorunError::DatabaseError(format!("Failed to read row: {}", e)))?;

            let session: Session = serde_json::from_str(&data).map_err(|e| {
                AutorunError::SerializationError(format!("Failed to deserialize session: {}", e))
            })?;

            sessions.push(session);
        }

        Ok(sessions)
    }

    /// Search sessions by query string
    pub async fn search_sessions(&self, query: &str, limit: usize) -> Result<Vec<Session>> {
        let sql_query = "
            SELECT data FROM sessions 
            WHERE (
                json_extract(data, '$.name') LIKE ? OR
                json_extract(data, '$.description') LIKE ? OR
                json_extract(data, '$.tags') LIKE ?
            )
            AND json_extract(data, '$.status') = 'Active'
            ORDER BY json_extract(data, '$.last_accessed') DESC 
            LIMIT ?
        ";

        let search_pattern = format!("%{}%", query);
        let mut stmt = self.connection.prepare(sql_query).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to prepare search query: {}", e))
        })?;

        let rows = stmt
            .query_map(
                params![search_pattern, search_pattern, search_pattern, limit],
                |row| {
                    let data: String = row.get(0)?;
                    Ok(data)
                },
            )
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to execute search query: {}", e))
            })?;

        let mut sessions = Vec::new();
        for row_result in rows {
            let data = row_result.map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to read search row: {}", e))
            })?;

            let session: Session = serde_json::from_str(&data).map_err(|e| {
                AutorunError::SerializationError(format!(
                    "Failed to deserialize searched session: {}",
                    e
                ))
            })?;

            sessions.push(session);
        }

        Ok(sessions)
    }

    /// Archive a session
    pub async fn archive_session(&mut self, session_id: Uuid) -> Result<bool> {
        if let Some(mut session) = self.retrieve(&session_id).await? {
            session.status = SessionStatus::Archived;
            self.store(session_id, session).await?;
            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// Get session statistics
    pub async fn get_session_stats(&self) -> Result<SessionStats> {
        let query = "
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN json_extract(data, '$.status') = 'Active' THEN 1 END) as active,
                COUNT(CASE WHEN json_extract(data, '$.status') = 'Archived' THEN 1 END) as archived,
                SUM(json_array_length(json_extract(data, '$.messages'))) as total_messages
            FROM sessions
        ";

        let mut stmt = self.connection.prepare(query).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to prepare stats query: {}", e))
        })?;

        let row = stmt
            .query_row(params![], |row| {
                Ok(SessionStats {
                    total_sessions: row.get::<_, i64>(0)? as usize,
                    active_sessions: row.get::<_, i64>(1)? as usize,
                    archived_sessions: row.get::<_, i64>(2)? as usize,
                    total_messages: row.get::<_, Option<i64>>(3)?.unwrap_or(0) as usize,
                })
            })
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to get session stats: {}", e))
            })?;

        Ok(row)
    }

    /// Export session data
    pub async fn export_session(&self, session_id: Uuid) -> Result<Option<String>> {
        if let Some(session) = self.retrieve(&session_id).await? {
            let exported = serde_json::to_string_pretty(&session).map_err(|e| {
                AutorunError::SerializationError(format!("Failed to export session: {}", e))
            })?;
            Ok(Some(exported))
        } else {
            Ok(None)
        }
    }

    /// Import session data
    pub async fn import_session(&mut self, session_data: &str) -> Result<Uuid> {
        let session: Session = serde_json::from_str(session_data).map_err(|e| {
            AutorunError::SerializationError(format!("Failed to import session: {}", e))
        })?;

        let session_id = session.id;
        self.store(session_id, session).await?;
        Ok(session_id)
    }
}

impl StorageBackend for SessionStore {
    type Item = Session;
    type Key = Uuid;

    async fn initialize(&mut self) -> Result<()> {
        let create_table_sql = "
            CREATE TABLE IF NOT EXISTS sessions (
                id TEXT PRIMARY KEY,
                data TEXT NOT NULL,
                created_at INTEGER NOT NULL,
                accessed_at INTEGER NOT NULL
            )
        ";

        self.connection
            .execute(create_table_sql, params![])
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to create sessions table: {}", e))
            })?;

        // Create indexes for performance
        let indexes = [
            "CREATE INDEX IF NOT EXISTS idx_sessions_created ON sessions(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_sessions_accessed ON sessions(accessed_at)",
            "CREATE INDEX IF NOT EXISTS idx_sessions_status ON sessions(json_extract(data, '$.status'))",
        ];

        for index_sql in indexes {
            self.connection
                .execute(index_sql, params![])
                .await
                .map_err(|e| {
                    AutorunError::DatabaseError(format!("Failed to create index: {}", e))
                })?;
        }

        Ok(())
    }

    async fn store(&mut self, key: Self::Key, item: Self::Item) -> Result<()> {
        let data = serde_json::to_string(&item).map_err(|e| {
            AutorunError::SerializationError(format!("Failed to serialize session: {}", e))
        })?;

        let now = SystemTime::now()
            .duration_since(SystemTime::UNIX_EPOCH)
            .unwrap()
            .as_secs() as i64;

        let sql = "
            INSERT OR REPLACE INTO sessions (id, data, created_at, accessed_at)
            VALUES (?, ?, ?, ?)
        ";

        self.connection
            .execute(sql, params![key.to_string(), data, now, now])
            .await
            .map_err(|e| AutorunError::DatabaseError(format!("Failed to store session: {}", e)))?;

        Ok(())
    }

    async fn retrieve(&self, key: &Self::Key) -> Result<Option<Self::Item>> {
        let sql = "SELECT data FROM sessions WHERE id = ?";

        let mut stmt = self.connection.prepare(sql).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to prepare retrieve query: {}", e))
        })?;

        let result = stmt
            .query_row(params![key.to_string()], |row| {
                let data: String = row.get(0)?;
                Ok(data)
            })
            .await;

        match result {
            Ok(data) => {
                let session: Session = serde_json::from_str(&data).map_err(|e| {
                    AutorunError::SerializationError(format!(
                        "Failed to deserialize session: {}",
                        e
                    ))
                })?;
                Ok(Some(session))
            }
            Err(tokio_rusqlite::Error::Rusqlite(rusqlite::Error::QueryReturnedNoRows)) => Ok(None),
            Err(e) => Err(AutorunError::DatabaseError(format!(
                "Failed to retrieve session: {}",
                e
            ))),
        }
    }

    async fn delete(&mut self, key: &Self::Key) -> Result<bool> {
        let sql = "DELETE FROM sessions WHERE id = ?";

        let changes = self
            .connection
            .execute(sql, params![key.to_string()])
            .await
            .map_err(|e| AutorunError::DatabaseError(format!("Failed to delete session: {}", e)))?;

        Ok(changes > 0)
    }

    async fn list_keys(&self) -> Result<Vec<Self::Key>> {
        let sql = "SELECT id FROM sessions";

        let mut stmt = self.connection.prepare(sql).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to prepare list query: {}", e))
        })?;

        let rows = stmt
            .query_map(params![], |row| {
                let id_str: String = row.get(0)?;
                Ok(id_str)
            })
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to execute list query: {}", e))
            })?;

        let mut keys = Vec::new();
        for row_result in rows {
            let id_str = row_result.map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to read list row: {}", e))
            })?;

            let uuid = Uuid::parse_str(&id_str).map_err(|e| {
                AutorunError::SerializationError(format!("Invalid UUID in database: {}", e))
            })?;

            keys.push(uuid);
        }

        Ok(keys)
    }

    async fn get_stats(&self) -> Result<StorageStats> {
        let sql = "
            SELECT 
                COUNT(*) as count,
                SUM(LENGTH(data)) as total_size,
                MIN(created_at) as earliest,
                MAX(accessed_at) as latest
            FROM sessions
        ";

        let mut stmt = self.connection.prepare(sql).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to prepare stats query: {}", e))
        })?;

        let row = stmt
            .query_row(params![], |row| {
                Ok((
                    row.get::<_, i64>(0)? as usize,
                    row.get::<_, i64>(1)? as u64,
                    row.get::<_, i64>(2)?,
                    row.get::<_, i64>(3)?,
                ))
            })
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to get storage stats: {}", e))
            })?;

        let (count, size, earliest, latest) = row;

        Ok(StorageStats {
            item_count: count,
            total_size_bytes: size,
            created_at: SystemTime::UNIX_EPOCH + std::time::Duration::from_secs(earliest as u64),
            last_access: SystemTime::UNIX_EPOCH + std::time::Duration::from_secs(latest as u64),
            free_space_bytes: 1024 * 1024 * 1024, // Simplified - 1GB
        })
    }

    async fn cleanup(&mut self) -> Result<usize> {
        if let Some(cleanup_days) = self.config.auto_cleanup_days {
            let cutoff_time = SystemTime::now()
                .duration_since(SystemTime::UNIX_EPOCH)
                .unwrap()
                .as_secs() as i64
                - (cleanup_days as i64 * 24 * 60 * 60);

            let sql = "
                DELETE FROM sessions 
                WHERE json_extract(data, '$.status') = 'Deleted' 
                AND accessed_at < ?
            ";

            let changes = self
                .connection
                .execute(sql, params![cutoff_time])
                .await
                .map_err(|e| {
                    AutorunError::DatabaseError(format!("Failed to cleanup sessions: {}", e))
                })?;

            Ok(changes)
        } else {
            Ok(0)
        }
    }
}

/// Session statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionStats {
    pub total_sessions: usize,
    pub active_sessions: usize,
    pub archived_sessions: usize,
    pub total_messages: usize,
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    async fn create_test_store() -> (SessionStore, TempDir) {
        let temp_dir = TempDir::new().unwrap();
        let mut config = StorageConfig::default();
        config.storage_dir = temp_dir.path().to_path_buf();

        let store = SessionStore::new(config).await.unwrap();
        (store, temp_dir)
    }

    #[tokio::test]
    async fn test_session_creation_and_retrieval() {
        let (mut store, _temp_dir) = create_test_store().await;

        let session = store
            .create_session(
                "Test Session".to_string(),
                Some("A test session".to_string()),
                std::env::current_dir().unwrap(),
                None,
            )
            .await
            .unwrap();

        let retrieved = store.retrieve(&session.id).await.unwrap();
        assert!(retrieved.is_some());
        assert_eq!(retrieved.unwrap().name, "Test Session");
    }

    #[tokio::test]
    async fn test_message_addition() {
        let (mut store, _temp_dir) = create_test_store().await;

        let session = store
            .create_session(
                "Test Session".to_string(),
                None,
                std::env::current_dir().unwrap(),
                None,
            )
            .await
            .unwrap();

        let message = store
            .add_message(
                session.id,
                MessageRole::User,
                "Hello, world!".to_string(),
                Vec::new(),
            )
            .await
            .unwrap();

        let updated_session = store.retrieve(&session.id).await.unwrap().unwrap();
        assert_eq!(updated_session.messages.len(), 1);
        assert_eq!(updated_session.messages[0].content, "Hello, world!");
    }

    #[tokio::test]
    async fn test_session_search() {
        let (mut store, _temp_dir) = create_test_store().await;

        let _session1 = store
            .create_session(
                "First Session".to_string(),
                Some("About Rust programming".to_string()),
                std::env::current_dir().unwrap(),
                None,
            )
            .await
            .unwrap();

        let _session2 = store
            .create_session(
                "Second Session".to_string(),
                Some("About Python programming".to_string()),
                std::env::current_dir().unwrap(),
                None,
            )
            .await
            .unwrap();

        let results = store.search_sessions("Rust", 10).await.unwrap();
        assert_eq!(results.len(), 1);
        assert_eq!(results[0].name, "First Session");
    }
}
