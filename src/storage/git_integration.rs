//! Git integration for repository analysis and reference extraction
//!
//! Provides functionality to analyze git repositories, extract metadata,
//! and manage git-related context information.

use super::{StorageBackend, StorageConfig, StorageEntry, StorageStats};
use crate::errors::{AutorunError, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::process::Command;
use std::time::SystemTime;
use tokio_rusqlite::{params, Connection};
use uuid::Uuid;

/// Git repository information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitRepository {
    /// Unique repository identifier
    pub id: Uuid,
    /// Repository root path
    pub root_path: PathBuf,
    /// Repository URL (if remote exists)
    pub remote_url: Option<String>,
    /// Current branch
    pub current_branch: String,
    /// Default branch (main/master)
    pub default_branch: String,
    /// Repository state
    pub state: RepoState,
    /// Last scan timestamp
    pub last_scanned: SystemTime,
    /// Branch information
    pub branches: Vec<BranchInfo>,
    /// Recent commits
    pub recent_commits: Vec<CommitInfo>,
    /// Working directory status
    pub status: WorkingDirStatus,
    /// Repository configuration
    pub config: GitConfig,
    /// Cached metadata
    pub metadata: HashMap<String, String>,
}

/// Repository state
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RepoState {
    Clean,
    Modified,
    Staged,
    Conflicted,
    Merging,
    Rebasing,
    Detached,
}

/// Branch information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BranchInfo {
    /// Branch name
    pub name: String,
    /// Branch type
    pub branch_type: BranchType,
    /// Last commit hash
    pub last_commit: String,
    /// Last commit message
    pub last_commit_message: String,
    /// Last commit author
    pub last_commit_author: String,
    /// Last commit date
    pub last_commit_date: SystemTime,
    /// Tracking remote
    pub tracking: Option<String>,
    /// Ahead/behind counts
    pub ahead_behind: Option<(u32, u32)>,
}

/// Branch type
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BranchType {
    Local,
    Remote,
    Tracking,
}

/// Commit information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommitInfo {
    /// Commit hash
    pub hash: String,
    /// Short hash
    pub short_hash: String,
    /// Commit message
    pub message: String,
    /// Author name
    pub author: String,
    /// Author email
    pub author_email: String,
    /// Commit date
    pub date: SystemTime,
    /// Changed files
    pub changed_files: Vec<String>,
    /// Insertions count
    pub insertions: u32,
    /// Deletions count
    pub deletions: u32,
}

/// Working directory status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkingDirStatus {
    /// Modified files
    pub modified: Vec<String>,
    /// Added files
    pub added: Vec<String>,
    /// Deleted files
    pub deleted: Vec<String>,
    /// Renamed files
    pub renamed: Vec<(String, String)>,
    /// Untracked files
    pub untracked: Vec<String>,
    /// Conflicted files
    pub conflicted: Vec<String>,
}

/// Git configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitConfig {
    /// User name
    pub user_name: Option<String>,
    /// User email
    pub user_email: Option<String>,
    /// Default editor
    pub editor: Option<String>,
    /// Auto fetch enabled
    pub auto_fetch: bool,
    /// Push default
    pub push_default: String,
    /// Custom configuration
    pub custom: HashMap<String, String>,
}

/// Git integration storage
pub struct GitIntegration {
    config: StorageConfig,
    connection: Connection,
}

impl GitIntegration {
    /// Create a new git integration
    pub async fn new(config: StorageConfig) -> Result<Self> {
        let db_path = config.storage_dir.join("git.db");
        let connection = Connection::open(&db_path).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to open git database: {}", e))
        })?;

        let mut integration = Self { config, connection };
        integration.initialize().await?;
        Ok(integration)
    }

    /// Scan a directory for git repositories
    pub async fn scan_repository(&mut self, path: &Path) -> Result<Option<GitRepository>> {
        if !self.is_git_repository(path).await? {
            return Ok(None);
        }

        let repo = self.analyze_repository(path).await?;
        self.store(repo.id, repo.clone()).await?;
        Ok(Some(repo))
    }

    /// Check if a directory is a git repository
    pub async fn is_git_repository(&self, path: &Path) -> Result<bool> {
        let git_dir = path.join(".git");
        Ok(git_dir.exists() && (git_dir.is_dir() || git_dir.is_file()))
    }

    /// Analyze a git repository and extract information
    async fn analyze_repository(&self, path: &Path) -> Result<GitRepository> {
        let mut repo = GitRepository {
            id: Uuid::new_v4(),
            root_path: path.to_path_buf(),
            remote_url: None,
            current_branch: String::new(),
            default_branch: "main".to_string(),
            state: RepoState::Clean,
            last_scanned: SystemTime::now(),
            branches: Vec::new(),
            recent_commits: Vec::new(),
            status: WorkingDirStatus::default(),
            config: GitConfig::default(),
            metadata: HashMap::new(),
        };

        // Get current branch
        if let Ok(branch) = self.get_current_branch(path).await {
            repo.current_branch = branch;
        }

        // Get remote URL
        if let Ok(url) = self.get_remote_url(path).await {
            repo.remote_url = Some(url);
        }

        // Get branches
        repo.branches = self.get_branches(path).await?;

        // Get recent commits
        repo.recent_commits = self.get_recent_commits(path, 20).await?;

        // Get working directory status
        repo.status = self.get_working_dir_status(path).await?;

        // Determine repository state
        repo.state = self.determine_repo_state(&repo.status, path).await?;

        // Get git configuration
        repo.config = self.get_git_config(path).await?;

        // Extract metadata
        repo.metadata = self.extract_repo_metadata(path).await?;

        Ok(repo)
    }

    /// Get current branch name
    async fn get_current_branch(&self, path: &Path) -> Result<String> {
        let output = Command::new("git")
            .current_dir(path)
            .args(["branch", "--show-current"])
            .output()
            .map_err(|e| AutorunError::GitError(format!("Failed to get current branch: {}", e)))?;

        if output.status.success() {
            let branch = String::from_utf8_lossy(&output.stdout).trim().to_string();
            Ok(branch)
        } else {
            // Fallback to HEAD parsing
            self.get_head_branch(path).await
        }
    }

    /// Get HEAD branch from .git/HEAD file
    async fn get_head_branch(&self, path: &Path) -> Result<String> {
        let head_file = path.join(".git").join("HEAD");
        if head_file.exists() {
            let content = tokio::fs::read_to_string(&head_file)
                .await
                .map_err(|e| AutorunError::IoError(format!("Failed to read HEAD file: {}", e)))?;

            if content.starts_with("ref: refs/heads/") {
                let branch = content.trim_start_matches("ref: refs/heads/").trim();
                Ok(branch.to_string())
            } else {
                Ok("detached".to_string())
            }
        } else {
            Err(AutorunError::GitError("HEAD file not found".to_string()))
        }
    }

    /// Get remote URL
    async fn get_remote_url(&self, path: &Path) -> Result<String> {
        let output = Command::new("git")
            .current_dir(path)
            .args(["remote", "get-url", "origin"])
            .output()
            .map_err(|e| AutorunError::GitError(format!("Failed to get remote URL: {}", e)))?;

        if output.status.success() {
            let url = String::from_utf8_lossy(&output.stdout).trim().to_string();
            Ok(url)
        } else {
            Err(AutorunError::GitError("No remote origin found".to_string()))
        }
    }

    /// Get all branches
    async fn get_branches(&self, path: &Path) -> Result<Vec<BranchInfo>> {
        let output = Command::new("git")
            .current_dir(path)
            .args(["branch", "-a", "--format=%(refname:short)|%(objectname:short)|%(subject)|%(authorname)|%(authordate:unix)"])
            .output()
            .map_err(|e| AutorunError::GitError(format!("Failed to get branches: {}", e)))?;

        if !output.status.success() {
            return Ok(Vec::new());
        }

        let branches_text = String::from_utf8_lossy(&output.stdout);
        let mut branches = Vec::new();

        for line in branches_text.lines() {
            if line.trim().is_empty() {
                continue;
            }

            let parts: Vec<&str> = line.split('|').collect();
            if parts.len() >= 5 {
                let name = parts[0].trim().to_string();
                let branch_type = if name.starts_with("remotes/") {
                    BranchType::Remote
                } else {
                    BranchType::Local
                };

                let timestamp = parts[4].parse::<u64>().unwrap_or(0);
                let date = SystemTime::UNIX_EPOCH + std::time::Duration::from_secs(timestamp);

                branches.push(BranchInfo {
                    name,
                    branch_type,
                    last_commit: parts[1].to_string(),
                    last_commit_message: parts[2].to_string(),
                    last_commit_author: parts[3].to_string(),
                    last_commit_date: date,
                    tracking: None,
                    ahead_behind: None,
                });
            }
        }

        Ok(branches)
    }

    /// Get recent commits
    async fn get_recent_commits(&self, path: &Path, limit: usize) -> Result<Vec<CommitInfo>> {
        let output = Command::new("git")
            .current_dir(path)
            .args([
                "log",
                &format!("-{}", limit),
                "--pretty=format:%H|%h|%s|%an|%ae|%at",
                "--numstat",
            ])
            .output()
            .map_err(|e| AutorunError::GitError(format!("Failed to get commits: {}", e)))?;

        if !output.status.success() {
            return Ok(Vec::new());
        }

        let log_text = String::from_utf8_lossy(&output.stdout);
        let mut commits = Vec::new();
        let mut current_commit: Option<CommitInfo> = None;

        for line in log_text.lines() {
            if line.contains('|') && line.chars().nth(0).map_or(false, |c| c.is_ascii_hexdigit()) {
                // This is a commit header line
                if let Some(commit) = current_commit.take() {
                    commits.push(commit);
                }

                let parts: Vec<&str> = line.split('|').collect();
                if parts.len() >= 6 {
                    let timestamp = parts[5].parse::<u64>().unwrap_or(0);
                    let date = SystemTime::UNIX_EPOCH + std::time::Duration::from_secs(timestamp);

                    current_commit = Some(CommitInfo {
                        hash: parts[0].to_string(),
                        short_hash: parts[1].to_string(),
                        message: parts[2].to_string(),
                        author: parts[3].to_string(),
                        author_email: parts[4].to_string(),
                        date,
                        changed_files: Vec::new(),
                        insertions: 0,
                        deletions: 0,
                    });
                }
            } else if let Some(ref mut commit) = current_commit {
                // This is a file change line
                let parts: Vec<&str> = line.split('\t').collect();
                if parts.len() >= 3 {
                    if let Ok(insertions) = parts[0].parse::<u32>() {
                        commit.insertions += insertions;
                    }
                    if let Ok(deletions) = parts[1].parse::<u32>() {
                        commit.deletions += deletions;
                    }
                    commit.changed_files.push(parts[2].to_string());
                }
            }
        }

        if let Some(commit) = current_commit {
            commits.push(commit);
        }

        Ok(commits)
    }

    /// Get working directory status
    async fn get_working_dir_status(&self, path: &Path) -> Result<WorkingDirStatus> {
        let output = Command::new("git")
            .current_dir(path)
            .args(["status", "--porcelain"])
            .output()
            .map_err(|e| AutorunError::GitError(format!("Failed to get status: {}", e)))?;

        let mut status = WorkingDirStatus::default();

        if output.status.success() {
            let status_text = String::from_utf8_lossy(&output.stdout);

            for line in status_text.lines() {
                if line.len() < 3 {
                    continue;
                }

                let index_status = line.chars().nth(0).unwrap();
                let work_tree_status = line.chars().nth(1).unwrap();
                let file_path = line[3..].to_string();

                match (index_status, work_tree_status) {
                    ('A', _) => status.added.push(file_path),
                    ('M', _) | (_, 'M') => status.modified.push(file_path),
                    ('D', _) | (_, 'D') => status.deleted.push(file_path),
                    ('R', _) => {
                        // Handle renames
                        if let Some(arrow_pos) = file_path.find(" -> ") {
                            let old_name = file_path[..arrow_pos].to_string();
                            let new_name = file_path[arrow_pos + 4..].to_string();
                            status.renamed.push((old_name, new_name));
                        }
                    }
                    ('?', '?') => status.untracked.push(file_path),
                    ('U', _) | (_, 'U') => status.conflicted.push(file_path),
                    _ => {}
                }
            }
        }

        Ok(status)
    }

    /// Determine repository state
    async fn determine_repo_state(
        &self,
        status: &WorkingDirStatus,
        path: &Path,
    ) -> Result<RepoState> {
        // Check for merge/rebase state
        let git_dir = path.join(".git");

        if git_dir.join("MERGE_HEAD").exists() {
            return Ok(RepoState::Merging);
        }

        if git_dir.join("rebase-apply").exists() || git_dir.join("rebase-merge").exists() {
            return Ok(RepoState::Rebasing);
        }

        if !status.conflicted.is_empty() {
            return Ok(RepoState::Conflicted);
        }

        if !status.added.is_empty() || !status.deleted.is_empty() || !status.renamed.is_empty() {
            return Ok(RepoState::Staged);
        }

        if !status.modified.is_empty() || !status.untracked.is_empty() {
            return Ok(RepoState::Modified);
        }

        Ok(RepoState::Clean)
    }

    /// Get git configuration
    async fn get_git_config(&self, path: &Path) -> Result<GitConfig> {
        let mut config = GitConfig::default();

        // Get user name
        if let Ok(output) = Command::new("git")
            .current_dir(path)
            .args(["config", "user.name"])
            .output()
        {
            if output.status.success() {
                config.user_name = Some(String::from_utf8_lossy(&output.stdout).trim().to_string());
            }
        }

        // Get user email
        if let Ok(output) = Command::new("git")
            .current_dir(path)
            .args(["config", "user.email"])
            .output()
        {
            if output.status.success() {
                config.user_email =
                    Some(String::from_utf8_lossy(&output.stdout).trim().to_string());
            }
        }

        // Get editor
        if let Ok(output) = Command::new("git")
            .current_dir(path)
            .args(["config", "core.editor"])
            .output()
        {
            if output.status.success() {
                config.editor = Some(String::from_utf8_lossy(&output.stdout).trim().to_string());
            }
        }

        Ok(config)
    }

    /// Extract repository metadata
    async fn extract_repo_metadata(&self, path: &Path) -> Result<HashMap<String, String>> {
        let mut metadata = HashMap::new();

        // Repository size
        if let Ok(output) = Command::new("git")
            .current_dir(path)
            .args(["count-objects", "-v"])
            .output()
        {
            if output.status.success() {
                let count_text = String::from_utf8_lossy(&output.stdout);
                for line in count_text.lines() {
                    if let Some((key, value)) = line.split_once(' ') {
                        metadata.insert(format!("count_{}", key), value.to_string());
                    }
                }
            }
        }

        // Last commit date
        if let Ok(output) = Command::new("git")
            .current_dir(path)
            .args(["log", "-1", "--format=%at"])
            .output()
        {
            if output.status.success() {
                let timestamp = String::from_utf8_lossy(&output.stdout).trim();
                metadata.insert("last_commit_timestamp".to_string(), timestamp.to_string());
            }
        }

        // Repository age
        if let Ok(output) = Command::new("git")
            .current_dir(path)
            .args(["log", "--reverse", "--format=%at", "-1"])
            .output()
        {
            if output.status.success() {
                let timestamp = String::from_utf8_lossy(&output.stdout).trim();
                metadata.insert("first_commit_timestamp".to_string(), timestamp.to_string());
            }
        }

        Ok(metadata)
    }

    /// Get repositories by status
    pub async fn get_repositories_by_state(&self, state: RepoState) -> Result<Vec<GitRepository>> {
        let state_str = serde_json::to_string(&state).map_err(|e| {
            AutorunError::SerializationError(format!("Failed to serialize state: {}", e))
        })?;

        let sql = "
            SELECT data FROM git_repositories 
            WHERE json_extract(data, '$.state') = ?
            ORDER BY json_extract(data, '$.last_scanned') DESC
        ";

        let mut stmt = self.connection.prepare(sql).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to prepare state query: {}", e))
        })?;

        let rows = stmt
            .query_map(params![state_str], |row| {
                let data: String = row.get(0)?;
                Ok(data)
            })
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to execute state query: {}", e))
            })?;

        let mut repositories = Vec::new();
        for row_result in rows {
            let data = row_result.map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to read state row: {}", e))
            })?;

            let repo: GitRepository = serde_json::from_str(&data).map_err(|e| {
                AutorunError::SerializationError(format!("Failed to deserialize repository: {}", e))
            })?;

            repositories.push(repo);
        }

        Ok(repositories)
    }

    /// Search repositories
    pub async fn search_repositories(&self, query: &str) -> Result<Vec<GitRepository>> {
        let search_pattern = format!("%{}%", query);
        let sql = "
            SELECT data FROM git_repositories 
            WHERE (
                json_extract(data, '$.root_path') LIKE ? OR
                json_extract(data, '$.remote_url') LIKE ? OR
                json_extract(data, '$.current_branch') LIKE ?
            )
            ORDER BY json_extract(data, '$.last_scanned') DESC
        ";

        let mut stmt = self.connection.prepare(sql).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to prepare search query: {}", e))
        })?;

        let rows = stmt
            .query_map(
                params![search_pattern, search_pattern, search_pattern],
                |row| {
                    let data: String = row.get(0)?;
                    Ok(data)
                },
            )
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to execute search query: {}", e))
            })?;

        let mut repositories = Vec::new();
        for row_result in rows {
            let data = row_result.map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to read search row: {}", e))
            })?;

            let repo: GitRepository = serde_json::from_str(&data).map_err(|e| {
                AutorunError::SerializationError(format!(
                    "Failed to deserialize searched repository: {}",
                    e
                ))
            })?;

            repositories.push(repo);
        }

        Ok(repositories)
    }
}

impl Default for WorkingDirStatus {
    fn default() -> Self {
        Self {
            modified: Vec::new(),
            added: Vec::new(),
            deleted: Vec::new(),
            renamed: Vec::new(),
            untracked: Vec::new(),
            conflicted: Vec::new(),
        }
    }
}

impl Default for GitConfig {
    fn default() -> Self {
        Self {
            user_name: None,
            user_email: None,
            editor: None,
            auto_fetch: false,
            push_default: "simple".to_string(),
            custom: HashMap::new(),
        }
    }
}

impl StorageBackend for GitIntegration {
    type Item = GitRepository;
    type Key = Uuid;

    async fn initialize(&mut self) -> Result<()> {
        let create_table_sql = "
            CREATE TABLE IF NOT EXISTS git_repositories (
                id TEXT PRIMARY KEY,
                data TEXT NOT NULL,
                root_path TEXT NOT NULL,
                last_scanned INTEGER NOT NULL
            )
        ";

        self.connection
            .execute(create_table_sql, params![])
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!(
                    "Failed to create git repositories table: {}",
                    e
                ))
            })?;

        // Create indexes for performance
        let indexes = [
            "CREATE INDEX IF NOT EXISTS idx_git_root_path ON git_repositories(root_path)",
            "CREATE INDEX IF NOT EXISTS idx_git_last_scanned ON git_repositories(last_scanned)",
            "CREATE INDEX IF NOT EXISTS idx_git_state ON git_repositories(json_extract(data, '$.state'))",
            "CREATE INDEX IF NOT EXISTS idx_git_branch ON git_repositories(json_extract(data, '$.current_branch'))",
        ];

        for index_sql in indexes {
            self.connection
                .execute(index_sql, params![])
                .await
                .map_err(|e| {
                    AutorunError::DatabaseError(format!("Failed to create git index: {}", e))
                })?;
        }

        Ok(())
    }

    async fn store(&mut self, key: Self::Key, item: Self::Item) -> Result<()> {
        let data = serde_json::to_string(&item).map_err(|e| {
            AutorunError::SerializationError(format!("Failed to serialize git repository: {}", e))
        })?;

        let last_scanned = item
            .last_scanned
            .duration_since(SystemTime::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs() as i64;

        let sql = "
            INSERT OR REPLACE INTO git_repositories (id, data, root_path, last_scanned)
            VALUES (?, ?, ?, ?)
        ";

        self.connection
            .execute(
                sql,
                params![
                    key.to_string(),
                    data,
                    item.root_path.to_string_lossy(),
                    last_scanned
                ],
            )
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to store git repository: {}", e))
            })?;

        Ok(())
    }

    async fn retrieve(&self, key: &Self::Key) -> Result<Option<Self::Item>> {
        let sql = "SELECT data FROM git_repositories WHERE id = ?";

        let mut stmt = self.connection.prepare(sql).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to prepare retrieve query: {}", e))
        })?;

        let result = stmt
            .query_row(params![key.to_string()], |row| {
                let data: String = row.get(0)?;
                Ok(data)
            })
            .await;

        match result {
            Ok(data) => {
                let repo: GitRepository = serde_json::from_str(&data).map_err(|e| {
                    AutorunError::SerializationError(format!(
                        "Failed to deserialize git repository: {}",
                        e
                    ))
                })?;
                Ok(Some(repo))
            }
            Err(tokio_rusqlite::Error::Rusqlite(rusqlite::Error::QueryReturnedNoRows)) => Ok(None),
            Err(e) => Err(AutorunError::DatabaseError(format!(
                "Failed to retrieve git repository: {}",
                e
            ))),
        }
    }

    async fn delete(&mut self, key: &Self::Key) -> Result<bool> {
        let sql = "DELETE FROM git_repositories WHERE id = ?";

        let changes = self
            .connection
            .execute(sql, params![key.to_string()])
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to delete git repository: {}", e))
            })?;

        Ok(changes > 0)
    }

    async fn list_keys(&self) -> Result<Vec<Self::Key>> {
        let sql = "SELECT id FROM git_repositories";

        let mut stmt = self.connection.prepare(sql).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to prepare list query: {}", e))
        })?;

        let rows = stmt
            .query_map(params![], |row| {
                let id_str: String = row.get(0)?;
                Ok(id_str)
            })
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to execute list query: {}", e))
            })?;

        let mut keys = Vec::new();
        for row_result in rows {
            let id_str = row_result.map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to read list row: {}", e))
            })?;

            let uuid = Uuid::parse_str(&id_str).map_err(|e| {
                AutorunError::SerializationError(format!("Invalid UUID in database: {}", e))
            })?;

            keys.push(uuid);
        }

        Ok(keys)
    }

    async fn get_stats(&self) -> Result<StorageStats> {
        let sql = "
            SELECT 
                COUNT(*) as count,
                SUM(LENGTH(data)) as total_size,
                MIN(last_scanned) as earliest,
                MAX(last_scanned) as latest
            FROM git_repositories
        ";

        let mut stmt = self.connection.prepare(sql).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to prepare stats query: {}", e))
        })?;

        let row = stmt
            .query_row(params![], |row| {
                Ok((
                    row.get::<_, i64>(0)? as usize,
                    row.get::<_, i64>(1)? as u64,
                    row.get::<_, i64>(2)?,
                    row.get::<_, i64>(3)?,
                ))
            })
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to get storage stats: {}", e))
            })?;

        let (count, size, earliest, latest) = row;

        Ok(StorageStats {
            item_count: count,
            total_size_bytes: size,
            created_at: SystemTime::UNIX_EPOCH + std::time::Duration::from_secs(earliest as u64),
            last_access: SystemTime::UNIX_EPOCH + std::time::Duration::from_secs(latest as u64),
            free_space_bytes: 1024 * 1024 * 1024, // Simplified - 1GB
        })
    }

    async fn cleanup(&mut self) -> Result<usize> {
        // Remove repositories that no longer exist on disk
        let sql = "SELECT id, json_extract(data, '$.root_path') as root_path FROM git_repositories";

        let mut stmt = self.connection.prepare(sql).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to prepare cleanup query: {}", e))
        })?;

        let rows = stmt
            .query_map(params![], |row| {
                Ok((row.get::<_, String>(0)?, row.get::<_, String>(1)?))
            })
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to execute cleanup query: {}", e))
            })?;

        let mut to_delete = Vec::new();
        for row_result in rows {
            let (id, root_path) = row_result.map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to read cleanup row: {}", e))
            })?;

            let path = PathBuf::from(root_path);
            if !path.exists() || !self.is_git_repository(&path).await.unwrap_or(false) {
                to_delete.push(id);
            }
        }

        let mut deleted_count = 0;
        for id in to_delete {
            let delete_sql = "DELETE FROM git_repositories WHERE id = ?";
            let changes = self
                .connection
                .execute(delete_sql, params![id])
                .await
                .map_err(|e| {
                    AutorunError::DatabaseError(format!("Failed to delete repository: {}", e))
                })?;

            if changes > 0 {
                deleted_count += 1;
            }
        }

        Ok(deleted_count)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    async fn create_test_integration() -> (GitIntegration, TempDir) {
        let temp_dir = TempDir::new().unwrap();
        let mut config = StorageConfig::default();
        config.storage_dir = temp_dir.path().to_path_buf();

        let integration = GitIntegration::new(config).await.unwrap();
        (integration, temp_dir)
    }

    #[tokio::test]
    async fn test_git_integration_initialization() {
        let (_integration, _temp_dir) = create_test_integration().await;
        // Test passes if no panic occurs
    }

    #[tokio::test]
    async fn test_is_git_repository() {
        let (integration, temp_dir) = create_test_integration().await;

        // Create a fake .git directory
        let git_dir = temp_dir.path().join(".git");
        tokio::fs::create_dir(&git_dir).await.unwrap();

        let is_git = integration
            .is_git_repository(temp_dir.path())
            .await
            .unwrap();
        assert!(is_git);

        // Test non-git directory
        let non_git_dir = temp_dir.path().join("not_git");
        tokio::fs::create_dir(&non_git_dir).await.unwrap();

        let is_not_git = integration.is_git_repository(&non_git_dir).await.unwrap();
        assert!(!is_not_git);
    }
}
