//! Memory storage system with full-text search capabilities
//!
//! Provides persistent storage for user memories, notes, and context information
//! with advanced search capabilities using SQLite FTS5.

use super::{StorageBackend, StorageConfig, StorageEntry, StorageStats};
use crate::errors::{<PERSON>runError, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::SystemTime;
use tokio_rusqlite::{params, Connection};
use uuid::Uuid;

/// Memory entry data structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Memory {
    /// Unique memory identifier
    pub id: Uuid,
    /// Memory title/summary
    pub title: String,
    /// Memory content
    pub content: String,
    /// Memory type
    pub memory_type: MemoryType,
    /// Creation timestamp
    pub created_at: SystemTime,
    /// Last modified timestamp
    pub modified_at: SystemTime,
    /// Last accessed timestamp
    pub accessed_at: SystemTime,
    /// Memory tags for organization
    pub tags: Vec<String>,
    /// Memory metadata
    pub metadata: HashMap<String, String>,
    /// Memory importance score (0-100)
    pub importance: u8,
    /// Memory status
    pub status: MemoryStatus,
    /// Associated project/context
    pub context: Option<String>,
    /// Parent memory (for hierarchical organization)
    pub parent_id: Option<Uuid>,
}

/// Types of memories
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MemoryType {
    /// General note or reminder
    Note,
    /// Code snippet or technical information
    Code,
    /// Project-specific information
    Project,
    /// User preference or setting
    Preference,
    /// Learning or insight
    Learning,
    /// Task or todo item
    Task,
    /// Reference or bookmark
    Reference,
    /// Template or boilerplate
    Template,
}

/// Memory status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MemoryStatus {
    Active,
    Archived,
    Deleted,
}

/// Search result with relevance scoring
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemorySearchResult {
    /// The memory entry
    pub memory: Memory,
    /// Search relevance score (0.0-1.0)
    pub relevance_score: f64,
    /// Matched text snippets with highlights
    pub snippets: Vec<SearchSnippet>,
}

/// Text snippet with highlighting information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchSnippet {
    /// The text snippet
    pub text: String,
    /// Character positions of highlighted terms
    pub highlights: Vec<(usize, usize)>,
    /// Context type (title, content, tags)
    pub context: SnippetContext,
}

/// Snippet context types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SnippetContext {
    Title,
    Content,
    Tags,
    Metadata,
}

/// Memory storage implementation with FTS
pub struct MemoryStore {
    config: StorageConfig,
    connection: Connection,
}

impl MemoryStore {
    /// Create a new memory store
    pub async fn new(config: StorageConfig) -> Result<Self> {
        let db_path = config.storage_dir.join("memory.db");
        let connection = Connection::open(&db_path).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to open memory database: {}", e))
        })?;

        let mut store = Self { config, connection };
        store.initialize().await?;
        Ok(store)
    }

    /// Create a new memory entry
    pub async fn create_memory(
        &mut self,
        title: String,
        content: String,
        memory_type: MemoryType,
        tags: Vec<String>,
        importance: u8,
    ) -> Result<Memory> {
        let memory = Memory {
            id: Uuid::new_v4(),
            title,
            content,
            memory_type,
            created_at: SystemTime::now(),
            modified_at: SystemTime::now(),
            accessed_at: SystemTime::now(),
            tags,
            metadata: HashMap::new(),
            importance: importance.min(100),
            status: MemoryStatus::Active,
            context: None,
            parent_id: None,
        };

        self.store(memory.id, memory.clone()).await?;
        Ok(memory)
    }

    /// Update an existing memory
    pub async fn update_memory(&mut self, memory_id: Uuid, updates: MemoryUpdate) -> Result<bool> {
        if let Some(mut memory) = self.retrieve(&memory_id).await? {
            if let Some(title) = updates.title {
                memory.title = title;
            }
            if let Some(content) = updates.content {
                memory.content = content;
            }
            if let Some(tags) = updates.tags {
                memory.tags = tags;
            }
            if let Some(importance) = updates.importance {
                memory.importance = importance.min(100);
            }
            if let Some(context) = updates.context {
                memory.context = Some(context);
            }

            memory.modified_at = SystemTime::now();
            memory.accessed_at = SystemTime::now();

            self.store(memory_id, memory).await?;
            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// Search memories using full-text search
    pub async fn search_memories(
        &self,
        query: &str,
        filters: SearchFilters,
        limit: usize,
    ) -> Result<Vec<MemorySearchResult>> {
        let mut sql_conditions = Vec::new();
        let mut params_vec = Vec::new();

        // Build FTS query
        let fts_query = if query.is_empty() {
            "*".to_string()
        } else {
            // Escape and format query for FTS
            format!("\"{}\"", query.replace("\"", "\"\""))
        };

        sql_conditions.push("memories_fts MATCH ?".to_string());
        params_vec.push(fts_query);

        // Add filters
        if let Some(memory_type) = &filters.memory_type {
            sql_conditions.push("json_extract(data, '$.memory_type') = ?".to_string());
            params_vec.push(serde_json::to_string(memory_type).unwrap_or_default());
        }

        if let Some(tags) = &filters.tags {
            for tag in tags {
                sql_conditions.push("json_extract(data, '$.tags') LIKE ?".to_string());
                params_vec.push(format!("%\"{}\"", tag));
            }
        }

        if let Some(min_importance) = filters.min_importance {
            sql_conditions.push("json_extract(data, '$.importance') >= ?".to_string());
            params_vec.push(min_importance.to_string());
        }

        if let Some(context) = &filters.context {
            sql_conditions.push("json_extract(data, '$.context') = ?".to_string());
            params_vec.push(context.clone());
        }

        // Add date range filter
        if let Some(after) = filters.created_after {
            let timestamp = after
                .duration_since(SystemTime::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs() as i64;
            sql_conditions
                .push("json_extract(data, '$.created_at.secs_since_epoch') > ?".to_string());
            params_vec.push(timestamp.to_string());
        }

        sql_conditions.push("json_extract(data, '$.status') = 'Active'".to_string());

        let conditions_str = if sql_conditions.is_empty() {
            "1=1".to_string()
        } else {
            sql_conditions.join(" AND ")
        };

        let sql = format!(
            "
            SELECT m.data, memories_fts.rank 
            FROM memories_fts 
            JOIN memories m ON memories_fts.rowid = m.rowid
            WHERE {}
            ORDER BY memories_fts.rank, json_extract(m.data, '$.importance') DESC
            LIMIT ?
        ",
            conditions_str
        );

        params_vec.push(limit.to_string());

        let mut stmt = self.connection.prepare(&sql).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to prepare search query: {}", e))
        })?;

        // Convert params to the format needed by rusqlite
        let params_refs: Vec<&dyn tokio_rusqlite::types::ToSql> = params_vec
            .iter()
            .map(|p| p as &dyn tokio_rusqlite::types::ToSql)
            .collect();

        let rows = stmt
            .query_map(&params_refs[..], |row| {
                let data: String = row.get(0)?;
                let rank: f64 = row.get(1)?;
                Ok((data, rank))
            })
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to execute search query: {}", e))
            })?;

        let mut results = Vec::new();
        for row_result in rows {
            let (data, rank) = row_result.map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to read search row: {}", e))
            })?;

            let memory: Memory = serde_json::from_str(&data).map_err(|e| {
                AutorunError::SerializationError(format!("Failed to deserialize memory: {}", e))
            })?;

            let snippets = self.extract_snippets(&memory, query).await?;

            results.push(MemorySearchResult {
                memory,
                relevance_score: rank,
                snippets,
            });
        }

        Ok(results)
    }

    /// Get memories by tag
    pub async fn get_memories_by_tag(&self, tag: &str, limit: usize) -> Result<Vec<Memory>> {
        let sql = "
            SELECT data FROM memories 
            WHERE json_extract(data, '$.tags') LIKE ?
            AND json_extract(data, '$.status') = 'Active'
            ORDER BY json_extract(data, '$.importance') DESC, json_extract(data, '$.modified_at') DESC
            LIMIT ?
        ";

        let tag_pattern = format!("%\"{}\"", tag);
        let mut stmt = self.connection.prepare(sql).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to prepare tag query: {}", e))
        })?;

        let rows = stmt
            .query_map(params![tag_pattern, limit], |row| {
                let data: String = row.get(0)?;
                Ok(data)
            })
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to execute tag query: {}", e))
            })?;

        let mut memories = Vec::new();
        for row_result in rows {
            let data = row_result.map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to read tag row: {}", e))
            })?;

            let memory: Memory = serde_json::from_str(&data).map_err(|e| {
                AutorunError::SerializationError(format!(
                    "Failed to deserialize tagged memory: {}",
                    e
                ))
            })?;

            memories.push(memory);
        }

        Ok(memories)
    }

    /// Get related memories based on content similarity
    pub async fn get_related_memories(&self, memory_id: Uuid, limit: usize) -> Result<Vec<Memory>> {
        if let Some(base_memory) = self.retrieve(&memory_id).await? {
            // Extract keywords from the base memory for similarity search
            let keywords = self.extract_keywords(&base_memory.content).await?;
            let search_query = keywords.join(" ");

            let filters = SearchFilters {
                memory_type: None,
                tags: None,
                min_importance: None,
                context: base_memory.context.clone(),
                created_after: None,
            };

            let results = self
                .search_memories(&search_query, filters, limit + 1)
                .await?;

            // Filter out the original memory and return only related ones
            let related: Vec<Memory> = results
                .into_iter()
                .filter(|result| result.memory.id != memory_id)
                .take(limit)
                .map(|result| result.memory)
                .collect();

            Ok(related)
        } else {
            Ok(Vec::new())
        }
    }

    /// Get all unique tags
    pub async fn get_all_tags(&self) -> Result<Vec<String>> {
        let sql = "
            SELECT DISTINCT json_each.value as tag
            FROM memories, json_each(json_extract(data, '$.tags'))
            WHERE json_extract(data, '$.status') = 'Active'
            ORDER BY tag
        ";

        let mut stmt = self.connection.prepare(sql).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to prepare tags query: {}", e))
        })?;

        let rows = stmt
            .query_map(params![], |row| {
                let tag: String = row.get(0)?;
                Ok(tag)
            })
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to execute tags query: {}", e))
            })?;

        let mut tags = Vec::new();
        for row_result in rows {
            let tag = row_result
                .map_err(|e| AutorunError::DatabaseError(format!("Failed to read tag: {}", e)))?;
            tags.push(tag);
        }

        Ok(tags)
    }

    /// Archive a memory
    pub async fn archive_memory(&mut self, memory_id: Uuid) -> Result<bool> {
        if let Some(mut memory) = self.retrieve(&memory_id).await? {
            memory.status = MemoryStatus::Archived;
            memory.modified_at = SystemTime::now();
            self.store(memory_id, memory).await?;
            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// Get memory statistics
    pub async fn get_memory_stats(&self) -> Result<MemoryStats> {
        let sql = "
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN json_extract(data, '$.status') = 'Active' THEN 1 END) as active,
                COUNT(CASE WHEN json_extract(data, '$.memory_type') = '\"Code\"' THEN 1 END) as code_memories,
                AVG(json_extract(data, '$.importance')) as avg_importance
            FROM memories
        ";

        let mut stmt = self.connection.prepare(sql).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to prepare memory stats query: {}", e))
        })?;

        let row = stmt
            .query_row(params![], |row| {
                Ok(MemoryStats {
                    total_memories: row.get::<_, i64>(0)? as usize,
                    active_memories: row.get::<_, i64>(1)? as usize,
                    code_memories: row.get::<_, i64>(2)? as usize,
                    average_importance: row.get::<_, Option<f64>>(3)?.unwrap_or(0.0),
                })
            })
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to get memory stats: {}", e))
            })?;

        Ok(row)
    }

    // Private helper methods

    /// Extract keywords from text for similarity search
    async fn extract_keywords(&self, text: &str) -> Result<Vec<String>> {
        // Simple keyword extraction - could be enhanced with NLP
        let words: Vec<String> = text
            .split_whitespace()
            .filter(|word| word.len() > 3)
            .filter(|word| !Self::is_stop_word(word))
            .map(|word| word.to_lowercase())
            .collect();

        Ok(words)
    }

    /// Check if word is a stop word
    fn is_stop_word(word: &str) -> bool {
        let stop_words = [
            "the", "and", "for", "are", "but", "not", "you", "all", "can", "had", "her", "was",
            "one", "our", "out", "day", "get", "has", "him", "his", "how", "man", "new", "now",
            "old", "see", "two", "way", "who", "boy", "did", "its", "let", "put", "say", "she",
            "too", "use",
        ];
        stop_words.contains(&word.to_lowercase().as_str())
    }

    /// Extract text snippets with highlighting
    async fn extract_snippets(&self, memory: &Memory, query: &str) -> Result<Vec<SearchSnippet>> {
        let mut snippets = Vec::new();

        if !query.is_empty() {
            // Search in title
            if let Some(snippet) =
                Self::extract_snippet_from_text(&memory.title, query, SnippetContext::Title)
            {
                snippets.push(snippet);
            }

            // Search in content
            if let Some(snippet) =
                Self::extract_snippet_from_text(&memory.content, query, SnippetContext::Content)
            {
                snippets.push(snippet);
            }

            // Search in tags
            let tags_text = memory.tags.join(" ");
            if let Some(snippet) =
                Self::extract_snippet_from_text(&tags_text, query, SnippetContext::Tags)
            {
                snippets.push(snippet);
            }
        }

        Ok(snippets)
    }

    /// Extract a single snippet from text
    fn extract_snippet_from_text(
        text: &str,
        query: &str,
        context: SnippetContext,
    ) -> Option<SearchSnippet> {
        let query_lower = query.to_lowercase();
        let text_lower = text.to_lowercase();

        if let Some(pos) = text_lower.find(&query_lower) {
            let start = pos.saturating_sub(50);
            let end = (pos + query.len() + 50).min(text.len());
            let snippet_text = text[start..end].to_string();

            let highlight_start = pos - start;
            let highlight_end = highlight_start + query.len();

            Some(SearchSnippet {
                text: snippet_text,
                highlights: vec![(highlight_start, highlight_end)],
                context,
            })
        } else {
            None
        }
    }
}

impl StorageBackend for MemoryStore {
    type Item = Memory;
    type Key = Uuid;

    async fn initialize(&mut self) -> Result<()> {
        // Create main table
        let create_table_sql = "
            CREATE TABLE IF NOT EXISTS memories (
                id TEXT PRIMARY KEY,
                data TEXT NOT NULL,
                created_at INTEGER NOT NULL,
                modified_at INTEGER NOT NULL
            )
        ";

        self.connection
            .execute(create_table_sql, params![])
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to create memories table: {}", e))
            })?;

        // Create FTS5 table for full-text search
        let create_fts_sql = "
            CREATE VIRTUAL TABLE IF NOT EXISTS memories_fts USING fts5(
                title,
                content,
                tags,
                content='memories',
                content_rowid='rowid'
            )
        ";

        self.connection
            .execute(create_fts_sql, params![])
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to create FTS table: {}", e))
            })?;

        // Create indexes for performance
        let indexes = [
            "CREATE INDEX IF NOT EXISTS idx_memories_created ON memories(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_memories_modified ON memories(modified_at)",
            "CREATE INDEX IF NOT EXISTS idx_memories_status ON memories(json_extract(data, '$.status'))",
            "CREATE INDEX IF NOT EXISTS idx_memories_importance ON memories(json_extract(data, '$.importance'))",
        ];

        for index_sql in indexes {
            self.connection
                .execute(index_sql, params![])
                .await
                .map_err(|e| {
                    AutorunError::DatabaseError(format!("Failed to create index: {}", e))
                })?;
        }

        // Create triggers to keep FTS table in sync
        let trigger_insert = "
            CREATE TRIGGER IF NOT EXISTS memories_ai AFTER INSERT ON memories BEGIN
                INSERT INTO memories_fts(rowid, title, content, tags) 
                VALUES (NEW.rowid, 
                        json_extract(NEW.data, '$.title'),
                        json_extract(NEW.data, '$.content'),
                        json_group_array(json_each.value))
                FROM json_each(json_extract(NEW.data, '$.tags'));
            END
        ";

        let trigger_update = "
            CREATE TRIGGER IF NOT EXISTS memories_au AFTER UPDATE ON memories BEGIN
                UPDATE memories_fts SET 
                    title = json_extract(NEW.data, '$.title'),
                    content = json_extract(NEW.data, '$.content'),
                    tags = (SELECT json_group_array(json_each.value) 
                           FROM json_each(json_extract(NEW.data, '$.tags')))
                WHERE rowid = NEW.rowid;
            END
        ";

        let trigger_delete = "
            CREATE TRIGGER IF NOT EXISTS memories_ad AFTER DELETE ON memories BEGIN
                DELETE FROM memories_fts WHERE rowid = OLD.rowid;
            END
        ";

        for trigger_sql in [trigger_insert, trigger_update, trigger_delete] {
            self.connection
                .execute(trigger_sql, params![])
                .await
                .map_err(|e| {
                    AutorunError::DatabaseError(format!("Failed to create trigger: {}", e))
                })?;
        }

        Ok(())
    }

    async fn store(&mut self, key: Self::Key, item: Self::Item) -> Result<()> {
        let data = serde_json::to_string(&item).map_err(|e| {
            AutorunError::SerializationError(format!("Failed to serialize memory: {}", e))
        })?;

        let created_at = item
            .created_at
            .duration_since(SystemTime::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs() as i64;

        let modified_at = item
            .modified_at
            .duration_since(SystemTime::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs() as i64;

        let sql = "
            INSERT OR REPLACE INTO memories (id, data, created_at, modified_at)
            VALUES (?, ?, ?, ?)
        ";

        self.connection
            .execute(sql, params![key.to_string(), data, created_at, modified_at])
            .await
            .map_err(|e| AutorunError::DatabaseError(format!("Failed to store memory: {}", e)))?;

        Ok(())
    }

    async fn retrieve(&self, key: &Self::Key) -> Result<Option<Self::Item>> {
        let sql = "SELECT data FROM memories WHERE id = ?";

        let mut stmt = self.connection.prepare(sql).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to prepare retrieve query: {}", e))
        })?;

        let result = stmt
            .query_row(params![key.to_string()], |row| {
                let data: String = row.get(0)?;
                Ok(data)
            })
            .await;

        match result {
            Ok(data) => {
                let memory: Memory = serde_json::from_str(&data).map_err(|e| {
                    AutorunError::SerializationError(format!("Failed to deserialize memory: {}", e))
                })?;
                Ok(Some(memory))
            }
            Err(tokio_rusqlite::Error::Rusqlite(rusqlite::Error::QueryReturnedNoRows)) => Ok(None),
            Err(e) => Err(AutorunError::DatabaseError(format!(
                "Failed to retrieve memory: {}",
                e
            ))),
        }
    }

    async fn delete(&mut self, key: &Self::Key) -> Result<bool> {
        let sql = "DELETE FROM memories WHERE id = ?";

        let changes = self
            .connection
            .execute(sql, params![key.to_string()])
            .await
            .map_err(|e| AutorunError::DatabaseError(format!("Failed to delete memory: {}", e)))?;

        Ok(changes > 0)
    }

    async fn list_keys(&self) -> Result<Vec<Self::Key>> {
        let sql = "SELECT id FROM memories";

        let mut stmt = self.connection.prepare(sql).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to prepare list query: {}", e))
        })?;

        let rows = stmt
            .query_map(params![], |row| {
                let id_str: String = row.get(0)?;
                Ok(id_str)
            })
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to execute list query: {}", e))
            })?;

        let mut keys = Vec::new();
        for row_result in rows {
            let id_str = row_result.map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to read list row: {}", e))
            })?;

            let uuid = Uuid::parse_str(&id_str).map_err(|e| {
                AutorunError::SerializationError(format!("Invalid UUID in database: {}", e))
            })?;

            keys.push(uuid);
        }

        Ok(keys)
    }

    async fn get_stats(&self) -> Result<StorageStats> {
        let sql = "
            SELECT 
                COUNT(*) as count,
                SUM(LENGTH(data)) as total_size,
                MIN(created_at) as earliest,
                MAX(modified_at) as latest
            FROM memories
        ";

        let mut stmt = self.connection.prepare(sql).await.map_err(|e| {
            AutorunError::DatabaseError(format!("Failed to prepare stats query: {}", e))
        })?;

        let row = stmt
            .query_row(params![], |row| {
                Ok((
                    row.get::<_, i64>(0)? as usize,
                    row.get::<_, i64>(1)? as u64,
                    row.get::<_, i64>(2)?,
                    row.get::<_, i64>(3)?,
                ))
            })
            .await
            .map_err(|e| {
                AutorunError::DatabaseError(format!("Failed to get storage stats: {}", e))
            })?;

        let (count, size, earliest, latest) = row;

        Ok(StorageStats {
            item_count: count,
            total_size_bytes: size,
            created_at: SystemTime::UNIX_EPOCH + std::time::Duration::from_secs(earliest as u64),
            last_access: SystemTime::UNIX_EPOCH + std::time::Duration::from_secs(latest as u64),
            free_space_bytes: 1024 * 1024 * 1024, // Simplified - 1GB
        })
    }

    async fn cleanup(&mut self) -> Result<usize> {
        if let Some(cleanup_days) = self.config.auto_cleanup_days {
            let cutoff_time = SystemTime::now()
                .duration_since(SystemTime::UNIX_EPOCH)
                .unwrap()
                .as_secs() as i64
                - (cleanup_days as i64 * 24 * 60 * 60);

            let sql = "
                DELETE FROM memories 
                WHERE json_extract(data, '$.status') = 'Deleted' 
                AND modified_at < ?
            ";

            let changes = self
                .connection
                .execute(sql, params![cutoff_time])
                .await
                .map_err(|e| {
                    AutorunError::DatabaseError(format!("Failed to cleanup memories: {}", e))
                })?;

            Ok(changes)
        } else {
            Ok(0)
        }
    }
}

/// Search filters for memory queries
#[derive(Debug, Clone, Default)]
pub struct SearchFilters {
    pub memory_type: Option<MemoryType>,
    pub tags: Option<Vec<String>>,
    pub min_importance: Option<u8>,
    pub context: Option<String>,
    pub created_after: Option<SystemTime>,
}

/// Memory update parameters
#[derive(Debug, Clone, Default)]
pub struct MemoryUpdate {
    pub title: Option<String>,
    pub content: Option<String>,
    pub tags: Option<Vec<String>>,
    pub importance: Option<u8>,
    pub context: Option<String>,
}

/// Memory statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryStats {
    pub total_memories: usize,
    pub active_memories: usize,
    pub code_memories: usize,
    pub average_importance: f64,
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    async fn create_test_store() -> (MemoryStore, TempDir) {
        let temp_dir = TempDir::new().unwrap();
        let mut config = StorageConfig::default();
        config.storage_dir = temp_dir.path().to_path_buf();

        let store = MemoryStore::new(config).await.unwrap();
        (store, temp_dir)
    }

    #[tokio::test]
    async fn test_memory_creation_and_retrieval() {
        let (mut store, _temp_dir) = create_test_store().await;

        let memory = store
            .create_memory(
                "Test Memory".to_string(),
                "This is a test memory about Rust programming".to_string(),
                MemoryType::Code,
                vec!["rust".to_string(), "programming".to_string()],
                80,
            )
            .await
            .unwrap();

        let retrieved = store.retrieve(&memory.id).await.unwrap();
        assert!(retrieved.is_some());
        assert_eq!(retrieved.unwrap().title, "Test Memory");
    }

    #[tokio::test]
    async fn test_memory_search() {
        let (mut store, _temp_dir) = create_test_store().await;

        let _memory1 = store
            .create_memory(
                "Rust Programming".to_string(),
                "Learning about Rust ownership and borrowing".to_string(),
                MemoryType::Learning,
                vec!["rust".to_string()],
                90,
            )
            .await
            .unwrap();

        let _memory2 = store
            .create_memory(
                "Python Scripting".to_string(),
                "Creating automation scripts with Python".to_string(),
                MemoryType::Code,
                vec!["python".to_string()],
                70,
            )
            .await
            .unwrap();

        let results = store
            .search_memories("Rust", SearchFilters::default(), 10)
            .await
            .unwrap();
        assert_eq!(results.len(), 1);
        assert_eq!(results[0].memory.title, "Rust Programming");
    }

    #[tokio::test]
    async fn test_memory_tags() {
        let (mut store, _temp_dir) = create_test_store().await;

        let _memory = store
            .create_memory(
                "Tagged Memory".to_string(),
                "A memory with tags".to_string(),
                MemoryType::Note,
                vec!["important".to_string(), "work".to_string()],
                85,
            )
            .await
            .unwrap();

        let tagged_memories = store.get_memories_by_tag("important", 10).await.unwrap();
        assert_eq!(tagged_memories.len(), 1);
        assert_eq!(tagged_memories[0].title, "Tagged Memory");

        let all_tags = store.get_all_tags().await.unwrap();
        assert!(all_tags.contains(&"important".to_string()));
        assert!(all_tags.contains(&"work".to_string()));
    }
}
