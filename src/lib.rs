// Re-export public modules and types for library usage

pub mod agent;
pub mod app;
pub mod cli;
pub mod commands;
pub mod config;
pub mod context;
pub mod core;
pub mod errors;
pub mod llm;
pub mod mcp;
pub mod prompts;
pub mod session;
pub mod storage;
pub mod templates;
pub mod tools;
pub mod ui;
pub mod utils;

// Re-export commonly used types
pub use errors::{AutorunError, Result};

// Version information
pub const VERSION: &str = env!("CARGO_PKG_VERSION");
pub const NAME: &str = env!("CARGO_PKG_NAME");
