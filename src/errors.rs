use thiserror::Error;
use std::time::SystemTime;

/// Provider-specific errors for LLM and external service interactions
#[derive(<PERSON><PERSON><PERSON>, Debug, Clone)]
pub enum ProviderError {
    /// Authentication failure (invalid API key, expired token, etc.)
    #[error("Authentication failed: {0}")]
    Authentication(String),

    /// Rate limit exceeded with detailed information
    #[error("Rate limit exceeded - Requests remaining: {requests_remaining:?}, Tokens remaining: {tokens_remaining:?}, Reset time: {reset_time:?}")]
    RateLimitExceeded {
        requests_remaining: Option<u32>,
        tokens_remaining: Option<u32>,
        reset_time: Option<SystemTime>,
    },

    /// Invalid or unsupported model specified
    #[error("Invalid model '{model}' - model not supported or not available")]
    InvalidModel { model: String },

    /// Provider service is unavailable
    #[error("Provider '{provider}' is currently unavailable")]
    ProviderUnavailable { provider: String },

    /// Configuration error (missing settings, invalid config, etc.)
    #[error("Configuration error: {0}")]
    Configuration(String),

    /// Network connectivity issues
    #[error("Network error: {0}")]
    Network(String),

    /// Request timeout
    #[error("Request timed out after {timeout_ms}ms")]
    Timeout { timeout_ms: u64 },

    /// Cost/usage limit exceeded
    #[error("Cost limit exceeded - Estimated cost: ${estimated_cost:.4}, Limit: ${cost_limit:.4}")]
    CostLimitExceeded {
        estimated_cost: f64,
        cost_limit: f64,
    },

    /// Provider-specific errors that don't fit other categories
    #[error("Provider '{provider}' error: {message}")]
    ProviderSpecific { provider: String, message: String },
}

impl ProviderError {
    /// Create a new authentication error
    pub fn authentication<S: Into<String>>(message: S) -> Self {
        Self::Authentication(message.into())
    }

    /// Create a new rate limit error with optional details
    pub fn rate_limit_exceeded(
        requests_remaining: Option<u32>,
        tokens_remaining: Option<u32>,
        reset_time: Option<SystemTime>,
    ) -> Self {
        Self::RateLimitExceeded {
            requests_remaining,
            tokens_remaining,
            reset_time,
        }
    }

    /// Create a new invalid model error
    pub fn invalid_model<S: Into<String>>(model: S) -> Self {
        Self::InvalidModel {
            model: model.into(),
        }
    }

    /// Create a new provider unavailable error
    pub fn provider_unavailable<S: Into<String>>(provider: S) -> Self {
        Self::ProviderUnavailable {
            provider: provider.into(),
        }
    }

    /// Create a new configuration error
    pub fn configuration<S: Into<String>>(message: S) -> Self {
        Self::Configuration(message.into())
    }

    /// Create a new network error
    pub fn network<S: Into<String>>(message: S) -> Self {
        Self::Network(message.into())
    }

    /// Create a new timeout error
    pub fn timeout(timeout_ms: u64) -> Self {
        Self::Timeout { timeout_ms }
    }

    /// Create a new cost limit exceeded error
    pub fn cost_limit_exceeded(estimated_cost: f64, cost_limit: f64) -> Self {
        Self::CostLimitExceeded {
            estimated_cost,
            cost_limit,
        }
    }

    /// Create a new provider-specific error
    pub fn provider_specific<S1: Into<String>, S2: Into<String>>(
        provider: S1,
        message: S2,
    ) -> Self {
        Self::ProviderSpecific {
            provider: provider.into(),
            message: message.into(),
        }
    }

    /// Check if this error is retryable (e.g., rate limits, timeouts, network issues)
    pub fn is_retryable(&self) -> bool {
        matches!(
            self,
            Self::RateLimitExceeded { .. }
                | Self::Timeout { .. }
                | Self::Network(_)
                | Self::ProviderUnavailable { .. }
        )
    }

    /// Check if this error is related to authentication/authorization
    pub fn is_auth_error(&self) -> bool {
        matches!(self, Self::Authentication(_))
    }

    /// Check if this error is a configuration issue
    pub fn is_config_error(&self) -> bool {
        matches!(self, Self::Configuration(_) | Self::InvalidModel { .. })
    }

    /// Get the provider name if available
    pub fn provider_name(&self) -> Option<&str> {
        match self {
            Self::ProviderUnavailable { provider } => Some(provider),
            Self::ProviderSpecific { provider, .. } => Some(provider),
            _ => None,
        }
    }
}

#[derive(Error, Debug)]
pub enum AutorunError {
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),

    #[error("IO error: {0}")]
    IoError(String),

    #[error("Configuration error: {0}")]
    Config(String),

    #[error("Configuration error: {0}")]
    ConfigError(String),

    #[error("LLM API error: {0}")]
    LlmApi(String),

    #[error("Tool execution error: {0}")]
    ToolExecution(String),

    #[error("Tool error: {0}")]
    ToolError(String),

    #[error("MCP protocol error: {0}")]
    McpProtocol(String),

    #[error("MCP error: {0}")]
    McpError(String),

    #[error("Session error: {0}")]
    Session(String),

    #[error("UI error: {0}")]
    Ui(String),

    #[error("Permission denied: {0}")]
    PermissionDenied(String),

    #[error("JSON error: {0}")]
    Json(#[from] serde_json::Error),

    #[error("TOML error: {0}")]
    Toml(#[from] toml::de::Error),

    #[error("TOML serialization error: {0}")]
    TomlSer(#[from] toml::ser::Error),

    #[error("Serialization error: {0}")]
    SerializationError(String),

    #[error("Database error: {0}")]
    DatabaseError(String),

    #[error("Not found: {0}")]
    NotFound(String),

    #[error("HTTP request error: {0}")]
    Http(#[from] reqwest::Error),

    #[error("Invalid input: {0}")]
    InvalidInput(String),

    #[error("Timeout: {0}")]
    Timeout(String),

    #[error("Cancelled by user")]
    Cancelled,

    /// Provider-related errors (LLM APIs, external services)
    #[error("Provider error: {0}")]
    Provider(#[from] ProviderError),

    /// Rate limiting error
    #[error("Rate limit exceeded: {0}")]
    RateLimitExceeded(String),

    /// Provider health check failure
    #[error("Provider health check failed: {0}")]
    ProviderHealthCheck(String),

    /// API key resolution error
    #[error("API key resolution error: {0}")]
    ApiKeyResolution(String),

    /// Configuration validation error
    #[error("Configuration validation error: {0}")]
    ConfigValidation(String),

    #[error("Unknown error: {0}")]
    Unknown(String),

    #[error("Parse error: {0}")]
    ParseError(String),

    #[error("Validation error: {0}")]
    ValidationError(String),

    #[error("File watcher error: {0}")]
    Notify(#[from] notify::Error),
}

pub type Result<T> = std::result::Result<T, AutorunError>;

impl From<crate::tools::ToolError> for AutorunError {
    fn from(err: crate::tools::ToolError) -> Self {
        match err {
            crate::tools::ToolError::InvalidInput(msg) => AutorunError::InvalidInput(msg),
            crate::tools::ToolError::PermissionDenied(msg) => AutorunError::PermissionDenied(msg),
            crate::tools::ToolError::ExecutionFailed(msg) => AutorunError::ToolExecution(msg),
            crate::tools::ToolError::ToolNotFound(msg) => AutorunError::NotFound(msg),
            crate::tools::ToolError::ValidationFailed(msg) => AutorunError::InvalidInput(msg),
            crate::tools::ToolError::Other(e) => AutorunError::Unknown(e.to_string()),
        }
    }
}

impl<T> From<tokio::sync::mpsc::error::SendError<T>> for AutorunError {
    fn from(err: tokio::sync::mpsc::error::SendError<T>) -> Self {
        AutorunError::Ui(format!("Message send error: {}", err))
    }
}

impl AutorunError {
    /// Create a new rate limit exceeded error
    pub fn rate_limit_exceeded<S: Into<String>>(message: S) -> Self {
        Self::RateLimitExceeded(message.into())
    }

    /// Create a new provider health check error
    pub fn provider_health_check<S: Into<String>>(message: S) -> Self {
        Self::ProviderHealthCheck(message.into())
    }

    /// Create a new API key resolution error
    pub fn api_key_resolution<S: Into<String>>(message: S) -> Self {
        Self::ApiKeyResolution(message.into())
    }

    /// Create a new configuration validation error
    pub fn config_validation<S: Into<String>>(message: S) -> Self {
        Self::ConfigValidation(message.into())
    }

    /// Check if this error is retryable
    pub fn is_retryable(&self) -> bool {
        match self {
            Self::Provider(provider_err) => provider_err.is_retryable(),
            Self::RateLimitExceeded(_) => true,
            Self::Timeout(_) => true,
            Self::Http(_) => true, // Network errors are generally retryable
            _ => false,
        }
    }

    /// Check if this error is related to authentication/authorization
    pub fn is_auth_error(&self) -> bool {
        match self {
            Self::Provider(provider_err) => provider_err.is_auth_error(),
            Self::ApiKeyResolution(_) => true,
            Self::PermissionDenied(_) => true,
            _ => false,
        }
    }

    /// Check if this error is a configuration issue
    pub fn is_config_error(&self) -> bool {
        match self {
            Self::Provider(provider_err) => provider_err.is_config_error(),
            Self::Config(_) | Self::ConfigError(_) => true,
            Self::ConfigValidation(_) => true,
            _ => false,
        }
    }

    /// Check if this error is a provider-related issue
    pub fn is_provider_error(&self) -> bool {
        matches!(
            self,
            Self::Provider(_)
                | Self::RateLimitExceeded(_)
                | Self::ProviderHealthCheck(_)
                | Self::ApiKeyResolution(_)
                | Self::LlmApi(_)
        )
    }

    /// Get the underlying provider error if this is a provider error
    pub fn provider_error(&self) -> Option<&ProviderError> {
        match self {
            Self::Provider(provider_err) => Some(provider_err),
            _ => None,
        }
    }

    /// Convert HTTP errors to provider errors when appropriate
    pub fn from_http_error(err: reqwest::Error, provider: &str) -> Self {
        if err.is_timeout() {
            Self::Provider(ProviderError::timeout(30000)) // Default 30s timeout
        } else if err.is_connect() {
            Self::Provider(ProviderError::network(format!(
                "Connection failed: {}",
                err
            )))
        } else if let Some(status) = err.status() {
            match status.as_u16() {
                401 => Self::Provider(ProviderError::authentication(format!(
                    "Unauthorized: {}",
                    err
                ))),
                429 => Self::Provider(ProviderError::rate_limit_exceeded(None, None, None)),
                503 => Self::Provider(ProviderError::provider_unavailable(provider)),
                _ => Self::Provider(ProviderError::provider_specific(
                    provider,
                    format!("HTTP {}: {}", status, err),
                )),
            }
        } else {
            Self::Provider(ProviderError::network(err.to_string()))
        }
    }
}

/// Conversion from standard timeout errors
impl From<tokio::time::error::Elapsed> for ProviderError {
    fn from(_err: tokio::time::error::Elapsed) -> Self {
        Self::Timeout {
            timeout_ms: 30000, // Default timeout, actual timeout duration not available from Elapsed
        }
    }
}

/// Conversion from URL parsing errors to ProviderError  
impl From<url::ParseError> for ProviderError {
    fn from(err: url::ParseError) -> Self {
        Self::Configuration(format!("Invalid URL: {}", err))
    }
}
