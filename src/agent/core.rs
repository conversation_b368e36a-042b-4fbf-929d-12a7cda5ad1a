use crate::errors::Result;
use crate::llm::{LLMProvider, LLMProviderWithTools, Message};
use crate::tools::ExecutionContext;
// Re-export these types for backward compatibility
pub use crate::tools::executor::{
    ToolCallRequest, ToolExecutionContext, ToolExecutionResult, ToolExecutor,
};
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::sync::Arc;
use tokio::sync::mpsc;
use tracing::{debug, error, info, warn};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LlmResponsePart {
    Text(String),
    ToolUse {
        id: String,
        name: String,
        input: Value,
    },
    Thinking(String),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WidgetGenerationRequest {
    pub widget_type: String,
    pub context: String,
    pub config: Value,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct WidgetUpdateEvent {
    pub widget_id: String,
    pub event_type: String,
    pub data: Value,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompletionContext {
    pub input_type: String, // "slash" or "mention"
    pub partial_input: String,
    pub cursor_position: usize,
    pub project_context: Value,
}

#[derive(Debug, Clone)]
pub enum AgentUpdate {
    LlmText(String),
    LlmThinking(String),
    ToolCallRequested { tool_name: String, call_id: String },
    ToolExecutionStarted { call_id: String, tool_name: String },
    ToolResult(ToolExecutionResult),
    ToolPermissionDenied { call_id: String, tool_name: String },
    ConversationHistoryUpdated,
    Error(String),
    // Widget-related updates
    WidgetGeneration(WidgetGenerationRequest),
    WidgetUpdate(WidgetUpdateEvent),
    AutocompleteRequest(CompletionContext),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversationHistory {
    messages: Vec<ConversationMessage>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum ConversationMessage {
    User {
        content: String,
        timestamp: chrono::DateTime<chrono::Utc>,
    },
    Assistant {
        content: Vec<MessageBlock>,
        timestamp: chrono::DateTime<chrono::Utc>,
    },
    System {
        content: String,
        timestamp: chrono::DateTime<chrono::Utc>,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum MessageBlock {
    Text {
        text: String,
    },
    ToolUse {
        id: String,
        name: String,
        input: Value,
    },
    ToolResult {
        tool_use_id: String,
        content: Value,
    },
}

impl ConversationHistory {
    pub fn new() -> Self {
        Self {
            messages: Vec::new(),
        }
    }

    pub fn add_user_message(&mut self, content: String) {
        self.messages.push(ConversationMessage::User {
            content,
            timestamp: chrono::Utc::now(),
        });
    }

    pub fn add_system_message(&mut self, content: String) {
        self.messages.push(ConversationMessage::System {
            content,
            timestamp: chrono::Utc::now(),
        });
    }

    pub fn start_assistant_message(&mut self) {
        self.messages.push(ConversationMessage::Assistant {
            content: Vec::new(),
            timestamp: chrono::Utc::now(),
        });
    }

    pub fn add_assistant_text(&mut self, text: String) {
        if let Some(ConversationMessage::Assistant { content, .. }) = self.messages.last_mut() {
            if let Some(MessageBlock::Text {
                text: existing_text,
            }) = content.last_mut()
            {
                existing_text.push_str(&text);
            } else {
                content.push(MessageBlock::Text { text });
            }
        }
    }

    pub fn add_assistant_tool_call(&mut self, id: String, name: String, input: Value) {
        if let Some(ConversationMessage::Assistant { content, .. }) = self.messages.last_mut() {
            content.push(MessageBlock::ToolUse { id, name, input });
        }
    }

    pub fn add_tool_result(&mut self, tool_use_id: String, result: ToolExecutionResult) {
        if let Some(ConversationMessage::Assistant { content, .. }) = self.messages.last_mut() {
            let result_content = match result {
                ToolExecutionResult::Success { output } => output,
                ToolExecutionResult::Failure { error_message, .. } => {
                    serde_json::json!({"error": error_message})
                }
            };
            content.push(MessageBlock::ToolResult {
                tool_use_id,
                content: result_content,
            });
        }
    }

    pub fn to_llm_messages(&self) -> Vec<Message> {
        let mut messages = Vec::new();

        for msg in &self.messages {
            match msg {
                ConversationMessage::User { content, .. } => {
                    messages.push(Message {
                        role: "user".to_string(),
                        content: content.clone(),
                        tool_calls: None,
                        tool_call_id: None,
                    });
                }
                ConversationMessage::System { content, .. } => {
                    messages.push(Message {
                        role: "system".to_string(),
                        content: content.clone(),
                        tool_calls: None,
                        tool_call_id: None,
                    });
                }
                ConversationMessage::Assistant { content, .. } => {
                    let mut text_parts = Vec::new();
                    let mut tool_uses = Vec::new();

                    for block in content {
                        match block {
                            MessageBlock::Text { text } => text_parts.push(text.clone()),
                            MessageBlock::ToolUse { id, name, input } => {
                                tool_uses.push(serde_json::json!({
                                    "type": "tool_use",
                                    "id": id,
                                    "name": name,
                                    "input": input
                                }));
                            }
                            MessageBlock::ToolResult {
                                tool_use_id,
                                content,
                            } => {
                                tool_uses.push(serde_json::json!({
                                    "type": "tool_result",
                                    "tool_use_id": tool_use_id,
                                    "content": content
                                }));
                            }
                        }
                    }

                    let content_text = if !text_parts.is_empty() {
                        text_parts.join("")
                    } else {
                        serde_json::to_string(&tool_uses).unwrap_or_default()
                    };

                    messages.push(Message {
                        role: "assistant".to_string(),
                        content: content_text,
                        tool_calls: None,
                        tool_call_id: None,
                    });
                }
            }
        }

        messages
    }
}

pub struct AgentCore {
    llm_provider: Arc<dyn LLMProvider>,
    llm_provider_with_tools: Option<Arc<dyn LLMProviderWithTools>>,
    tool_executor: Arc<ToolExecutor>,
    conversation_history: ConversationHistory,
    ui_update_tx: Option<mpsc::Sender<AgentUpdate>>,
    execution_context: ExecutionContext,
    widget_integration:
        Option<Arc<tokio::sync::RwLock<crate::ui::llm_integration::LLMWidgetIntegration>>>,
}

impl AgentCore {
    pub fn new(
        llm_provider: Arc<dyn LLMProvider>,
        tool_executor: Arc<ToolExecutor>,
        execution_context: ExecutionContext,
    ) -> Self {
        // For now, we'll set this to None and handle tool calling differently
        // In the future, this could be enhanced to properly detect provider types
        let llm_provider_with_tools: Option<Arc<dyn LLMProviderWithTools>> = None;

        Self {
            llm_provider,
            llm_provider_with_tools,
            tool_executor,
            conversation_history: ConversationHistory::new(),
            ui_update_tx: None,
            execution_context,
            widget_integration: None,
        }
    }

    pub fn new_with_tool_support<T>(
        llm_provider: Arc<T>,
        tool_executor: Arc<ToolExecutor>,
        execution_context: ExecutionContext,
    ) -> Self
    where
        T: LLMProvider + LLMProviderWithTools + 'static,
    {
        let base_provider = llm_provider.clone() as Arc<dyn LLMProvider>;
        let tools_provider = llm_provider as Arc<dyn LLMProviderWithTools>;

        Self {
            llm_provider: base_provider,
            llm_provider_with_tools: Some(tools_provider),
            tool_executor,
            conversation_history: ConversationHistory::new(),
            ui_update_tx: None,
            execution_context,
            widget_integration: None,
        }
    }

    pub fn set_ui_update_channel(&mut self, tx: mpsc::Sender<AgentUpdate>) {
        self.ui_update_tx = Some(tx);
    }

    pub fn set_widget_integration(
        &mut self,
        integration: Arc<tokio::sync::RwLock<crate::ui::llm_integration::LLMWidgetIntegration>>,
    ) {
        self.widget_integration = Some(integration);
    }

    pub async fn add_user_message(&mut self, message: String) -> Result<()> {
        self.conversation_history.add_user_message(message);
        self.send_ui_update(AgentUpdate::ConversationHistoryUpdated)
            .await;
        Ok(())
    }

    pub async fn add_system_message(&mut self, message: String) -> Result<()> {
        self.conversation_history.add_system_message(message);
        self.send_ui_update(AgentUpdate::ConversationHistoryUpdated)
            .await;
        Ok(())
    }

    pub async fn agent_loop(&mut self) -> Result<()> {
        info!("Starting agent loop");
        debug!(
            "Working directory: {}",
            self.execution_context.working_directory.display()
        );
        debug!("Session ID: {}", self.execution_context.session_id);

        let messages = self.conversation_history.to_llm_messages();
        debug!("Conversation history has {} messages", messages.len());

        // Log the last user message for debugging
        if let Some(last_msg) = messages.last() {
            if last_msg.role == "user" {
                debug!(
                    "Last user message: {}",
                    &last_msg.content[..last_msg.content.len().min(200)]
                );
            }
        }

        // Try to use tool-enhanced provider first, fallback to basic provider
        info!("Checking for tool-enhanced provider...");
        info!(
            "llm_provider_with_tools is Some: {}",
            self.llm_provider_with_tools.is_some()
        );
        let response = if let Some(provider_with_tools) = &self.llm_provider_with_tools {
            info!("Using LLM provider with native tool support");
            // Get tool definitions from registry
            let tool_definitions = self.get_tool_definitions_for_llm().await?;

            // Add system context
            let context = self.assemble_context_for_tools().await?;
            debug!("System context assembled: {} chars", context.len());

            let mut final_messages = vec![Message {
                role: "system".to_string(),
                content: context,
                tool_calls: None,
                tool_call_id: None,
            }];
            final_messages.extend(messages);

            // Debug log the final constructed prompt
            info!("=== FINAL PROMPT DEBUG ===");
            for (i, msg) in final_messages.iter().enumerate() {
                info!(
                    "Message {}: role={}, content_length={}",
                    i,
                    msg.role,
                    msg.content.len()
                );
                if msg.role == "system" {
                    info!("System prompt content:\n{}", msg.content);
                } else {
                    info!(
                        "Message content preview: {}",
                        &msg.content[..msg.content.len().min(200)]
                    );
                }
            }
            info!("=== END PROMPT DEBUG ===");

            info!(
                "Calling LLM with {} messages and {} tools",
                final_messages.len(),
                tool_definitions.len()
            );
            debug!(
                "Available tools: {:?}",
                tool_definitions
                    .iter()
                    .filter_map(|t| t
                        .get("name")
                        .or(t.get("function").and_then(|f| f.get("name"))))
                    .collect::<Vec<_>>()
            );

            let tool_response = provider_with_tools
                .complete_with_tools(
                    final_messages,
                    tool_definitions,
                    Some(crate::llm::ToolChoice::Auto),
                )
                .await?;

            // Process any tool calls directly
            if !tool_response.tool_calls.is_empty() {
                info!(
                    "Tool response contains {} tool calls",
                    tool_response.tool_calls.len()
                );
                for tool_call in &tool_response.tool_calls {
                    info!(
                        "Tool call: id={}, name={}, input={:?}",
                        tool_call.id, tool_call.name, tool_call.input
                    );

                    // Add the tool call to conversation history
                    self.conversation_history.add_assistant_tool_call(
                        tool_call.id.clone(),
                        tool_call.name.clone(),
                        tool_call.input.clone(),
                    );

                    // Send UI update about tool call
                    self.send_ui_update(AgentUpdate::ToolCallRequested {
                        tool_name: tool_call.name.clone(),
                        call_id: tool_call.id.clone(),
                    })
                    .await;

                    // Execute the tool
                    let tool_call_request = ToolCallRequest {
                        call_id: tool_call.id.clone(),
                        tool_name: tool_call.name.clone(),
                        params: tool_call.input.clone(),
                    };

                    let mut execution_context = self.execution_context.clone();
                    let execution_result = self
                        .tool_executor
                        .execute_tool(tool_call_request, &mut execution_context)
                        .await
                        .unwrap_or_else(|e| {
                            error!("Tool '{}' execution failed: {}", tool_call.name, e);
                            ToolExecutionResult::simple_failure(e.to_string())
                        });

                    // Add tool result to history
                    self.conversation_history
                        .add_tool_result(tool_call.id.clone(), execution_result.clone());

                    // Send UI update about tool result
                    self.send_ui_update(AgentUpdate::ToolResult(execution_result))
                        .await;
                }

                // After executing tools, we need to get the final response
                // The model needs to see the tool results and generate a final response
                info!("Tool calls executed, continuing conversation for final response");

                // Return a special marker to indicate we should continue
                "__CONTINUE_AFTER_TOOLS__".to_string()
            } else {
                // No tool calls, just return the content
                tool_response.content
            }
        } else {
            info!("Using basic LLM provider (manual tool parsing)");
            // Fallback to basic provider without tool calling
            let context = self.assemble_context().await?;
            debug!("System context assembled: {} chars", context.len());

            let mut final_messages = vec![Message {
                role: "system".to_string(),
                content: context,
                tool_calls: None,
                tool_call_id: None,
            }];
            final_messages.extend(messages);

            // Debug log the final constructed prompt
            info!("=== FINAL PROMPT DEBUG ===");
            for (i, msg) in final_messages.iter().enumerate() {
                info!(
                    "Message {}: role={}, content_length={}",
                    i,
                    msg.role,
                    msg.content.len()
                );
                if msg.role == "system" {
                    info!("System prompt content:\n{}", msg.content);
                } else {
                    info!(
                        "Message content preview: {}",
                        &msg.content[..msg.content.len().min(200)]
                    );
                }
            }
            info!("=== END PROMPT DEBUG ===");

            info!(
                "Calling LLM with {} messages (no native tool support)",
                final_messages.len()
            );

            self.llm_provider.complete(final_messages).await?
        };

        debug!("LLM response received: {} chars", response.len());

        // Process the response
        self.process_llm_response(response).await?;

        Ok(())
    }

    async fn assemble_context(&self) -> Result<String> {
        let local_tools = self.tool_executor.get_local_tools_info();
        let mut tool_descriptions = Vec::new();

        for (name, (description, schema)) in local_tools {
            let description_text = format!("- {}: {} (schema: {})", name, description, schema);
            tool_descriptions.push(description_text);
        }

        let tool_definitions = tool_descriptions.join("\n");

        // Get current working directory for context
        let working_dir = &self.execution_context.working_directory;
        let working_dir_str = working_dir.to_string_lossy();

        let context = format!(
            r#"You are an AI coding assistant with access to various tools for file operations, code execution, and system interaction.

Current working directory: {}

You have access to the following tools:

{}

When you want to use a tool, respond with a JSON object in this format:
{{
  "type": "tool_use",
  "id": "unique_call_id",
  "name": "tool_name",
  "input": {{ "param1": "value1", "param2": "value2" }}
}}

You can mix regular text with tool calls. Tool calls will be executed and their results will be provided back to you.

IMPORTANT: 
- Always use absolute paths when working with files, or paths relative to the current working directory
- You have direct access to the file system and can read/write files
- Be helpful and proactive in completing tasks
- When asked about the project or folder name, look at the current working directory path"#,
            working_dir_str, tool_definitions
        );

        Ok(context)
    }

    async fn assemble_context_for_tools(&self) -> Result<String> {
        // Get current working directory for context
        let working_dir = &self.execution_context.working_directory;
        let working_dir_str = working_dir.to_string_lossy();

        // For native tool support, we don't need to describe tools in the system prompt
        // The tools are passed separately in the API request
        let context = format!(
            r#"You are an AI coding assistant with access to various tools for file operations, code execution, and system interaction.

Current working directory: {}

You have direct access to tools for file operations, code execution, and system interaction. Use them as needed to help users with their coding tasks.

IMPORTANT:
- Always use absolute paths when working with files, or paths relative to the current working directory
- Be mindful of security and permissions
- Provide clear explanations of what you're doing
- Handle errors gracefully and inform the user
- When asked about the project or folder name, look at the current working directory path"#,
            working_dir_str
        );

        Ok(context)
    }

    async fn get_tool_definitions_for_llm(&self) -> Result<Vec<serde_json::Value>> {
        // Get tool definitions from local tools
        let local_tools = self.tool_executor.get_local_tools_info();

        // Determine which format to use based on the provider
        if let Some(_provider_with_tools) = &self.llm_provider_with_tools {
            // Check if this is an OpenAI-compatible provider (OpenRouter)
            if self.is_openai_compatible_provider().await {
                // Convert to OpenAI format
                let openai_tools = local_tools
                    .iter()
                    .map(|(name, (description, schema))| {
                        serde_json::json!({
                            "type": "function",
                            "function": {
                                "name": name,
                                "description": description,
                                "parameters": schema
                            }
                        })
                    })
                    .collect();
                Ok(openai_tools)
            } else {
                // Convert to Anthropic format
                let anthropic_tools = local_tools
                    .iter()
                    .map(|(name, (description, schema))| {
                        serde_json::json!({
                            "name": name,
                            "description": description,
                            "input_schema": schema
                        })
                    })
                    .collect();
                Ok(anthropic_tools)
            }
        } else {
            Ok(Vec::new())
        }
    }

    async fn is_openai_compatible_provider(&self) -> bool {
        // Check if the base provider can be downcast to OpenAICompatibleClient
        self.llm_provider
            .as_any()
            .map(|any| any.is::<crate::llm::client::OpenAICompatibleClient>())
            .unwrap_or(false)
    }

    async fn process_llm_response(&mut self, response: String) -> Result<()> {
        info!("Processing LLM response");

        // Check for the continuation marker
        if response == "__CONTINUE_AFTER_TOOLS__" {
            info!("Continuation marker detected, will make another agent loop call");
            // Don't add this to history, just return to trigger another loop
            return Ok(());
        }

        self.conversation_history.start_assistant_message();

        // Parse the response for tool calls
        debug!("Parsing response for tool calls");
        let tool_calls = self.parse_tool_calls(&response).await?;

        if tool_calls.is_empty() {
            info!("No tool calls found in response - treating as text only");
            // Just text response
            self.conversation_history
                .add_assistant_text(response.clone());
            self.send_ui_update(AgentUpdate::LlmText(response)).await;
        } else {
            info!("Found {} tool calls in response", tool_calls.len());

            // Process tool calls
            for (idx, tool_call) in tool_calls.iter().enumerate() {
                info!(
                    "Processing tool call {}/{}: {} (id: {})",
                    idx + 1,
                    tool_calls.len(),
                    tool_call.tool_name,
                    tool_call.call_id
                );
                debug!("Tool parameters: {:?}", tool_call.params);

                self.send_ui_update(AgentUpdate::ToolCallRequested {
                    tool_name: tool_call.tool_name.clone(),
                    call_id: tool_call.call_id.clone(),
                })
                .await;

                // Check permissions (simplified for now)
                let permission_granted = true;
                debug!(
                    "Permission check for tool '{}': {}",
                    tool_call.tool_name, permission_granted
                );

                if permission_granted {
                    // Add tool use to conversation history
                    self.conversation_history.add_assistant_tool_call(
                        tool_call.call_id.clone(),
                        tool_call.tool_name.clone(),
                        tool_call.params.clone(),
                    );

                    self.send_ui_update(AgentUpdate::ToolExecutionStarted {
                        call_id: tool_call.call_id.clone(),
                        tool_name: tool_call.tool_name.clone(),
                    })
                    .await;

                    // Execute tool with agent's execution context
                    let mut execution_context = self.execution_context.clone();
                    debug!("Executing tool '{}' with context", tool_call.tool_name);

                    let start_time = std::time::Instant::now();
                    let execution_result = self
                        .tool_executor
                        .execute_tool(tool_call.clone(), &mut execution_context)
                        .await;
                    let execution_duration = start_time.elapsed();

                    debug!(
                        "Tool '{}' execution completed in {:?}",
                        tool_call.tool_name, execution_duration
                    );

                    match &execution_result {
                        Ok(result) => {
                            info!("Tool '{}' executed successfully", tool_call.tool_name);
                            debug!("Tool result: {:?}", result);

                            self.conversation_history
                                .add_tool_result(tool_call.call_id.clone(), result.clone());
                            self.send_ui_update(AgentUpdate::ToolResult(result.clone()))
                                .await;
                        }
                        Err(e) => {
                            error!("Tool '{}' execution failed: {}", tool_call.tool_name, e);

                            let error_result = ToolExecutionResult::simple_failure(e.to_string());
                            self.conversation_history
                                .add_tool_result(tool_call.call_id.clone(), error_result.clone());
                            self.send_ui_update(AgentUpdate::ToolResult(error_result))
                                .await;
                        }
                    }
                } else {
                    warn!("Tool '{}' permission denied", tool_call.tool_name);
                    self.send_ui_update(AgentUpdate::ToolPermissionDenied {
                        call_id: tool_call.call_id.clone(),
                        tool_name: tool_call.tool_name.clone(),
                    })
                    .await;
                }
            }
        }

        debug!("Updating conversation history");
        self.send_ui_update(AgentUpdate::ConversationHistoryUpdated)
            .await;
        Ok(())
    }

    async fn parse_tool_calls(&self, response: &str) -> Result<Vec<ToolCallRequest>> {
        let mut tool_calls = Vec::new();

        debug!(
            "Parsing response for tool calls (response length: {} chars)",
            response.len()
        );

        // Try to parse each line as a potential tool call JSON
        for (line_num, line) in response.lines().enumerate() {
            let trimmed = line.trim();
            if trimmed.is_empty() {
                continue;
            }

            // Look for JSON objects that might be tool calls
            if trimmed.starts_with('{') {
                debug!(
                    "Attempting to parse line {} as JSON: {}",
                    line_num + 1,
                    &trimmed[..trimmed.len().min(100)]
                );

                // Try to parse as JSON
                match serde_json::from_str::<Value>(trimmed) {
                    Ok(json_value) => {
                        if let Some(obj) = json_value.as_object() {
                            if obj.get("type").and_then(|v| v.as_str()) == Some("tool_use") {
                                if let (Some(id), Some(name), Some(input)) = (
                                    obj.get("id").and_then(|v| v.as_str()),
                                    obj.get("name").and_then(|v| v.as_str()),
                                    obj.get("input"),
                                ) {
                                    info!("Found tool call: {} (id: {})", name, id);
                                    tool_calls.push(ToolCallRequest {
                                        call_id: id.to_string(),
                                        tool_name: name.to_string(),
                                        params: input.clone(),
                                    });
                                } else {
                                    debug!("JSON object missing required fields for tool call");
                                }
                            }
                        }
                    }
                    Err(e) => {
                        debug!("Failed to parse line {} as JSON: {}", line_num + 1, e);
                    }
                }
            }
        }

        // Also try parsing the entire response as a single JSON object
        if tool_calls.is_empty() && response.trim().starts_with('{') {
            debug!("No line-by-line tool calls found, trying to parse entire response as JSON");
            match serde_json::from_str::<Value>(response) {
                Ok(json_value) => {
                    if let Some(obj) = json_value.as_object() {
                        if obj.get("type").and_then(|v| v.as_str()) == Some("tool_use") {
                            if let (Some(id), Some(name), Some(input)) = (
                                obj.get("id").and_then(|v| v.as_str()),
                                obj.get("name").and_then(|v| v.as_str()),
                                obj.get("input"),
                            ) {
                                info!("Found tool call in full response: {} (id: {})", name, id);
                                tool_calls.push(ToolCallRequest {
                                    call_id: id.to_string(),
                                    tool_name: name.to_string(),
                                    params: input.clone(),
                                });
                            }
                        }
                    }
                }
                Err(e) => {
                    debug!("Failed to parse entire response as JSON: {}", e);
                }
            }
        }

        debug!("Total tool calls found: {}", tool_calls.len());
        Ok(tool_calls)
    }

    async fn send_ui_update(&self, update: AgentUpdate) {
        if let Some(tx) = &self.ui_update_tx {
            if let Err(e) = tx.send(update).await {
                warn!("Failed to send UI update: {}", e);
            }
        }
    }

    pub fn get_conversation_history(&self) -> &ConversationHistory {
        &self.conversation_history
    }

    pub fn get_last_assistant_response(&self) -> Option<String> {
        // Iterate through messages in reverse to find the last assistant message
        for msg in self.conversation_history.messages.iter().rev() {
            if let ConversationMessage::Assistant { content, .. } = msg {
                // Collect all text content from the assistant message
                let mut response_parts = Vec::new();
                for block in content {
                    if let MessageBlock::Text { text } = block {
                        response_parts.push(text.clone());
                    }
                }
                if !response_parts.is_empty() {
                    return Some(response_parts.join(""));
                }
            }
        }
        None
    }

    pub fn needs_continuation(&self) -> bool {
        // Check if the last message has tool results that need a response
        if let Some(msg) = self.conversation_history.messages.last() {
            if let ConversationMessage::Assistant { content, .. } = msg {
                // If the last assistant message has tool results but no text, we need continuation
                let has_tool_results = content
                    .iter()
                    .any(|block| matches!(block, MessageBlock::ToolResult { .. }));
                let has_text = content
                    .iter()
                    .any(|block| matches!(block, MessageBlock::Text { .. }));
                return has_tool_results && !has_text;
            }
        }
        false
    }

    /// Handle widget generation request
    pub async fn handle_widget_generation(
        &mut self,
        request: WidgetGenerationRequest,
    ) -> Result<String> {
        info!(
            "Handling widget generation request: {}",
            request.widget_type
        );

        if let Some(integration) = &self.widget_integration {
            let widget_id = {
                let mut integration_guard = integration.write().await;
                integration_guard.generate_widget(request.clone()).await?
            };

            // Send UI update
            self.send_ui_update(AgentUpdate::WidgetGeneration(request))
                .await;

            Ok(widget_id)
        } else {
            warn!("Widget integration not available");
            Err(crate::errors::AutorunError::Config(
                "Widget integration not configured".to_string(),
            ))
        }
    }

    /// Handle widget update event
    pub async fn handle_widget_update(&self, event: WidgetUpdateEvent) -> Result<()> {
        info!(
            "Handling widget update: {} ({})",
            event.widget_id, event.event_type
        );

        if let Some(integration) = &self.widget_integration {
            let integration_guard = integration.read().await;
            integration_guard
                .handle_widget_update(event.clone())
                .await?;

            // Send UI update
            self.send_ui_update(AgentUpdate::WidgetUpdate(event)).await;

            Ok(())
        } else {
            warn!("Widget integration not available");
            Err(crate::errors::AutorunError::Config(
                "Widget integration not configured".to_string(),
            ))
        }
    }

    /// Generate widget based on LLM analysis of conversation context
    pub async fn generate_contextual_widget(&mut self, widget_type: &str) -> Result<String> {
        info!("Generating contextual widget: {}", widget_type);

        // Analyze conversation history to determine context
        let context = self.analyze_conversation_context().await?;

        let request = WidgetGenerationRequest {
            widget_type: widget_type.to_string(),
            context,
            config: serde_json::json!({}),
        };

        self.handle_widget_generation(request).await
    }

    /// Analyze conversation history to provide context for widget generation
    async fn analyze_conversation_context(&self) -> Result<String> {
        let messages = self.conversation_history.to_llm_messages();

        // Extract recent user messages and tool results for context
        let mut context_parts = Vec::new();

        for message in messages.iter().rev().take(10) {
            match message.role.as_str() {
                "user" => {
                    context_parts.push(format!("User request: {}", message.content));
                }
                "assistant" => {
                    // Only include non-tool assistant messages
                    if !message.content.contains("tool_use") {
                        context_parts.push(format!("Assistant response: {}", message.content));
                    }
                }
                _ => {}
            }
        }

        // Current working directory context
        let working_dir = self.execution_context.working_directory.to_string_lossy();
        context_parts.push(format!("Working directory: {}", working_dir));

        // Reverse to get chronological order
        context_parts.reverse();

        Ok(context_parts.join("\n"))
    }
}
