use crate::agent::core::{<PERSON><PERSON><PERSON>, ToolExecutor};
use crate::config::LLMConfig;
use crate::errors::Result;
use crate::llm::{LLMProvider, ProviderFactory};
use crate::tools::ExecutionContext;
use std::sync::Arc;
use tracing::{info, warn};

/// Centralized factory for creating AgentCore instances with unified tool support detection
pub struct AgentCoreFactory {
    provider_factory: Arc<ProviderFactory>,
}

impl AgentCoreFactory {
    /// Create a new AgentCoreFactory
    pub fn new() -> Self {
        Self {
            provider_factory: Arc::new(ProviderFactory::new()),
        }
    }

    /// Create a new AgentCoreFactory with a custom ProviderFactory
    pub fn with_provider_factory(provider_factory: Arc<ProviderFactory>) -> Self {
        Self { provider_factory }
    }

    /// Create an AgentCore instance with automatic tool support detection
    ///
    /// This method consolidates the logic from app.rs and app_core.rs by:
    /// 1. Using the ProviderFactory to create the appropriate client
    /// 2. Detecting if the provider supports native tool calling
    /// 3. Creating the appropriate AgentCore instance (with or without tool support)
    /// 4. Providing proper error handling and fallback strategies
    pub async fn create_agent_core(
        &self,
        config: &LLMConfig,
        tool_executor: Arc<ToolExecutor>,
        execution_context: ExecutionContext,
    ) -> Result<AgentCore> {
        info!("Creating AgentCore for provider: {}", config.provider);

        // Validate provider first
        if let Err(e) = self.provider_factory.validate_provider(&config.provider) {
            warn!("Provider validation failed: {}, using fallback", e);
            // Create a basic provider instance and fall back
            return self
                .create_fallback_agent_core(config, tool_executor, execution_context)
                .await;
        }

        // Create provider instance using the factory
        match self.provider_factory.create_provider(config).await {
            Ok(provider) => {
                // Check if this provider supports native tool calling
                Ok(self.create_agent_core_with_tool_detection(
                    provider,
                    tool_executor,
                    execution_context,
                ))
            }
            Err(e) => {
                warn!("Failed to create provider: {}, using fallback", e);
                self.create_fallback_agent_core(config, tool_executor, execution_context)
                    .await
            }
        }
    }

    /// Create AgentCore from an existing LLM provider with tool support detection
    ///
    /// This method handles the tool support detection logic that was previously
    /// duplicated in app.rs and app_core.rs
    pub fn create_agent_core_from_provider(
        &self,
        llm_provider: Arc<dyn LLMProvider>,
        tool_executor: Arc<ToolExecutor>,
        execution_context: ExecutionContext,
    ) -> AgentCore {
        self.create_agent_core_with_tool_detection(llm_provider, tool_executor, execution_context)
    }

    /// Internal method to create AgentCore with tool support detection
    fn create_agent_core_with_tool_detection(
        &self,
        llm_provider: Arc<dyn LLMProvider>,
        tool_executor: Arc<ToolExecutor>,
        execution_context: ExecutionContext,
    ) -> AgentCore {
        // Check if the provider supports native tool calling
        // We need to check if we can downcast to a provider with tool support
        if self.provider_supports_native_tools(&llm_provider) {
            info!("Provider supports native tool calling, creating AgentCore with tool support");

            // Try to create with tool support based on the specific provider type
            if let Some(anthropic_client) = llm_provider
                .as_any()
                .and_then(|any| any.downcast_ref::<crate::llm::client::AnthropicClient>())
            {
                info!("Using Anthropic client with native tool support");
                let client_arc = Arc::new(anthropic_client.clone());
                return AgentCore::new_with_tool_support(
                    client_arc,
                    tool_executor,
                    execution_context,
                );
            }

            if let Some(openai_client) = llm_provider
                .as_any()
                .and_then(|any| any.downcast_ref::<crate::llm::client::OpenAICompatibleClient>())
            {
                info!("Using OpenAI-compatible client with native tool support");
                let client_arc = Arc::new(openai_client.clone());
                return AgentCore::new_with_tool_support(
                    client_arc,
                    tool_executor,
                    execution_context,
                );
            }

            warn!("Provider claims tool support but type casting failed, falling back to basic AgentCore");
        } else {
            info!("Provider does not support native tool calling, using basic AgentCore");
        }

        // Fall back to basic AgentCore
        AgentCore::new(llm_provider, tool_executor, execution_context)
    }

    /// Check if a provider supports native tool calling
    fn provider_supports_native_tools(&self, provider: &Arc<dyn LLMProvider>) -> bool {
        use crate::llm::ToolSupport;

        match provider.supports_tools() {
            ToolSupport::Full => {
                // Check if we can actually cast to a tool-supporting type
                provider
                    .as_any()
                    .and_then(|any| any.downcast_ref::<crate::llm::client::AnthropicClient>())
                    .is_some()
                    || provider
                        .as_any()
                        .and_then(|any| {
                            any.downcast_ref::<crate::llm::client::OpenAICompatibleClient>()
                        })
                        .is_some()
            }
            ToolSupport::Partial => {
                // For partial support, we might want to use tool support anyway
                // but this can be configured based on requirements
                false
            }
            ToolSupport::None | ToolSupport::Unknown => false,
        }
    }

    /// Create a fallback AgentCore when provider creation fails
    async fn create_fallback_agent_core(
        &self,
        config: &LLMConfig,
        tool_executor: Arc<ToolExecutor>,
        execution_context: ExecutionContext,
    ) -> Result<AgentCore> {
        warn!("Creating fallback AgentCore due to provider creation failure");

        // Try to create a basic provider as fallback
        // This uses the same logic as the original app.rs implementation
        match config.provider.as_str() {
            "anthropic" => {
                info!("Creating fallback Anthropic client");
                let anthropic_config = crate::llm::client::AnthropicConfig {
                    api_key: config.api_key.clone().unwrap_or_default(),
                    base_url: config.effective_base_url(),
                    model: config.model.clone(),
                    max_tokens: config.max_tokens.unwrap_or(4096),
                    temperature: config.temperature.unwrap_or(0.7),
                    timeout: std::time::Duration::from_secs(120),
                };

                let client = crate::llm::client::AnthropicClient::new(anthropic_config)?;
                let provider = Arc::new(client);
                Ok(AgentCore::new(provider, tool_executor, execution_context))
            }
            "openrouter" => {
                info!("Creating fallback OpenRouter client");
                let openai_config = crate::llm::client::OpenAICompatibleConfig {
                    api_key: config.api_key.clone().unwrap_or_default(),
                    base_url: config.effective_base_url(),
                    model: config.model.clone(),
                    max_tokens: config.max_tokens.unwrap_or(4096),
                    temperature: config.temperature.unwrap_or(0.7),
                    timeout: std::time::Duration::from_secs(120),
                };

                let client = crate::llm::client::OpenAICompatibleClient::new(openai_config)?;
                let provider = Arc::new(client);
                Ok(AgentCore::new(provider, tool_executor, execution_context))
            }
            _ => {
                return Err(crate::errors::AutorunError::Config(format!(
                    "Unsupported provider for fallback: {}",
                    config.provider
                )));
            }
        }
    }

    /// Get the underlying ProviderFactory
    pub fn provider_factory(&self) -> &ProviderFactory {
        &self.provider_factory
    }
}

impl Default for AgentCoreFactory {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::LLMConfig;
    use crate::tools::ExecutionContext;
    use std::path::PathBuf;
    use uuid::Uuid;

    #[tokio::test]
    async fn test_agent_core_factory_creation() {
        let factory = AgentCoreFactory::new();

        // Test with a valid Anthropic config
        let config = LLMConfig {
            provider: "anthropic".to_string(),
            api_key: Some("test-key".to_string()),
            base_url: Some("https://api.anthropic.com".to_string()),
            model: "claude-3-5-sonnet-20241022".to_string(),
            temperature: Some(0.7),
            max_tokens: Some(4096),
        };

        let tool_registry = Arc::new(crate::tools::registry::ToolRegistry::new());
        let mcp_manager = Arc::new(tokio::sync::RwLock::new(
            crate::mcp::client::McpClientManager::new(),
        ));
        let tool_executor = Arc::new(ToolExecutor::new(tool_registry, mcp_manager));

        let execution_context =
            ExecutionContext::new(PathBuf::from("/tmp"), Uuid::new_v4().to_string());

        let result = factory
            .create_agent_core(&config, tool_executor, execution_context)
            .await;
        assert!(result.is_ok());
    }

    #[test]
    fn test_provider_supports_native_tools() {
        let factory = AgentCoreFactory::new();

        // This test would need actual provider instances to work properly
        // For now, we just test that the method exists and compiles
        // In a real test, we'd create mock providers and test the detection logic
    }
}
