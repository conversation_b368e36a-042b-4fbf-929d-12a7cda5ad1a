// LLM module - handles language model interactions

pub mod client;
pub mod factory;

pub use crate::config::LLMConfig;
use crate::errors::{AutorunError, Result};
use chrono;
use serde::{Deserialize, Serialize};
use std::sync::Arc;

pub use client::{
    AnthropicClient, AnthropicConfig, LLMProviderWithStreaming, LLMProviderWithTools,
    OpenAICompatibleClient, OpenAICompatibleConfig, StreamChunk, StreamingResponse,
    StreamingToolCall, ToolCallResponse,
};
pub use factory::{ProviderBuilder, ProviderFactory};

// Export the new capability types - defined below

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Message {
    pub role: String,
    pub content: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tool_calls: Option<Vec<ToolCall>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tool_call_id: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolCall {
    pub id: String,
    pub name: String,
    pub input: serde_json::Value,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ToolChoice {
    Auto,
    None,
    Required,
    Function { name: String },
}

#[derive(Debug, Clone, PartialEq)]
pub enum ToolSupport {
    Full,    // Supports function calling natively
    Partial, // Limited support or requires workarounds
    None,    // No tool support
    Unknown, // Support status not determined
}

#[derive(Debug, Clone, PartialEq)]
pub enum ModelCapability {
    Standard, // Normal model capabilities
    Thinking, // Has reasoning/thinking capabilities (like :thinking models)
    Beta,     // Beta version with experimental features
    Free,     // Free tier with potential limitations
    High,     // High performance variant
    Nano,     // Lightweight/small model variant
}

impl Message {
    pub fn system(content: &str) -> Self {
        Self {
            role: "system".to_string(),
            content: content.to_string(),
            tool_calls: None,
            tool_call_id: None,
        }
    }

    pub fn user(content: &str) -> Self {
        Self {
            role: "user".to_string(),
            content: content.to_string(),
            tool_calls: None,
            tool_call_id: None,
        }
    }

    pub fn assistant(content: &str) -> Self {
        Self {
            role: "assistant".to_string(),
            content: content.to_string(),
            tool_calls: None,
            tool_call_id: None,
        }
    }

    pub fn assistant_with_tool_calls(content: &str, tool_calls: Vec<ToolCall>) -> Self {
        Self {
            role: "assistant".to_string(),
            content: content.to_string(),
            tool_calls: Some(tool_calls),
            tool_call_id: None,
        }
    }

    pub fn tool_result(content: &str, tool_call_id: &str) -> Self {
        Self {
            role: "tool".to_string(),
            content: content.to_string(),
            tool_calls: None,
            tool_call_id: Some(tool_call_id.to_string()),
        }
    }
}

// ===============================
// Phase 1: Provider Information & Health Monitoring
// ===============================

/// Information about a provider's capabilities and metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProviderInfo {
    /// Provider name (e.g., "anthropic", "openrouter")
    pub name: String,
    /// Human-readable display name
    pub display_name: String,
    /// Provider version or API version
    pub version: String,
    /// List of supported capabilities
    pub capabilities: Vec<ProviderCapability>,
    /// Rate limiting information
    pub rate_limits: RateLimitInfo,
    /// Additional metadata about the provider
    pub metadata: std::collections::HashMap<String, String>,
}

/// Different capabilities a provider might support
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ProviderCapability {
    /// Supports text completion
    TextCompletion,
    /// Supports function/tool calling
    FunctionCalling,
    /// Supports streaming responses
    Streaming,
    /// Supports vision/image inputs
    Vision,
    /// Supports code generation and analysis
    CodeGeneration,
    /// Supports reasoning/thinking modes
    Reasoning,
    /// Supports embeddings generation
    Embeddings,
    /// Supports custom system prompts
    SystemPrompts,
    /// Supports conversation context management
    ConversationContext,
}

/// Rate limiting information for a provider
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitInfo {
    /// Requests per minute limit
    pub requests_per_minute: Option<u32>,
    /// Requests per hour limit
    pub requests_per_hour: Option<u32>,
    /// Requests per day limit
    pub requests_per_day: Option<u32>,
    /// Tokens per minute limit
    pub tokens_per_minute: Option<u32>,
    /// Maximum concurrent requests
    pub max_concurrent_requests: Option<u32>,
}

/// Health status of a provider
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ProviderHealthStatus {
    /// Overall status of the provider
    pub status: ApiStatus,
    /// Detailed status message
    pub message: String,
    /// Response time in milliseconds
    pub response_time_ms: Option<u64>,
    /// Last successful request timestamp
    pub last_success: Option<chrono::DateTime<chrono::Utc>>,
    /// Additional health details
    pub details: std::collections::HashMap<String, String>,
}

/// API status levels
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ApiStatus {
    /// Provider is fully operational
    Healthy,
    /// Provider has minor issues but is functional
    Degraded,
    /// Provider is experiencing significant issues
    Unhealthy,
    /// Provider status cannot be determined
    Unknown,
    /// Provider is not available
    Unavailable,
}

/// Current rate limit status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitStatus {
    /// Current requests made in the current window
    pub current_requests: u32,
    /// Maximum requests allowed in the current window
    pub max_requests: u32,
    /// Current tokens used in the current window
    pub current_tokens: u32,
    /// Maximum tokens allowed in the current window
    pub max_tokens: u32,
    /// Time until rate limits reset (in seconds)
    pub reset_time_seconds: u32,
    /// Whether the provider is currently rate limited
    pub is_rate_limited: bool,
}

/// Configuration validation result
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ConfigValidationResult {
    /// Whether the configuration is valid
    pub is_valid: bool,
    /// List of validation errors
    pub errors: Vec<String>,
    /// List of validation warnings
    pub warnings: Vec<String>,
    /// Suggestions for fixing configuration issues
    pub suggestions: Vec<String>,
}

/// Provider performance and usage metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProviderMetrics {
    /// Total number of requests made
    pub total_requests: u64,
    /// Number of successful requests
    pub successful_requests: u64,
    /// Number of failed requests
    pub failed_requests: u64,
    /// Average response time in milliseconds
    pub average_response_time_ms: f64,
    /// Total tokens consumed
    pub total_tokens_consumed: u64,
    /// Total cost incurred (if available)
    pub total_cost: Option<f64>,
    /// Metrics collection start time
    pub metrics_start_time: chrono::DateTime<chrono::Utc>,
    /// Last updated timestamp
    pub last_updated: chrono::DateTime<chrono::Utc>,
}

/// Cost estimation for a request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CostEstimate {
    /// Estimated cost for input tokens
    pub input_cost: CostInfo,
    /// Estimated cost for output tokens
    pub output_cost: CostInfo,
    /// Total estimated cost
    pub total_cost: f64,
    /// Currency used for cost calculation
    pub currency: String,
    /// Cost calculation timestamp
    pub calculated_at: chrono::DateTime<chrono::Utc>,
}

/// Cost information for a specific component
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CostInfo {
    /// Number of tokens
    pub tokens: u32,
    /// Cost per token
    pub cost_per_token: f64,
    /// Total cost for this component
    pub total_cost: f64,
}

#[async_trait::async_trait]
pub trait LLMProvider: Send + Sync {
    // ===============================
    // Existing Core Methods
    // ===============================

    async fn complete(&self, messages: Vec<Message>) -> Result<String>;

    /// Check if this provider/model supports tool calling
    fn supports_tools(&self) -> ToolSupport;

    /// Get the model name for tool support detection
    fn model_name(&self) -> &str;

    /// Get model capabilities (thinking, beta, etc.)
    fn model_capabilities(&self) -> Vec<ModelCapability> {
        if let Some(model_info) = crate::config::registry::get_model_info(self.model_name()) {
            model_info.capabilities.clone()
        } else {
            detect_model_capabilities(self.model_name())
        }
    }

    /// Check if model has thinking/reasoning capabilities
    fn has_thinking_capability(&self) -> bool {
        self.model_capabilities()
            .contains(&ModelCapability::Thinking)
    }

    fn as_any(&self) -> Option<&dyn std::any::Any> {
        None
    }

    // ===============================
    // Phase 1: New Enhanced Methods
    // ===============================

    /// Get comprehensive information about this provider
    ///
    /// Returns metadata about the provider including capabilities,
    /// rate limits, version information, and other relevant details.
    fn provider_info(&self) -> ProviderInfo {
        ProviderInfo {
            name: self
                .model_name()
                .split('/')
                .next()
                .unwrap_or("unknown")
                .to_string(),
            display_name: format!(
                "{} Provider",
                self.model_name().split('/').next().unwrap_or("Unknown")
            ),
            version: "1.0.0".to_string(),
            capabilities: self.default_capabilities(),
            rate_limits: RateLimitInfo {
                requests_per_minute: Some(60),
                requests_per_hour: Some(3600),
                requests_per_day: None,
                tokens_per_minute: Some(100000),
                max_concurrent_requests: Some(10),
            },
            metadata: std::collections::HashMap::new(),
        }
    }

    /// Perform a health check on the provider
    ///
    /// Tests connectivity and basic functionality of the provider.
    /// Default implementation returns Unknown status.
    async fn health_check(&self) -> Result<ProviderHealthStatus> {
        Ok(ProviderHealthStatus {
            status: ApiStatus::Unknown,
            message: "Health check not implemented for this provider".to_string(),
            response_time_ms: None,
            last_success: None,
            details: std::collections::HashMap::new(),
        })
    }

    /// Get current rate limit status
    ///
    /// Returns information about current usage against rate limits.
    /// Default implementation returns a permissive status.
    fn rate_limit_status(&self) -> RateLimitStatus {
        RateLimitStatus {
            current_requests: 0,
            max_requests: 1000,
            current_tokens: 0,
            max_tokens: 100000,
            reset_time_seconds: 3600,
            is_rate_limited: false,
        }
    }

    /// Validate the provider's configuration
    ///
    /// Checks if the current configuration is valid and provides
    /// suggestions for improvement if needed.
    fn validate_configuration(&self) -> Result<ConfigValidationResult> {
        Ok(ConfigValidationResult {
            is_valid: true,
            errors: Vec::new(),
            warnings: Vec::new(),
            suggestions: Vec::new(),
        })
    }

    /// Get performance and usage metrics
    ///
    /// Returns statistics about the provider's usage and performance.
    /// Default implementation returns empty metrics.
    fn get_metrics(&self) -> ProviderMetrics {
        let now = chrono::Utc::now();
        ProviderMetrics {
            total_requests: 0,
            successful_requests: 0,
            failed_requests: 0,
            average_response_time_ms: 0.0,
            total_tokens_consumed: 0,
            total_cost: None,
            metrics_start_time: now,
            last_updated: now,
        }
    }

    /// Check if the provider supports streaming responses
    ///
    /// Returns true if the provider can handle streaming completions.
    /// Default implementation returns false for backward compatibility.
    fn supports_streaming(&self) -> bool {
        false
    }

    /// Estimate the cost for a given request
    ///
    /// Provides cost estimation based on input and expected output tokens.
    /// Returns None if cost estimation is not available for this provider.
    fn estimate_cost(
        &self,
        input_tokens: u32,
        estimated_output_tokens: u32,
    ) -> Option<CostEstimate> {
        // Default implementation returns None (no cost estimation available)
        let _ = (input_tokens, estimated_output_tokens);
        None
    }

    // ===============================
    // Helper Methods (Default Implementations)
    // ===============================

    /// Get default capabilities based on provider type
    ///
    /// This is a helper method that can be overridden by specific providers
    /// to return their actual capabilities.
    fn default_capabilities(&self) -> Vec<ProviderCapability> {
        let mut capabilities = vec![
            ProviderCapability::TextCompletion,
            ProviderCapability::SystemPrompts,
            ProviderCapability::ConversationContext,
        ];

        // Add capabilities based on existing trait methods
        match self.supports_tools() {
            ToolSupport::Full => capabilities.push(ProviderCapability::FunctionCalling),
            ToolSupport::Partial => capabilities.push(ProviderCapability::FunctionCalling),
            _ => {}
        }

        if self.supports_streaming() {
            capabilities.push(ProviderCapability::Streaming);
        }

        if self.has_thinking_capability() {
            capabilities.push(ProviderCapability::Reasoning);
        }

        capabilities
    }
}

/// Create an LLM provider instance based on configuration using the provider factory
pub async fn create_llm_provider(config: &LLMConfig) -> Result<Arc<dyn LLMProvider>> {
    let factory = ProviderFactory::new();
    factory.create_provider(config).await
}

/// Validate that a provider is supported
pub fn validate_provider(provider: &str) -> Result<()> {
    let factory = ProviderFactory::new();
    factory.validate_provider(provider)
}

/// Comprehensive list of models with verified tool calling support
static TOOL_CAPABLE_MODELS: &[&str] = &[
    // Google Gemini models
    "google/gemini-2.0-flash-lite-001",
    "google/gemini-2.0-flash-001",
    "google/gemini-2.5-flash-preview",
    "google/gemini-2.5-flash-preview:thinking",
    "google/gemini-2.5-flash-preview-05-20",
    "google/gemini-2.5-flash-preview-05-20:thinking",
    "google/gemini-2.5-pro-preview",
    "google/gemini-pro-1.5",
    // Anthropic Claude models
    "anthropic/claude-3.5-haiku",
    "anthropic/claude-sonnet-4",
    "anthropic/claude-3.7-sonnet",
    "anthropic/claude-3.5-sonnet",
    "anthropic/claude-opus-4",
    "anthropic/claude-3.7-sonnet:thinking",
    "anthropic/claude-3.7-sonnet:beta",
    "anthropic/claude-3.5-sonnet:beta",
    // OpenAI models
    "openai/gpt-4o",
    "openai/gpt-4o-mini",
    "openai/gpt-4o-mini-2024-07-18",
    "openai/gpt-4.1",
    "openai/gpt-4.1-mini",
    "openai/gpt-4.1-nano",
    "openai/codex-mini",
    "openai/o3",
    "openai/o3-mini",
    "openai/o3-mini-high",
    "openai/o4-mini",
    "openai/o4-mini-high",
    // DeepSeek models
    "deepseek/deepseek-chat-v3-0324:free",
    "deepseek/deepseek-chat-v3-0324",
    "deepseek/deepseek-chat",
    "deepseek/deepseek-r1-0528-qwen3-8b:free",
    "deepseek/deepseek-r1",
    // Meta LLaMA models
    "meta-llama/llama-4-maverick:free",
    "meta-llama/llama-4-maverick",
    "meta-llama/llama-4-scout:free",
    "meta-llama/llama-4-scout",
    // Mistral models
    "mistralai/mistral-medium-3",
    "mistralai/devstral-small:free",
    "mistralai/devstral-small",
];

/// Detect model capabilities including thinking/reasoning features
pub fn detect_model_capabilities(model: &str) -> Vec<ModelCapability> {
    let mut capabilities = vec![ModelCapability::Standard];

    if model.contains(":thinking") {
        capabilities.push(ModelCapability::Thinking);
    }

    if model.contains(":beta") {
        capabilities.push(ModelCapability::Beta);
    }

    if model.contains(":free") {
        capabilities.push(ModelCapability::Free);
    }

    if model.contains("-high") {
        capabilities.push(ModelCapability::High);
    }

    if model.contains("-nano") || model.contains("-mini") {
        capabilities.push(ModelCapability::Nano);
    }

    capabilities
}

/// Detect tool support for a given model name with comprehensive model database
pub fn detect_tool_support(provider: &str, model: &str) -> ToolSupport {
    // First check the registry
    if let Some(model_info) = crate::config::registry::get_model_info(model) {
        return model_info.tool_support.clone();
    }

    // Fall back to pattern matching
    match provider {
        "anthropic" => {
            // Direct Anthropic models - all Claude 3.x+ support tools
            if model.contains("claude-3")
                || model.contains("claude-4")
                || model.contains("claude-sonnet")
                || model.contains("claude-opus")
                || model.contains("claude-haiku")
            {
                ToolSupport::Full
            } else {
                ToolSupport::Unknown
            }
        }
        "openrouter" => {
            // Check against comprehensive verified list first
            if TOOL_CAPABLE_MODELS.contains(&model) {
                ToolSupport::Full
            } else {
                // Fallback to pattern matching for unlisted models
                if model.starts_with("anthropic/claude-3") || 
                   model.starts_with("anthropic/claude-4") ||
                   model.starts_with("openai/gpt-4") ||
                   model.starts_with("openai/gpt-3.5-turbo") ||
                   model.starts_with("openai/o") || // o3, o4 series
                   model.starts_with("google/gemini") ||
                   model.starts_with("mistralai/") ||
                   model.starts_with("deepseek/") ||
                   model.starts_with("meta-llama/llama-4") ||
                   model.starts_with("cohere/")
                {
                    ToolSupport::Full
                } else if model.starts_with("perplexity/")
                    || model.starts_with("microsoft/")
                    || model.contains("llama-3")
                    || model.contains("mixtral")
                {
                    ToolSupport::Partial
                } else {
                    ToolSupport::Unknown
                }
            }
        }
        "openai" => {
            // Direct OpenAI models
            if model.starts_with("gpt-4")
                || model.starts_with("gpt-3.5-turbo")
                || model.starts_with("o3")
                || model.starts_with("o4")
            {
                ToolSupport::Full
            } else {
                ToolSupport::Unknown
            }
        }
        "google" => {
            // Google/Gemini models
            if model.contains("gemini") {
                ToolSupport::Full
            } else {
                ToolSupport::Unknown
            }
        }
        "deepseek" => {
            // DeepSeek models
            if model.contains("deepseek-chat") || model.contains("deepseek-r1") {
                ToolSupport::Full
            } else {
                ToolSupport::Unknown
            }
        }
        "mistral" | "mistralai" => {
            // Mistral models
            if model.contains("mistral") || model.contains("devstral") {
                ToolSupport::Full
            } else {
                ToolSupport::Unknown
            }
        }
        "ollama" => {
            // Local Ollama models - varies by model
            if model.contains("llama3")
                || model.contains("mistral")
                || model.contains("mixtral")
                || model.contains("qwen")
                || model.contains("gemma")
                || model.contains("deepseek")
            {
                ToolSupport::Partial
            } else {
                ToolSupport::Unknown
            }
        }
        _ => ToolSupport::Unknown,
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::LLMConfig;

    #[test]
    fn test_validate_provider() {
        assert!(validate_provider("openrouter").is_ok());
        assert!(validate_provider("anthropic").is_ok());
        assert!(validate_provider("unknown").is_err());
    }

    #[tokio::test]
    async fn test_create_llm_provider_anthropic() {
        let config = LLMConfig {
            provider: "anthropic".to_string(),
            api_key: Some("test-key".to_string()),
            base_url: Some("https://api.anthropic.com".to_string()),
            model: "claude-3-5-sonnet-20241022".to_string(),
            temperature: Some(0.7),
            max_tokens: Some(4096),
        };

        let result = create_llm_provider(&config).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_create_llm_provider_openrouter() {
        let config = LLMConfig {
            provider: "openrouter".to_string(),
            api_key: Some("test-key".to_string()),
            base_url: Some("https://openrouter.ai/api/v1".to_string()),
            model: "openai/gpt-4o".to_string(),
            temperature: Some(0.7),
            max_tokens: Some(4096),
        };

        let result = create_llm_provider(&config).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_create_llm_provider_unknown() {
        let config = LLMConfig {
            provider: "unknown".to_string(),
            api_key: Some("test-key".to_string()),
            base_url: None,
            model: "some-model".to_string(),
            temperature: Some(0.7),
            max_tokens: Some(4096),
        };

        let result = create_llm_provider(&config).await;
        assert!(result.is_err());

        match result {
            Err(AutorunError::Config(msg)) => {
                // The error should come from validate_provider which uses "Unsupported"
                assert!(msg.contains("Unsupported LLM provider: unknown"));
            }
            Err(other) => panic!("Expected Config error, got: {:?}", other),
            Ok(_) => panic!("Expected error, got success"),
        }
    }

    #[tokio::test]
    async fn test_create_llm_provider_empty_api_key() {
        let config = LLMConfig {
            provider: "anthropic".to_string(),
            api_key: Some("".to_string()),
            base_url: None,
            model: "claude-3-5-sonnet-20241022".to_string(),
            temperature: Some(0.7),
            max_tokens: Some(4096),
        };

        let result = create_llm_provider(&config).await;
        assert!(result.is_err());

        if let Err(AutorunError::Config(msg)) = result {
            assert!(msg.contains("API key is required"));
        } else {
            panic!("Expected Config error for empty API key");
        }
    }

    #[tokio::test]
    async fn test_provider_info_default_implementation() {
        let config = LLMConfig {
            provider: "anthropic".to_string(),
            api_key: Some("test-key".to_string()),
            base_url: None,
            model: "claude-3-5-sonnet-20241022".to_string(),
            temperature: Some(0.7),
            max_tokens: Some(4096),
        };

        let provider = create_llm_provider(&config).await.unwrap();
        let info = provider.provider_info();

        assert_eq!(info.name, "claude-3-5-sonnet-20241022");
        assert_eq!(info.display_name, "claude-3-5-sonnet-20241022 Provider");
        assert_eq!(info.version, "1.0.0");
        assert!(!info.capabilities.is_empty());
        assert!(info
            .capabilities
            .contains(&ProviderCapability::TextCompletion));
    }

    #[tokio::test]
    async fn test_health_check_default_implementation() {
        let config = LLMConfig {
            provider: "anthropic".to_string(),
            api_key: Some("test-key".to_string()),
            base_url: None,
            model: "claude-3-5-sonnet-20241022".to_string(),
            temperature: Some(0.7),
            max_tokens: Some(4096),
        };

        let provider = create_llm_provider(&config).await.unwrap();
        let health = provider.health_check().await.unwrap();

        assert_eq!(health.status, ApiStatus::Unknown);
        assert_eq!(
            health.message,
            "Health check not implemented for this provider"
        );
    }

    #[tokio::test]
    async fn test_rate_limit_status_default_implementation() {
        let config = LLMConfig {
            provider: "anthropic".to_string(),
            api_key: Some("test-key".to_string()),
            base_url: None,
            model: "claude-3-5-sonnet-20241022".to_string(),
            temperature: Some(0.7),
            max_tokens: Some(4096),
        };

        let provider = create_llm_provider(&config).await.unwrap();
        let rate_limit = provider.rate_limit_status();

        assert_eq!(rate_limit.current_requests, 0);
        assert_eq!(rate_limit.max_requests, 1000);
        assert!(!rate_limit.is_rate_limited);
    }

    #[tokio::test]
    async fn test_validate_configuration_default_implementation() {
        let config = LLMConfig {
            provider: "anthropic".to_string(),
            api_key: Some("test-key".to_string()),
            base_url: None,
            model: "claude-3-5-sonnet-20241022".to_string(),
            temperature: Some(0.7),
            max_tokens: Some(4096),
        };

        let provider = create_llm_provider(&config).await.unwrap();
        let validation = provider.validate_configuration().unwrap();

        assert!(validation.is_valid);
        assert!(validation.errors.is_empty());
        assert!(validation.warnings.is_empty());
    }

    #[tokio::test]
    async fn test_get_metrics_default_implementation() {
        let config = LLMConfig {
            provider: "anthropic".to_string(),
            api_key: Some("test-key".to_string()),
            base_url: None,
            model: "claude-3-5-sonnet-20241022".to_string(),
            temperature: Some(0.7),
            max_tokens: Some(4096),
        };

        let provider = create_llm_provider(&config).await.unwrap();
        let metrics = provider.get_metrics();

        assert_eq!(metrics.total_requests, 0);
        assert_eq!(metrics.successful_requests, 0);
        assert_eq!(metrics.failed_requests, 0);
        assert_eq!(metrics.average_response_time_ms, 0.0);
    }

    #[tokio::test]
    async fn test_supports_streaming_default_implementation() {
        let config = LLMConfig {
            provider: "anthropic".to_string(),
            api_key: Some("test-key".to_string()),
            base_url: None,
            model: "claude-3-5-sonnet-20241022".to_string(),
            temperature: Some(0.7),
            max_tokens: Some(4096),
        };

        let provider = create_llm_provider(&config).await.unwrap();

        // Default implementation should return false
        assert!(!provider.supports_streaming());
    }

    #[tokio::test]
    async fn test_estimate_cost_default_implementation() {
        let config = LLMConfig {
            provider: "anthropic".to_string(),
            api_key: Some("test-key".to_string()),
            base_url: None,
            model: "claude-3-5-sonnet-20241022".to_string(),
            temperature: Some(0.7),
            max_tokens: Some(4096),
        };

        let provider = create_llm_provider(&config).await.unwrap();
        let cost_estimate = provider.estimate_cost(100, 200);

        // Default implementation should return None
        assert!(cost_estimate.is_none());
    }

    #[test]
    fn test_provider_capability_serialization() {
        let capability = ProviderCapability::FunctionCalling;
        let json = serde_json::to_string(&capability).unwrap();
        let deserialized: ProviderCapability = serde_json::from_str(&json).unwrap();
        assert_eq!(capability, deserialized);
    }

    #[test]
    fn test_api_status_serialization() {
        let status = ApiStatus::Healthy;
        let json = serde_json::to_string(&status).unwrap();
        let deserialized: ApiStatus = serde_json::from_str(&json).unwrap();
        assert_eq!(status, deserialized);
    }

    #[test]
    fn test_provider_info_creation() {
        use std::collections::HashMap;

        let info = ProviderInfo {
            name: "test-provider".to_string(),
            display_name: "Test Provider".to_string(),
            version: "1.0.0".to_string(),
            capabilities: vec![ProviderCapability::TextCompletion],
            rate_limits: RateLimitInfo {
                requests_per_minute: Some(60),
                requests_per_hour: Some(1000),
                requests_per_day: None,
                tokens_per_minute: Some(50000),
                max_concurrent_requests: Some(5),
            },
            metadata: HashMap::new(),
        };

        assert_eq!(info.name, "test-provider");
        assert_eq!(info.capabilities.len(), 1);
        assert!(info
            .capabilities
            .contains(&ProviderCapability::TextCompletion));
    }

    #[tokio::test]
    async fn test_openrouter_uses_correct_base_url() {
        let config = LLMConfig {
            provider: "openrouter".to_string(),
            api_key: Some("test-key".to_string()),
            base_url: None, // Should use default OpenRouter URL
            model: "openai/gpt-4.1".to_string(),
            temperature: Some(0.7),
            max_tokens: Some(4096),
        };

        // Test that effective_base_url returns the correct OpenRouter URL
        assert_eq!(config.effective_base_url(), "https://openrouter.ai/api/v1");

        let result = create_llm_provider(&config).await;
        assert!(result.is_ok());
    }
}
