use crate::errors::{<PERSON>runError, Result};
use crate::llm::{detect_tool_support, LLMProvider, Message, ToolChoice, ToolSupport};
use async_trait::async_trait;
use futures_util::stream::StreamExt;
use futures_util::Stream;
use reqwest::Client;
use reqwest::Response;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::pin::Pin;
use std::time::Duration;
use tracing::{debug, error, info};

// Re-export the new types defined here for backwards compatibility

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolCall {
    pub id: String,
    pub name: String,
    pub input: serde_json::Value,
}

#[derive(Debug, Clone)]
pub struct AnthropicConfig {
    pub api_key: String,
    pub base_url: String,
    pub model: String,
    pub max_tokens: u32,
    pub temperature: f32,
    pub timeout: Duration,
}

impl Default for AnthropicConfig {
    fn default() -> Self {
        Self {
            api_key: String::new(),
            base_url: "https://api.anthropic.com".to_string(),
            model: "claude-3-5-sonnet-20241022".to_string(),
            max_tokens: 4096,
            temperature: 0.7,
            timeout: Duration::from_secs(120),
        }
    }
}

#[derive(Debug, Serialize)]
struct AnthropicRequest {
    model: String,
    max_tokens: u32,
    temperature: f32,
    messages: Vec<AnthropicMessage>,
    system: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    tools: Option<Vec<Value>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    tool_choice: Option<Value>,
}

#[derive(Debug, Serialize, Deserialize)]
struct AnthropicMessage {
    role: String,
    content: String,
}

#[derive(Debug, Deserialize)]
struct AnthropicResponse {
    id: String,
    #[serde(rename = "type")]
    response_type: String,
    role: String,
    content: Vec<AnthropicContent>,
    model: String,
    stop_reason: Option<String>,
    stop_sequence: Option<String>,
    usage: AnthropicUsage,
}

#[derive(Debug, Deserialize)]
#[serde(tag = "type")]
enum AnthropicContent {
    #[serde(rename = "text")]
    Text { text: String },
    #[serde(rename = "tool_use")]
    ToolUse {
        id: String,
        name: String,
        input: Value,
    },
}

#[derive(Debug, Deserialize)]
struct AnthropicUsage {
    input_tokens: u32,
    output_tokens: u32,
}

#[derive(Debug, Deserialize)]
struct AnthropicError {
    #[serde(rename = "type")]
    error_type: String,
    message: String,
}

#[derive(Debug, Deserialize)]
struct AnthropicErrorResponse {
    error: AnthropicError,
}

#[derive(Clone)]
pub struct AnthropicClient {
    client: Client,
    config: AnthropicConfig,
}

impl AnthropicClient {
    pub fn new(config: AnthropicConfig) -> Result<Self> {
        if config.api_key.is_empty() {
            return Err(AutorunError::Config(
                "Anthropic API key is required. Set ANTHROPIC_API_KEY environment variable, or use AUTORUN_LLM_PROVIDER=openrouter with OPENROUTER_API_KEY for OpenRouter models".to_string(),
            ));
        }

        let client = Client::builder()
            .timeout(config.timeout)
            .user_agent("autorun/0.1.0")
            .build()
            .map_err(|e| AutorunError::Http(e))?;

        Ok(Self { client, config })
    }

    async fn make_request(&self, messages: Vec<Message>) -> Result<String> {
        self.make_request_with_tools(messages, Vec::new()).await
    }

    async fn make_request_with_tools_structured(
        &self,
        messages: Vec<Message>,
        tools: Vec<Value>,
        tool_choice: Option<ToolChoice>,
    ) -> Result<ToolCallResponse> {
        let (system_message, user_messages) = self.separate_system_messages(messages);

        let anthropic_messages: Vec<AnthropicMessage> = user_messages
            .into_iter()
            .map(|msg| AnthropicMessage {
                role: msg.role,
                content: msg.content,
            })
            .collect();

        let tools_option = if tools.is_empty() { None } else { Some(tools) };
        let tool_choice_val = match tool_choice.unwrap_or(ToolChoice::Auto) {
            ToolChoice::Auto => Some(serde_json::json!({"type": "auto"})),
            ToolChoice::None => None,
            ToolChoice::Required => Some(serde_json::json!({"type": "any"})),
            ToolChoice::Function { name } => {
                Some(serde_json::json!({"type": "tool", "name": name}))
            }
        };

        let request = AnthropicRequest {
            model: self.config.model.clone(),
            max_tokens: self.config.max_tokens,
            temperature: self.config.temperature,
            messages: anthropic_messages,
            system: system_message,
            tools: tools_option,
            tool_choice: tool_choice_val,
        };

        info!("Sending request to Anthropic API: {:?}", request);
        debug!(
            "Request JSON: {}",
            serde_json::to_string_pretty(&request)
                .unwrap_or_else(|_| "Failed to serialize".to_string())
        );

        let response = self
            .client
            .post(&format!("{}/v1/messages", self.config.base_url))
            .header("Content-Type", "application/json")
            .header("x-api-key", &self.config.api_key)
            .header("anthropic-version", "2023-06-01")
            .json(&request)
            .send()
            .await
            .map_err(|e| {
                error!("Failed to send request to Anthropic API: {}", e);
                AutorunError::Http(e)
            })?;

        let status = response.status();
        let response_text = response.text().await.map_err(|e| {
            error!("Failed to read response body: {}", e);
            AutorunError::Http(e)
        })?;

        info!(
            "Received response from Anthropic API (status: {}): {}",
            status, response_text
        );

        if !status.is_success() {
            error!(
                "Anthropic API error response ({}): {}",
                status, response_text
            );
            if let Ok(error_response) =
                serde_json::from_str::<AnthropicErrorResponse>(&response_text)
            {
                return Err(AutorunError::LlmApi(format!(
                    "Anthropic API error ({}): {}",
                    error_response.error.error_type, error_response.error.message
                )));
            } else {
                return Err(AutorunError::LlmApi(format!(
                    "HTTP {} error: {}",
                    status, response_text
                )));
            }
        }

        let anthropic_response: AnthropicResponse =
            serde_json::from_str(&response_text).map_err(|e| {
                error!("Failed to parse Anthropic response JSON: {}", e);
                error!("Full response text: {}", response_text);

                if let Ok(generic_json) = serde_json::from_str::<serde_json::Value>(&response_text)
                {
                    error!("Response parsed as generic JSON: {:#}", generic_json);
                } else {
                    error!("Response is not valid JSON at all");
                }

                AutorunError::Json(e)
            })?;

        info!(
            "Anthropic API usage: {} input tokens, {} output tokens",
            anthropic_response.usage.input_tokens, anthropic_response.usage.output_tokens
        );

        // Process the content blocks
        let mut text_content = Vec::new();
        let mut tool_calls = Vec::new();

        for content in anthropic_response.content {
            match content {
                AnthropicContent::Text { text } => {
                    text_content.push(text);
                }
                AnthropicContent::ToolUse { id, name, input } => {
                    tool_calls.push(ToolCall { id, name, input });
                }
            }
        }

        Ok(ToolCallResponse {
            content: text_content.join("\n"),
            tool_calls,
            finish_reason: anthropic_response.stop_reason,
        })
    }

    async fn make_request_with_tools(
        &self,
        messages: Vec<Message>,
        tools: Vec<Value>,
    ) -> Result<String> {
        let (system_message, user_messages) = self.separate_system_messages(messages);

        let anthropic_messages: Vec<AnthropicMessage> = user_messages
            .into_iter()
            .map(|msg| AnthropicMessage {
                role: msg.role,
                content: msg.content,
            })
            .collect();

        let tools_option = if tools.is_empty() { None } else { Some(tools) };
        let tool_choice = if tools_option.is_some() {
            Some(serde_json::json!({"type": "auto"}))
        } else {
            None
        };

        let request = AnthropicRequest {
            model: self.config.model.clone(),
            max_tokens: self.config.max_tokens,
            temperature: self.config.temperature,
            messages: anthropic_messages,
            system: system_message,
            tools: tools_option,
            tool_choice,
        };

        info!("Sending request to Anthropic API: {:?}", request);
        debug!(
            "Request JSON: {}",
            serde_json::to_string_pretty(&request)
                .unwrap_or_else(|_| "Failed to serialize".to_string())
        );

        let response = self
            .client
            .post(&format!("{}/v1/messages", self.config.base_url))
            .header("Content-Type", "application/json")
            .header("x-api-key", &self.config.api_key)
            .header("anthropic-version", "2023-06-01")
            .json(&request)
            .send()
            .await
            .map_err(|e| {
                error!("Failed to send request to Anthropic API: {}", e);
                AutorunError::Http(e)
            })?;

        let status = response.status();
        let response_text = response.text().await.map_err(|e| {
            error!("Failed to read response body: {}", e);
            AutorunError::Http(e)
        })?;

        info!(
            "Received response from Anthropic API (status: {}): {}",
            status, response_text
        );

        if !status.is_success() {
            error!(
                "Anthropic API error response ({}): {}",
                status, response_text
            );
            if let Ok(error_response) =
                serde_json::from_str::<AnthropicErrorResponse>(&response_text)
            {
                return Err(AutorunError::LlmApi(format!(
                    "Anthropic API error ({}): {}",
                    error_response.error.error_type, error_response.error.message
                )));
            } else {
                return Err(AutorunError::LlmApi(format!(
                    "HTTP {} error: {}",
                    status, response_text
                )));
            }
        }

        // Log the raw response for debugging before attempting to parse
        debug!("Raw JSON response length: {} bytes", response_text.len());
        debug!(
            "Raw JSON response (first 500 chars): {}",
            &response_text.chars().take(500).collect::<String>()
        );

        let anthropic_response: AnthropicResponse =
            serde_json::from_str(&response_text).map_err(|e| {
                error!("Failed to parse Anthropic response JSON: {}", e);
                error!("Full response text: {}", response_text);

                // Try to parse as generic JSON to see structure
                if let Ok(generic_json) = serde_json::from_str::<serde_json::Value>(&response_text)
                {
                    error!("Response parsed as generic JSON: {:#}", generic_json);
                } else {
                    error!("Response is not valid JSON at all");
                }

                AutorunError::Json(e)
            })?;

        info!(
            "Anthropic API usage: {} input tokens, {} output tokens",
            anthropic_response.usage.input_tokens, anthropic_response.usage.output_tokens
        );

        // Process the content blocks
        let mut result_content = Vec::new();

        for content in anthropic_response.content {
            match content {
                AnthropicContent::Text { text } => {
                    result_content.push(text);
                }
                AnthropicContent::ToolUse { id, name, input } => {
                    // Format tool use as JSON for parsing by AgentCore
                    let tool_use_json = serde_json::json!({
                        "type": "tool_use",
                        "id": id,
                        "name": name,
                        "input": input
                    });
                    result_content.push(tool_use_json.to_string());
                }
            }
        }

        Ok(result_content.join("\n"))
    }

    fn separate_system_messages(&self, messages: Vec<Message>) -> (Option<String>, Vec<Message>) {
        let mut system_messages = Vec::new();
        let mut other_messages = Vec::new();

        for message in messages {
            if message.role == "system" {
                system_messages.push(message.content);
            } else {
                other_messages.push(message);
            }
        }

        let system_content = if system_messages.is_empty() {
            None
        } else {
            Some(system_messages.join("\n\n"))
        };

        (system_content, other_messages)
    }
}

#[async_trait]
impl LLMProvider for AnthropicClient {
    async fn complete(&self, messages: Vec<Message>) -> Result<String> {
        self.make_request(messages).await
    }

    fn supports_tools(&self) -> ToolSupport {
        detect_tool_support("anthropic", &self.config.model)
    }

    fn model_name(&self) -> &str {
        &self.config.model
    }

    fn as_any(&self) -> Option<&dyn std::any::Any> {
        Some(self)
    }
}

// Stream support for SSE (Server-Sent Events)
use tokio::sync::oneshot;

pub struct StreamingResponse {
    stream: Pin<Box<dyn Stream<Item = std::result::Result<bytes::Bytes, reqwest::Error>> + Send>>,
    buffer: String,
    is_openrouter: bool,
    abort_handle: Option<oneshot::Sender<()>>,
}

impl StreamingResponse {
    pub fn new(response: Response, is_openrouter: bool) -> Self {
        Self {
            stream: Box::pin(response.bytes_stream()),
            buffer: String::new(),
            is_openrouter,
            abort_handle: None,
        }
    }

    pub fn with_abort_handle(mut self, abort_handle: oneshot::Sender<()>) -> Self {
        self.abort_handle = Some(abort_handle);
        self
    }

    pub fn abort(&mut self) {
        if let Some(handle) = self.abort_handle.take() {
            let _ = handle.send(());
        }
    }

    pub async fn next_chunk(&mut self) -> Result<Option<StreamChunk>> {
        while let Some(chunk_result) = self.stream.next().await {
            let chunk = chunk_result.map_err(|e| AutorunError::Http(e))?;
            self.buffer.push_str(&String::from_utf8_lossy(&chunk));

            // Process complete SSE lines from buffer
            while let Some(line_end) = self.buffer.find('\n') {
                let line = self.buffer[..line_end].trim().to_string();
                self.buffer = self.buffer[line_end + 1..].to_string();

                if line.is_empty() {
                    continue;
                }

                // Skip SSE comments
                if line.starts_with(':') {
                    continue;
                }

                if line.starts_with("data: ") {
                    let data = &line[6..];

                    if data == "[DONE]" {
                        return Ok(None);
                    }

                    // Parse the JSON data
                    if let Ok(parsed) = serde_json::from_str::<Value>(data) {
                        if self.is_openrouter {
                            return self.parse_openrouter_chunk(parsed);
                        } else {
                            return self.parse_anthropic_chunk(parsed);
                        }
                    }
                }
            }
        }

        Ok(None)
    }

    fn parse_openrouter_chunk(&self, data: Value) -> Result<Option<StreamChunk>> {
        if let Some(choices) = data.get("choices").and_then(|c| c.as_array()) {
            if let Some(choice) = choices.first() {
                let mut chunk = StreamChunk {
                    content: None,
                    tool_calls: Vec::new(),
                    finish_reason: choice
                        .get("finish_reason")
                        .and_then(|f| f.as_str())
                        .map(|s| s.to_string()),
                };

                // Handle content delta
                if let Some(delta) = choice.get("delta") {
                    if let Some(content) = delta.get("content").and_then(|c| c.as_str()) {
                        chunk.content = Some(content.to_string());
                    }

                    // Handle tool calls in delta
                    if let Some(tool_calls) = delta.get("tool_calls").and_then(|t| t.as_array()) {
                        for tc in tool_calls {
                            if let (Some(id), Some(func)) = (tc.get("id"), tc.get("function")) {
                                if let (Some(name), Some(args)) = (
                                    func.get("name").and_then(|n| n.as_str()),
                                    func.get("arguments").and_then(|a| a.as_str()),
                                ) {
                                    chunk.tool_calls.push(StreamingToolCall {
                                        id: id.as_str().unwrap_or("").to_string(),
                                        name: name.to_string(),
                                        arguments: args.to_string(),
                                    });
                                }
                            }
                        }
                    }
                }

                return Ok(Some(chunk));
            }
        }

        Ok(None)
    }

    fn parse_anthropic_chunk(&self, data: Value) -> Result<Option<StreamChunk>> {
        let mut chunk = StreamChunk {
            content: None,
            tool_calls: Vec::new(),
            finish_reason: data
                .get("stop_reason")
                .and_then(|s| s.as_str())
                .map(|s| s.to_string()),
        };

        // Handle different event types for Anthropic
        if let Some(event_type) = data.get("type").and_then(|t| t.as_str()) {
            match event_type {
                "content_block_delta" => {
                    if let Some(delta) = data.get("delta") {
                        if let Some(text) = delta.get("text").and_then(|t| t.as_str()) {
                            chunk.content = Some(text.to_string());
                        }
                    }
                }
                "content_block_start" => {
                    if let Some(content_block) = data.get("content_block") {
                        if let Some(block_type) = content_block.get("type").and_then(|t| t.as_str())
                        {
                            if block_type == "tool_use" {
                                if let (Some(id), Some(name)) = (
                                    content_block.get("id").and_then(|i| i.as_str()),
                                    content_block.get("name").and_then(|n| n.as_str()),
                                ) {
                                    chunk.tool_calls.push(StreamingToolCall {
                                        id: id.to_string(),
                                        name: name.to_string(),
                                        arguments: String::new(),
                                    });
                                }
                            }
                        }
                    }
                }
                _ => {}
            }
        }

        Ok(Some(chunk))
    }
}

#[derive(Debug, Clone)]
pub struct StreamChunk {
    pub content: Option<String>,
    pub tool_calls: Vec<StreamingToolCall>,
    pub finish_reason: Option<String>,
}

#[derive(Debug, Clone)]
pub struct StreamingToolCall {
    pub id: String,
    pub name: String,
    pub arguments: String,
}

// OpenAI-compatible client for other providers
#[derive(Debug, Clone)]
pub struct OpenAICompatibleConfig {
    pub api_key: String,
    pub base_url: String,
    pub model: String,
    pub max_tokens: u32,
    pub temperature: f32,
    pub timeout: Duration,
}

impl Default for OpenAICompatibleConfig {
    fn default() -> Self {
        Self {
            api_key: String::new(),
            base_url: "https://api.openai.com".to_string(),
            model: "gpt-4".to_string(),
            max_tokens: 4096,
            temperature: 0.7,
            timeout: Duration::from_secs(120),
        }
    }
}

#[derive(Debug, Serialize)]
struct OpenAIRequest {
    model: String,
    messages: Vec<OpenAIMessage>,
    max_tokens: u32,
    temperature: f32,
    #[serde(skip_serializing_if = "Option::is_none")]
    tools: Option<Vec<Value>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    tool_choice: Option<Value>,
}

#[derive(Debug, Serialize, Deserialize)]
struct OpenAIMessage {
    role: String,
    content: Value,
    #[serde(skip_serializing_if = "Option::is_none")]
    tool_calls: Option<Vec<OpenAIToolCall>>,
}

#[derive(Debug, Serialize, Deserialize)]
struct OpenAIToolCall {
    id: String,
    #[serde(rename = "type")]
    call_type: String,
    function: OpenAIFunction,
}

#[derive(Debug, Serialize, Deserialize)]
struct OpenAIFunction {
    name: String,
    arguments: String,
}

#[derive(Debug, Deserialize)]
struct OpenAIResponse {
    id: String,
    object: String,
    created: u64,
    model: String,
    choices: Vec<OpenAIChoice>,
    usage: OpenAIUsage,
}

#[derive(Debug, Deserialize)]
struct OpenAIChoice {
    index: u32,
    message: OpenAIChoiceMessage,
    finish_reason: Option<String>,
}

#[derive(Debug, Deserialize)]
struct OpenAIChoiceMessage {
    role: String,
    content: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    tool_calls: Option<Vec<OpenAIToolCall>>,
}

#[derive(Debug, Deserialize)]
struct OpenAIUsage {
    prompt_tokens: u32,
    completion_tokens: u32,
    total_tokens: u32,
}

#[derive(Clone)]
pub struct OpenAICompatibleClient {
    client: Client,
    config: OpenAICompatibleConfig,
}

impl OpenAICompatibleClient {
    pub fn new(config: OpenAICompatibleConfig) -> Result<Self> {
        if config.api_key.is_empty() {
            return Err(AutorunError::Config(
                "OpenRouter API key is required. Set OPENROUTER_API_KEY environment variable, or use AUTORUN_LLM_PROVIDER=anthropic with ANTHROPIC_API_KEY for Anthropic models".to_string(),
            ));
        }

        let client = Client::builder()
            .timeout(config.timeout)
            .user_agent("autorun/0.1.0")
            .build()
            .map_err(|e| AutorunError::Http(e))?;

        Ok(Self { client, config })
    }

    async fn make_request_with_tools_structured(
        &self,
        messages: Vec<Message>,
        tools: Option<Vec<Value>>,
        tool_choice: Option<ToolChoice>,
    ) -> Result<ToolCallResponse> {
        let openai_messages: Vec<OpenAIMessage> = messages
            .into_iter()
            .map(|msg| {
                // Try to parse content as JSON first (for structured content with tool calls)
                let content = if let Ok(json_val) = serde_json::from_str::<Value>(&msg.content) {
                    json_val
                } else {
                    Value::String(msg.content)
                };

                OpenAIMessage {
                    role: msg.role,
                    content,
                    tool_calls: None,
                }
            })
            .collect();

        let tool_choice_val = match tool_choice.unwrap_or(ToolChoice::Auto) {
            ToolChoice::Auto => Some(Value::String("auto".to_string())),
            ToolChoice::None => Some(Value::String("none".to_string())),
            ToolChoice::Required => Some(Value::String("required".to_string())),
            ToolChoice::Function { name } => {
                Some(serde_json::json!({"type": "function", "function": {"name": name}}))
            }
        };

        let request = OpenAIRequest {
            model: self.config.model.clone(),
            messages: openai_messages,
            max_tokens: self.config.max_tokens,
            temperature: self.config.temperature,
            tools: tools.clone(),
            tool_choice: if tools.is_some() {
                tool_choice_val
            } else {
                None
            },
        };

        info!("Sending request to OpenAI-compatible API: {:?}", request);
        debug!(
            "Request JSON: {}",
            serde_json::to_string_pretty(&request)
                .unwrap_or_else(|_| "Failed to serialize".to_string())
        );

        let response = self
            .client
            .post(&format!("{}/chat/completions", self.config.base_url))
            .header("Content-Type", "application/json")
            .header("Authorization", format!("Bearer {}", self.config.api_key))
            .json(&request)
            .send()
            .await
            .map_err(|e| {
                error!("Failed to send request to OpenAI-compatible API: {}", e);
                AutorunError::Http(e)
            })?;

        let status = response.status();
        let response_text = response.text().await.map_err(|e| {
            error!("Failed to read response body: {}", e);
            AutorunError::Http(e)
        })?;

        info!(
            "Received response from OpenAI-compatible API (status: {}): {}",
            status, response_text
        );

        if !status.is_success() {
            error!("OpenAI API error response ({}): {}", status, response_text);
            return Err(AutorunError::LlmApi(format!(
                "HTTP {} error: {}",
                status, response_text
            )));
        }

        let openai_response: OpenAIResponse =
            serde_json::from_str(&response_text).map_err(|e| {
                error!("Failed to parse OpenAI response JSON: {}", e);
                error!("Full response text: {}", response_text);

                if let Ok(generic_json) = serde_json::from_str::<serde_json::Value>(&response_text)
                {
                    error!("Response parsed as generic JSON: {:#}", generic_json);
                } else {
                    error!("Response is not valid JSON at all");
                }

                AutorunError::Json(e)
            })?;

        info!(
            "OpenAI API usage: {} prompt tokens, {} completion tokens",
            openai_response.usage.prompt_tokens, openai_response.usage.completion_tokens
        );

        if let Some(choice) = openai_response.choices.first() {
            let content = choice.message.content.clone().unwrap_or_default();
            let mut tool_calls = Vec::new();

            info!(
                "OpenAI response choice: content={:?}, tool_calls={:?}",
                choice.message.content,
                choice.message.tool_calls.as_ref().map(|tc| tc.len())
            );

            // Process tool calls if present
            if let Some(openai_tool_calls) = &choice.message.tool_calls {
                info!("Processing {} tool calls", openai_tool_calls.len());
                for tool_call in openai_tool_calls {
                    info!(
                        "Tool call: id={}, name={}, arguments={}",
                        tool_call.id, tool_call.function.name, tool_call.function.arguments
                    );

                    let input = serde_json::from_str::<Value>(&tool_call.function.arguments)
                        .unwrap_or_else(|_| Value::Object(Default::default()));

                    tool_calls.push(ToolCall {
                        id: tool_call.id.clone(),
                        name: tool_call.function.name.clone(),
                        input,
                    });
                }
            }

            Ok(ToolCallResponse {
                content,
                tool_calls,
                finish_reason: choice.finish_reason.clone(),
            })
        } else {
            Err(AutorunError::LlmApi(
                "No choices in OpenAI response".to_string(),
            ))
        }
    }

    async fn make_streaming_request(
        &self,
        messages: Vec<Message>,
        tools: Option<Vec<Value>>,
        tool_choice: Option<ToolChoice>,
    ) -> Result<StreamingResponse> {
        let openai_messages: Vec<OpenAIMessage> = messages
            .into_iter()
            .map(|msg| {
                let content = if let Ok(json_val) = serde_json::from_str::<Value>(&msg.content) {
                    json_val
                } else {
                    Value::String(msg.content)
                };

                OpenAIMessage {
                    role: msg.role,
                    content,
                    tool_calls: None,
                }
            })
            .collect();

        let tool_choice_val = match tool_choice.unwrap_or(ToolChoice::Auto) {
            ToolChoice::Auto => Some(Value::String("auto".to_string())),
            ToolChoice::None => Some(Value::String("none".to_string())),
            ToolChoice::Required => Some(Value::String("required".to_string())),
            ToolChoice::Function { name } => {
                Some(serde_json::json!({"type": "function", "function": {"name": name}}))
            }
        };

        let request = OpenAIRequest {
            model: self.config.model.clone(),
            messages: openai_messages,
            max_tokens: self.config.max_tokens,
            temperature: self.config.temperature,
            tools: tools.clone(),
            tool_choice: if tools.is_some() {
                tool_choice_val
            } else {
                None
            },
        };

        // Add stream field to request
        let mut request_json = serde_json::to_value(&request).map_err(|e| AutorunError::Json(e))?;
        request_json["stream"] = serde_json::json!(true);

        info!("Sending streaming request to OpenRouter API");
        debug!(
            "Streaming request JSON: {}",
            serde_json::to_string_pretty(&request_json)
                .unwrap_or_else(|_| "Failed to serialize".to_string())
        );

        let response = self
            .client
            .post(&format!("{}/chat/completions", self.config.base_url))
            .header("Content-Type", "application/json")
            .header("Authorization", format!("Bearer {}", self.config.api_key))
            .json(&request_json)
            .send()
            .await
            .map_err(|e| {
                error!("Failed to send streaming request to OpenRouter API: {}", e);
                AutorunError::Http(e)
            })?;

        let status = response.status();
        if !status.is_success() {
            let error_text = response.text().await.map_err(|e| AutorunError::Http(e))?;
            error!(
                "OpenRouter API streaming error response ({}): {}",
                status, error_text
            );
            return Err(AutorunError::LlmApi(format!(
                "HTTP {} error: {}",
                status, error_text
            )));
        }

        Ok(StreamingResponse::new(response, true))
    }

    async fn make_request_with_tools(
        &self,
        messages: Vec<Message>,
        tools: Option<Vec<Value>>,
    ) -> Result<String> {
        let openai_messages: Vec<OpenAIMessage> = messages
            .into_iter()
            .map(|msg| {
                // Try to parse content as JSON first (for structured content with tool calls)
                let content = if let Ok(json_val) = serde_json::from_str::<Value>(&msg.content) {
                    json_val
                } else {
                    Value::String(msg.content)
                };

                OpenAIMessage {
                    role: msg.role,
                    content,
                    tool_calls: None,
                }
            })
            .collect();

        let request = OpenAIRequest {
            model: self.config.model.clone(),
            messages: openai_messages,
            max_tokens: self.config.max_tokens,
            temperature: self.config.temperature,
            tools: tools.clone(),
            tool_choice: if tools.is_some() {
                Some(Value::String("auto".to_string()))
            } else {
                None
            },
        };

        info!("Sending request to OpenAI-compatible API: {:?}", request);
        debug!(
            "Request JSON: {}",
            serde_json::to_string_pretty(&request)
                .unwrap_or_else(|_| "Failed to serialize".to_string())
        );

        let response = self
            .client
            .post(&format!("{}/chat/completions", self.config.base_url))
            .header("Content-Type", "application/json")
            .header("Authorization", format!("Bearer {}", self.config.api_key))
            .json(&request)
            .send()
            .await
            .map_err(|e| {
                error!("Failed to send request to OpenAI-compatible API: {}", e);
                AutorunError::Http(e)
            })?;

        let status = response.status();
        let response_text = response.text().await.map_err(|e| {
            error!("Failed to read response body: {}", e);
            AutorunError::Http(e)
        })?;

        info!(
            "Received response from OpenAI-compatible API (status: {}): {}",
            status, response_text
        );

        if !status.is_success() {
            error!("OpenAI API error response ({}): {}", status, response_text);
            return Err(AutorunError::LlmApi(format!(
                "HTTP {} error: {}",
                status, response_text
            )));
        }

        // Log the raw response for debugging before attempting to parse
        debug!("Raw JSON response length: {} bytes", response_text.len());
        debug!(
            "Raw JSON response (first 500 chars): {}",
            &response_text.chars().take(500).collect::<String>()
        );

        let openai_response: OpenAIResponse =
            serde_json::from_str(&response_text).map_err(|e| {
                error!("Failed to parse OpenAI response JSON: {}", e);
                error!("Full response text: {}", response_text);

                // Try to parse as generic JSON to see structure
                if let Ok(generic_json) = serde_json::from_str::<serde_json::Value>(&response_text)
                {
                    error!("Response parsed as generic JSON: {:#}", generic_json);
                } else {
                    error!("Response is not valid JSON at all");
                }

                AutorunError::Json(e)
            })?;

        info!(
            "OpenAI API usage: {} prompt tokens, {} completion tokens",
            openai_response.usage.prompt_tokens, openai_response.usage.completion_tokens
        );

        if let Some(choice) = openai_response.choices.first() {
            let mut result_content = Vec::new();

            // Add text content if present
            if let Some(content) = &choice.message.content {
                result_content.push(content.clone());
            }

            // Process tool calls if present
            if let Some(tool_calls) = &choice.message.tool_calls {
                for tool_call in tool_calls {
                    let tool_use_json = serde_json::json!({
                        "type": "tool_use",
                        "id": tool_call.id,
                        "name": tool_call.function.name,
                        "input": serde_json::from_str::<Value>(&tool_call.function.arguments)
                            .unwrap_or_else(|_| Value::Object(Default::default()))
                    });
                    result_content.push(tool_use_json.to_string());
                }
            }

            Ok(result_content.join("\n"))
        } else {
            Err(AutorunError::LlmApi(
                "No choices in OpenAI response".to_string(),
            ))
        }
    }
}

#[async_trait]
impl LLMProvider for OpenAICompatibleClient {
    async fn complete(&self, messages: Vec<Message>) -> Result<String> {
        self.make_request_with_tools(messages, None).await
    }

    fn supports_tools(&self) -> ToolSupport {
        detect_tool_support("openrouter", &self.config.model)
    }

    fn model_name(&self) -> &str {
        &self.config.model
    }

    fn as_any(&self) -> Option<&dyn std::any::Any> {
        Some(self)
    }
}

/// Enhanced response structure for tool calling
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolCallResponse {
    pub content: String,
    pub tool_calls: Vec<ToolCall>,
    pub finish_reason: Option<String>,
}

// Extended LLM Provider trait for tool calling
#[async_trait]
pub trait LLMProviderWithTools: LLMProvider {
    /// Complete with tools and return structured response
    async fn complete_with_tools(
        &self,
        messages: Vec<Message>,
        tools: Vec<Value>,
        tool_choice: Option<ToolChoice>,
    ) -> Result<ToolCallResponse>;

    /// Legacy method for backwards compatibility
    async fn complete_with_tools_legacy(
        &self,
        messages: Vec<Message>,
        tools: Vec<Value>,
    ) -> Result<String> {
        let response = self
            .complete_with_tools(messages, tools, Some(ToolChoice::Auto))
            .await?;
        Ok(response.content)
    }
}

// Streaming LLM Provider trait
#[async_trait]
pub trait LLMProviderWithStreaming: LLMProvider {
    /// Complete with streaming response
    async fn complete_streaming(&self, messages: Vec<Message>) -> Result<StreamingResponse>;

    /// Complete with tools and streaming response
    async fn complete_with_tools_streaming(
        &self,
        messages: Vec<Message>,
        tools: Vec<Value>,
        tool_choice: Option<ToolChoice>,
    ) -> Result<StreamingResponse>;
}

#[async_trait]
impl LLMProviderWithTools for OpenAICompatibleClient {
    async fn complete_with_tools(
        &self,
        messages: Vec<Message>,
        tools: Vec<Value>,
        tool_choice: Option<ToolChoice>,
    ) -> Result<ToolCallResponse> {
        self.make_request_with_tools_structured(messages, Some(tools), tool_choice)
            .await
    }
}

#[async_trait]
impl LLMProviderWithTools for AnthropicClient {
    async fn complete_with_tools(
        &self,
        messages: Vec<Message>,
        tools: Vec<Value>,
        tool_choice: Option<ToolChoice>,
    ) -> Result<ToolCallResponse> {
        self.make_request_with_tools_structured(messages, tools, tool_choice)
            .await
    }
}

#[async_trait]
impl LLMProviderWithStreaming for OpenAICompatibleClient {
    async fn complete_streaming(&self, messages: Vec<Message>) -> Result<StreamingResponse> {
        self.make_streaming_request(messages, None, None).await
    }

    async fn complete_with_tools_streaming(
        &self,
        messages: Vec<Message>,
        tools: Vec<Value>,
        tool_choice: Option<ToolChoice>,
    ) -> Result<StreamingResponse> {
        self.make_streaming_request(messages, Some(tools), tool_choice)
            .await
    }
}
