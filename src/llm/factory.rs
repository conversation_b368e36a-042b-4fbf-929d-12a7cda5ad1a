use crate::config::LLMConfig;
use crate::errors::{AutorunError, Result};
use crate::llm::client::{
    AnthropicClient, AnthropicConfig, OpenAICompatibleClient, OpenAICompatibleConfig,
};
use crate::llm::LLMProvider;
use async_trait::async_trait;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;

/// Trait for building provider instances
#[async_trait]
pub trait ProviderBuilder: Send + Sync {
    /// Build a provider instance from configuration
    async fn build(&self, config: &LLMConfig) -> Result<Arc<dyn LLMProvider>>;

    /// Validate configuration for this provider
    fn validate_config(&self, config: &LLMConfig) -> Result<()>;

    /// Get provider name
    fn provider_name(&self) -> &'static str;
}

/// Builder for Anthropic provider
pub struct AnthropicBuilder;

#[async_trait]
impl ProviderBuilder for AnthropicBuilder {
    async fn build(&self, config: &LLMConfig) -> Result<Arc<dyn LLMProvider>> {
        let anthropic_config = AnthropicConfig {
            api_key: config.api_key.clone().unwrap_or_default(),
            base_url: config.effective_base_url(),
            model: config.model.clone(),
            max_tokens: config.max_tokens.unwrap_or(4096),
            temperature: config.temperature.unwrap_or(0.7),
            timeout: Duration::from_secs(120),
        };
        Ok(Arc::new(AnthropicClient::new(anthropic_config)?))
    }

    fn validate_config(&self, config: &LLMConfig) -> Result<()> {
        if config.provider != "anthropic" {
            return Err(AutorunError::Config(format!(
                "Provider mismatch: expected 'anthropic', got '{}'",
                config.provider
            )));
        }

        if !config.has_api_key() {
            return Err(AutorunError::Config(
                "API key is required for Anthropic provider".to_string(),
            ));
        }

        Ok(())
    }

    fn provider_name(&self) -> &'static str {
        "anthropic"
    }
}

/// Builder for OpenRouter provider (OpenAI-compatible API)
pub struct OpenRouterBuilder;

#[async_trait]
impl ProviderBuilder for OpenRouterBuilder {
    async fn build(&self, config: &LLMConfig) -> Result<Arc<dyn LLMProvider>> {
        let openai_config = OpenAICompatibleConfig {
            api_key: config.api_key.clone().unwrap_or_default(),
            base_url: config.effective_base_url(),
            model: config.model.clone(),
            max_tokens: config.max_tokens.unwrap_or(4096),
            temperature: config.temperature.unwrap_or(0.7),
            timeout: Duration::from_secs(120),
        };
        Ok(Arc::new(OpenAICompatibleClient::new(openai_config)?))
    }

    fn validate_config(&self, config: &LLMConfig) -> Result<()> {
        if config.provider != "openrouter" {
            return Err(AutorunError::Config(format!(
                "Provider mismatch: expected 'openrouter', got '{}'",
                config.provider
            )));
        }

        if !config.has_api_key() {
            return Err(AutorunError::Config(
                "API key is required for OpenRouter provider".to_string(),
            ));
        }

        Ok(())
    }

    fn provider_name(&self) -> &'static str {
        "openrouter"
    }
}

/// Centralized provider factory for creating LLM provider instances
pub struct ProviderFactory {
    builders: HashMap<String, Box<dyn ProviderBuilder>>,
}

impl ProviderFactory {
    /// Create a new provider factory with all supported providers
    pub fn new() -> Self {
        let mut builders: HashMap<String, Box<dyn ProviderBuilder>> = HashMap::new();

        builders.insert("anthropic".to_string(), Box::new(AnthropicBuilder));
        builders.insert("openrouter".to_string(), Box::new(OpenRouterBuilder));

        Self { builders }
    }

    /// Create a provider instance from configuration
    pub async fn create_provider(&self, config: &LLMConfig) -> Result<Arc<dyn LLMProvider>> {
        let builder = self.builders.get(&config.provider).ok_or_else(|| {
            AutorunError::Config(format!(
                "Unsupported LLM provider: {}. Supported providers: {}",
                config.provider,
                self.supported_providers().join(", ")
            ))
        })?;

        // Validate configuration first
        builder.validate_config(config)?;

        // Build the provider
        builder.build(config).await
    }

    /// Validate that a provider is supported
    pub fn validate_provider(&self, provider: &str) -> Result<()> {
        if self.builders.contains_key(provider) {
            Ok(())
        } else {
            Err(AutorunError::Config(format!(
                "Unsupported LLM provider: {}. Supported providers: {}",
                provider,
                self.supported_providers().join(", ")
            )))
        }
    }

    /// Get list of supported provider names
    pub fn supported_providers(&self) -> Vec<String> {
        self.builders.keys().cloned().collect()
    }

    /// Register a new provider builder
    pub fn register_provider(&mut self, name: String, builder: Box<dyn ProviderBuilder>) {
        self.builders.insert(name, builder);
    }
}

impl Default for ProviderFactory {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::LLMConfig;

    #[tokio::test]
    async fn test_anthropic_builder() {
        let builder = AnthropicBuilder;
        let config = LLMConfig {
            provider: "anthropic".to_string(),
            api_key: Some("test-key".to_string()),
            base_url: Some("https://api.anthropic.com".to_string()),
            model: "claude-3-5-sonnet-20241022".to_string(),
            temperature: Some(0.7),
            max_tokens: Some(4096),
        };

        assert!(builder.validate_config(&config).is_ok());
        assert_eq!(builder.provider_name(), "anthropic");

        let result = builder.build(&config).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_openrouter_builder() {
        let builder = OpenRouterBuilder;
        let config = LLMConfig {
            provider: "openrouter".to_string(),
            api_key: Some("test-key".to_string()),
            base_url: Some("https://openrouter.ai/api/v1".to_string()),
            model: "openai/gpt-4o".to_string(),
            temperature: Some(0.7),
            max_tokens: Some(4096),
        };

        assert!(builder.validate_config(&config).is_ok());
        assert_eq!(builder.provider_name(), "openrouter");

        let result = builder.build(&config).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_provider_factory() {
        let factory = ProviderFactory::new();

        // Test supported providers
        let providers = factory.supported_providers();
        assert!(providers.contains(&"anthropic".to_string()));
        assert!(providers.contains(&"openrouter".to_string()));

        // Test validation
        assert!(factory.validate_provider("anthropic").is_ok());
        assert!(factory.validate_provider("openrouter").is_ok());
        assert!(factory.validate_provider("unknown").is_err());

        // Test provider creation
        let anthropic_config = LLMConfig {
            provider: "anthropic".to_string(),
            api_key: Some("test-key".to_string()),
            base_url: None,
            model: "claude-3-5-sonnet-20241022".to_string(),
            temperature: Some(0.7),
            max_tokens: Some(4096),
        };

        let result = factory.create_provider(&anthropic_config).await;
        assert!(result.is_ok());

        let openrouter_config = LLMConfig {
            provider: "openrouter".to_string(),
            api_key: Some("test-key".to_string()),
            base_url: None,
            model: "openai/gpt-4o".to_string(),
            temperature: Some(0.7),
            max_tokens: Some(4096),
        };

        let result = factory.create_provider(&openrouter_config).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_factory_validation_errors() {
        let factory = ProviderFactory::new();

        // Test missing API key
        let config_no_key = LLMConfig {
            provider: "anthropic".to_string(),
            api_key: Some("".to_string()), // Empty key
            base_url: None,
            model: "claude-3-5-sonnet-20241022".to_string(),
            temperature: Some(0.7),
            max_tokens: Some(4096),
        };

        let result = factory.create_provider(&config_no_key).await;
        assert!(result.is_err());

        // Test unknown provider
        let config_unknown = LLMConfig {
            provider: "unknown".to_string(),
            api_key: Some("test-key".to_string()),
            base_url: None,
            model: "some-model".to_string(),
            temperature: Some(0.7),
            max_tokens: Some(4096),
        };

        let result = factory.create_provider(&config_unknown).await;
        assert!(result.is_err());

        if let Err(AutorunError::Config(msg)) = result {
            assert!(msg.contains("Unsupported LLM provider: unknown"));
            assert!(msg.contains("anthropic, openrouter"));
        } else {
            panic!("Expected Config error with provider list");
        }
    }
}
