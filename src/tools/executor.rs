//! ToolExecutor - Routes tool calls to local implementations or MCP servers
//!
//! The ToolExecutor is responsible for deciding how and where a requested tool
//! should be executed based on tool naming conventions and dispatching the
//! tool execution accordingly.

use serde_json::Value;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, error, info};

use crate::errors::Result;
use crate::mcp::client::McpClientManager;
use crate::mcp::McpServer;
use crate::tools::context::ExecutionContext;
use crate::tools::registry::ToolRegistry;
use crate::tools::{ToolError, ToolInput, ToolOutput, ToolResult};

/// Result of a tool execution
#[derive(Debug, Clone)]
pub enum ToolExecutionResult {
    /// Successful execution with output
    Success { output: Value },
    /// Failed execution with enhanced error information
    Failure {
        error_message: String,
        error_code: Option<String>,
        context: Option<ToolExecutionContext>,
        retry_suggested: bool,
    },
}

/// Context information for tool execution errors
#[derive(Debug, Clone)]
pub struct ToolExecutionContext {
    pub tool_name: String,
    pub call_id: String,
    pub execution_type: String,    // "local" or "mcp"
    pub server_id: Option<String>, // For MCP tools
    pub input_summary: String,
    pub execution_duration: Option<std::time::Duration>,
}

impl From<ToolOutput> for ToolExecutionResult {
    fn from(output: ToolOutput) -> Self {
        match output {
            ToolOutput::Success(value) => ToolExecutionResult::Success { output: value },
            ToolOutput::Error { error, details } => {
                let error_message = match details {
                    Some(details) => format!("{}: {}", error, details),
                    None => error,
                };
                ToolExecutionResult::Failure {
                    error_message,
                    error_code: None,
                    context: None,
                    retry_suggested: false,
                }
            }
        }
    }
}

impl ToolExecutionResult {
    /// Create a failure result with enhanced error context
    pub fn failure_with_context(
        error_message: String,
        error_code: Option<String>,
        context: ToolExecutionContext,
        retry_suggested: bool,
    ) -> Self {
        Self::Failure {
            error_message,
            error_code,
            context: Some(context),
            retry_suggested,
        }
    }

    /// Create a simple failure result for backward compatibility
    pub fn simple_failure(error_message: String) -> Self {
        Self::Failure {
            error_message,
            error_code: None,
            context: None,
            retry_suggested: false,
        }
    }

    /// Check if this result indicates a retryable error
    pub fn is_retryable(&self) -> bool {
        match self {
            Self::Success { .. } => false,
            Self::Failure {
                retry_suggested, ..
            } => *retry_suggested,
        }
    }

    /// Get a user-friendly error summary
    pub fn error_summary(&self) -> Option<String> {
        match self {
            Self::Success { .. } => None,
            Self::Failure {
                error_message,
                error_code,
                context,
                ..
            } => {
                let mut summary = error_message.clone();

                if let Some(code) = error_code {
                    summary = format!("[{}] {}", code, summary);
                }

                if let Some(ctx) = context {
                    summary = format!(
                        "{} (tool: {}, type: {})",
                        summary, ctx.tool_name, ctx.execution_type
                    );
                }

                Some(summary)
            }
        }
    }
}

/// Request for tool execution
#[derive(Debug, Clone)]
pub struct ToolCallRequest {
    pub call_id: String,
    pub tool_name: String,
    pub params: Value,
}

/// ToolExecutor handles routing and execution of tool calls
pub struct ToolExecutor {
    /// Registry for local tool implementations
    tool_registry: Arc<ToolRegistry>,
    /// Manager for MCP client connections
    mcp_client_manager: Arc<RwLock<McpClientManager>>,
}

impl ToolExecutor {
    /// Create a new ToolExecutor
    pub fn new(
        tool_registry: Arc<ToolRegistry>,
        mcp_client_manager: Arc<RwLock<McpClientManager>>,
    ) -> Self {
        Self {
            tool_registry,
            mcp_client_manager,
        }
    }

    /// Execute a tool call, routing to local or MCP implementation
    pub async fn execute_tool(
        &self,
        request: ToolCallRequest,
        context: &mut ExecutionContext,
    ) -> Result<ToolExecutionResult> {
        let tool_name = &request.tool_name;
        let params = &request.params;
        let call_id = &request.call_id;

        debug!("Executing tool: {} (call_id: {})", tool_name, call_id);

        // Determine execution strategy based on tool naming convention
        if self.is_mcp_tool(tool_name) {
            // Route to MCP server
            self.execute_mcp_tool(tool_name, params, call_id).await
        } else {
            // Execute locally via ToolRegistry
            self.execute_local_tool(tool_name, params, call_id, context)
                .await
        }
    }

    /// Check if a tool should be routed to MCP based on naming convention
    fn is_mcp_tool(&self, tool_name: &str) -> bool {
        // MCP tools are prefixed with "mcp::" or start with known MCP prefixes
        tool_name.starts_with("mcp::")
            || tool_name.starts_with("ide::")
            || tool_name.starts_with("mcp__")
    }

    /// Execute a tool locally using the ToolRegistry
    async fn execute_local_tool(
        &self,
        tool_name: &str,
        params: &Value,
        call_id: &str,
        context: &mut ExecutionContext,
    ) -> Result<ToolExecutionResult> {
        debug!("Executing local tool: {} (call_id: {})", tool_name, call_id);
        let start_time = std::time::Instant::now();

        // Convert Value to ToolInput
        let tool_input = ToolInput::from(params.clone());

        // Create input summary for error context
        let input_summary = self.create_input_summary(params);

        // Execute via ToolRegistry with validation and permissions
        match self
            .tool_registry
            .execute_tool(tool_name, tool_input, context)
            .await
        {
            Ok(output) => {
                info!("Local tool '{}' executed successfully", tool_name);
                Ok(ToolExecutionResult::from(output))
            }
            Err(e) => {
                let execution_duration = start_time.elapsed();
                error!(
                    "Local tool '{}' execution failed after {:?}: {}",
                    tool_name, execution_duration, e
                );

                // Enhanced error context
                let error_context = ToolExecutionContext {
                    tool_name: tool_name.to_string(),
                    call_id: call_id.to_string(),
                    execution_type: "local".to_string(),
                    server_id: None,
                    input_summary,
                    execution_duration: Some(execution_duration),
                };

                // Determine if the error is retryable
                let (error_code, retry_suggested) = self.classify_tool_error(&e);

                Ok(ToolExecutionResult::failure_with_context(
                    e.to_string(),
                    error_code,
                    error_context,
                    retry_suggested,
                ))
            }
        }
    }

    /// Execute a tool via MCP server
    async fn execute_mcp_tool(
        &self,
        full_tool_name: &str,
        params: &Value,
        call_id: &str,
    ) -> Result<ToolExecutionResult> {
        debug!(
            "Executing MCP tool: {} (call_id: {})",
            full_tool_name, call_id
        );
        let start_time = std::time::Instant::now();

        // Parse the tool name to extract server ID and actual tool name
        let (server_id, actual_tool_name) = self.parse_mcp_tool_name(full_tool_name)?;

        debug!(
            "Parsed MCP tool - server: '{}', tool: '{}'",
            server_id, actual_tool_name
        );

        // Get the MCP client for the target server
        let client_manager = self.mcp_client_manager.read().await;
        let client = client_manager.get_client(&server_id).ok_or_else(|| {
            crate::errors::AutorunError::McpError(format!(
                "MCP server '{}' not connected or not found",
                server_id
            ))
        })?;

        // Convert Value to JsonObject for MCP call
        let arguments = match params {
            Value::Object(map) => {
                let mut json_object = rmcp::model::JsonObject::new();
                for (key, value) in map {
                    json_object.insert(key.clone(), value.clone());
                }
                Some(json_object)
            }
            Value::Null => None,
            _ => {
                // For non-object params, wrap in an "input" field
                let mut json_object = rmcp::model::JsonObject::new();
                json_object.insert("input".to_string(), params.clone());
                Some(json_object)
            }
        };

        // Execute the tool via MCP client
        match client.call_tool(&actual_tool_name, arguments).await {
            Ok(result) => {
                info!(
                    "MCP tool '{}' executed successfully on server '{}'",
                    actual_tool_name, server_id
                );

                // Convert MCP result to our format
                let output_value = self.format_mcp_result(result)?;
                Ok(ToolExecutionResult::Success {
                    output: output_value,
                })
            }
            Err(e) => {
                let execution_duration = std::time::Instant::now().duration_since(start_time);
                error!(
                    "MCP tool '{}' execution failed on server '{}' after {:?}: {}",
                    actual_tool_name, server_id, execution_duration, e
                );

                // Enhanced error context for MCP tools
                let error_context = ToolExecutionContext {
                    tool_name: actual_tool_name.clone(),
                    call_id: call_id.to_string(),
                    execution_type: "mcp".to_string(),
                    server_id: Some(server_id.clone()),
                    input_summary: self.create_input_summary(params),
                    execution_duration: Some(execution_duration),
                };

                // MCP errors might be retryable (network issues, server restart, etc.)
                let retry_suggested = self.is_mcp_error_retryable(&e);

                Ok(ToolExecutionResult::failure_with_context(
                    format!("MCP tool execution failed: {}", e),
                    Some("MCP_ERROR".to_string()),
                    error_context,
                    retry_suggested,
                ))
            }
        }
    }

    /// Parse MCP tool name to extract server ID and tool name
    fn parse_mcp_tool_name(&self, full_tool_name: &str) -> Result<(String, String)> {
        // Handle different MCP naming conventions:
        // - "mcp::server_id::tool_name"
        // - "mcp__server_id__tool_name"
        // - "ide::tool_name" (special case for IDE server)

        if let Some(stripped) = full_tool_name.strip_prefix("mcp::") {
            if let Some((server_id, tool_name)) = stripped.split_once("::") {
                return Ok((server_id.to_string(), tool_name.to_string()));
            }
        }

        if let Some(stripped) = full_tool_name.strip_prefix("mcp__") {
            if let Some((server_id, tool_name)) = stripped.split_once("__") {
                return Ok((server_id.to_string(), tool_name.to_string()));
            }
        }

        if let Some(tool_name) = full_tool_name.strip_prefix("ide::") {
            return Ok(("ide".to_string(), tool_name.to_string()));
        }

        Err(crate::errors::AutorunError::ToolError(format!(
            "Invalid MCP tool name format: '{}'. Expected 'mcp::server::tool', 'mcp__server__tool', or 'ide::tool'",
            full_tool_name
        )))
    }

    /// Format MCP call result into our standard Value format
    fn format_mcp_result(&self, result: rmcp::model::CallToolResult) -> Result<Value> {
        use serde_json::json;

        // Check if the result indicates an error
        if result.is_error.unwrap_or(false) {
            // Extract error information from content
            let error_content = result
                .content
                .first()
                .map(|content| {
                    // Convert the content to string representation
                    serde_json::to_string(content)
                        .unwrap_or_else(|_| "Unknown error content".to_string())
                })
                .unwrap_or_else(|| "Unknown MCP tool error".to_string());

            return Ok(json!({
                "error": true,
                "message": error_content
            }));
        }

        // Format successful result - convert the content to JSON
        let formatted_content = result
            .content
            .iter()
            .map(|content| serde_json::to_value(content).unwrap_or(json!({"type": "unknown"})))
            .collect::<Vec<_>>();

        Ok(json!({
            "success": true,
            "content": formatted_content
        }))
    }

    /// Get information about available local tools
    pub fn get_local_tools_info(&self) -> std::collections::HashMap<String, (String, Value)> {
        self.tool_registry.get_all_tools_info()
    }

    /// Get information about available MCP tools from all connected servers
    pub async fn get_mcp_tools_info(
        &self,
    ) -> Result<std::collections::HashMap<String, Vec<rmcp::model::Tool>>> {
        let client_manager = self.mcp_client_manager.read().await;
        client_manager.get_all_tools().await.map_err(Into::into)
    }

    /// Validate tool input against tool schema
    pub async fn validate_tool_input(&self, tool_name: &str, input: &ToolInput) -> ToolResult<()> {
        if self.is_mcp_tool(tool_name) {
            // For MCP tools, we let the MCP server handle validation
            debug!(
                "Skipping validation for MCP tool '{}' - will be validated by MCP server",
                tool_name
            );
            Ok(())
        } else {
            // For local tools, validate via registry
            if let Some(tool) = self.tool_registry.get(tool_name) {
                tool.validate_input(input).await
            } else {
                Err(ToolError::ToolNotFound(tool_name.to_string()))
            }
        }
    }

    /// Check if a tool exists (either locally or via MCP)
    pub async fn tool_exists(&self, tool_name: &str) -> bool {
        if self.is_mcp_tool(tool_name) {
            // Check if we can parse the tool name and have the corresponding MCP server
            if let Ok((server_id, _)) = self.parse_mcp_tool_name(tool_name) {
                let client_manager = self.mcp_client_manager.read().await;
                client_manager.get_client(&server_id).is_some()
            } else {
                false
            }
        } else {
            self.tool_registry.contains(tool_name)
        }
    }

    /// Connect to a new MCP server
    pub async fn connect_mcp_server(&self, server: McpServer) -> Result<()> {
        let mut client_manager = self.mcp_client_manager.write().await;
        client_manager.connect_server(server).await
    }

    /// Disconnect from an MCP server
    pub async fn disconnect_mcp_server(&self, server_name: &str) -> Result<()> {
        let mut client_manager = self.mcp_client_manager.write().await;
        client_manager.disconnect_server(server_name).await
    }

    /// List all connected MCP servers
    pub async fn list_mcp_servers(&self) -> Vec<String> {
        let client_manager = self.mcp_client_manager.read().await;
        client_manager
            .list_clients()
            .into_iter()
            .map(|s| s.to_string())
            .collect()
    }

    /// Create a summary of tool input for error reporting
    fn create_input_summary(&self, params: &Value) -> String {
        match params {
            Value::Object(map) => {
                let keys: Vec<_> = map.keys().collect();
                if keys.len() <= 3 {
                    format!("params: {:?}", keys)
                } else {
                    format!("params: {:?}... ({} total)", &keys[..3], keys.len())
                }
            }
            Value::String(s) => {
                if s.len() <= 50 {
                    format!("string: \"{}\"", s)
                } else {
                    format!("string: \"{}...\" ({} chars)", &s[..47], s.len())
                }
            }
            Value::Array(arr) => format!("array[{}]", arr.len()),
            other => format!("{:?}", other),
        }
    }

    /// Classify tool errors and determine if they're retryable
    fn classify_tool_error(&self, error: &ToolError) -> (Option<String>, bool) {
        match error {
            ToolError::PermissionDenied(_) => (Some("PERMISSION_DENIED".to_string()), false),
            ToolError::InvalidInput(_) => (Some("INVALID_INPUT".to_string()), false),
            ToolError::ValidationFailed(_) => (Some("VALIDATION_FAILED".to_string()), false),
            ToolError::ToolNotFound(_) => (Some("TOOL_NOT_FOUND".to_string()), false),
            ToolError::ExecutionFailed(msg) => {
                // Check if it's a retryable execution failure
                let retry = msg.contains("timeout")
                    || msg.contains("connection")
                    || msg.contains("network")
                    || msg.contains("temporary")
                    || msg.contains("busy");
                (Some("EXECUTION_FAILED".to_string()), retry)
            }
            ToolError::Other(_) => (Some("UNKNOWN_ERROR".to_string()), true), // Unknown errors might be retryable
        }
    }

    /// Check if an MCP error is potentially retryable
    fn is_mcp_error_retryable(&self, error: &crate::errors::AutorunError) -> bool {
        let error_str = error.to_string().to_lowercase();

        // Check for common retryable error patterns
        error_str.contains("connection")
            || error_str.contains("timeout")
            || error_str.contains("network")
            || error_str.contains("unavailable")
            || error_str.contains("busy")
            || error_str.contains("temporary")
            || error_str.contains("reset")
            || error_str.contains("broken pipe")
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::mcp::client::McpClientManager;
    use crate::tools::registry::ToolRegistry;
    use serde_json::json;
    use std::collections::HashMap;

    #[tokio::test]
    async fn test_tool_name_parsing() {
        let registry = Arc::new(ToolRegistry::new());
        let mcp_manager = Arc::new(RwLock::new(McpClientManager::new()));
        let executor = ToolExecutor::new(registry, mcp_manager);

        // Test MCP tool name parsing
        assert!(executor.is_mcp_tool("mcp::server::tool"));
        assert!(executor.is_mcp_tool("mcp__server__tool"));
        assert!(executor.is_mcp_tool("ide::tool"));
        assert!(!executor.is_mcp_tool("local_tool"));

        // Test parsing
        let (server, tool) = executor
            .parse_mcp_tool_name("mcp::test_server::test_tool")
            .unwrap();
        assert_eq!(server, "test_server");
        assert_eq!(tool, "test_tool");

        let (server, tool) = executor
            .parse_mcp_tool_name("mcp__test_server__test_tool")
            .unwrap();
        assert_eq!(server, "test_server");
        assert_eq!(tool, "test_tool");

        let (server, tool) = executor
            .parse_mcp_tool_name("ide::get_diagnostics")
            .unwrap();
        assert_eq!(server, "ide");
        assert_eq!(tool, "get_diagnostics");
    }

    #[test]
    fn test_tool_execution_result_conversion() {
        let success_output = ToolOutput::Success(json!({"result": "success"}));
        let result: ToolExecutionResult = success_output.into();

        match result {
            ToolExecutionResult::Success { output } => {
                assert_eq!(output, json!({"result": "success"}));
            }
            _ => panic!("Expected Success result"),
        }

        let error_output = ToolOutput::Error {
            error: "Test error".to_string(),
            details: Some(json!({"code": 500})),
        };
        let result: ToolExecutionResult = error_output.into();

        match result {
            ToolExecutionResult::Failure { error_message, .. } => {
                assert!(error_message.contains("Test error"));
                assert!(error_message.contains("500"));
            }
            _ => panic!("Expected Failure result"),
        }
    }
}
