// Tools module - defines tool trait and implementations

pub mod context;
pub mod executor;
pub mod file_io;
pub mod local_tools;
pub mod permissions;
pub mod registry;

use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::collections::HashMap;
use thiserror::Error;

pub use context::ExecutionContext;
pub use executor::{ToolCallRequest, ToolExecutionContext, ToolExecutionResult, ToolExecutor};
pub use file_io::{ReadTool, WriteTool};
pub use permissions::{PermissionAction, PermissionManager, PermissionRule, PermissionScope};
pub use registry::ToolRegistry;

#[derive(Error, Debug)]
pub enum ToolError {
    #[error("Invalid input: {0}")]
    InvalidInput(String),

    #[error("Permission denied: {0}")]
    PermissionDenied(String),

    #[error("Execution failed: {0}")]
    ExecutionFailed(String),

    #[error("Tool not found: {0}")]
    ToolNotFound(String),

    #[error("Validation failed: {0}")]
    ValidationFailed(String),

    #[error(transparent)]
    Other(#[from] anyhow::Error),
}

pub type ToolResult<T> = Result<T, ToolError>;

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum ToolInput {
    Object(HashMap<String, Value>),
    Array(Vec<Value>),
    String(String),
    Number(f64),
    Bool(bool),
    Null,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum ToolOutput {
    Success(Value),
    Error {
        error: String,
        details: Option<Value>,
    },
}

#[async_trait]
pub trait Tool: Send + Sync {
    fn name(&self) -> &str;
    fn description(&self) -> &str;

    /// Returns the JSON Schema for the tool's input parameters
    fn input_schema_json(&self) -> Value;

    /// Validates the input against the tool's schema
    async fn validate_input(&self, input: &ToolInput) -> ToolResult<()>;

    /// Checks if the tool has required permissions in the given context
    async fn check_permissions(&self, context: &ExecutionContext) -> ToolResult<()>;

    /// Executes the tool with the given input and context
    async fn execute(
        &self,
        input: ToolInput,
        context: &mut ExecutionContext,
    ) -> ToolResult<ToolOutput>;
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolCall {
    pub name: String,
    pub input: ToolInput,
}

impl From<ToolInput> for Value {
    fn from(input: ToolInput) -> Self {
        match input {
            ToolInput::Object(map) => Value::Object(map.into_iter().collect()),
            ToolInput::Array(vec) => Value::Array(vec),
            ToolInput::String(s) => Value::String(s),
            ToolInput::Number(n) => Value::Number(
                serde_json::Number::from_f64(n).unwrap_or_else(|| serde_json::Number::from(0)),
            ),
            ToolInput::Bool(b) => Value::Bool(b),
            ToolInput::Null => Value::Null,
        }
    }
}

impl From<Value> for ToolInput {
    fn from(value: Value) -> Self {
        match value {
            Value::Object(map) => ToolInput::Object(map.into_iter().collect()),
            Value::Array(vec) => ToolInput::Array(vec),
            Value::String(s) => ToolInput::String(s),
            Value::Number(n) => ToolInput::Number(n.as_f64().unwrap_or(0.0)),
            Value::Bool(b) => ToolInput::Bool(b),
            Value::Null => ToolInput::Null,
        }
    }
}
