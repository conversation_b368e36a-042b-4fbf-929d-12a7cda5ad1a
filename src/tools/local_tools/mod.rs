//! Local Tools Implementation
//!
//! This module implements the comprehensive set of local tools as described in the
//! Local Tools documentation. These tools provide essential file operations,
//! directory listing, pattern matching, shell execution, and todo management.

pub mod bash_tool;
pub mod edit_tool;
pub mod glob_tool;
pub mod grep_tool;
pub mod list_tool;
pub mod todo_tools;

// Re-export all local tools for easy access
pub use bash_tool::BashTool;
pub use edit_tool::{EditTool, MultiEditTool};
pub use glob_tool::GlobTool;
pub use grep_tool::GrepTool;
pub use list_tool::ListTool;
pub use todo_tools::{TodoReadTool, TodoWriteTool};

use crate::tools::registry::ToolRegistry;
use crate::tools::ToolResult;

/// Register all local tools in the provided registry
pub fn register_all_local_tools(registry: &ToolRegistry) -> ToolResult<()> {
    // Core file I/O tools (already implemented)
    registry.register(crate::tools::file_io::ReadTool::new())?;
    registry.register(crate::tools::file_io::WriteTool::new())?;

    // Directory and file management
    registry.register(ListTool::new())?;
    registry.register(GlobTool::new())?;
    registry.register(GrepTool::new())?;

    // File editing
    registry.register(EditTool::new())?;
    registry.register(MultiEditTool::new())?;

    // Shell execution
    registry.register(BashTool::new())?;

    // Todo management
    registry.register(TodoReadTool::new())?;
    registry.register(TodoWriteTool::new())?;

    Ok(())
}
