use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::fs::{self, OpenOptions};
use std::io::{<PERSON><PERSON><PERSON>K<PERSON>, Write};
use std::path::PathBuf;
use tracing::debug;
use uuid::Uuid;

use crate::tools::context::ExecutionContext;
use crate::tools::{Tool, ToolError, ToolInput, ToolOutput, ToolResult};

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "PascalCase")]
pub enum TodoStatus {
    Pending,
    InProgress,
    Completed,
    Cancelled,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "PascalCase")]
pub enum TodoPriority {
    High,
    Medium,
    Low,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq, Eq)]
pub struct TodoItem {
    pub id: String,
    pub content: String,
    pub status: TodoStatus,
    pub priority: TodoPriority,
}

/// Get the path to the todo file for the current session
fn get_todo_file_path(context: &ExecutionContext) -> PathBuf {
    // Create todos directory in the working directory under .claude_cache
    let cache_dir = context
        .working_directory
        .join(".claude_cache")
        .join("todos");
    cache_dir.join(format!("{}.json", context.session_id))
}

/// Read todos from file
fn read_todos_from_file(file_path: &PathBuf) -> ToolResult<Vec<TodoItem>> {
    match fs::read_to_string(file_path) {
        Ok(content) => {
            if content.trim().is_empty() {
                Ok(Vec::new())
            } else {
                serde_json::from_str(&content).map_err(|e| {
                    ToolError::ExecutionFailed(format!("Failed to parse todos: {}", e))
                })
            }
        }
        Err(e) if e.kind() == ErrorKind::NotFound => Ok(Vec::new()), // No file means no todos
        Err(e) => Err(ToolError::ExecutionFailed(format!(
            "Failed to read todo file: {}",
            e
        ))),
    }
}

/// Write todos to file
fn write_todos_to_file(file_path: &PathBuf, todos: &[TodoItem]) -> ToolResult<()> {
    // Create parent directories if needed
    if let Some(parent) = file_path.parent() {
        if !parent.exists() {
            fs::create_dir_all(parent).map_err(|e| {
                ToolError::ExecutionFailed(format!("Failed to create todo directory: {}", e))
            })?;
        }
    }

    let mut file = OpenOptions::new()
        .write(true)
        .create(true)
        .truncate(true)
        .open(file_path)
        .map_err(|e| {
            ToolError::ExecutionFailed(format!("Failed to open todo file for writing: {}", e))
        })?;

    let json_content = serde_json::to_string_pretty(todos)
        .map_err(|e| ToolError::ExecutionFailed(format!("Failed to serialize todos: {}", e)))?;

    file.write_all(json_content.as_bytes())
        .map_err(|e| ToolError::ExecutionFailed(format!("Failed to write todos: {}", e)))?;

    Ok(())
}

pub struct TodoReadTool;

impl TodoReadTool {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait]
impl Tool for TodoReadTool {
    fn name(&self) -> &str {
        "TodoRead"
    }

    fn description(&self) -> &str {
        "Reads the current todo list for the session"
    }

    fn input_schema_json(&self) -> Value {
        json!({
            "type": "object",
            "properties": {},
            "description": "No input is required. Leave blank or provide an empty object.",
            "additionalProperties": false
        })
    }

    async fn validate_input(&self, input: &ToolInput) -> ToolResult<()> {
        match input {
            ToolInput::Object(_) => Ok(()),
            ToolInput::Null => Ok(()),
            _ => Err(ToolError::InvalidInput(
                "Input must be an object or null".to_string(),
            )),
        }
    }

    async fn check_permissions(&self, context: &ExecutionContext) -> ToolResult<()> {
        if !context.has_permission("read") {
            return Err(ToolError::PermissionDenied(
                "Read permission not granted".to_string(),
            ));
        }
        Ok(())
    }

    async fn execute(
        &self,
        _input: ToolInput,
        context: &mut ExecutionContext,
    ) -> ToolResult<ToolOutput> {
        let todo_file = get_todo_file_path(context);
        debug!("Reading todos from: {:?}", todo_file);

        let todos = read_todos_from_file(&todo_file)?;

        Ok(ToolOutput::Success(json!({
            "todos": todos,
            "total_count": todos.len(),
            "pending_count": todos.iter().filter(|t| t.status == TodoStatus::Pending).count(),
            "in_progress_count": todos.iter().filter(|t| t.status == TodoStatus::InProgress).count(),
            "completed_count": todos.iter().filter(|t| t.status == TodoStatus::Completed).count(),
            "cancelled_count": todos.iter().filter(|t| t.status == TodoStatus::Cancelled).count(),
            "session_id": context.session_id
        })))
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct TodoWriteToolInput {
    todos: Vec<TodoItem>,
}

pub struct TodoWriteTool;

impl TodoWriteTool {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait]
impl Tool for TodoWriteTool {
    fn name(&self) -> &str {
        "TodoWrite"
    }

    fn description(&self) -> &str {
        "Updates the todo list for the session. Provide the complete new list."
    }

    fn input_schema_json(&self) -> Value {
        json!({
            "type": "object",
            "properties": {
                "todos": {
                    "type": "array",
                    "description": "The complete updated list of todo items",
                    "items": {
                        "type": "object",
                        "properties": {
                            "id": {
                                "type": "string",
                                "description": "Unique ID of the todo item. Generate using UUID v4 for new items."
                            },
                            "content": {
                                "type": "string",
                                "description": "The content of the todo item."
                            },
                            "status": {
                                "type": "string",
                                "enum": ["Pending", "InProgress", "Completed", "Cancelled"],
                                "description": "The status of the todo item"
                            },
                            "priority": {
                                "type": "string",
                                "enum": ["High", "Medium", "Low"],
                                "description": "The priority of the todo item"
                            }
                        },
                        "required": ["id", "content", "status", "priority"],
                        "additionalProperties": false
                    }
                }
            },
            "required": ["todos"],
            "additionalProperties": false
        })
    }

    async fn validate_input(&self, input: &ToolInput) -> ToolResult<()> {
        match input {
            ToolInput::Object(map) => {
                if !map.contains_key("todos") {
                    return Err(ToolError::InvalidInput(
                        "Missing required field: todos".to_string(),
                    ));
                }

                if let Some(Value::Array(todos)) = map.get("todos") {
                    for (i, todo) in todos.iter().enumerate() {
                        if let Some(todo_obj) = todo.as_object() {
                            // Check required fields
                            for field in ["id", "content", "status", "priority"] {
                                if !todo_obj.contains_key(field) {
                                    return Err(ToolError::InvalidInput(format!(
                                        "Todo {}: missing required field: {}",
                                        i, field
                                    )));
                                }
                            }

                            // Validate field types
                            if let Some(Value::String(_)) = todo_obj.get("id") {
                                // Valid
                            } else {
                                return Err(ToolError::InvalidInput(format!(
                                    "Todo {}: id must be a string",
                                    i
                                )));
                            }

                            if let Some(Value::String(_)) = todo_obj.get("content") {
                                // Valid
                            } else {
                                return Err(ToolError::InvalidInput(format!(
                                    "Todo {}: content must be a string",
                                    i
                                )));
                            }

                            // Validate status enum
                            if let Some(Value::String(status)) = todo_obj.get("status") {
                                if !matches!(
                                    status.as_str(),
                                    "Pending" | "InProgress" | "Completed" | "Cancelled"
                                ) {
                                    return Err(ToolError::InvalidInput(format!(
                                        "Todo {}: invalid status '{}'. Must be one of: Pending, InProgress, Completed, Cancelled",
                                        i, status
                                    )));
                                }
                            } else {
                                return Err(ToolError::InvalidInput(format!(
                                    "Todo {}: status must be a string",
                                    i
                                )));
                            }

                            // Validate priority enum
                            if let Some(Value::String(priority)) = todo_obj.get("priority") {
                                if !matches!(priority.as_str(), "High" | "Medium" | "Low") {
                                    return Err(ToolError::InvalidInput(format!(
                                        "Todo {}: invalid priority '{}'. Must be one of: High, Medium, Low",
                                        i, priority
                                    )));
                                }
                            } else {
                                return Err(ToolError::InvalidInput(format!(
                                    "Todo {}: priority must be a string",
                                    i
                                )));
                            }
                        } else {
                            return Err(ToolError::InvalidInput(format!(
                                "Todo {} must be an object",
                                i
                            )));
                        }
                    }
                } else {
                    return Err(ToolError::InvalidInput(
                        "todos must be an array".to_string(),
                    ));
                }

                Ok(())
            }
            _ => Err(ToolError::InvalidInput(
                "Input must be an object".to_string(),
            )),
        }
    }

    async fn check_permissions(&self, context: &ExecutionContext) -> ToolResult<()> {
        if !context.has_permission("write") {
            return Err(ToolError::PermissionDenied(
                "Write permission not granted".to_string(),
            ));
        }
        Ok(())
    }

    async fn execute(
        &self,
        input: ToolInput,
        context: &mut ExecutionContext,
    ) -> ToolResult<ToolOutput> {
        let params: TodoWriteToolInput = match input {
            ToolInput::Object(map) => {
                let json_value = Value::Object(map.into_iter().collect());
                serde_json::from_value(json_value)
                    .map_err(|e| ToolError::InvalidInput(format!("Failed to parse input: {}", e)))?
            }
            _ => {
                return Err(ToolError::InvalidInput(
                    "Input must be an object".to_string(),
                ))
            }
        };

        let todo_file = get_todo_file_path(context);
        debug!("Writing todos to: {:?}", todo_file);

        // Ensure all items have valid IDs, generate if missing or empty
        let processed_todos: Vec<TodoItem> = params
            .todos
            .into_iter()
            .map(|mut item| {
                if item.id.is_empty() {
                    item.id = Uuid::new_v4().to_string();
                }
                item
            })
            .collect();

        write_todos_to_file(&todo_file, &processed_todos)?;

        Ok(ToolOutput::Success(json!({
            "message": "Todo list updated successfully",
            "todos_saved": processed_todos.len(),
            "session_id": context.session_id,
            "file_path": todo_file.display().to_string()
        })))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::Config;
    use crate::tools::context::Permission;
    use std::collections::HashMap;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_todo_read_empty() {
        let temp_dir = TempDir::new().unwrap();

        let tool = TodoReadTool::new();
        let config = Config::default();
        let mut context = ExecutionContext::with_config(
            temp_dir.path().to_path_buf(),
            config,
            "test-session".to_string(),
        )
        .with_permissions(Permission {
            read: true,
            ..Default::default()
        });

        let input = ToolInput::Object(HashMap::new());
        let result = tool.execute(input, &mut context).await.unwrap();

        match result {
            ToolOutput::Success(value) => {
                let todos = value["todos"].as_array().unwrap();
                assert_eq!(todos.len(), 0);
                assert_eq!(value["total_count"], 0);
            }
            _ => panic!("Expected Success output"),
        }
    }

    #[tokio::test]
    async fn test_todo_write_and_read() {
        let temp_dir = TempDir::new().unwrap();

        let write_tool = TodoWriteTool::new();
        let read_tool = TodoReadTool::new();
        let config = Config::default();
        let mut context = ExecutionContext::with_config(
            temp_dir.path().to_path_buf(),
            config,
            "test-session".to_string(),
        )
        .with_permissions(Permission {
            read: true,
            write: true,
            ..Default::default()
        });

        // Write todos
        let todo_id = Uuid::new_v4().to_string();
        let input = ToolInput::Object(HashMap::from([(
            "todos".to_string(),
            json!([
                {
                    "id": todo_id,
                    "content": "Test todo item",
                    "status": "Pending",
                    "priority": "High"
                }
            ]),
        )]));

        let write_result = write_tool.execute(input, &mut context).await.unwrap();
        match write_result {
            ToolOutput::Success(value) => {
                assert_eq!(value["todos_saved"], 1);
            }
            _ => panic!("Expected Success output"),
        }

        // Read todos back
        let read_input = ToolInput::Object(HashMap::new());
        let read_result = read_tool.execute(read_input, &mut context).await.unwrap();

        match read_result {
            ToolOutput::Success(value) => {
                let todos = value["todos"].as_array().unwrap();
                assert_eq!(todos.len(), 1);
                assert_eq!(value["total_count"], 1);
                assert_eq!(value["pending_count"], 1);

                let todo = &todos[0];
                assert_eq!(todo["content"], "Test todo item");
                assert_eq!(todo["status"], "Pending");
                assert_eq!(todo["priority"], "High");
                assert_eq!(todo["id"], todo_id);
            }
            _ => panic!("Expected Success output"),
        }
    }

    #[tokio::test]
    async fn test_todo_auto_generate_id() {
        let temp_dir = TempDir::new().unwrap();

        let tool = TodoWriteTool::new();
        let config = Config::default();
        let mut context = ExecutionContext::with_config(
            temp_dir.path().to_path_buf(),
            config,
            "test-session".to_string(),
        )
        .with_permissions(Permission {
            write: true,
            ..Default::default()
        });

        // Write todo with empty ID
        let input = ToolInput::Object(HashMap::from([(
            "todos".to_string(),
            json!([
                {
                    "id": "",
                    "content": "Test todo with auto ID",
                    "status": "Pending",
                    "priority": "Medium"
                }
            ]),
        )]));

        let result = tool.execute(input, &mut context).await.unwrap();
        match result {
            ToolOutput::Success(value) => {
                assert_eq!(value["todos_saved"], 1);

                // Read the file to verify ID was generated
                let todo_file = get_todo_file_path(&context);
                let todos = read_todos_from_file(&todo_file).unwrap();
                assert_eq!(todos.len(), 1);
                assert!(!todos[0].id.is_empty());
                // Should be a valid UUID
                assert!(Uuid::parse_str(&todos[0].id).is_ok());
            }
            _ => panic!("Expected Success output"),
        }
    }
}
