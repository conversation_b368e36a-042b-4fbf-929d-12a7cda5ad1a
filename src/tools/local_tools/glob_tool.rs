use async_trait::async_trait;
use glob::{glob_with, MatchOptions};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::path::{Path, PathBuf};
use tracing::debug;

use crate::tools::context::ExecutionContext;
use crate::tools::{Tool, ToolError, ToolInput, ToolOutput, ToolResult};

const MAX_MATCHES: usize = 1000;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
struct GlobToolInput {
    pattern: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    path: Option<String>,
}

pub struct GlobTool;

impl GlobTool {
    pub fn new() -> Self {
        Self
    }
}

/// Resolve path relative to working directory
fn resolve_path(working_dir: &Path, path: &str) -> PathBuf {
    let path_buf = PathBuf::from(path);
    if path_buf.is_absolute() {
        path_buf
    } else {
        working_dir.join(path_buf)
    }
}

/// Get relative path for display
fn display_path(base: &Path, absolute_path: &Path) -> String {
    pathdiff::diff_paths(absolute_path, base)
        .map(|p| p.to_string_lossy().into_owned())
        .unwrap_or_else(|| absolute_path.to_string_lossy().into_owned())
}

#[async_trait]
impl Tool for GlobTool {
    fn name(&self) -> &str {
        "Glob"
    }

    fn description(&self) -> &str {
        "Finds files matching a glob pattern, supporting wildcards like *, ?, and **"
    }

    fn input_schema_json(&self) -> Value {
        json!({
            "type": "object",
            "properties": {
                "pattern": {
                    "type": "string",
                    "description": "The glob pattern to match (e.g., \"**/*.rs\", \"*.txt\")"
                },
                "path": {
                    "type": "string",
                    "description": "Optional directory to search in. Defaults to current working directory."
                }
            },
            "required": ["pattern"],
            "additionalProperties": false
        })
    }

    async fn validate_input(&self, input: &ToolInput) -> ToolResult<()> {
        match input {
            ToolInput::Object(map) => {
                if !map.contains_key("pattern") {
                    return Err(ToolError::InvalidInput(
                        "Missing required field: pattern".to_string(),
                    ));
                }

                // Validate pattern is a string
                if let Some(Value::String(_)) = map.get("pattern") {
                    // Valid
                } else {
                    return Err(ToolError::InvalidInput(
                        "pattern must be a string".to_string(),
                    ));
                }

                // Validate path if present
                if let Some(path_val) = map.get("path") {
                    if !path_val.is_string() {
                        return Err(ToolError::InvalidInput("path must be a string".to_string()));
                    }
                }

                Ok(())
            }
            _ => Err(ToolError::InvalidInput(
                "Input must be an object".to_string(),
            )),
        }
    }

    async fn check_permissions(&self, context: &ExecutionContext) -> ToolResult<()> {
        if !context.has_permission("read") {
            return Err(ToolError::PermissionDenied(
                "Read permission not granted".to_string(),
            ));
        }
        Ok(())
    }

    async fn execute(
        &self,
        input: ToolInput,
        context: &mut ExecutionContext,
    ) -> ToolResult<ToolOutput> {
        let params: GlobToolInput = match input {
            ToolInput::Object(map) => {
                let json_value = Value::Object(map.into_iter().collect());
                serde_json::from_value(json_value)
                    .map_err(|e| ToolError::InvalidInput(format!("Failed to parse input: {}", e)))?
            }
            _ => {
                return Err(ToolError::InvalidInput(
                    "Input must be an object".to_string(),
                ))
            }
        };

        let base_path = params
            .path
            .map(|p| resolve_path(&context.working_directory, &p))
            .unwrap_or_else(|| context.working_directory.clone());

        debug!(
            "Globbing pattern '{}' in path: {:?}",
            params.pattern, base_path
        );

        // Construct the full pattern
        let full_pattern = if params.pattern.starts_with('/') {
            // Absolute pattern
            params.pattern.clone()
        } else {
            // Relative pattern - join with base path
            base_path
                .join(&params.pattern)
                .to_string_lossy()
                .into_owned()
        };

        debug!("Full glob pattern: {}", full_pattern);

        // Set up glob options
        let options = MatchOptions {
            case_sensitive: false, // Case-insensitive matching for cross-platform compatibility
            require_literal_separator: false,
            require_literal_leading_dot: false,
        };

        let mut found_files = Vec::new();
        let mut match_count = 0;

        match glob_with(&full_pattern, options) {
            Ok(paths) => {
                for entry in paths {
                    if match_count >= MAX_MATCHES {
                        found_files.push("... [TRUNCATED: Too many matches]".to_string());
                        break;
                    }

                    match entry {
                        Ok(path) => {
                            // Return paths relative to the working directory for consistency
                            let display_path_str = display_path(&context.working_directory, &path);
                            found_files.push(display_path_str);
                            match_count += 1;
                        }
                        Err(e) => {
                            debug!("Glob entry error: {}", e);
                            // Continue processing other matches
                        }
                    }
                }
            }
            Err(e) => {
                return Err(ToolError::ExecutionFailed(format!(
                    "Invalid glob pattern '{}': {}",
                    params.pattern, e
                )));
            }
        }

        // Sort for consistent output
        found_files.sort();

        Ok(ToolOutput::Success(json!({
            "matches": found_files,
            "pattern": params.pattern,
            "search_path": base_path.display().to_string(),
            "match_count": match_count,
            "truncated": match_count >= MAX_MATCHES
        })))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::Config;
    use crate::tools::context::Permission;
    use std::collections::HashMap;
    use std::fs;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_glob_tool() {
        let temp_dir = TempDir::new().unwrap();

        // Create test files
        fs::create_dir_all(temp_dir.path().join("src")).unwrap();
        fs::write(temp_dir.path().join("src/main.rs"), "fn main() {}").unwrap();
        fs::write(temp_dir.path().join("src/lib.rs"), "// lib").unwrap();
        fs::write(temp_dir.path().join("README.md"), "# Test").unwrap();
        fs::write(temp_dir.path().join("Cargo.toml"), "[package]").unwrap();

        let tool = GlobTool::new();
        let config = Config::default();
        let mut context = ExecutionContext::with_config(
            temp_dir.path().to_path_buf(),
            config,
            "test-session".to_string(),
        )
        .with_permissions(Permission {
            read: true,
            ..Default::default()
        });

        // Test *.rs pattern
        let input = ToolInput::Object(HashMap::from([("pattern".to_string(), json!("**/*.rs"))]));

        let result = tool.execute(input, &mut context).await.unwrap();

        match result {
            ToolOutput::Success(value) => {
                let matches = value["matches"].as_array().unwrap();
                assert_eq!(matches.len(), 2);

                let match_strings: Vec<&str> =
                    matches.iter().map(|v| v.as_str().unwrap()).collect();

                assert!(match_strings.contains(&"src/main.rs"));
                assert!(match_strings.contains(&"src/lib.rs"));
                assert_eq!(value["match_count"], 2);
            }
            _ => panic!("Expected Success output"),
        }
    }

    #[tokio::test]
    async fn test_glob_tool_with_path() {
        let temp_dir = TempDir::new().unwrap();

        // Create test structure
        let src_dir = temp_dir.path().join("src");
        fs::create_dir_all(&src_dir).unwrap();
        fs::write(src_dir.join("main.rs"), "fn main() {}").unwrap();

        let tool = GlobTool::new();
        let config = Config::default();
        let mut context = ExecutionContext::with_config(
            temp_dir.path().to_path_buf(),
            config,
            "test-session".to_string(),
        )
        .with_permissions(Permission {
            read: true,
            ..Default::default()
        });

        // Test with specific path
        let input = ToolInput::Object(HashMap::from([
            ("pattern".to_string(), json!("*.rs")),
            ("path".to_string(), json!("src")),
        ]));

        let result = tool.execute(input, &mut context).await.unwrap();

        match result {
            ToolOutput::Success(value) => {
                let matches = value["matches"].as_array().unwrap();
                assert_eq!(matches.len(), 1);
                assert_eq!(matches[0].as_str().unwrap(), "src/main.rs");
            }
            _ => panic!("Expected Success output"),
        }
    }
}
