use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::process::Stdio;
use std::time::{Duration, Instant};
use tokio::process::Command as TokioCommand;
use tracing::{debug, warn};

use crate::tools::context::ExecutionContext;
use crate::tools::{Tool, ToolError, ToolInput, ToolOutput, ToolResult};

const DEFAULT_TIMEOUT_MS: u64 = 60000; // 60 seconds
const MAX_OUTPUT_SIZE: usize = 100_000; // 100KB

#[derive(Debug, Clone, Serialize, Deserialize)]
struct BashToolInput {
    command: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    timeout_ms: Option<u64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    sandbox: Option<bool>,
    #[serde(skip_serializing_if = "Option::is_none")]
    description: Option<String>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
struct BashToolOutput {
    stdout: String,
    stderr: String,
    exit_code: Option<i32>,
    timed_out: bool,
    execution_time_ms: u64,
    command: String,
}

pub struct BashTool;

impl BashTool {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait]
impl Tool for BashTool {
    fn name(&self) -> &str {
        "Bash"
    }

    fn description(&self) -> &str {
        "Executes shell commands with optional timeout and sandboxing. Use with caution as it can modify the system."
    }

    fn input_schema_json(&self) -> Value {
        json!({
            "type": "object",
            "properties": {
                "command": {
                    "type": "string",
                    "description": "The shell command to execute"
                },
                "timeout_ms": {
                    "type": "integer",
                    "description": "Optional timeout in milliseconds (default: 60000)",
                    "minimum": 1000,
                    "maximum": 600000
                },
                "sandbox": {
                    "type": "boolean",
                    "description": "If true, attempts to run in a restricted environment (experimental)"
                },
                "description": {
                    "type": "string",
                    "description": "Optional description of what the command does for logging"
                }
            },
            "required": ["command"],
            "additionalProperties": false
        })
    }

    async fn validate_input(&self, input: &ToolInput) -> ToolResult<()> {
        match input {
            ToolInput::Object(map) => {
                if !map.contains_key("command") {
                    return Err(ToolError::InvalidInput(
                        "Missing required field: command".to_string(),
                    ));
                }

                // Validate command is a string
                if let Some(Value::String(cmd)) = map.get("command") {
                    if cmd.trim().is_empty() {
                        return Err(ToolError::InvalidInput(
                            "command cannot be empty".to_string(),
                        ));
                    }
                } else {
                    return Err(ToolError::InvalidInput(
                        "command must be a string".to_string(),
                    ));
                }

                // Validate timeout if present
                if let Some(timeout_val) = map.get("timeout_ms") {
                    if let Some(timeout) = timeout_val.as_u64() {
                        if timeout < 1000 || timeout > 600000 {
                            return Err(ToolError::InvalidInput(
                                "timeout_ms must be between 1000 and 600000".to_string(),
                            ));
                        }
                    } else {
                        return Err(ToolError::InvalidInput(
                            "timeout_ms must be a number".to_string(),
                        ));
                    }
                }

                // Validate other optional fields
                if let Some(sandbox_val) = map.get("sandbox") {
                    if !sandbox_val.is_boolean() {
                        return Err(ToolError::InvalidInput(
                            "sandbox must be a boolean".to_string(),
                        ));
                    }
                }

                if let Some(desc_val) = map.get("description") {
                    if !desc_val.is_string() {
                        return Err(ToolError::InvalidInput(
                            "description must be a string".to_string(),
                        ));
                    }
                }

                Ok(())
            }
            _ => Err(ToolError::InvalidInput(
                "Input must be an object".to_string(),
            )),
        }
    }

    async fn check_permissions(&self, context: &ExecutionContext) -> ToolResult<()> {
        if !context.has_permission("execute") {
            return Err(ToolError::PermissionDenied(
                "Execute permission not granted".to_string(),
            ));
        }
        Ok(())
    }

    async fn execute(
        &self,
        input: ToolInput,
        context: &mut ExecutionContext,
    ) -> ToolResult<ToolOutput> {
        let params: BashToolInput = match input {
            ToolInput::Object(map) => {
                let json_value = Value::Object(map.into_iter().collect());
                serde_json::from_value(json_value)
                    .map_err(|e| ToolError::InvalidInput(format!("Failed to parse input: {}", e)))?
            }
            _ => {
                return Err(ToolError::InvalidInput(
                    "Input must be an object".to_string(),
                ))
            }
        };

        let timeout_duration =
            Duration::from_millis(params.timeout_ms.unwrap_or(DEFAULT_TIMEOUT_MS));
        let sandbox_mode = params.sandbox.unwrap_or(false);

        debug!(
            "Executing command: '{}' (timeout: {:?}, sandbox: {})",
            params.command, timeout_duration, sandbox_mode
        );

        if let Some(ref description) = params.description {
            debug!("Command description: {}", description);
        }

        let start_time = Instant::now();

        // Execute the command
        let result = if sandbox_mode {
            warn!("Sandbox mode requested but not fully implemented - running with basic restrictions");
            self.execute_sandboxed(&params.command, timeout_duration, context)
                .await
        } else {
            self.execute_normal(&params.command, timeout_duration, context)
                .await
        };

        let execution_time = start_time.elapsed();

        match result {
            Ok((stdout, stderr, exit_code, timed_out)) => {
                let output = BashToolOutput {
                    stdout: self.truncate_output(stdout),
                    stderr: self.truncate_output(stderr),
                    exit_code,
                    timed_out,
                    execution_time_ms: execution_time.as_millis() as u64,
                    command: params.command.clone(),
                };

                Ok(ToolOutput::Success(json!(output)))
            }
            Err(e) => Err(e),
        }
    }
}

impl BashTool {
    /// Execute command normally
    async fn execute_normal(
        &self,
        command: &str,
        timeout: Duration,
        context: &ExecutionContext,
    ) -> ToolResult<(String, String, Option<i32>, bool)> {
        let shell = if cfg!(windows) { "cmd" } else { "sh" };
        let shell_arg = if cfg!(windows) { "/C" } else { "-c" };

        let mut cmd = TokioCommand::new(shell);
        cmd.arg(shell_arg)
            .arg(command)
            .current_dir(&context.working_directory)
            .stdout(Stdio::piped())
            .stderr(Stdio::piped());

        // Execute with timeout
        match tokio::time::timeout(timeout, cmd.output()).await {
            Ok(Ok(output)) => {
                let stdout = String::from_utf8_lossy(&output.stdout).to_string();
                let stderr = String::from_utf8_lossy(&output.stderr).to_string();
                let exit_code = output.status.code();
                Ok((stdout, stderr, exit_code, false))
            }
            Ok(Err(e)) => Err(ToolError::ExecutionFailed(format!(
                "Failed to execute command: {}",
                e
            ))),
            Err(_) => {
                // Timeout occurred
                Ok((String::new(), "Command timed out".to_string(), None, true))
            }
        }
    }

    /// Execute command with basic sandboxing (experimental)
    async fn execute_sandboxed(
        &self,
        command: &str,
        timeout: Duration,
        context: &ExecutionContext,
    ) -> ToolResult<(String, String, Option<i32>, bool)> {
        // Basic sandboxing approach:
        // 1. Set environment variables to restrict access
        // 2. Use a restricted PATH
        // 3. Run with lower privileges if possible

        let shell = if cfg!(windows) { "cmd" } else { "sh" };
        let shell_arg = if cfg!(windows) { "/C" } else { "-c" };

        let mut cmd = TokioCommand::new(shell);
        cmd.arg(shell_arg)
            .arg(command)
            .current_dir(&context.working_directory)
            .stdout(Stdio::piped())
            .stderr(Stdio::piped());

        // Restrict environment for sandboxing
        cmd.env_clear();
        cmd.env("PATH", "/usr/bin:/bin"); // Minimal PATH on Unix
        cmd.env("HOME", "/tmp"); // Fake home directory
        cmd.env("USER", "sandbox");
        cmd.env("SHELL", shell);

        // Additional security measures could be added here:
        // - Use systemd-run with restrictions
        // - Use firejail if available
        // - Use Docker/podman containers
        // - Use namespace isolation

        // Execute with timeout
        match tokio::time::timeout(timeout, cmd.output()).await {
            Ok(Ok(output)) => {
                let stdout = String::from_utf8_lossy(&output.stdout).to_string();
                let stderr = String::from_utf8_lossy(&output.stderr).to_string();
                let exit_code = output.status.code();
                Ok((stdout, stderr, exit_code, false))
            }
            Ok(Err(e)) => {
                // Check if the error is due to sandboxing restrictions
                let error_msg = e.to_string();
                if error_msg.contains("Permission denied") || error_msg.contains("No such file") {
                    // Retry without sandbox if it's a permission issue
                    warn!(
                        "Sandboxed execution failed, retrying without sandbox: {}",
                        e
                    );
                    return self.execute_normal(command, timeout, context).await;
                }
                Err(ToolError::ExecutionFailed(format!(
                    "Failed to execute sandboxed command: {}",
                    e
                )))
            }
            Err(_) => {
                // Timeout occurred
                Ok((String::new(), "Command timed out".to_string(), None, true))
            }
        }
    }

    /// Truncate output to prevent excessive memory usage
    fn truncate_output(&self, mut output: String) -> String {
        if output.len() > MAX_OUTPUT_SIZE {
            output.truncate(MAX_OUTPUT_SIZE);
            output.push_str("\n... [OUTPUT TRUNCATED]");
        }
        output
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::Config;
    use crate::tools::context::Permission;
    use std::collections::HashMap;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_bash_tool_simple_command() {
        let temp_dir = TempDir::new().unwrap();

        let tool = BashTool::new();
        let config = Config::default();
        let mut context = ExecutionContext::with_config(
            temp_dir.path().to_path_buf(),
            config,
            "test-session".to_string(),
        )
        .with_permissions(Permission {
            execute: true,
            ..Default::default()
        });

        let command = if cfg!(windows) {
            "echo Hello"
        } else {
            "echo 'Hello'"
        };
        let input = ToolInput::Object(HashMap::from([("command".to_string(), json!(command))]));

        let result = tool.execute(input, &mut context).await.unwrap();

        match result {
            ToolOutput::Success(value) => {
                let output: BashToolOutput = serde_json::from_value(value).unwrap();
                assert!(output.stdout.contains("Hello"));
                assert_eq!(output.exit_code, Some(0));
                assert!(!output.timed_out);
            }
            _ => panic!("Expected Success output"),
        }
    }

    #[tokio::test]
    async fn test_bash_tool_with_timeout() {
        let temp_dir = TempDir::new().unwrap();

        let tool = BashTool::new();
        let config = Config::default();
        let mut context = ExecutionContext::with_config(
            temp_dir.path().to_path_buf(),
            config,
            "test-session".to_string(),
        )
        .with_permissions(Permission {
            execute: true,
            ..Default::default()
        });

        let command = if cfg!(windows) {
            "timeout /t 2 /nobreak > nul"
        } else {
            "sleep 2"
        };

        let input = ToolInput::Object(HashMap::from([
            ("command".to_string(), json!(command)),
            ("timeout_ms".to_string(), json!(1000)), // 1 second timeout
        ]));

        let result = tool.execute(input, &mut context).await.unwrap();

        match result {
            ToolOutput::Success(value) => {
                let output: BashToolOutput = serde_json::from_value(value).unwrap();
                assert!(output.timed_out);
            }
            _ => panic!("Expected Success output"),
        }
    }

    #[tokio::test]
    async fn test_bash_tool_error_command() {
        let temp_dir = TempDir::new().unwrap();

        let tool = BashTool::new();
        let config = Config::default();
        let mut context = ExecutionContext::with_config(
            temp_dir.path().to_path_buf(),
            config,
            "test-session".to_string(),
        )
        .with_permissions(Permission {
            execute: true,
            ..Default::default()
        });

        let input = ToolInput::Object(HashMap::from([(
            "command".to_string(),
            json!("nonexistent_command_12345"),
        )]));

        let result = tool.execute(input, &mut context).await.unwrap();

        match result {
            ToolOutput::Success(value) => {
                let output: BashToolOutput = serde_json::from_value(value).unwrap();
                assert!(output.exit_code.is_some());
                assert_ne!(output.exit_code, Some(0));
                // Should have error output
                assert!(!output.stderr.is_empty() || !output.stdout.is_empty());
            }
            _ => panic!("Expected Success output"),
        }
    }
}
