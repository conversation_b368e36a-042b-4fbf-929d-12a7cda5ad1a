use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::fs;
use std::path::{Path, PathBuf};
use tracing::debug;

use crate::tools::context::ExecutionContext;
use crate::tools::{Tool, ToolError, ToolInput, ToolOutput, ToolResult};

/// Resolve path relative to working directory
fn resolve_path(working_dir: &Path, path: &str) -> PathBuf {
    let path_buf = PathBuf::from(path);
    if path_buf.is_absolute() {
        path_buf
    } else {
        working_dir.join(path_buf)
    }
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
struct EditToolInput {
    file_path: String,
    old_string: String,
    new_string: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    expected_replacements: Option<usize>,
}

pub struct EditTool;

impl EditTool {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait]
impl Tool for EditTool {
    fn name(&self) -> &str {
        "Edit"
    }

    fn description(&self) -> &str {
        "Performs exact string replacements in files with strict occurrence count validation"
    }

    fn input_schema_json(&self) -> Value {
        json!({
            "type": "object",
            "properties": {
                "file_path": {
                    "type": "string",
                    "description": "The absolute path to the file to modify"
                },
                "old_string": {
                    "type": "string",
                    "description": "The exact text to replace"
                },
                "new_string": {
                    "type": "string",
                    "description": "The text to replace it with (must be different from old_string)"
                },
                "expected_replacements": {
                    "type": "integer",
                    "description": "The expected number of replacements (default: 1)",
                    "minimum": 1
                }
            },
            "required": ["file_path", "old_string", "new_string"],
            "additionalProperties": false
        })
    }

    async fn validate_input(&self, input: &ToolInput) -> ToolResult<()> {
        match input {
            ToolInput::Object(map) => {
                // Check required fields
                for field in ["file_path", "old_string", "new_string"] {
                    if !map.contains_key(field) {
                        return Err(ToolError::InvalidInput(format!(
                            "Missing required field: {}",
                            field
                        )));
                    }
                    if let Some(Value::String(_)) = map.get(field) {
                        // Valid
                    } else {
                        return Err(ToolError::InvalidInput(format!(
                            "{} must be a string",
                            field
                        )));
                    }
                }

                // Validate old_string and new_string are different
                if let (Some(Value::String(old)), Some(Value::String(new))) =
                    (map.get("old_string"), map.get("new_string"))
                {
                    if old == new {
                        return Err(ToolError::InvalidInput(
                            "old_string and new_string must be different".to_string(),
                        ));
                    }
                }

                // Validate expected_replacements if present
                if let Some(expected_val) = map.get("expected_replacements") {
                    if let Some(expected) = expected_val.as_u64() {
                        if expected == 0 {
                            return Err(ToolError::InvalidInput(
                                "expected_replacements must be at least 1".to_string(),
                            ));
                        }
                    } else {
                        return Err(ToolError::InvalidInput(
                            "expected_replacements must be a number".to_string(),
                        ));
                    }
                }

                Ok(())
            }
            _ => Err(ToolError::InvalidInput(
                "Input must be an object".to_string(),
            )),
        }
    }

    async fn check_permissions(&self, context: &ExecutionContext) -> ToolResult<()> {
        if !context.has_permission("write") {
            return Err(ToolError::PermissionDenied(
                "Write permission not granted".to_string(),
            ));
        }
        Ok(())
    }

    async fn execute(
        &self,
        input: ToolInput,
        context: &mut ExecutionContext,
    ) -> ToolResult<ToolOutput> {
        let params: EditToolInput = match input {
            ToolInput::Object(map) => {
                let json_value = Value::Object(map.into_iter().collect());
                serde_json::from_value(json_value)
                    .map_err(|e| ToolError::InvalidInput(format!("Failed to parse input: {}", e)))?
            }
            _ => {
                return Err(ToolError::InvalidInput(
                    "Input must be an object".to_string(),
                ))
            }
        };

        let file_path = resolve_path(&context.working_directory, &params.file_path);
        debug!("Editing file: {:?}", file_path);

        // Check if file exists
        if !file_path.exists() {
            return Err(ToolError::ExecutionFailed(format!(
                "File not found: {:?}",
                file_path
            )));
        }

        if file_path.is_dir() {
            return Err(ToolError::ExecutionFailed(format!(
                "Path is a directory: {:?}",
                file_path
            )));
        }

        // Read file content
        let content = fs::read_to_string(&file_path)
            .map_err(|e| ToolError::ExecutionFailed(format!("Failed to read file: {}", e)))?;

        // Count occurrences of old_string
        let occurrence_count = content.matches(&params.old_string).count();
        let expected_replacements = params.expected_replacements.unwrap_or(1);

        // Validate occurrence count
        if occurrence_count != expected_replacements {
            return Err(ToolError::ValidationFailed(format!(
                "Expected {} occurrences of '{}', but found {}",
                expected_replacements, params.old_string, occurrence_count
            )));
        }

        // Perform replacement
        let new_content = content.replace(&params.old_string, &params.new_string);

        // Write back to file
        fs::write(&file_path, &new_content)
            .map_err(|e| ToolError::ExecutionFailed(format!("Failed to write file: {}", e)))?;

        // Update file state tracking
        if let Some(file_state) = context.file_states.get_mut(&file_path) {
            file_state.modified = true;
            // Update hash would require recalculating, which we'll skip for now
        }

        Ok(ToolOutput::Success(json!({
            "message": format!("Successfully replaced {} occurrences in {:?}", occurrence_count, file_path),
            "file_path": file_path.display().to_string(),
            "replacements_made": occurrence_count,
            "old_string": params.old_string,
            "new_string": params.new_string
        })))
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct EditOperation {
    old_string: String,
    new_string: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    expected_replacements: Option<usize>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct MultiEditToolInput {
    file_path: String,
    edits: Vec<EditOperation>,
}

pub struct MultiEditTool;

impl MultiEditTool {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait]
impl Tool for MultiEditTool {
    fn name(&self) -> &str {
        "MultiEdit"
    }

    fn description(&self) -> &str {
        "Performs multiple string replacements in a single file in sequence"
    }

    fn input_schema_json(&self) -> Value {
        json!({
            "type": "object",
            "properties": {
                "file_path": {
                    "type": "string",
                    "description": "The absolute path to the file to modify"
                },
                "edits": {
                    "type": "array",
                    "description": "Array of edit operations to perform sequentially",
                    "items": {
                        "type": "object",
                        "properties": {
                            "old_string": {
                                "type": "string",
                                "description": "The exact text to replace"
                            },
                            "new_string": {
                                "type": "string",
                                "description": "The text to replace it with"
                            },
                            "expected_replacements": {
                                "type": "integer",
                                "description": "The expected number of replacements (default: 1)",
                                "minimum": 1
                            }
                        },
                        "required": ["old_string", "new_string"]
                    },
                    "minItems": 1
                }
            },
            "required": ["file_path", "edits"],
            "additionalProperties": false
        })
    }

    async fn validate_input(&self, input: &ToolInput) -> ToolResult<()> {
        match input {
            ToolInput::Object(map) => {
                if !map.contains_key("file_path") {
                    return Err(ToolError::InvalidInput(
                        "Missing required field: file_path".to_string(),
                    ));
                }

                if let Some(Value::String(_)) = map.get("file_path") {
                    // Valid
                } else {
                    return Err(ToolError::InvalidInput(
                        "file_path must be a string".to_string(),
                    ));
                }

                if !map.contains_key("edits") {
                    return Err(ToolError::InvalidInput(
                        "Missing required field: edits".to_string(),
                    ));
                }

                if let Some(Value::Array(edits)) = map.get("edits") {
                    if edits.is_empty() {
                        return Err(ToolError::InvalidInput(
                            "edits array cannot be empty".to_string(),
                        ));
                    }

                    for (i, edit) in edits.iter().enumerate() {
                        if let Some(edit_obj) = edit.as_object() {
                            // Check required fields for each edit
                            for field in ["old_string", "new_string"] {
                                if !edit_obj.contains_key(field) {
                                    return Err(ToolError::InvalidInput(format!(
                                        "Edit {}: missing required field: {}",
                                        i, field
                                    )));
                                }
                                if let Some(Value::String(_)) = edit_obj.get(field) {
                                    // Valid
                                } else {
                                    return Err(ToolError::InvalidInput(format!(
                                        "Edit {}: {} must be a string",
                                        i, field
                                    )));
                                }
                            }

                            // Validate old_string and new_string are different
                            if let (Some(Value::String(old)), Some(Value::String(new))) =
                                (edit_obj.get("old_string"), edit_obj.get("new_string"))
                            {
                                if old == new {
                                    return Err(ToolError::InvalidInput(format!(
                                        "Edit {}: old_string and new_string must be different",
                                        i
                                    )));
                                }
                            }
                        } else {
                            return Err(ToolError::InvalidInput(format!(
                                "Edit {} must be an object",
                                i
                            )));
                        }
                    }
                } else {
                    return Err(ToolError::InvalidInput(
                        "edits must be an array".to_string(),
                    ));
                }

                Ok(())
            }
            _ => Err(ToolError::InvalidInput(
                "Input must be an object".to_string(),
            )),
        }
    }

    async fn check_permissions(&self, context: &ExecutionContext) -> ToolResult<()> {
        if !context.has_permission("write") {
            return Err(ToolError::PermissionDenied(
                "Write permission not granted".to_string(),
            ));
        }
        Ok(())
    }

    async fn execute(
        &self,
        input: ToolInput,
        context: &mut ExecutionContext,
    ) -> ToolResult<ToolOutput> {
        let params: MultiEditToolInput = match input {
            ToolInput::Object(map) => {
                let json_value = Value::Object(map.into_iter().collect());
                serde_json::from_value(json_value)
                    .map_err(|e| ToolError::InvalidInput(format!("Failed to parse input: {}", e)))?
            }
            _ => {
                return Err(ToolError::InvalidInput(
                    "Input must be an object".to_string(),
                ))
            }
        };

        let file_path = resolve_path(&context.working_directory, &params.file_path);
        debug!("Multi-editing file: {:?}", file_path);

        // Check if file exists
        if !file_path.exists() {
            return Err(ToolError::ExecutionFailed(format!(
                "File not found: {:?}",
                file_path
            )));
        }

        if file_path.is_dir() {
            return Err(ToolError::ExecutionFailed(format!(
                "Path is a directory: {:?}",
                file_path
            )));
        }

        // Read file content
        let mut content = fs::read_to_string(&file_path)
            .map_err(|e| ToolError::ExecutionFailed(format!("Failed to read file: {}", e)))?;

        let mut total_replacements = 0;
        let mut edit_results = Vec::new();

        // Apply edits sequentially
        for (i, edit) in params.edits.iter().enumerate() {
            debug!(
                "Applying edit {}: '{}' -> '{}'",
                i, edit.old_string, edit.new_string
            );

            // Count occurrences in current content
            let occurrence_count = content.matches(&edit.old_string).count();
            let expected_replacements = edit.expected_replacements.unwrap_or(1);

            // Validate occurrence count
            if occurrence_count != expected_replacements {
                return Err(ToolError::ValidationFailed(format!(
                    "Edit {}: Expected {} occurrences of '{}', but found {}",
                    i, expected_replacements, edit.old_string, occurrence_count
                )));
            }

            // Perform replacement
            content = content.replace(&edit.old_string, &edit.new_string);
            total_replacements += occurrence_count;

            edit_results.push(json!({
                "edit_index": i,
                "old_string": edit.old_string,
                "new_string": edit.new_string,
                "replacements_made": occurrence_count
            }));
        }

        // Write back to file
        fs::write(&file_path, &content)
            .map_err(|e| ToolError::ExecutionFailed(format!("Failed to write file: {}", e)))?;

        // Update file state tracking
        if let Some(file_state) = context.file_states.get_mut(&file_path) {
            file_state.modified = true;
        }

        Ok(ToolOutput::Success(json!({
            "message": format!("Successfully applied {} edits with {} total replacements in {:?}",
                              params.edits.len(), total_replacements, file_path),
            "file_path": file_path.display().to_string(),
            "total_edits": params.edits.len(),
            "total_replacements": total_replacements,
            "edit_details": edit_results
        })))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::Config;
    use crate::tools::context::Permission;
    use std::collections::HashMap;
    use std::fs;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_edit_tool() {
        let temp_dir = TempDir::new().unwrap();
        let file_path = temp_dir.path().join("test.txt");

        // Write test content
        fs::write(&file_path, "Hello world!\nHello again!").unwrap();

        let tool = EditTool::new();
        let config = Config::default();
        let mut context = ExecutionContext::with_config(
            temp_dir.path().to_path_buf(),
            config,
            "test-session".to_string(),
        )
        .with_permissions(Permission {
            write: true,
            ..Default::default()
        });

        let input = ToolInput::Object(HashMap::from([
            (
                "file_path".to_string(),
                json!(file_path.display().to_string()),
            ),
            ("old_string".to_string(), json!("Hello")),
            ("new_string".to_string(), json!("Hi")),
            ("expected_replacements".to_string(), json!(2)),
        ]));

        let result = tool.execute(input, &mut context).await.unwrap();

        match result {
            ToolOutput::Success(value) => {
                assert_eq!(value["replacements_made"], 2);

                // Verify file was modified
                let content = fs::read_to_string(&file_path).unwrap();
                assert_eq!(content, "Hi world!\nHi again!");
            }
            _ => panic!("Expected Success output"),
        }
    }

    #[tokio::test]
    async fn test_multi_edit_tool() {
        let temp_dir = TempDir::new().unwrap();
        let file_path = temp_dir.path().join("test.txt");

        // Write test content
        fs::write(&file_path, "Hello world!\nGoodbye world!").unwrap();

        let tool = MultiEditTool::new();
        let config = Config::default();
        let mut context = ExecutionContext::with_config(
            temp_dir.path().to_path_buf(),
            config,
            "test-session".to_string(),
        )
        .with_permissions(Permission {
            write: true,
            ..Default::default()
        });

        let input = ToolInput::Object(HashMap::from([
            (
                "file_path".to_string(),
                json!(file_path.display().to_string()),
            ),
            (
                "edits".to_string(),
                json!([
                    {
                        "old_string": "Hello",
                        "new_string": "Hi"
                    },
                    {
                        "old_string": "Goodbye",
                        "new_string": "Bye"
                    }
                ]),
            ),
        ]));

        let result = tool.execute(input, &mut context).await.unwrap();

        match result {
            ToolOutput::Success(value) => {
                assert_eq!(value["total_edits"], 2);
                assert_eq!(value["total_replacements"], 2);

                // Verify file was modified
                let content = fs::read_to_string(&file_path).unwrap();
                assert_eq!(content, "Hi world!\nBye world!");
            }
            _ => panic!("Expected Success output"),
        }
    }
}
