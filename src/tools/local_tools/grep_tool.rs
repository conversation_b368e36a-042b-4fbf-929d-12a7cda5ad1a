use async_trait::async_trait;
use regex::Regex;
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::fs;
use std::path::{Path, PathBuf};
use std::process::{Command, Stdio};
use tracing::debug;

use crate::tools::context::ExecutionContext;
use crate::tools::{Tool, ToolError, ToolInput, ToolOutput, ToolResult};

const MAX_FILES: usize = 1000;

#[derive(Debug, Clone, Serialize, Deserialize)]
struct GrepToolInput {
    pattern: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    path: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    include: Option<String>,
}

pub struct GrepTool;

impl GrepTool {
    pub fn new() -> Self {
        Self
    }
}

/// Resolve path relative to working directory
fn resolve_path(working_dir: &Path, path: &str) -> PathBuf {
    let path_buf = PathBuf::from(path);
    if path_buf.is_absolute() {
        path_buf
    } else {
        working_dir.join(path_buf)
    }
}

/// Get relative path for display
fn display_path(base: &Path, absolute_path: &Path) -> String {
    pathdiff::diff_paths(absolute_path, base)
        .map(|p| p.to_string_lossy().into_owned())
        .unwrap_or_else(|| absolute_path.to_string_lossy().into_owned())
}

#[async_trait]
impl Tool for GrepTool {
    fn name(&self) -> &str {
        "Grep"
    }

    fn description(&self) -> &str {
        "Searches file contents using a regular expression pattern. Returns files that contain at least one match."
    }

    fn input_schema_json(&self) -> Value {
        json!({
            "type": "object",
            "properties": {
                "pattern": {
                    "type": "string",
                    "description": "The regular expression pattern to search for"
                },
                "path": {
                    "type": "string",
                    "description": "Optional directory to search in. Defaults to current working directory."
                },
                "include": {
                    "type": "string",
                    "description": "Optional glob pattern for files to include (e.g., \"*.rs\", \"*.{js,ts}\")"
                }
            },
            "required": ["pattern"],
            "additionalProperties": false
        })
    }

    async fn validate_input(&self, input: &ToolInput) -> ToolResult<()> {
        match input {
            ToolInput::Object(map) => {
                if !map.contains_key("pattern") {
                    return Err(ToolError::InvalidInput(
                        "Missing required field: pattern".to_string(),
                    ));
                }

                // Validate pattern is a string
                if let Some(Value::String(pattern)) = map.get("pattern") {
                    // Try to compile the regex to validate it
                    if let Err(e) = Regex::new(pattern) {
                        return Err(ToolError::InvalidInput(format!(
                            "Invalid regex pattern: {}",
                            e
                        )));
                    }
                } else {
                    return Err(ToolError::InvalidInput(
                        "pattern must be a string".to_string(),
                    ));
                }

                // Validate path if present
                if let Some(path_val) = map.get("path") {
                    if !path_val.is_string() {
                        return Err(ToolError::InvalidInput("path must be a string".to_string()));
                    }
                }

                // Validate include if present
                if let Some(include_val) = map.get("include") {
                    if !include_val.is_string() {
                        return Err(ToolError::InvalidInput(
                            "include must be a string".to_string(),
                        ));
                    }
                }

                Ok(())
            }
            _ => Err(ToolError::InvalidInput(
                "Input must be an object".to_string(),
            )),
        }
    }

    async fn check_permissions(&self, context: &ExecutionContext) -> ToolResult<()> {
        if !context.has_permission("read") {
            return Err(ToolError::PermissionDenied(
                "Read permission not granted".to_string(),
            ));
        }
        Ok(())
    }

    async fn execute(
        &self,
        input: ToolInput,
        context: &mut ExecutionContext,
    ) -> ToolResult<ToolOutput> {
        let params: GrepToolInput = match input {
            ToolInput::Object(map) => {
                let json_value = Value::Object(map.into_iter().collect());
                serde_json::from_value(json_value)
                    .map_err(|e| ToolError::InvalidInput(format!("Failed to parse input: {}", e)))?
            }
            _ => {
                return Err(ToolError::InvalidInput(
                    "Input must be an object".to_string(),
                ))
            }
        };

        let search_path = params
            .path
            .as_ref()
            .map(|p| resolve_path(&context.working_directory, p))
            .unwrap_or_else(|| context.working_directory.clone());

        debug!(
            "Searching for pattern '{}' in path: {:?}",
            params.pattern, search_path
        );

        // Try to use ripgrep first, fallback to built-in search
        if let Ok(files) = self
            .search_with_ripgrep(&params, &search_path, context)
            .await
        {
            Ok(ToolOutput::Success(json!({
                "matches": files,
                "pattern": params.pattern,
                "search_path": search_path.display().to_string(),
                "method": "ripgrep"
            })))
        } else {
            // Fallback to built-in search
            debug!("Ripgrep not available, using built-in search");
            let files = self.search_builtin(&params, &search_path, context).await?;
            Ok(ToolOutput::Success(json!({
                "matches": files,
                "pattern": params.pattern,
                "search_path": search_path.display().to_string(),
                "method": "builtin"
            })))
        }
    }
}

impl GrepTool {
    /// Search using ripgrep if available
    async fn search_with_ripgrep(
        &self,
        params: &GrepToolInput,
        search_path: &Path,
        context: &ExecutionContext,
    ) -> Result<Vec<String>, ToolError> {
        // Try to find ripgrep in PATH
        let rg_cmd = if Command::new("rg").arg("--version").output().is_ok() {
            "rg"
        } else {
            // Try common alternative paths
            return Err(ToolError::ExecutionFailed(
                "ripgrep (rg) not found in PATH".to_string(),
            ));
        };

        let mut cmd = Command::new(rg_cmd);
        cmd.arg("--files-with-matches") // Only return filenames
            .arg("--no-heading")
            .arg("--hidden") // Include hidden files
            .arg("-i") // Case-insensitive
            .arg("--type-not")
            .arg("binary"); // Skip binary files

        // Add glob pattern if specified
        if let Some(ref include_pattern) = params.include {
            cmd.arg("-g").arg(include_pattern);
        }

        cmd.arg(&params.pattern).arg(search_path);

        cmd.stdout(Stdio::piped()).stderr(Stdio::piped());

        debug!("Executing ripgrep command: {:?}", cmd);

        let output = cmd
            .output()
            .map_err(|e| ToolError::ExecutionFailed(format!("Failed to execute ripgrep: {}", e)))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            // Exit code 1 means no matches found, which is not an error
            if output.status.code() == Some(1) && stderr.is_empty() {
                return Ok(Vec::new());
            }
            return Err(ToolError::ExecutionFailed(format!(
                "ripgrep failed: {}",
                stderr
            )));
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        let mut files: Vec<String> = stdout
            .lines()
            .take(MAX_FILES)
            .map(|line| display_path(&context.working_directory, &PathBuf::from(line.trim())))
            .collect();

        files.sort();
        Ok(files)
    }

    /// Built-in search implementation
    async fn search_builtin(
        &self,
        params: &GrepToolInput,
        search_path: &Path,
        context: &ExecutionContext,
    ) -> ToolResult<Vec<String>> {
        let regex = Regex::new(&params.pattern)
            .map_err(|e| ToolError::ValidationFailed(format!("Invalid regex pattern: {}", e)))?;

        let mut matching_files = Vec::new();
        let mut processed_count = 0;

        self.search_directory_recursive(
            search_path,
            &regex,
            &params.include,
            &mut matching_files,
            &mut processed_count,
            context,
        )?;

        matching_files.sort();
        Ok(matching_files)
    }

    /// Recursively search directories
    fn search_directory_recursive(
        &self,
        dir: &Path,
        regex: &Regex,
        include_pattern: &Option<String>,
        matching_files: &mut Vec<String>,
        processed_count: &mut usize,
        context: &ExecutionContext,
    ) -> ToolResult<()> {
        if *processed_count >= MAX_FILES {
            return Ok(());
        }

        let entries = fs::read_dir(dir).map_err(|e| {
            ToolError::ExecutionFailed(format!("Failed to read directory {:?}: {}", dir, e))
        })?;

        for entry in entries {
            if *processed_count >= MAX_FILES {
                break;
            }

            let entry = entry.map_err(|e| {
                ToolError::ExecutionFailed(format!("Failed to read directory entry: {}", e))
            })?;

            let path = entry.path();

            if path.is_dir() {
                // Skip hidden directories and common ignored directories
                if let Some(dir_name) = path.file_name().and_then(|n| n.to_str()) {
                    if dir_name.starts_with('.')
                        || dir_name == "node_modules"
                        || dir_name == "target"
                        || dir_name == ".git"
                    {
                        continue;
                    }
                }

                self.search_directory_recursive(
                    &path,
                    regex,
                    include_pattern,
                    matching_files,
                    processed_count,
                    context,
                )?;
            } else if path.is_file() {
                // Check include pattern if specified
                if let Some(pattern) = include_pattern {
                    if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
                        if !self.matches_glob_pattern(file_name, pattern) {
                            continue;
                        }
                    }
                }

                // Skip binary files
                if self.is_likely_binary(&path) {
                    continue;
                }

                if self.file_contains_pattern(&path, regex)? {
                    let relative_path = display_path(&context.working_directory, &path);
                    matching_files.push(relative_path);
                }

                *processed_count += 1;
            }
        }

        Ok(())
    }

    /// Simple glob pattern matching
    fn matches_glob_pattern(&self, filename: &str, pattern: &str) -> bool {
        // Simple implementation for common patterns
        if pattern.contains('*') {
            if pattern.starts_with("*.") {
                // Extension matching like "*.rs"
                let ext = &pattern[2..];
                filename.ends_with(&format!(".{}", ext))
            } else if pattern.contains("{") && pattern.contains("}") {
                // Brace expansion like "*.{js,ts}"
                if let Some(start) = pattern.find('{') {
                    if let Some(end) = pattern.find('}') {
                        let prefix = &pattern[..start];
                        let suffix = &pattern[end + 1..];
                        let extensions = &pattern[start + 1..end];

                        for ext in extensions.split(',') {
                            let full_pattern = format!("{}{}{}", prefix, ext, suffix);
                            if self.matches_glob_pattern(filename, &full_pattern) {
                                return true;
                            }
                        }
                        return false;
                    }
                }
                // Fallback: simple contains check
                let pattern_without_stars = pattern.replace('*', "");
                filename.contains(&pattern_without_stars)
            } else {
                // Other star patterns - simple contains check
                let pattern_without_stars = pattern.replace('*', "");
                filename.contains(&pattern_without_stars)
            }
        } else {
            filename == pattern
        }
    }

    /// Check if a file is likely binary
    fn is_likely_binary(&self, path: &Path) -> bool {
        // Check file extension
        if let Some(ext) = path.extension().and_then(|e| e.to_str()) {
            matches!(
                ext.to_lowercase().as_str(),
                "exe"
                    | "dll"
                    | "so"
                    | "dylib"
                    | "bin"
                    | "obj"
                    | "o"
                    | "a"
                    | "lib"
                    | "zip"
                    | "tar"
                    | "gz"
                    | "bz2"
                    | "7z"
                    | "rar"
                    | "pdf"
                    | "doc"
                    | "docx"
                    | "jpg"
                    | "jpeg"
                    | "png"
                    | "gif"
                    | "bmp"
                    | "ico"
                    | "mp3"
                    | "mp4"
                    | "avi"
                    | "mov"
                    | "wav"
            )
        } else {
            false
        }
    }

    /// Check if file contains the regex pattern
    fn file_contains_pattern(&self, path: &Path, regex: &Regex) -> ToolResult<bool> {
        match fs::read_to_string(path) {
            Ok(content) => Ok(regex.is_match(&content)),
            Err(e) => {
                // If we can't read the file (e.g., it's binary), skip it
                debug!("Could not read file {:?}: {}", path, e);
                Ok(false)
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::Config;
    use crate::tools::context::Permission;
    use std::collections::HashMap;
    use std::fs;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_grep_tool() {
        let temp_dir = TempDir::new().unwrap();

        // Create test files
        fs::create_dir_all(temp_dir.path().join("src")).unwrap();
        fs::write(
            temp_dir.path().join("src/main.rs"),
            "fn main() {\n    println!(\"Hello, world!\");\n}",
        )
        .unwrap();
        fs::write(
            temp_dir.path().join("src/lib.rs"),
            "pub fn hello() {\n    println!(\"Hello from lib!\");\n}",
        )
        .unwrap();
        fs::write(
            temp_dir.path().join("README.md"),
            "# Test Project\n\nThis is a test.",
        )
        .unwrap();

        let tool = GrepTool::new();
        let config = Config::default();
        let mut context = ExecutionContext::with_config(
            temp_dir.path().to_path_buf(),
            config,
            "test-session".to_string(),
        )
        .with_permissions(Permission {
            read: true,
            ..Default::default()
        });

        // Search for "println"
        let input = ToolInput::Object(HashMap::from([("pattern".to_string(), json!("println"))]));

        let result = tool.execute(input, &mut context).await.unwrap();

        match result {
            ToolOutput::Success(value) => {
                let matches = value["matches"].as_array().unwrap();
                assert_eq!(matches.len(), 2);

                let match_strings: Vec<&str> =
                    matches.iter().map(|v| v.as_str().unwrap()).collect();

                assert!(match_strings.contains(&"src/main.rs"));
                assert!(match_strings.contains(&"src/lib.rs"));
            }
            _ => panic!("Expected Success output"),
        }
    }

    #[tokio::test]
    async fn test_grep_tool_with_include() {
        let temp_dir = TempDir::new().unwrap();

        // Create test files
        fs::write(temp_dir.path().join("test.rs"), "fn test() {}").unwrap();
        fs::write(temp_dir.path().join("test.txt"), "test content").unwrap();

        let tool = GrepTool::new();
        let config = Config::default();
        let mut context = ExecutionContext::with_config(
            temp_dir.path().to_path_buf(),
            config,
            "test-session".to_string(),
        )
        .with_permissions(Permission {
            read: true,
            ..Default::default()
        });

        // Search for "test" only in .rs files
        let input = ToolInput::Object(HashMap::from([
            ("pattern".to_string(), json!("test")),
            ("include".to_string(), json!("*.rs")),
        ]));

        let result = tool.execute(input, &mut context).await.unwrap();

        match result {
            ToolOutput::Success(value) => {
                let matches = value["matches"].as_array().unwrap();
                assert_eq!(matches.len(), 1);
                assert_eq!(matches[0].as_str().unwrap(), "test.rs");
            }
            _ => panic!("Expected Success output"),
        }
    }
}
