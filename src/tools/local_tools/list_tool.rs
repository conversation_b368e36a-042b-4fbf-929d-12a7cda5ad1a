use async_trait::async_trait;
use ignore::Walk<PERSON><PERSON><PERSON>;
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::path::{Path, PathBuf};
use tracing::debug;

use crate::tools::context::ExecutionContext;
use crate::tools::{Tool, ToolError, ToolInput, ToolOutput, ToolResult};

const MAX_ENTRIES: usize = 1000;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
struct ListToolInput {
    path: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    ignore: Option<Vec<String>>,
}

pub struct ListTool;

impl ListTool {
    pub fn new() -> Self {
        Self
    }
}

/// Resolve path relative to working directory
fn resolve_path(working_dir: &Path, path: &str) -> PathBuf {
    let path_buf = PathBuf::from(path);
    if path_buf.is_absolute() {
        path_buf
    } else {
        working_dir.join(path_buf)
    }
}

#[async_trait]
impl Tool for ListTool {
    fn name(&self) -> &str {
        "LS"
    }

    fn description(&self) -> &str {
        "Lists files and directories in a given path, respecting .gitignore and custom ignore patterns"
    }

    fn input_schema_json(&self) -> Value {
        json!({
            "type": "object",
            "properties": {
                "path": {
                    "type": "string",
                    "description": "The absolute path to the directory to list"
                },
                "ignore": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Optional list of glob patterns to ignore"
                }
            },
            "required": ["path"],
            "additionalProperties": false
        })
    }

    async fn validate_input(&self, input: &ToolInput) -> ToolResult<()> {
        match input {
            ToolInput::Object(map) => {
                if !map.contains_key("path") {
                    return Err(ToolError::InvalidInput(
                        "Missing required field: path".to_string(),
                    ));
                }

                // Validate path is a string
                if let Some(Value::String(_)) = map.get("path") {
                    // Valid
                } else {
                    return Err(ToolError::InvalidInput("path must be a string".to_string()));
                }

                // Validate ignore if present
                if let Some(ignore_val) = map.get("ignore") {
                    if !ignore_val.is_array() {
                        return Err(ToolError::InvalidInput(
                            "ignore must be an array".to_string(),
                        ));
                    }
                }

                Ok(())
            }
            _ => Err(ToolError::InvalidInput(
                "Input must be an object".to_string(),
            )),
        }
    }

    async fn check_permissions(&self, context: &ExecutionContext) -> ToolResult<()> {
        if !context.has_permission("read") {
            return Err(ToolError::PermissionDenied(
                "Read permission not granted".to_string(),
            ));
        }
        Ok(())
    }

    async fn execute(
        &self,
        input: ToolInput,
        context: &mut ExecutionContext,
    ) -> ToolResult<ToolOutput> {
        let params: ListToolInput = match input {
            ToolInput::Object(map) => {
                let json_value = Value::Object(map.into_iter().collect());
                serde_json::from_value(json_value)
                    .map_err(|e| ToolError::InvalidInput(format!("Failed to parse input: {}", e)))?
            }
            _ => {
                return Err(ToolError::InvalidInput(
                    "Input must be an object".to_string(),
                ))
            }
        };

        let target_path = resolve_path(&context.working_directory, &params.path);
        debug!("Listing directory: {:?}", target_path);

        // Check if path exists
        if !target_path.exists() {
            return Err(ToolError::ExecutionFailed(format!(
                "Path not found: {:?}",
                target_path
            )));
        }

        if !target_path.is_dir() {
            return Err(ToolError::ExecutionFailed(format!(
                "Path is not a directory: {:?}",
                target_path
            )));
        }

        // Set up walker with ignore patterns
        let mut walker_builder = WalkBuilder::new(&target_path);
        walker_builder
            .max_depth(Some(1)) // Only list immediate children
            .hidden(false) // Include hidden files by default
            .parents(false)
            .ignore(true) // Respect .ignore files
            .git_global(true)
            .git_ignore(true)
            .git_exclude(true);

        // Add custom ignore patterns
        if let Some(ref ignore_patterns) = params.ignore {
            for pattern in ignore_patterns {
                // Note: The ignore crate doesn't have add_ignore method in current API
                // We'll need to handle this differently or use a different approach
                debug!("Custom ignore pattern: {}", pattern);
            }
        }

        let mut entries = Vec::new();
        let mut entry_count = 0;

        // Walk the directory
        for result in walker_builder.build().skip(1) {
            // Skip the root directory itself
            if entry_count >= MAX_ENTRIES {
                entries.push("... [TRUNCATED: Too many entries]".to_string());
                break;
            }

            match result {
                Ok(entry) => {
                    let entry_path = entry.path();
                    let display_name = entry_path.file_name().map_or_else(
                        || "".to_string(),
                        |name| name.to_string_lossy().into_owned(),
                    );

                    if display_name.is_empty() {
                        continue;
                    }

                    // Check against custom ignore patterns if provided
                    if let Some(ref ignore_patterns) = params.ignore {
                        let should_ignore = ignore_patterns.iter().any(|pattern| {
                            // Simple glob-like matching
                            display_name.contains(pattern) || display_name.starts_with(pattern)
                        });
                        if should_ignore {
                            continue;
                        }
                    }

                    let entry_type = if entry.file_type().map_or(false, |ft| ft.is_dir()) {
                        "/" // Indicate directory
                    } else {
                        ""
                    };

                    entries.push(format!("{}{}", display_name, entry_type));
                    entry_count += 1;
                }
                Err(e) => {
                    debug!("Error walking directory {:?}: {}", target_path, e);
                    // Continue processing other entries
                }
            }
        }

        // Sort entries for consistent output
        entries.sort();

        let output_text = entries.join("\n");

        Ok(ToolOutput::Success(json!({
            "content": output_text,
            "entries_count": entries.len(),
            "path": target_path.display().to_string(),
            "truncated": entry_count >= MAX_ENTRIES
        })))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::Config;
    use crate::tools::context::Permission;
    use std::collections::HashMap;
    use std::fs;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_list_tool() {
        let temp_dir = TempDir::new().unwrap();

        // Create test files and directories
        fs::create_dir(temp_dir.path().join("subdir")).unwrap();
        fs::write(temp_dir.path().join("file1.txt"), "content").unwrap();
        fs::write(temp_dir.path().join("file2.rs"), "fn main() {}").unwrap();

        let tool = ListTool::new();
        let config = Config::default();
        let mut context = ExecutionContext::with_config(
            temp_dir.path().to_path_buf(),
            config,
            "test-session".to_string(),
        )
        .with_permissions(Permission {
            read: true,
            ..Default::default()
        });

        let input = ToolInput::Object(HashMap::from([(
            "path".to_string(),
            json!(temp_dir.path().display().to_string()),
        )]));

        let result = tool.execute(input, &mut context).await.unwrap();

        match result {
            ToolOutput::Success(value) => {
                let content = value["content"].as_str().unwrap();
                assert!(content.contains("file1.txt"));
                assert!(content.contains("file2.rs"));
                assert!(content.contains("subdir/"));
                assert_eq!(value["entries_count"], 3);
            }
            _ => panic!("Expected Success output"),
        }
    }
}
