# Start Task

A comprehensive task execution command that primes context from relevant files, enforces specification creation for code changes, and ensures proper testing for core logic modifications.

## Arguments
- `$ARGUMENTS 1`: Task description/Specification document (required)
- `$ARGUMENTS 2`: Priority level (optional, default: "medium")
- `$ARGUMENTS 3`: Force skip spec (optional, default: "false")

## Workflow

```mermaid
flowchart TD
    A[Start Task] --> B["Parse Task Description/Document"]
    B --> C{Code Modification Task?}
    C -->|Yes| D{Core Logic Changes?}
    C -->|No| E[Prime Context]
    D -->|Yes| F[Create Spec First]
    D -->|No| G[Create Spec First]
    F --> H{Tests Required?}
    G --> I[Prime Context]
    H -->|Yes| J[Ensure Failing Tests Exist]
    H -->|No| I
    J --> K{Tests Approved?}
    K -->|No| L[Request Test Approval]
    K -->|Yes| I
    L --> K
    I --> M[Execute Task]
    E --> M
    M --> N[Validate Completion]
    N --> O[End]
```

## Process Flow

```mermaid
sequenceDiagram
    participant U as User
    participant C as Command
    participant S as Spec Generator
    participant T as Test System
    participant F as File System
    
    U->>C: Execute with task description
    C->>F: Analyze codebase for context
    F-->>C: Relevant files identified
    C->>C: Prime context from files
    
    alt Code modification task
        C->>S: Generate specification
        S-->>C: Spec created
        
        alt Core logic changes
            C->>T: Check for failing tests
            T-->>C: Test status
            
            alt No approved tests
                C-->>U: Request test approval
                U->>C: Approval provided
            end
        end
    end
    
    C->>F: Execute task
    F-->>C: Task results
    C-->>U: Completion report
```

## Command Logic

### Step 1: Task Analysis and Context Priming
- Parse the task description to understand scope and requirements
- Identify relevant files using project structure analysis
- Read and analyze key files to build context:
  - Configuration files (package.json, pyproject.toml, etc.)
  - Main application files
  - Related source files based on task keywords
  - Test files for understanding existing patterns

### Step 2: Specification Requirement Check
If task involves source code modification:
- Automatically trigger `/project:make-spec` command
- Wait for spec completion before proceeding
- Exception: Skip spec only if `$ARGUMENTS 3` is "true" AND task is minor (documentation, comments, formatting)

### Step 3: Core Logic and Testing Validation
If changes affect core business logic:
- Scan for existing tests related to the functionality
- Ensure failing tests exist that validate the expected behavior
- Require explicit approval before proceeding without proper tests
- Reject "irrelevant BS tests" - focus on meaningful test coverage

### Step 4: Task Execution
- Execute the task following the generated specification
- Apply coding standards and patterns from the codebase
- Ensure all changes align with the approved specification
- Run appropriate linting and type checking as defined in CLAUDE.md

### Step 5: Completion Validation
- Verify all requirements from the specification are met
- Run tests to ensure functionality works as expected
- Confirm no breaking changes were introduced


## Error Handling
- **Missing task description**: Prompt user for clear task specification
- **No relevant files found**: Request more specific task description or file paths
- **Spec generation failure**: Abort task and request manual specification
- **Missing required tests**: Block execution until tests are provided and approved
- **Build/lint failures**: Report errors and request fixes before proceeding

## Examples

### Basic Usage
```
/project:start-task "Add user authentication middleware to the FastAPI backend"
```

### With Priority
```
/project:start-task "Fix critical bug in payment processing" "high"
```

### Skip Spec (Use Cautiously)
```
/project:start-task "Update docstrings in user service" "low" "true"
```

## Context Priming Strategy

The command automatically identifies and reads relevant files based on:

1. **Task Keywords**: Maps common terms to file patterns
   - "auth" → authentication, middleware, security files
   - "api" → route definitions, endpoint files
   - "database" → models, migrations, connection files
   - "frontend" → React components, styles, hooks

2. **Project Structure**: Follows established patterns
   - Backend: `app/`, `src/`, `services/`, `models/`
   - Frontend: `components/`, `pages/`, `hooks/`, `utils/`
   - Tests: `tests/`, `__tests__/`, `*.test.*`

3. **Configuration Files**: Always includes
   - `package.json`, `pyproject.toml`, `Cargo.toml`
   - `CLAUDE.md`, `README.md`
   - Environment and config files

## Testing Requirements

### Core Logic Definition
Core logic includes:
- Business logic and domain models
- Data processing and transformation
- Security and authentication
- Payment and financial operations
- User data handling
- API endpoint logic (beyond simple CRUD)

### Test Quality Standards
- Tests must validate actual business requirements
- Tests should fail when the feature doesn't work
- No placeholder tests or trivial assertions
- Integration tests for complex workflows
- Unit tests for isolated business logic

## Notes
- This command enforces discipline in development workflow
- Specification creation ensures clear requirements before coding
- Testing requirements prevent shipping untested core functionality
- Context priming reduces the need for manual file exploration
- Can be extended with additional validation steps as needed