{"permissions": {"allow": ["Bash(find:*)", "Bash(ls:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)", "mcp__shrimp-task-manager__research_mode", "mcp__shrimp-task-manager__plan_task", "mcp__shrimp-task-manager__analyze_task", "mcp__shrimp-task-manager__reflect_task", "mcp__shrimp-task-manager__split_tasks", "mcp__shrimp-task-manager__get_task_detail", "mcp__shrimp-task-manager__execute_task", "Bash(cargo:*)", "mcp__shrimp-task-manager__verify_task", "mcp__rmcp-docs__fetch_rust_sdk_documentation", "mcp__rmcp-docs__search_rust_sdk_documentation", "mcp__rmcp-docs__search_rust_sdk_code", "mcp__rmcp-docs__fetch_generic_url_content", "<PERSON><PERSON>(sed:*)", "WebFetch(domain:openrouter.ai)", "WebFetch(domain:lib.rs)", "WebFetch(domain:github.com)", "Bash(OPENROUTER_API_KEY=test-key AUTORUN_LLM_PROVIDER=openrouter cargo test config::tests::test_default_permissions --lib)", "Bash(OPENROUTER_API_KEY=test-openrouter-key AUTORUN_LLM_PROVIDER=openrouter OPENROUTER_MODEL=anthropic/claude-3-sonnet cargo test -p autorun --lib -- --nocapture --exact config::tests::test_llm_config_has_api_key)", "<PERSON><PERSON>(chmod:*)", "Bash(./target/debug/autorun:*)", "<PERSON><PERSON>(env)", "Bash(RUST_LOG=debug cargo run -- --debug)", "Bash(ANTHROPIC_API_KEY=\"test-key\" RUST_LOG=debug cargo run -- --debug --prompt \"hello\")", "<PERSON><PERSON>(cat:*)", "Bash(RUST_LOG=debug timeout 30s cargo run 2 >& 1)", "Bash(grep:*)", "mcp__shrimp-task-manager__query_task", "Bash(rg:*)", "Bash(timeout 5s cargo run)", "Bash(rm:*)", "WebFetch(domain:ratatui.rs)", "WebFetch(domain:dev.to)", "WebFetch(domain:docs.rs)", "Ba<PERSON>(timeout 5s cargo run -- --debug)", "<PERSON><PERSON>(true)", "mcp__shrimp-task-manager__list_tasks", "Bash(rustc:*)", "Bash(RUST_LOG=info cargo run -- --config test_openrouter_config.toml --print \"create a new file called test.md in autorun-md with a poem about a developer building a CLI application.\" 2 >& 1)", "Bash(RUST_LOG=info cargo build 2 >& 1)", "Bash(RUST_LOG=info cargo run -- --config test_openrouter_config.toml --print \"hello\" 2 >& 1)", "Bash(RUST_LOG=info timeout 5 cargo run -- --config test_openrouter_config.toml --print \"hello\" 2 >& 1)", "Bash(RUST_LOG=info cargo run -- --config test_openrouter_config.toml --print \"create a file called test.txt with the content ''hello world''\" 2 >& 1)", "Bash(/Users/<USER>/.bun/install/global/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 5 -B 5 \"StreamingResponse\")", "Bash(./test_fallback)", "mcp__serena__initial_instructions", "mcp__serena__check_onboarding_performed", "mcp__serena__onboarding", "mcp__serena__list_dir", "mcp__serena__read_file", "mcp__serena__get_symbols_overview", "mcp__serena__search_for_pattern", "mcp__serena__find_symbol", "mcp__serena__find_file", "mcp__serena__think_about_collected_information", "mcp__serena__write_memory", "mcp__serena__replace_regex", "mcp__serena__execute_shell_command", "mcp__serena__get_current_config", "mcp__serena__think_about_whether_you_are_done", "mcp__serena__summarize_changes", "mcp__serena__think_about_task_adherence", "mcp__serena__read_memory", "mcp__serena__execute_shell_command", "mcp__clear-thought__debuggingapproach", "mcp__clear-thought__systemsthinking", "mcp__clear-thought__sequentialthinking", "mcp__serena__restart_language_server", "mcp__serena__insert_after_symbol", "mcp__serena__create_text_file", "mcp__serena__replace_symbol_body", "mcp__serena__activate_project", "mcp__serena__insert_before_symbol", "mcp__serena__find_referencing_symbols", "Bash(rustfmt:*)"], "deny": ["mcp__serena__create_text_file"]}, "enableAllProjectMcpServers": false}