# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Semantic Code Analysis

**IMPORTANT**: When working with this Rust codebase, ALWAYS prefer <PERSON>'s semantic tools for code analysis and navigation. <PERSON> provides Language Server Protocol (LSP) integration that offers superior code understanding compared to basic text-based tools.

### Preferred Workflow for Code Tasks:
1. **Use `get_symbols_overview`** to understand file/directory structure. whenever possible be as narrow as possible with your search. For example, `get_symbols_overview (relative_path: "src/agent")` is fine, `get_symbols_overview (relative_path: "src")` is not.
2. **Use `find_symbol`** to locate specific functions, structs, enums, methods
3. **Use `find_referencing_symbols`** to understand code relationships and dependencies
4. **Use symbolic editing tools** (`replace_symbol_body`, `insert_after_symbol`, `insert_before_symbol`) for precise modifications
5. **Only use `search_for_pattern`** when symbolic tools cannot locate what you need
6. **Avoid reading entire files** unless absolutely necessary - use symbolic tools to read only relevant code sections. 

IMPORTANT: If serena tool fails

### Available serena tools
- activate_project
- check_onboarding_performed
- delete_memory
- find_file
- find_referencing_symbols
- find_symbol
- get_current_config
- get_symbols_overview
- initial_instructions
- insert_after_symbol
- insert_before_symbol
- list_dir
- list_memories
- onboarding
- prepare_for_new_conversation
- read_memory
- remove_project
- replace_regex
- replace_symbol_body
- restart_language_server
- search_for_pattern
- summarize_changes
- switch_modes
- think_about_collected_information
- think_about_task_adherence
- think_about_whether_you_are_done
- write_memory

### Why Serena's Tools Are Superior:
- **LSP Integration**: Understands Rust syntax, semantics, and project structure
- **Symbol Navigation**: Precise location of functions, types, traits, implementations
- **Reference Analysis**: Find all usages of symbols across the codebase
- **Context-Aware**: Understands scope, visibility, and relationships
- **Efficient**: Read only the code sections you need, not entire files

### Example Semantic Workflow:
```bash
# 1. Get overview of a module
get_symbols_overview("src/agent/")

# 2. Find specific function with body
find_symbol("process_message", relative_path="src/agent/core.rs", include_body=true)

# 3. Find all references to understand usage
find_referencing_symbols("process_message", relative_path="src/agent/core.rs")

# 4. Make precise edits
replace_symbol_body("process_message", relative_path="src/agent/core.rs", body="...")
```

## Build and Development Commands

### Basic Commands
```bash
# Build the project
cargo build

# Run the application (interactive TUI mode)
cargo run

# Run with debug logging
cargo run -- --debug

# Run tests
cargo test

# Run tests with verbose output
cargo test -- --nocapture

# Format code
cargo fmt

# Lint code
cargo clippy

# Check code without building
cargo check

# Run with console logging forced in TUI mode (useful for debugging)
AUTORUN_FORCE_CONSOLE_LOG=1 cargo run
```

### Non-Interactive Mode
```bash
# Run with a prompt (text output)
cargo run -- --print "your prompt here"

# JSON output format
cargo run -- --print "your prompt" --output-format json

# Streaming JSON output
cargo run -- --print "your prompt" --output-format stream-json
```

### Subcommands
```bash
# Configuration management
cargo run -- config get <key>
cargo run -- config set <key> <value>
cargo run -- config list

# MCP server management
cargo run -- mcp add <name> <command>
cargo run -- mcp list
cargo run -- mcp serve

# Health checks and updates
cargo run -- doctor
cargo run -- update
```

## LLM Provider Auto-Detection

AutoRun automatically detects your LLM provider based on available environment variables:

### Automatic Detection Priority:
1. **Explicit Override**: `AUTORUN_LLM_PROVIDER` (anthropic|openrouter)
2. **OpenRouter**: If `OPENROUTER_API_KEY` is set
3. **Anthropic**: If `ANTHROPIC_API_KEY` is set
4. **Default**: Falls back to Anthropic

### Environment Variables:
```bash
# For OpenRouter (automatically detected)
export OPENROUTER_API_KEY="your-openrouter-key"
export OPENROUTER_MODEL="anthropic/claude-3-sonnet"  # optional

# For Anthropic (automatically detected)
export ANTHROPIC_API_KEY="your-anthropic-key"

# Manual override (takes precedence over auto-detection)
export AUTORUN_LLM_PROVIDER="openrouter"
export OPENROUTER_API_KEY="your-key"
```

### Model Selection Examples:
```bash
# Use OpenRouter with auto-detection
export OPENROUTER_API_KEY="sk-or-..."
cargo run

# Use specific model with provider override
export AUTORUN_LLM_PROVIDER="openrouter"
cargo run -- --model "anthropic/claude-3-sonnet"
```

## Logging and Debugging

AutoRun uses a dual-layer logging system to prevent TUI interference:

### Log Output
- **File Logs**: Always written to `logs/autorun-YYYYMMDD-HHMMSS.log` in JSON format
- **Console Logs**: Automatically suppressed in TUI mode to prevent UI corruption
  - All console output is redirected to a null writer in TUI mode
  - Enabled in non-interactive mode (`--print`)
  - Can be forced in TUI mode with `AUTORUN_FORCE_CONSOLE_LOG=1` (will break UI)
- **TUI Log Panel**: Integrated log viewer in the right panel of the TUI interface
  - Currently shows placeholder content due to ratatui version compatibility issues
  - Future versions will display live logs using tui-logger when compatibility is resolved

### Viewing Logs
```bash
# Use the log viewer tool (recommended for TUI debugging)
./target/debug/log_viewer -- --debug

# Or tail the log file directly
tail -f logs/autorun-*.log | jq '.'

# Or use your external log monitoring
./scripts/monitor_agent_logs.sh
```

## Architecture Overview

### Core Design
AutoRun is an agentic coding assistant built as a high-performance Rust CLI with TUI interface. The architecture follows a modular async-first design:

- **TUI Framework**: Ratatui with Crossterm for cross-platform terminal UI
- **Async Runtime**: Tokio for concurrent operations between UI, LLM calls, and tool execution
- **MCP Protocol**: Model Context Protocol integration for extensible tool ecosystem via `rmcp` crate
- **Permission System**: Multi-layered safety for tool execution
- **Session Management**: Persistent conversation state with UUID tracking

### Module Structure
- **`agent/`**: AI agent core with conversation orchestration and LLM interaction patterns
- **`tools/`**: Tool trait definitions, execution context, registry, and built-in tools (file I/O, etc.)
- **`mcp/`**: MCP server/client implementation for external tool integration
- **`ui/`**: Ratatui-based TUI with message rendering, input handling, and status display
- **`llm/`**: Language model client abstractions and API integrations
- **`session/`**: Conversation persistence and state management
- **`config/`**: Configuration management for user preferences and tool settings

### Key Patterns
1. **Async Message Passing**: `mpsc::channel` for UI ↔ Agent communication via `AppMessage` enum
2. **Tool Abstraction**: `Tool` trait with async execution, permission checking, and JSON schema validation
3. **Execution Context**: `ExecutionContext` carries permissions, working directory, and session state
4. **Error Handling**: `thiserror` for structured errors with `AutorunError` and `ToolError` hierarchies

### Application Modes
- **Interactive TUI**: Default mode with real-time chat interface (Normal/Input modes)
- **Non-Interactive CLI**: Batch processing with text/JSON/streaming JSON output formats
- **MCP Server**: Standalone server mode for external tool integration

### Tool System
The tool system supports both built-in tools and MCP-based external tools:
- Built-in tools implement the `Tool` trait directly
- MCP tools integrate via the rmcp client with transport-agnostic communication
- All tools go through permission validation and execution context management
- Tool registry manages available tools and their capabilities

### Development Notes
- Uses standard Rust tooling without custom configuration
- Release builds are optimized for size (`opt-level = "z"`, LTO enabled)
- Panic handler restores terminal state in TUI mode
- Concurrent data structures via `dashmap` for thread-safe operations

## IMPORTANT: Concurrent Sub-Agent Delegation & Tool-Oriented Orchestration Strategy

**ALWAYS maximize concurrency and parallelization for complex tasks by leveraging sub-agents and tool-triggering protocols:**

1. **Task Decomposition & Sub-Agent Assignment**: Break down large implementations into granular, independent components. Assign each component to a dedicated sub-agent, ensuring each sub-agent is responsible for a clearly defined, tool-triggerable task.
2. **Automated Tool Invocation**: Design sub-agents to autonomously trigger relevant tools or APIs as soon as their assigned task is ready for execution. Sub-agents should monitor for tool completion events and handle results asynchronously.
3. **Dynamic Dependency Graphs**: Construct and maintain a real-time dependency graph of all sub-agent tasks. Use this graph to batch sub-agent execution, launching all sub-agents whose dependencies are satisfied in parallel. Default batch size: 2, but dynamically adjust for optimal throughput.
4. **Explicit Interface Contracts**: Define robust interface contracts and shared state protocols for all sub-agents. Ensure that each sub-agent exposes a clear contract for tool invocation, result reporting, and error handling, regardless of the underlying language or platform.
5. **Coordination & Shared State Protocols**: Employ thread-safe or concurrency-safe mechanisms for shared state among sub-agents (e.g., locks, semaphores, message queues, or distributed state management). Use asynchronous event systems or message-passing channels for sub-agent coordination, progress updates, and tool-triggered event propagation.
6. **Sub-Agent Lifecycle Management**: Implement sub-agent spawning, monitoring, and termination protocols. Sub-agents should be able to self-terminate upon task completion or escalate to a supervisor agent in case of tool failure or deadlock.
7. **Integration & Validation**: After all sub-agents in a batch complete, trigger integration tests or validation routines to ensure deliverable consistency before progressing to dependent batches.

**Example Workflow:**
- Analyze the overall task and generate a dependency graph mapping all sub-agent tasks
- Group independent sub-agent tasks into parallelizable batches
- Spawn sub-agents for each batch, each equipped to trigger tools as needed
- Sub-agents execute, invoke tools, and update shared state asynchronously
- Upon batch completion, run integration/validation routines
- Repeat for subsequent batches until the full task is complete

**Benefits:**
- True parallelism via concurrent sub-agent execution and tool-triggering
- Higher code quality through focused, independent sub-agent development
- Explicit architecture with well-defined sub-agent and tool interfaces
- Reduced coordination overhead through automated batch processing and event-driven protocols

**MANDATE:**
Apply this sub-agent and tool-triggering orchestration strategy to ALL complex implementations to maximize development velocity, code quality, and system scalability, regardless of programming language or platform.

### Mandatory Implementation Strategy:
**FOR COMPLEX TASKS:**
1. **Identify Independent Components**: Break task into truly independent pieces
2. **Create Multiple Files Simultaneously**: Use Write/Edit tools in single response
3. **Build Core Functionality First**: Working implementation beats perfect architecture
4. **Leverage Existing Infrastructure**: Don't reinvent what already works
5. **Iterate and Extend**: Add features incrementally after core works

**FOR SIMPLE TASKS:**
- Just implement directly - no "Agent" needed

### Critical Guidelines:

**✅ DO:**
- Make multiple tool calls in single response for parallelism
- Build minimal working implementations first
- Focus on integration with existing codebase
- Test and validate as you build

### Success Metrics:
- **30 minutes or less** for complex feature implementation
- **Working code** that integrates with existing system
- **Minimal coordination overhead** between components
- **Real parallelism** through simultaneous tool usage